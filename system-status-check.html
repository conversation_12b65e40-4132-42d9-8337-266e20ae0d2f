<!DOCTYPE html>
<html>
<head>
    <title>Investment System Status Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .status-item { margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 4px solid #ccc; }
        .success { background: #d4edda; border-left-color: #28a745; }
        .error { background: #f8d7da; border-left-color: #dc3545; }
        .warning { background: #fff3cd; border-left-color: #ffc107; }
        .info { background: #d1ecf1; border-left-color: #17a2b8; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .credentials { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Investment System Status Check</h1>
        
        <div class="credentials">
            <h3>🔐 Login Credentials</h3>
            <p><strong>Admin:</strong> <EMAIL> / password</p>
            <p><strong>Manager:</strong> <EMAIL> / password</p>
            <p><strong>Partner:</strong> <EMAIL> / password</p>
        </div>
        
        <button onclick="runFullSystemCheck()">🔍 Run Full System Check</button>
        <button onclick="testLogin()">🔑 Test Login Only</button>
        <button onclick="clearResults()">🧹 Clear Results</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost/Investment-System-eSkillVisor/backend';
        
        function addResult(title, status, message, details = null) {
            const resultsDiv = document.getElementById('results');
            const statusClass = status === 'success' ? 'success' : status === 'error' ? 'error' : status === 'warning' ? 'warning' : 'info';
            const icon = status === 'success' ? '✅' : status === 'error' ? '❌' : status === 'warning' ? '⚠️' : 'ℹ️';
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `status-item ${statusClass}`;
            resultDiv.innerHTML = `
                <h4>${icon} ${title}</h4>
                <p>${message}</p>
                ${details ? `<pre>${JSON.stringify(details, null, 2)}</pre>` : ''}
            `;
            
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function checkEndpoint(name, url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: { 'Origin': 'http://localhost:5173', ...options.headers },
                    ...options
                });
                
                const responseText = await response.text();
                
                if (!response.ok) {
                    addResult(name, 'error', `HTTP ${response.status}: ${response.statusText}`, responseText);
                    return { success: false, status: response.status, error: responseText };
                }
                
                try {
                    const data = JSON.parse(responseText);
                    addResult(name, 'success', `Working correctly`, data);
                    return { success: true, data };
                } catch (parseError) {
                    addResult(name, 'error', `Invalid JSON response`, responseText);
                    return { success: false, error: 'Invalid JSON' };
                }
                
            } catch (error) {
                addResult(name, 'error', `Network error: ${error.message}`);
                return { success: false, error: error.message };
            }
        }
        
        async function testLogin() {
            clearResults();
            addResult('Login Test', 'info', 'Testing login functionality...');
            
            const loginResult = await checkEndpoint('Login API', `${API_BASE}/login.php`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    email: '<EMAIL>',
                    password: 'password'
                })
            });
            
            if (loginResult.success && loginResult.data.success) {
                const token = loginResult.data.data.access_token;
                
                // Test getCurrentUser with the token
                await checkEndpoint('Get Current User', `${API_BASE}/me.php`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                addResult('Login Flow', 'success', 'Complete login flow working! You can now login at http://localhost:5173');
            } else {
                addResult('Login Flow', 'error', 'Login failed - check backend configuration');
            }
        }
        
        async function runFullSystemCheck() {
            clearResults();
            addResult('System Check', 'info', 'Running comprehensive system check...');
            
            // Check frontend
            try {
                const frontendResponse = await fetch('http://localhost:5173');
                if (frontendResponse.ok) {
                    addResult('Frontend Server', 'success', 'React app running on http://localhost:5173');
                } else {
                    addResult('Frontend Server', 'error', 'Frontend not accessible');
                }
            } catch (error) {
                addResult('Frontend Server', 'error', 'Frontend server not running');
            }
            
            // Check backend endpoints
            await checkEndpoint('Backend API', `${API_BASE}/`);
            await checkEndpoint('Local Test', `${API_BASE}/local-test.php`);
            
            // Test login flow
            await testLogin();
            
            // Check database
            const dbTest = await checkEndpoint('Database Test', `${API_BASE}/local-test.php`);
            if (dbTest.success && dbTest.data.success) {
                const tests = dbTest.data.tests;
                if (tests.db_connection && tests.db_connection.status === 'PASS') {
                    addResult('Database Connection', 'success', 'Database connected successfully');
                } else {
                    addResult('Database Connection', 'error', 'Database connection failed');
                }
                
                if (tests.db_tables && tests.db_tables.status === 'PASS') {
                    addResult('Database Tables', 'success', tests.db_tables.result);
                } else {
                    addResult('Database Tables', 'warning', 'Database tables may not be set up');
                }
            }
            
            addResult('System Status', 'info', 'System check completed. Review results above.');
        }
        
        // Auto-run basic check on page load
        window.onload = function() {
            setTimeout(() => {
                addResult('Welcome', 'info', 'Investment System Status Checker loaded. Click "Run Full System Check" to test all components.');
            }, 500);
        };
    </script>
</body>
</html>
