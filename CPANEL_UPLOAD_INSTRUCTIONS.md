# 🚀 EskillVisor cPanel Deployment Instructions

## 📋 Pre-Deployment Checklist

### ✅ What You Have Ready
- **Fresh Production Build**: `cpanel-deployment-2025-07-19/` directory
- **cPanel Credentials**: wallistry / +GlESn;lJteQ%VXf
- **Database Configuration**: Ready for wallistry.pk hosting
- **All Files Prepared**: Frontend + Backend + Database migrations

## 🔐 cPanel Access Information

```
Domain: wallistry.pk
cPanel URL: https://wallistry.pk:2083
Username: wallistry
Password: +GlESn;lJteQ%VXf
Package: hexatech_2GB Plan
Home Directory: /home9/wallistry/
```

## 📁 File Upload Structure

### Complete Directory Mapping

```
LOCAL DIRECTORY                          → CPANEL DESTINATION
cpanel-deployment-2025-07-19/public_html/ → /home9/wallistry/public_html/

Specific Files:
├── index.html                           → /home9/wallistry/public_html/index.html
├── .htaccess                           → /home9/wallistry/public_html/.htaccess
├── assets/index-DMBFIW6C.css           → /home9/wallistry/public_html/assets/index-DMBFIW6C.css
├── assets/index-DN-MEaKQ.js            → /home9/wallistry/public_html/assets/index-DN-MEaKQ.js
├── assets/ui-D4-VV43f.js               → /home9/wallistry/public_html/assets/ui-D4-VV43f.js
├── assets/vendor-CMsH-4Bd.js           → /home9/wallistry/public_html/assets/vendor-CMsH-4Bd.js
└── api/                                → /home9/wallistry/public_html/api/
    ├── index.php                       → /home9/wallistry/public_html/api/index.php
    ├── .htaccess                       → /home9/wallistry/public_html/api/.htaccess
    ├── config/                         → /home9/wallistry/public_html/api/config/
    ├── controllers/                    → /home9/wallistry/public_html/api/controllers/
    ├── models/                         → /home9/wallistry/public_html/api/models/
    ├── services/                       → /home9/wallistry/public_html/api/services/
    ├── migrations/                     → /home9/wallistry/public_html/api/migrations/
    └── uploads/                        → /home9/wallistry/public_html/api/uploads/
```

## 🔧 Step-by-Step Deployment Process

### Step 1: Access cPanel File Manager
1. **Login to cPanel**:
   - Go to: https://wallistry.pk:2083
   - Username: `wallistry`
   - Password: `+GlESn;lJteQ%VXf`

2. **Open File Manager**:
   - Click "File Manager" in the Files section
   - Navigate to `/home9/wallistry/public_html/`

### Step 2: Backup Existing Files (Optional)
1. **Create Backup Directory**:
   ```
   /home9/wallistry/backups/backup-2025-07-19/
   ```

2. **Move Current Files**:
   - Select all files in `public_html/`
   - Move to backup directory

### Step 3: Upload New Files

#### Method A: ZIP Upload (Recommended)
1. **Create ZIP file locally**:
   - Compress `cpanel-deployment-2025-07-19/public_html/` contents
   - Name it: `eskillvisor-deployment-2025-07-19.zip`

2. **Upload ZIP**:
   - In File Manager, navigate to `/home9/wallistry/public_html/`
   - Click "Upload" button
   - Select the ZIP file
   - Wait for upload completion

3. **Extract ZIP**:
   - Right-click the uploaded ZIP file
   - Select "Extract"
   - Choose "Extract to current directory"
   - Delete the ZIP file after extraction

#### Method B: Direct File Upload
1. **Upload Frontend Files**:
   ```
   Upload to: /home9/wallistry/public_html/
   Files:
   - index.html
   - .htaccess
   - assets/ (entire directory)
   ```

2. **Upload Backend Files**:
   ```
   Upload to: /home9/wallistry/public_html/api/
   Files:
   - All PHP files from backend/
   - All subdirectories (config/, controllers/, models/, etc.)
   ```

### Step 4: Set File Permissions
1. **Set Directory Permissions**:
   ```
   public_html/           → 755
   public_html/api/       → 755
   public_html/api/uploads/ → 777 (writable)
   ```

2. **Set File Permissions**:
   ```
   .htaccess files        → 644
   PHP files             → 644
   HTML/CSS/JS files     → 644
   ```

## 🗄️ Database Setup

### Step 1: Create Database
1. **Access MySQL Databases**:
   - In cPanel, click "MySQL Databases"

2. **Create Database**:
   - Database Name: `eskillvisor_db`
   - Full Name: `wallistry_eskillvisor_db`

3. **Create User**:
   - Username: `eskill`
   - Full Username: `wallistry_eskill`
   - Password: `EskillVisor2024!`

4. **Assign User to Database**:
   - Select user: `wallistry_eskill`
   - Select database: `wallistry_eskillvisor_db`
   - Grant ALL PRIVILEGES

### Step 2: Run Database Installation
1. **Access Installation Script**:
   - Go to: https://wallistry.pk/api/install.php

2. **Follow Installation Process**:
   - Creates all database tables
   - Sets up default users
   - Configures initial data

## 🧪 Testing & Verification

### Step 1: Test API Endpoints
```bash
# Test basic API
https://wallistry.pk/api/test

# Expected Response:
{
  "success": true,
  "data": {
    "message": "API endpoint is working!"
  }
}
```

### Step 2: Test Database Connection
```bash
# Test database
https://wallistry.pk/api/db-test.php

# Expected Response:
{
  "success": true,
  "message": "Database connection successful"
}
```

### Step 3: Test Frontend
1. **Access Application**:
   - Go to: https://wallistry.pk

2. **Test Login**:
   - Email: `<EMAIL>`
   - Password: `password`

3. **Verify Features**:
   - Dashboard loads
   - API calls work
   - Navigation functions

## 🔒 Post-Deployment Security

### Immediate Actions Required
1. **Change Default Passwords**:
   - Login as admin
   - Go to Settings → User Management
   - Change all default passwords

2. **Configure SMTP** (Optional):
   - Edit `/api/config/config.php`
   - Update SMTP settings for email functionality

3. **Enable SSL Certificate**:
   - In cPanel, go to SSL/TLS
   - Install Let's Encrypt certificate

## 📞 Troubleshooting

### Common Issues & Solutions

#### 1. "500 Internal Server Error"
- **Check**: File permissions (PHP files should be 644)
- **Check**: .htaccess syntax
- **Check**: PHP error logs in cPanel

#### 2. "Database Connection Failed"
- **Verify**: Database credentials in `/api/config/config.php`
- **Check**: Database user has proper privileges
- **Test**: Database connection via cPanel phpMyAdmin

#### 3. "CORS Errors"
- **Check**: .htaccess CORS headers
- **Verify**: Frontend API URL configuration
- **Test**: API endpoints directly

#### 4. "Frontend Not Loading"
- **Check**: index.html in public_html root
- **Verify**: .htaccess React Router configuration
- **Test**: Static assets loading

## 📊 System URLs

```
Frontend Application: https://wallistry.pk
API Base URL:        https://wallistry.pk/api
Database Admin:      https://wallistry.pk:2083 (cPanel → phpMyAdmin)
File Manager:        https://wallistry.pk:2083 (cPanel → File Manager)
```

## ✅ Deployment Complete!

Your EskillVisor Investment System is now deployed and ready for production use!

**Next Steps**:
1. Change default passwords
2. Configure email settings
3. Set up SSL certificate
4. Monitor system performance
5. Create regular backups
