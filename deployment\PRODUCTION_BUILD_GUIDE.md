# 🚀 EskillVisor Production Build & Deployment

## **📦 PRODUCTION ZIP CREATION (Built Files)**

### **STEP 1: Create Production Folder Structure**

Create a new folder: `eskillvisor-production-2025-07-18`

### **STEP 2: Copy Built Frontend Files**

From `C:\Users\<USER>\Desktop\EskillVisor\dist\`:

```
📁 Copy these to root of production folder:
✅ index.html                 (Built frontend entry point)

📁 Copy this entire folder:
✅ assets/                    (Built CSS, JS, and other assets)
   ├── index-ByH_DjoP.css    (Built CSS)
   ├── index-C2o0ZJAW.js     (Built main JS)
   ├── ui-DUfY2fMy.js        (Built UI components)
   └── vendor-CMsH-4Bd.js    (Built vendor libraries)
```

### **STEP 3: Copy Backend Files as 'api'**

From `C:\Users\<USER>\Desktop\EskillVisor\backend\`:

```
📁 Copy entire backend folder and rename to 'api':
✅ api/                       (Renamed from 'backend')
   ├── controllers/
   ├── models/
   ├── config/
   ├── core/
   ├── services/
   ├── migrations/
   └── index.php
```

### **STEP 4: Add Updated Files**

Replace these files in the api folder with updated versions:

From `C:\Users\<USER>\Desktop\EskillVisor\deployment\files\backend\`:

```
✅ api/controllers/CompanyController.php    (REPLACE with updated version)
✅ api/controllers/UserController.php       (REPLACE with updated version)
✅ api/models/Company.php                   (REPLACE with updated version)
```

### **STEP 5: Add Database Migration**

From `C:\Users\<USER>\Desktop\EskillVisor\deployment\database\`:

```
✅ api/migrations/014_add_company_extended_fields.sql    (ADD new migration)
```

### **STEP 6: Add .htaccess File**

Create `.htaccess` in the root of production folder:

```apache
RewriteEngine On

# Handle React Router (History API fallback)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/api/
RewriteRule . /index.html [L]

# API Routes - Forward to backend
RewriteCond %{REQUEST_URI} ^/api/
RewriteRule ^api/(.*)$ api/index.php [QSA,L]

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
</IfModule>
```

### **STEP 7: Final Production Structure**

Your production folder should look like this:

```
eskillvisor-production-2025-07-18/
├── api/                              (Backend PHP files)
│   ├── controllers/
│   │   ├── CompanyController.php     (UPDATED)
│   │   ├── UserController.php        (UPDATED)
│   │   └── ... (other controllers)
│   ├── models/
│   │   ├── Company.php               (UPDATED)
│   │   └── ... (other models)
│   ├── migrations/
│   │   ├── 014_add_company_extended_fields.sql (NEW)
│   │   └── ... (other migrations)
│   ├── config/
│   ├── core/
│   ├── services/
│   └── index.php
├── assets/                           (Built frontend assets)
│   ├── index-ByH_DjoP.css           (Built CSS)
│   ├── index-C2o0ZJAW.js            (Built main JS)
│   ├── ui-DUfY2fMy.js               (Built UI components)
│   └── vendor-CMsH-4Bd.js           (Built vendor libraries)
├── .htaccess                         (Server configuration)
└── index.html                        (Built frontend entry point)
```

### **STEP 8: Create ZIP File**

1. **Select the entire folder:** `eskillvisor-production-2025-07-18`
2. **Right-click** → **Send to** → **Compressed (zipped) folder**
3. **Rename** to: `eskillvisor-production-2025-07-18.zip`

---

## **📍 PRODUCTION ZIP DETAILS**

```
📦 File Name: eskillvisor-production-2025-07-18.zip
📁 Location: C:\Users\<USER>\Desktop\eskillvisor-production-2025-07-18.zip
📊 Size: ~5-10 MB (optimized production build)
📋 Contents: Built frontend + Backend API + Configuration
```

---

## **🎯 DEPLOYMENT TO CPANEL**

### **Upload Path:**
```
/home9/wallistry/eskillvisor.wallistry.pk/
```

### **Deployment Steps:**
1. **Login to cPanel** for wallistry.pk
2. **File Manager** → Navigate to `/home9/wallistry/eskillvisor.wallistry.pk/`
3. **Upload** → `eskillvisor-production-2025-07-18.zip`
4. **Extract** → Right-click ZIP → Extract to current directory
5. **Overwrite** → Choose "Yes" to overwrite existing files
6. **Delete ZIP** → Remove ZIP file after extraction

### **Result After Extraction:**
```
/home9/wallistry/eskillvisor.wallistry.pk/
├── api/                    (Backend accessible at /api/)
├── assets/                 (Frontend assets)
├── .htaccess              (Server configuration)
└── index.html             (Frontend entry point)
```

---

## **🗄️ DATABASE MIGRATION (REQUIRED)**

**Execute in phpMyAdmin:**

```sql
-- Add extended fields to companies table for enhanced company management
ALTER TABLE companies 
ADD COLUMN company_director_id INT NULL AFTER name,
ADD COLUMN registration_territory VARCHAR(255) NULL AFTER company_director_id,
ADD COLUMN ein_number VARCHAR(50) NULL AFTER registration_territory,
ADD COLUMN marketplace VARCHAR(100) NULL AFTER ein_number,
ADD COLUMN custom_marketplace VARCHAR(255) NULL AFTER marketplace,
ADD COLUMN approval_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' AFTER status,
ADD COLUMN approved_by INT NULL AFTER approval_status,
ADD COLUMN approved_at TIMESTAMP NULL AFTER approved_by,
ADD COLUMN rejected_by INT NULL AFTER approved_at,
ADD COLUMN rejected_at TIMESTAMP NULL AFTER rejected_by,
ADD COLUMN rejection_reason TEXT NULL AFTER rejected_at,
ADD INDEX idx_company_director (company_director_id),
ADD INDEX idx_registration_territory (registration_territory),
ADD INDEX idx_ein_number (ein_number),
ADD INDEX idx_marketplace (marketplace),
ADD INDEX idx_approval_status (approval_status),
ADD FOREIGN KEY (company_director_id) REFERENCES users(id) ON DELETE SET NULL,
ADD FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
ADD FOREIGN KEY (rejected_by) REFERENCES users(id) ON DELETE SET NULL;

UPDATE companies SET approval_status = 'approved' WHERE approval_status IS NULL OR approval_status = 'pending';
```

---

## **✅ PRODUCTION BUILD ADVANTAGES**

### **✅ Optimized Performance:**
- Minified CSS and JavaScript
- Bundled and compressed assets
- Optimized for production

### **✅ Proper Structure:**
- Frontend served from root
- Backend API accessible at /api/
- Clean URL routing with .htaccess

### **✅ Security:**
- No source code exposed
- Proper server configuration
- Security headers enabled

---

## **🚀 READY FOR PRODUCTION DEPLOYMENT!**

**This production build contains:**
- ✅ **Built Frontend:** Optimized React app (index.html + assets/)
- ✅ **Backend API:** Complete PHP backend (api/ directory)
- ✅ **Server Config:** .htaccess for routing and security
- ✅ **Database Migration:** SQL for new company fields
- ✅ **User Management Updates:** All latest features included

**Follow the manual steps above to create the production ZIP file!** 🚀
