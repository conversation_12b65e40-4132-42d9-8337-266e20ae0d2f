const fs = require('fs');
const path = require('path');

class DynamicChangeAnalyzer {
    constructor(sourceDir) {
        this.sourceDir = sourceDir;
        this.changePatterns = {
            // Frontend patterns
            'dashboard_quick_actions': {
                files: ['src/pages/superadmin/Dashboard.jsx', 'src/pages/manager/Dashboard.jsx'],
                patterns: [/Create New Partner/i, /Add Partner/i, /Add Manager/i],
                description: 'Dashboard Quick Actions Updates'
            },
            'user_modal_enhancements': {
                files: ['src/components/modals/AddUserModal.jsx'],
                patterns: [/firstName|lastName|mobile|address|businessModel|cnicDocument/i],
                description: 'Enhanced User Creation Modal with Extended Fields'
            },
            'user_management_buttons': {
                files: ['src/pages/superadmin/UserManagement.jsx'],
                patterns: [/Add Manager|Add Partner/i, /showAddManagerModal/i],
                description: 'User Management Interface Updates'
            },
            
            // Backend patterns
            'user_controller_enhancements': {
                files: ['backend/controllers/UserController.php'],
                patterns: [/firstName|lastName|mobile|address|business_model|cnic_document/i],
                description: 'Enhanced User Controller with New Fields'
            },
            'user_model_updates': {
                files: ['backend/models/User.php'],
                patterns: [/first_name|last_name|mobile|address|business_model|cnic_document_path/i],
                description: 'User Model Extended with New Fields'
            },
            'document_upload_system': {
                files: ['backend/controllers/UserController.php'],
                patterns: [/handleDocumentUpload|cnicDocument|uploads\/documents/i],
                description: 'Document Upload System for User Verification'
            },
            
            // Database patterns
            'user_table_extensions': {
                files: ['backend/migrations/013_add_user_extended_fields.sql'],
                patterns: [/ALTER TABLE users|first_name|last_name|mobile|address|business_model/i],
                description: 'Database Schema Updates for Extended User Fields'
            }
        };
    }

    /**
     * Analyze actual changes in the codebase
     */
    analyzeChanges() {
        const detectedChanges = {
            frontend: [],
            backend: [],
            database: [],
            features: []
        };

        for (const [changeKey, config] of Object.entries(this.changePatterns)) {
            const changeInfo = this.analyzeChangePattern(changeKey, config);
            if (changeInfo.detected) {
                // Categorize changes
                if (config.files.some(f => f.startsWith('src/'))) {
                    detectedChanges.frontend.push(changeInfo);
                } else if (config.files.some(f => f.startsWith('backend/controllers') || f.startsWith('backend/models'))) {
                    detectedChanges.backend.push(changeInfo);
                } else if (config.files.some(f => f.includes('migrations'))) {
                    detectedChanges.database.push(changeInfo);
                }
                
                detectedChanges.features.push(changeInfo);
            }
        }

        return detectedChanges;
    }

    /**
     * Analyze specific change pattern
     */
    analyzeChangePattern(changeKey, config) {
        const result = {
            key: changeKey,
            description: config.description,
            detected: false,
            files: [],
            details: []
        };

        for (const filePath of config.files) {
            const fullPath = path.join(this.sourceDir, filePath);
            
            if (fs.existsSync(fullPath)) {
                try {
                    const content = fs.readFileSync(fullPath, 'utf8');
                    const matches = this.findPatternMatches(content, config.patterns);
                    
                    if (matches.length > 0) {
                        result.detected = true;
                        result.files.push(filePath);
                        result.details.push(...matches);
                    }
                } catch (error) {
                    console.warn(`Warning: Could not read ${filePath}: ${error.message}`);
                }
            }
        }

        return result;
    }

    /**
     * Find pattern matches in content
     */
    findPatternMatches(content, patterns) {
        const matches = [];
        
        for (const pattern of patterns) {
            const found = content.match(pattern);
            if (found) {
                matches.push({
                    pattern: pattern.toString(),
                    match: found[0],
                    context: this.getMatchContext(content, found.index, 50)
                });
            }
        }
        
        return matches;
    }

    /**
     * Get context around a match
     */
    getMatchContext(content, index, contextLength) {
        const start = Math.max(0, index - contextLength);
        const end = Math.min(content.length, index + contextLength);
        return content.substring(start, end).replace(/\n/g, ' ').trim();
    }

    /**
     * Generate dynamic feature descriptions
     */
    generateFeatureDescriptions(changes) {
        const descriptions = [];

        // Analyze frontend changes
        if (changes.frontend.length > 0) {
            descriptions.push('🎨 Frontend Enhancements:');
            changes.frontend.forEach(change => {
                descriptions.push(`   ✅ ${change.description}`);
            });
        }

        // Analyze backend changes
        if (changes.backend.length > 0) {
            descriptions.push('🔧 Backend Improvements:');
            changes.backend.forEach(change => {
                descriptions.push(`   ✅ ${change.description}`);
            });
        }

        // Analyze database changes
        if (changes.database.length > 0) {
            descriptions.push('🗄️ Database Updates:');
            changes.database.forEach(change => {
                descriptions.push(`   ✅ ${change.description}`);
            });
        }

        return descriptions;
    }

    /**
     * Generate migration information
     */
    generateMigrationInfo() {
        const migrationsDir = path.join(this.sourceDir, 'backend/migrations');
        const migrations = [];

        if (fs.existsSync(migrationsDir)) {
            const files = fs.readdirSync(migrationsDir)
                .filter(f => f.endsWith('.sql'))
                .sort()
                .reverse(); // Get latest migrations first

            for (const file of files.slice(0, 3)) { // Get last 3 migrations
                const filePath = path.join(migrationsDir, file);
                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    const description = this.extractMigrationDescription(content);
                    
                    migrations.push({
                        file: file,
                        path: `backend/migrations/${file}`,
                        description: description,
                        changes: this.extractMigrationChanges(content)
                    });
                } catch (error) {
                    console.warn(`Warning: Could not read migration ${file}: ${error.message}`);
                }
            }
        }

        return migrations;
    }

    /**
     * Extract migration description from SQL comments
     */
    extractMigrationDescription(content) {
        const lines = content.split('\n');
        const commentLine = lines.find(line => line.trim().startsWith('--') && line.length > 10);
        
        if (commentLine) {
            return commentLine.replace(/^--\s*/, '').trim();
        }
        
        return 'Database schema updates';
    }

    /**
     * Extract specific changes from migration
     */
    extractMigrationChanges(content) {
        const changes = [];
        
        // Look for ALTER TABLE statements
        const alterMatches = content.match(/ALTER TABLE\s+(\w+)\s+([^;]+)/gi);
        if (alterMatches) {
            alterMatches.forEach(match => {
                const tableMatch = match.match(/ALTER TABLE\s+(\w+)/i);
                if (tableMatch) {
                    changes.push(`Modified ${tableMatch[1]} table structure`);
                }
            });
        }

        // Look for CREATE TABLE statements
        const createMatches = content.match(/CREATE TABLE\s+(\w+)/gi);
        if (createMatches) {
            createMatches.forEach(match => {
                const tableMatch = match.match(/CREATE TABLE\s+(\w+)/i);
                if (tableMatch) {
                    changes.push(`Created ${tableMatch[1]} table`);
                }
            });
        }

        // Look for ADD COLUMN statements
        const columnMatches = content.match(/ADD COLUMN\s+(\w+)/gi);
        if (columnMatches) {
            columnMatches.forEach(match => {
                const columnMatch = match.match(/ADD COLUMN\s+(\w+)/i);
                if (columnMatch) {
                    changes.push(`Added ${columnMatch[1]} column`);
                }
            });
        }

        return changes.length > 0 ? changes : ['Database schema modifications'];
    }

    /**
     * Generate dynamic title based on detected changes
     */
    generateDynamicTitle(dynamicChanges) {
        if (dynamicChanges.frontend.length > 0 && dynamicChanges.backend.length > 0) {
            return 'FULL-STACK ENHANCEMENTS READY FOR DEPLOYMENT';
        } else if (dynamicChanges.frontend.length > 0) {
            return 'FRONTEND ENHANCEMENTS READY FOR DEPLOYMENT';
        } else if (dynamicChanges.backend.length > 0) {
            return 'BACKEND IMPROVEMENTS READY FOR DEPLOYMENT';
        } else if (dynamicChanges.database.length > 0) {
            return 'DATABASE UPDATES READY FOR DEPLOYMENT';
        } else {
            return 'SYSTEM UPDATES READY FOR DEPLOYMENT';
        }
    }
}

module.exports = DynamicChangeAnalyzer;
