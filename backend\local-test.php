<?php
/**
 * Local Development Test Script
 * Simple test to verify local environment setup
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:5173');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$tests = [];

// Test 1: PHP Version
$tests['php_version'] = [
    'test' => 'PHP Version',
    'result' => phpversion(),
    'status' => version_compare(phpversion(), '7.4.0', '>=') ? 'PASS' : 'FAIL'
];

// Test 2: Config File
$config_exists = file_exists(__DIR__ . '/config/config.php');
$tests['config_file'] = [
    'test' => 'Config File Exists',
    'result' => $config_exists ? 'Found' : 'Not Found',
    'status' => $config_exists ? 'PASS' : 'FAIL'
];

// Test 3: Load Config
if ($config_exists) {
    try {
        require_once __DIR__ . '/config/config.php';
        $tests['config_load'] = [
            'test' => 'Config File Load',
            'result' => 'Loaded successfully',
            'status' => 'PASS'
        ];
        
        // Test 4: Database Constants
        $db_constants = defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER');
        $tests['db_constants'] = [
            'test' => 'Database Constants',
            'result' => $db_constants ? 'All defined' : 'Missing constants',
            'status' => $db_constants ? 'PASS' : 'FAIL'
        ];
        
    } catch (Exception $e) {
        $tests['config_load'] = [
            'test' => 'Config File Load',
            'result' => 'Error: ' . $e->getMessage(),
            'status' => 'FAIL'
        ];
    }
}

// Test 5: Database Connection
if (isset($db_constants) && $db_constants) {
    try {
        require_once __DIR__ . '/config/database.php';
        $db = Database::getInstance();
        $connection = $db->getConnection();
        
        $tests['db_connection'] = [
            'test' => 'Database Connection',
            'result' => 'Connected successfully',
            'status' => 'PASS'
        ];
        
        // Test 6: Test Query
        $stmt = $connection->query("SELECT 1 as test");
        $result = $stmt->fetch();
        
        $tests['db_query'] = [
            'test' => 'Database Query',
            'result' => 'Query executed, result: ' . $result['test'],
            'status' => 'PASS'
        ];
        
        // Test 7: Check if tables exist
        $stmt = $connection->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $tests['db_tables'] = [
            'test' => 'Database Tables',
            'result' => count($tables) . ' tables found',
            'status' => count($tables) > 0 ? 'PASS' : 'FAIL'
        ];
        
    } catch (Exception $e) {
        $tests['db_connection'] = [
            'test' => 'Database Connection',
            'result' => 'Error: ' . $e->getMessage(),
            'status' => 'FAIL'
        ];
    }
}

// Test 8: Uploads Directory
$uploads_dir = __DIR__ . '/uploads';
$uploads_writable = is_dir($uploads_dir) && is_writable($uploads_dir);
$tests['uploads_dir'] = [
    'test' => 'Uploads Directory',
    'result' => $uploads_writable ? 'Exists and writable' : 'Not writable or missing',
    'status' => $uploads_writable ? 'PASS' : 'FAIL'
];

// Test 9: Required Extensions
$required_extensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
$missing_extensions = [];
foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        $missing_extensions[] = $ext;
    }
}

$tests['php_extensions'] = [
    'test' => 'Required PHP Extensions',
    'result' => empty($missing_extensions) ? 'All extensions loaded' : 'Missing: ' . implode(', ', $missing_extensions),
    'status' => empty($missing_extensions) ? 'PASS' : 'FAIL'
];

// Summary
$total_tests = count($tests);
$passed_tests = count(array_filter($tests, function($test) { return $test['status'] === 'PASS'; }));
$failed_tests = $total_tests - $passed_tests;

$summary = [
    'total' => $total_tests,
    'passed' => $passed_tests,
    'failed' => $failed_tests,
    'success_rate' => round(($passed_tests / $total_tests) * 100, 2) . '%'
];

echo json_encode([
    'success' => $failed_tests === 0,
    'message' => $failed_tests === 0 ? 'All tests passed!' : "$failed_tests test(s) failed",
    'timestamp' => date('c'),
    'environment' => 'local_development',
    'summary' => $summary,
    'tests' => $tests
], JSON_PRETTY_PRINT);
?>
