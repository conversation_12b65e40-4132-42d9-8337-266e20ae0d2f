import{r as l,a as Ue,g as <PERSON>,u as H,b as De,c as Pe,B as Te,R as $e,d as I,N as D}from"./vendor-CMsH-4Bd.js";import{L as he,M as ye,C as Y,E as Le,a as K,b as Z,A as fe,U as oe,B as P,D as z,T as q,c as le,d as J,e as de,f as me,g as B,h as U,i as Me,F as qe,S as Oe,P as M,j as ze,R as pe,k as Ve,l as Be,X as V,m as X,n as W,o as je,p as xe,q as G,r as be,s as He,t as ce,u as We,v as we,w as Ge,x as Qe,y as L,H as re,z as ae,G as Ye,I as Je,J as Ke,K as Ze,N as Xe}from"./ui-DuuvEgzt.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))a(i);new MutationObserver(i=>{for(const d of i)if(d.type==="childList")for(const m of d.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&a(m)}).observe(document,{childList:!0,subtree:!0});function t(i){const d={};return i.integrity&&(d.integrity=i.integrity),i.referrerPolicy&&(d.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?d.credentials="include":i.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function a(i){if(i.ep)return;i.ep=!0;const d=t(i);fetch(i.href,d)}})();var Ne={exports:{}},ee={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var es=l,ss=Symbol.for("react.element"),ts=Symbol.for("react.fragment"),rs=Object.prototype.hasOwnProperty,as=es.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ns={key:!0,ref:!0,__self:!0,__source:!0};function ve(o,s,t){var a,i={},d=null,m=null;t!==void 0&&(d=""+t),s.key!==void 0&&(d=""+s.key),s.ref!==void 0&&(m=s.ref);for(a in s)rs.call(s,a)&&!ns.hasOwnProperty(a)&&(i[a]=s[a]);if(o&&o.defaultProps)for(a in s=o.defaultProps,s)i[a]===void 0&&(i[a]=s[a]);return{$$typeof:ss,type:o,key:d,ref:m,props:i,_owner:as.current}}ee.Fragment=ts;ee.jsx=ve;ee.jsxs=ve;Ne.exports=ee;var e=Ne.exports,Se,ge=Ue;Se=ge.createRoot,ge.hydrateRoot;var ke={exports:{}},is="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",os=is,ls=os;function Ce(){}function Ie(){}Ie.resetWarningCache=Ce;var cs=function(){function o(a,i,d,m,c,r){if(r!==ls){var n=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw n.name="Invariant Violation",n}}o.isRequired=o;function s(){return o}var t={array:o,bigint:o,bool:o,func:o,number:o,object:o,string:o,symbol:o,any:o,arrayOf:s,element:o,elementType:o,instanceOf:s,node:o,objectOf:s,oneOf:s,oneOfType:s,shape:s,exact:s,checkPropTypes:Ie,resetWarningCache:Ce};return t.PropTypes=t,t};ke.exports=cs();var ds=ke.exports;const y=Re(ds),ms=()=>window.location.hostname==="eskillvisor.wallistry.pk"||window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1"?"https://eskillvisor.wallistry.pk/api":"https://wallistry.pk/api",xs=ms();class us{constructor(){this.baseURL=xs,this.token=localStorage.getItem("auth_token")}setToken(s){this.token=s,s?localStorage.setItem("auth_token",s):localStorage.removeItem("auth_token")}getHeaders(){const s={"Content-Type":"application/json"};return this.token&&(s.Authorization=`Bearer ${this.token}`),s}async request(s,t={}){const a=`${this.baseURL}${s}`,i={headers:this.getHeaders(),...t};try{const d=await fetch(a,i),m=await d.json();if(!d.ok)throw new Error(m.message||"API request failed");return m}catch(d){throw console.error("API request failed:",d),d}}async get(s){return this.request(s,{method:"GET"})}async post(s,t){return this.request(s,{method:"POST",body:JSON.stringify(t)})}async put(s,t){return this.request(s,{method:"PUT",body:JSON.stringify(t)})}async delete(s){return this.request(s,{method:"DELETE"})}async login(s,t){const a=await this.post("/login.php",{email:s,password:t});return a.success&&a.data.access_token&&this.setToken(a.data.access_token),a}async logout(){try{await this.post("/api/auth/logout")}finally{this.setToken(null)}}async getCurrentUser(){return this.get("/api/auth/me")}async refreshToken(s){const t=await this.post("/api/auth/refresh",{refresh_token:s});return t.success&&t.data.access_token&&this.setToken(t.data.access_token),t}async getUsers(s={}){const t=new URLSearchParams(s).toString();return this.get(`/users.php${t?`?${t}`:""}`)}async getUser(s){return this.get(`/users.php?id=${s}`)}async createUser(s){return this.post("/users.php",s)}async updateUser(s,t){return this.put(`/users.php?id=${s}`,t)}async deleteUser(s){return this.delete(`/users.php?id=${s}`)}async getCompanies(s={}){const t=new URLSearchParams(s).toString();return this.get(`/companies.php${t?`?${t}`:""}`)}async getCompany(s){return this.get(`/companies.php?id=${s}`)}async createCompany(s){return this.post("/companies.php",s)}async updateCompany(s,t){return this.put(`/companies.php?id=${s}`,t)}async deleteCompany(s){return this.delete(`/companies.php?id=${s}`)}async assignPartner(s,t){return this.post(`/api/companies/${s}/partners`,{user_id:t})}async removePartner(s,t){return this.delete(`/api/companies/${s}/partners/${t}`)}async getInventory(s={}){const t=new URLSearchParams(s).toString();return this.get(`/api/inventory${t?`?${t}`:""}`)}async getInventoryItem(s){return this.get(`/api/inventory/${s}`)}async createInventoryItem(s){return this.post("/api/inventory",s)}async updateInventoryItem(s,t){return this.put(`/api/inventory/${s}`,t)}async deleteInventoryItem(s){return this.delete(`/api/inventory/${s}`)}async getLowStockItems(){return this.get("/api/inventory/low-stock")}async getTransactions(s={}){const t=new URLSearchParams(s).toString();return this.get(`/api/transactions${t?`?${t}`:""}`)}async createTransaction(s){return this.post("/api/transactions",s)}async uploadFile(s){const t=new FormData;t.append("file",s);const a=await fetch(`${this.baseURL}/api/files/upload`,{method:"POST",headers:{Authorization:`Bearer ${this.token}`},body:t}),i=await a.json();if(!a.ok)throw new Error(i.message||"File upload failed");return i}async getUploads(s={}){const t=new URLSearchParams(s).toString();return this.get(`/api/files/uploads${t?`?${t}`:""}`)}async processFile(s){return this.post(`/api/files/process/${s}`)}async getDashboardData(){return this.get("/api/analytics/dashboard")}async getInventoryStats(){return this.get("/api/analytics/inventory-stats")}async getTrends(){return this.get("/api/analytics/trends")}async getCompanyStats(){return this.get("/api/analytics/company-stats")}async getAssignedCompanies(){return this.get("/api/companies/assigned")}async getCompanyStats(){return this.get("/api/analytics/company-stats")}async getTrends(){return this.get("/api/analytics/trends")}async exportData(s,t="csv"){return this.get(`/api/analytics/export?type=${s}&format=${t}`)}async getNotifications(s={}){const t=new URLSearchParams(s).toString();return this.get(`/api/notifications${t?`?${t}`:""}`)}async markNotificationAsRead(s){return this.put(`/api/notifications/${s}/read`)}async markAllNotificationsAsRead(){return this.post("/api/notifications/mark-all-read")}async deleteNotification(s){return this.delete(`/api/notifications/${s}`)}}const N=new us;class hs{constructor(){this.currentUser=null,this.token=localStorage.getItem("auth_token"),this.refreshToken=localStorage.getItem("refresh_token"),this.token&&this.initializeFromToken()}async initializeFromToken(){try{if(this.token){N.setToken(this.token);const s=await N.getCurrentUser();s.success?this.currentUser=s.data:this.logout()}}catch(s){console.error("Failed to initialize from token:",s),this.logout()}}async login(s,t){try{const a=await N.login(s,t);if(a.success)return this.token=a.data.access_token,this.refreshToken=a.data.refresh_token,this.currentUser=a.data.user,localStorage.setItem("auth_token",this.token),localStorage.setItem("refresh_token",this.refreshToken),{success:!0,user:this.currentUser,token:this.token,refreshToken:this.refreshToken};throw new Error(a.message||"Login failed")}catch(a){throw new Error(a.message||"Login failed")}}async logout(){try{this.token&&await N.logout()}catch(s){console.error("Logout error:",s)}finally{localStorage.removeItem("auth_token"),localStorage.removeItem("refresh_token"),this.token=null,this.refreshToken=null,this.currentUser=null,N.setToken(null)}return{success:!0}}async refreshAuthToken(){try{if(!this.refreshToken)throw new Error("No refresh token available");const s=await N.refreshToken(this.refreshToken);if(s.success)return this.token=s.data.access_token,localStorage.setItem("auth_token",this.token),N.setToken(this.token),{success:!0,token:this.token};throw new Error(s.message||"Token refresh failed")}catch{throw this.logout(),new Error("Token refresh failed")}}async resetPassword(s){try{return await N.post("/api/auth/forgot-password",{email:s})}catch(t){throw new Error(t.message||"Password reset failed")}}getCurrentUser(){return this.currentUser}getToken(){return this.token}isAuthenticated(){return!!this.token&&!!this.currentUser}hasRole(s){var t;return((t=this.currentUser)==null?void 0:t.role)===s}hasAnyRole(s){var t;return s.includes((t=this.currentUser)==null?void 0:t.role)}isSuperAdmin(){return this.hasRole("superadmin")}isManager(){return this.hasAnyRole(["superadmin","manager"])}isPartner(){return this.hasRole("partner")}canAccessCompany(s){var t;return this.currentUser?this.hasAnyRole(["superadmin","manager"])?!0:this.hasRole("partner")&&((t=this.currentUser.assigned_companies)==null?void 0:t.some(a=>a.id===s))||!1:!1}getAccessibleCompanies(){return this.currentUser?this.hasAnyRole(["superadmin","manager"])?"all":this.hasRole("partner")?this.currentUser.assigned_companies||[]:[]:[]}}const $=new hs,ps=()=>{const[o,s]=l.useState(""),[t,a]=l.useState(""),[i,d]=l.useState(!1),[m,c]=l.useState(!1),[r,n]=l.useState(""),[x,w]=l.useState(""),[p,g]=l.useState(!1),{login:h,isAuthenticated:b,userRole:v}=l.useContext(A),j=H();l.useEffect(()=>{b&&j(v==="superadmin"?"/superadmin":v==="manager"?"/manager":"/partner")},[b,v,j]);const k=f=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(f),F=async f=>{if(f.preventDefault(),!k(o)){n("Please enter a valid email address");return}n(""),w(""),c(!0);try{const S=await $.login(o,t);S.success&&(h(S.user),S.user.role==="superadmin"?j("/superadmin"):S.user.role==="manager"?j("/manager"):j("/partner"))}catch(S){w(S.message||"Login failed. Please try again.")}finally{c(!1)}},R=async(f,S)=>{s(f),a(S),n(""),w(""),c(!0);try{const C=await $.login(f,S);C.success&&(h(C.user),C.user.role==="superadmin"?j("/superadmin"):C.user.role==="manager"?j("/manager"):j("/partner"))}catch(C){w(C.message||"Demo login failed. Please try again.")}finally{c(!1)}},u=[{email:"<EMAIL>",password:"password",role:"Super Admin",description:"Full system access"},{email:"<EMAIL>",password:"password",role:"Manager",description:"Manage partners and companies"},{email:"<EMAIL>",password:"password",role:"Partner",description:"View assigned companies"}];return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-primary to-secondary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-20"}),e.jsxs("div",{className:"max-w-md w-full space-y-8 relative z-10",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mx-auto h-16 w-16 bg-white rounded-full flex items-center justify-center shadow-lg",children:e.jsx(he,{className:"h-8 w-8 text-primary"})}),e.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Enterprise Portal"}),e.jsx("p",{className:"mt-2 text-center text-sm text-white text-opacity-80",children:"Sign in to your account"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-xl p-8",children:[e.jsxs("form",{className:"space-y-6",onSubmit:F,children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(ye,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:o,onChange:f=>s(f.target.value),className:`appearance-none relative block w-full pl-10 pr-3 py-2 border ${r?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm`,placeholder:"Enter your email"})]}),r&&e.jsxs("div",{className:"mt-1 flex items-center text-sm text-red-600",children:[e.jsx(Y,{className:"h-4 w-4 mr-1"}),r]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(he,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{id:"password",name:"password",type:i?"text":"password",autoComplete:"current-password",required:!0,value:t,onChange:f=>a(f.target.value),className:"appearance-none relative block w-full pl-10 pr-10 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm",placeholder:"Enter your password"}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>d(!i),children:i?e.jsx(Le,{className:"h-5 w-5 text-gray-400"}):e.jsx(K,{className:"h-5 w-5 text-gray-400"})})]})]}),x&&e.jsxs("div",{className:"flex items-center text-sm text-red-600 bg-red-50 p-3 rounded-md",children:[e.jsx(Y,{className:"h-4 w-4 mr-2"}),x]}),e.jsx("div",{children:e.jsx("button",{type:"submit",disabled:m,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:m?"Signing in...":"Sign in"})}),e.jsx("div",{className:"text-center",children:e.jsxs("button",{type:"button",onClick:()=>g(!p),className:"text-sm text-primary hover:text-primary-dark",children:[p?"Hide":"Show"," Demo Accounts"]})})]}),p&&e.jsxs("div",{className:"mt-6 border-t border-gray-200 pt-6",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Demo Accounts:"}),e.jsx("div",{className:"space-y-2",children:u.map((f,S)=>e.jsx("button",{onClick:()=>R(f.email,f.password),className:"w-full text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:f.role}),e.jsx("p",{className:"text-xs text-gray-500",children:f.email})]}),e.jsx("p",{className:"text-xs text-gray-400",children:f.description})]})},S))})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsx("a",{href:"/reset-password",onClick:f=>{f.preventDefault(),j("/reset-password")},className:"text-sm text-primary hover:text-primary-dark",children:"Forgot your password?"})})]})]})]})},gs=()=>{const[o,s]=l.useState(""),[t,a]=l.useState(!1),[i,d]=l.useState(!1),m=H(),c=r=>{r.preventDefault(),a(!0),setTimeout(()=>{a(!1),d(!0)},1500)};return i?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary to-secondary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"max-w-md w-full space-y-8",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-xl p-8 text-center",children:[e.jsx(Z,{className:"mx-auto h-16 w-16 text-green-500"}),e.jsx("h2",{className:"mt-6 text-2xl font-bold text-gray-900",children:"Check your email"}),e.jsxs("p",{className:"mt-2 text-sm text-gray-600",children:["We've sent a password reset link to ",o]}),e.jsx("button",{onClick:()=>m("/login"),className:"mt-6 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:"Back to Login"})]})})}):e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary to-secondary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-md w-full space-y-8",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Reset your password"}),e.jsx("p",{className:"mt-2 text-center text-sm text-white text-opacity-80",children:"Enter your email address and we'll send you a link to reset your password."})]}),e.jsx("div",{className:"bg-white rounded-lg shadow-xl p-8",children:e.jsxs("form",{className:"space-y-6",onSubmit:c,children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(ye,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:o,onChange:r=>s(r.target.value),className:"appearance-none relative block w-full pl-10 pr-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm",placeholder:"Enter your email"})]})]}),e.jsx("div",{children:e.jsx("button",{type:"submit",disabled:t,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:t?"Sending...":"Send reset link"})}),e.jsx("div",{className:"text-center",children:e.jsxs("button",{type:"button",onClick:()=>m("/login"),className:"inline-flex items-center text-sm text-primary hover:text-primary-dark",children:[e.jsx(fe,{className:"h-4 w-4 mr-1"}),"Back to login"]})})]})})]})})};class ys{async getInventoryItems(s={}){try{const t=await N.getInventory(s);if(t.success)return t.data||[];throw new Error(t.message||"Failed to fetch inventory items")}catch(t){throw console.error("Error fetching inventory items:",t),t}}async getInventoryItem(s){try{const t=await N.getInventoryItem(s);if(t.success)return t.data;throw new Error(t.message||"Failed to fetch inventory item")}catch(t){throw console.error("Error fetching inventory item:",t),t}}async createInventoryItem(s){try{const t=await N.createInventoryItem(s);if(t.success)return t.data;throw new Error(t.message||"Failed to create inventory item")}catch(t){throw console.error("Error creating inventory item:",t),t}}async updateInventoryItem(s,t){try{const a=await N.updateInventoryItem(s,t);if(a.success)return a.data;throw new Error(a.message||"Failed to update inventory item")}catch(a){throw console.error("Error updating inventory item:",a),a}}async deleteInventoryItem(s){try{const t=await N.deleteInventoryItem(s);if(t.success)return!0;throw new Error(t.message||"Failed to delete inventory item")}catch(t){throw console.error("Error deleting inventory item:",t),t}}async getLowStockItems(){try{const s=await N.getLowStockItems();if(s.success)return s.data||[];throw new Error(s.message||"Failed to fetch low stock items")}catch(s){throw console.error("Error fetching low stock items:",s),s}}async getTransactions(s={}){try{const t=await N.getTransactions(s);if(t.success)return t.data||[];throw new Error(t.message||"Failed to fetch transactions")}catch(t){throw console.error("Error fetching transactions:",t),t}}async createTransaction(s){try{const t=await N.createTransaction(s);if(t.success)return t.data;throw new Error(t.message||"Failed to create transaction")}catch(t){throw console.error("Error creating transaction:",t),t}}async uploadFile(s){try{const t=await N.uploadFile(s);if(t.success)return t.data;throw new Error(t.message||"Failed to upload file")}catch(t){throw console.error("Error uploading file:",t),t}}async processFile(s){try{const t=await N.processFile(s);if(t.success)return t.data;throw new Error(t.message||"Failed to process file")}catch(t){throw console.error("Error processing file:",t),t}}async getUploads(s={}){try{const t=await N.getUploads(s);if(t.success)return t.data||[];throw new Error(t.message||"Failed to fetch uploads")}catch(t){throw console.error("Error fetching uploads:",t),t}}async getInventoryStats(){try{const s=await N.getInventoryStats();if(s.success)return s.data;throw new Error(s.message||"Failed to fetch inventory stats")}catch(s){throw console.error("Error fetching inventory stats:",s),s}}async getDashboardData(){try{const s=await N.getDashboardData();if(s.success)return s.data;throw new Error(s.message||"Failed to fetch dashboard data")}catch(s){throw console.error("Error fetching dashboard data:",s),s}}async getTrends(){try{const s=await N.getTrends();if(s.success)return s.data;throw new Error(s.message||"Failed to fetch trends data")}catch(s){throw console.error("Get trends data failed:",s),s}}async getCompanyStats(){try{const s=await N.getCompanyStats();if(s.success)return s.data;throw new Error(s.message||"Failed to fetch company stats")}catch(s){throw console.error("Get company stats failed:",s),s}}async exportData(s="inventory",t="csv"){try{return await N.exportData(s,t)}catch(a){throw console.error("Error exporting data:",a),a}}}const T=new ys;class fs{async getUsers(s={}){try{const t=await N.getUsers(s);if(t.success)return t.data||[];throw new Error(t.message||"Failed to fetch users")}catch(t){throw console.error("Error fetching users:",t),t}}async getUser(s){try{const t=await N.getUser(s);if(t.success)return t.data;throw new Error(t.message||"Failed to fetch user")}catch(t){throw console.error("Error fetching user:",t),t}}async createUser(s){try{const t=await N.createUser(s);if(t.success)return t.data;throw new Error(t.message||"Failed to create user")}catch(t){throw console.error("Error creating user:",t),t}}async updateUser(s,t){try{const a=await N.updateUser(s,t);if(a.success)return a.data;throw new Error(a.message||"Failed to update user")}catch(a){throw console.error("Error updating user:",a),a}}async deleteUser(s){try{const t=await N.deleteUser(s);if(t.success)return!0;throw new Error(t.message||"Failed to delete user")}catch(t){throw console.error("Error deleting user:",t),t}}}const js=new fs;class bs{async getCompanies(s={}){try{const t=await N.getCompanies(s);if(t.success)return t.data||[];throw new Error(t.message||"Failed to fetch companies")}catch(t){throw console.error("Error fetching companies:",t),t}}async getCompany(s){try{const t=await N.getCompany(s);if(t.success)return t.data;throw new Error(t.message||"Failed to fetch company")}catch(t){throw console.error("Error fetching company:",t),t}}async createCompany(s){try{const t=await N.createCompany(s);if(t.success)return t.data;throw new Error(t.message||"Failed to create company")}catch(t){throw console.error("Error creating company:",t),t}}async updateCompany(s,t){try{const a=await N.updateCompany(s,t);if(a.success)return a.data;throw new Error(a.message||"Failed to update company")}catch(a){throw console.error("Error updating company:",a),a}}async deleteCompany(s){try{const t=await N.deleteCompany(s);if(t.success)return!0;throw new Error(t.message||"Failed to delete company")}catch(t){throw console.error("Error deleting company:",t),t}}async assignPartner(s,t){try{const a=await N.assignPartner(s,t);if(a.success)return!0;throw new Error(a.message||"Failed to assign partner")}catch(a){throw console.error("Error assigning partner:",a),a}}async removePartner(s,t){try{const a=await N.removePartner(s,t);if(a.success)return!0;throw new Error(a.message||"Failed to remove partner")}catch(a){throw console.error("Error removing partner:",a),a}}async getCompanyStats(){try{const s=await N.getCompanyStats();if(s.success)return s.data;throw new Error(s.message||"Failed to fetch company stats")}catch(s){throw console.error("Error fetching company stats:",s),s}}async getAssignedCompanies(){try{const s=await N.getAssignedCompanies();if(s.success)return s.data||[];throw new Error(s.message||"Failed to fetch assigned companies")}catch(s){throw console.error("Error fetching assigned companies:",s),s}}}const O=new bs,ws=()=>{const{user:o}=l.useContext(A),[s,t]=l.useState({users:{total_users:0,active_users:0,new_users_today:0},companies:{total_companies:0,active_companies:0,new_companies_today:0},inventory:{total_items:0,total_value:0,low_stock_count:0},top_categories:[],recent_activity:[]}),[a,i]=l.useState(!0),[d,m]=l.useState([]);l.useEffect(()=>{o&&(async()=>{try{i(!0);const n=await T.getDashboardData();n&&t({users:n.users||{total_users:0,active_users:0,new_users_today:0},companies:n.companies||{total_companies:0,active_companies:0,new_companies_today:0},inventory:n.inventory||{total_items:0,total_value:0,low_stock_count:0},top_categories:n.top_categories||[],recent_activity:n.recent_activity||[]});const x=await T.getTrends();x&&m(x.trends||[])}catch(n){console.error("Failed to load dashboard data:",n)}finally{i(!1)}})()},[o]);const c=({title:r,value:n,icon:x,color:w="blue",subtitle:p,trend:g})=>{const h={blue:"bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 border-blue-200",green:"bg-gradient-to-br from-green-50 to-green-100 text-green-600 border-green-200",purple:"bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600 border-purple-200",orange:"bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600 border-orange-200",red:"bg-gradient-to-br from-red-50 to-red-100 text-red-600 border-red-200",indigo:"bg-gradient-to-br from-indigo-50 to-indigo-100 text-indigo-600 border-indigo-200"};return e.jsx("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:r}),e.jsx("p",{className:"text-3xl font-bold text-gray-900 mb-2",children:n}),p&&e.jsx("p",{className:"text-sm text-gray-500",children:p}),g&&e.jsxs("div",{className:"flex items-center mt-2",children:[g.direction==="up"?e.jsx(U,{className:"h-4 w-4 text-green-500 mr-1"}):e.jsx(Me,{className:"h-4 w-4 text-red-500 mr-1"}),e.jsx("span",{className:`text-sm font-medium ${g.direction==="up"?"text-green-600":"text-red-600"}`,children:g.value}),e.jsx("span",{className:"text-sm text-gray-500 ml-1",children:"vs last month"})]})]}),e.jsx("div",{className:`p-4 rounded-xl border ${h[w]}`,children:x})]})})};return a?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Super Admin Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"System-wide overview and analytics"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})]}):e.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Super Admin Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Complete system overview and business intelligence"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[e.jsx(c,{title:"Total Users",value:s.users.total_users,subtitle:`${s.users.active_users} active users`,icon:e.jsx(oe,{className:"h-8 w-8"}),color:"blue",trend:{direction:"up",value:"+12%"}}),e.jsx(c,{title:"Companies",value:s.companies.total_companies,subtitle:`${s.companies.active_companies} active companies`,icon:e.jsx(P,{className:"h-8 w-8"}),color:"green",trend:{direction:"up",value:"+8%"}}),e.jsx(c,{title:"Total Inventory Value",value:`$${s.inventory.total_value.toLocaleString()}`,subtitle:`${s.inventory.total_items} total items`,icon:e.jsx(z,{className:"h-8 w-8"}),color:"purple",trend:{direction:"up",value:"+15%"}})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsx(c,{title:"Low Stock Alerts",value:s.inventory.low_stock_count,subtitle:"Items need restocking",icon:e.jsx(q,{className:"h-6 w-6"}),color:"red"}),e.jsx(c,{title:"New Users Today",value:s.users.new_users_today,subtitle:"User registrations",icon:e.jsx(le,{className:"h-6 w-6"}),color:"indigo"}),e.jsx(c,{title:"New Companies",value:s.companies.new_companies_today,subtitle:"Companies added today",icon:e.jsx(J,{className:"h-6 w-6"}),color:"orange"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(de,{className:"h-5 w-5 text-purple-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Top Inventory Categories"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:s.top_categories.slice(0,5).map((r,n)=>{var x;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-3 h-3 rounded-full mr-3 ${["bg-blue-500","bg-green-500","bg-purple-500","bg-orange-500","bg-red-500"][n]}`}),e.jsx("span",{className:"font-medium text-gray-900",children:r.category||"Uncategorized"})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-sm font-semibold text-gray-900",children:[r.item_count," items"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["$",(x=r.total_value)==null?void 0:x.toLocaleString()]})]})]},n)})})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(me,{className:"h-5 w-5 text-blue-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent System Activity"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:s.recent_activity.slice(0,6).map((r,n)=>e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:`w-2 h-2 rounded-full mt-2 ${r.action==="create"?"bg-green-500":r.action==="update"?"bg-blue-500":r.action==="delete"?"bg-red-500":"bg-gray-500"}`}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:[r.action==="create"&&"Created ",r.action==="update"&&"Updated ",r.action==="delete"&&"Deleted ",r.entity_type]}),e.jsxs("p",{className:"text-xs text-gray-500",children:[r.user_name," • ",new Date(r.created_at).toLocaleDateString()]})]})]},n))})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(B,{className:"h-5 w-5 text-green-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Growth Trends (Last 30 Days)"})]})}),e.jsx("div",{className:"p-6",children:d.length>0?e.jsx("div",{className:"space-y-4",children:d.slice(-7).map((r,n)=>{var x;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:new Date(r.date).toLocaleDateString()}),e.jsxs("div",{className:"text-xs text-gray-500",children:[r.items_added," items added"]})]}),e.jsx("div",{className:"text-right",children:e.jsxs("div",{className:"text-sm font-semibold text-green-600",children:["+$",(x=r.value_added)==null?void 0:x.toLocaleString()]})})]},n)})}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(B,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500",children:"No trend data available"})]})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Quick Actions"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(le,{className:"h-5 w-5 text-blue-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Add New User"})]}),e.jsx(U,{className:"h-4 w-4 text-blue-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(J,{className:"h-5 w-5 text-green-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-green-900",children:"Add Company"})]}),e.jsx(U,{className:"h-4 w-4 text-green-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(B,{className:"h-5 w-5 text-purple-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-purple-900",children:"View Reports"})]}),e.jsx(U,{className:"h-4 w-4 text-purple-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(K,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-orange-900",children:"System Overview"})]}),e.jsx(U,{className:"h-4 w-4 text-orange-600 transform rotate-45"})]})]})})]})]})]})},Ns=()=>{const{user:o}=l.useContext(A),[s,t]=l.useState({companies:{total_companies:0,active_companies:0},inventory:{total_items:0,total_value:0,low_stock_count:0},top_categories:[],recent_activity:[],assigned_companies:[]}),[a,i]=l.useState(!0),[d,m]=l.useState([]);l.useEffect(()=>{o&&(async()=>{try{i(!0);const n=await T.getDashboardData();n&&t({companies:n.companies||{total_companies:0,active_companies:0},inventory:n.inventory||{total_items:0,total_value:0,low_stock_count:0},top_categories:n.top_categories||[],recent_activity:n.recent_activity||[],assigned_companies:n.assigned_companies||[]});try{const x=await T.getCompanyStats();x&&m(x.top_companies||[])}catch(x){console.error("Failed to load company stats:",x)}}catch(n){console.error("Failed to load dashboard data:",n)}finally{i(!1)}})()},[o]);const c=({title:r,value:n,icon:x,color:w="blue",subtitle:p,trend:g})=>{const h={blue:"bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 border-blue-200",green:"bg-gradient-to-br from-green-50 to-green-100 text-green-600 border-green-200",purple:"bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600 border-purple-200",orange:"bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600 border-orange-200",red:"bg-gradient-to-br from-red-50 to-red-100 text-red-600 border-red-200",indigo:"bg-gradient-to-br from-indigo-50 to-indigo-100 text-indigo-600 border-indigo-200"};return e.jsx("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:r}),e.jsx("p",{className:"text-3xl font-bold text-gray-900 mb-2",children:n}),p&&e.jsx("p",{className:"text-sm text-gray-500",children:p})]}),e.jsx("div",{className:`p-4 rounded-xl border ${h[w]}`,children:x})]})})};return a?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Manager Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Company management and partner oversight"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})]}):e.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Manager Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Comprehensive company management and partner oversight"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[e.jsx(c,{title:"Managed Companies",value:s.companies.total_companies,subtitle:`${s.companies.active_companies} active companies`,icon:e.jsx(P,{className:"h-8 w-8"}),color:"blue"}),e.jsx(c,{title:"Total Inventory Value",value:`$${s.inventory.total_value.toLocaleString()}`,subtitle:`${s.inventory.total_items} total items`,icon:e.jsx(z,{className:"h-8 w-8"}),color:"green"}),e.jsx(c,{title:"Low Stock Alerts",value:s.inventory.low_stock_count,subtitle:"Items need attention",icon:e.jsx(q,{className:"h-8 w-8"}),color:"red"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(B,{className:"h-5 w-5 text-green-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Top Companies by Value"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:d.slice(0,5).map((r,n)=>{var x;return e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 ${["bg-blue-500","bg-green-500","bg-purple-500","bg-orange-500","bg-red-500"][n]}`,children:n+1}),e.jsxs("div",{children:[e.jsx("p",{className:"font-semibold text-gray-900",children:r.name}),e.jsx("p",{className:"text-sm text-gray-500",children:r.industry})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-bold text-green-600",children:["$",(x=r.total_value)==null?void 0:x.toLocaleString()]}),e.jsxs("p",{className:"text-sm text-gray-500",children:[r.inventory_count," items"]})]})]},n)})})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(de,{className:"h-5 w-5 text-purple-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Inventory Categories"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:s.top_categories.slice(0,5).map((r,n)=>{var x;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-3 h-3 rounded-full mr-3 ${["bg-blue-500","bg-green-500","bg-purple-500","bg-orange-500","bg-red-500"][n]}`}),e.jsx("span",{className:"font-medium text-gray-900",children:r.category||"Uncategorized"})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-sm font-semibold text-gray-900",children:[r.item_count," items"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["$",(x=r.total_value)==null?void 0:x.toLocaleString()]})]})]},n)})})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(me,{className:"h-5 w-5 text-blue-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Company Activity"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:s.recent_activity.slice(0,6).map((r,n)=>e.jsxs("div",{className:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:`w-2 h-2 rounded-full mt-2 ${r.action==="create"?"bg-green-500":r.action==="update"?"bg-blue-500":r.action==="delete"?"bg-red-500":"bg-gray-500"}`}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:[r.action==="create"&&"Created ",r.action==="update"&&"Updated ",r.action==="delete"&&"Deleted ",r.entity_type]}),e.jsxs("p",{className:"text-xs text-gray-500",children:[r.user_name," • ",new Date(r.created_at).toLocaleDateString()]})]})]},n))})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Quick Actions"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(le,{className:"h-5 w-5 text-blue-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Add Partner"})]}),e.jsx(U,{className:"h-4 w-4 text-blue-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(J,{className:"h-5 w-5 text-green-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-green-900",children:"Manage Companies"})]}),e.jsx(U,{className:"h-4 w-4 text-green-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(qe,{className:"h-5 w-5 text-purple-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-purple-900",children:"View Reports"})]}),e.jsx(U,{className:"h-4 w-4 text-purple-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Oe,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-orange-900",children:"Audit Trail"})]}),e.jsx(U,{className:"h-4 w-4 text-orange-600 transform rotate-45"})]})]})})]})]})]})},vs=()=>{const{user:o}=l.useContext(A),[s,t]=l.useState({assigned_companies:[],inventory:{total_items:0,total_value:0,low_stock_count:0},top_categories:[],recent_activity:[]}),[a,i]=l.useState(!0),[d,m]=l.useState([]);l.useEffect(()=>{o&&(async()=>{try{i(!0);const n=await T.getDashboardData();n&&t({assigned_companies:n.assigned_companies||[],inventory:n.inventory||{total_items:0,total_value:0,low_stock_count:0},top_categories:n.top_categories||[],recent_activity:n.recent_activity||[]});try{const x=await O.getAssignedCompanies();x&&m(x)}catch(x){console.error("Failed to load companies:",x)}}catch(n){console.error("Failed to load dashboard data:",n)}finally{i(!1)}})()},[o]);const c=({title:r,value:n,icon:x,color:w="blue",subtitle:p,actionButton:g})=>{const h={blue:"bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 border-blue-200",green:"bg-gradient-to-br from-green-50 to-green-100 text-green-600 border-green-200",purple:"bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600 border-purple-200",red:"bg-gradient-to-br from-red-50 to-red-100 text-red-600 border-red-200",orange:"bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600 border-orange-200"};return e.jsx("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:r}),e.jsx("p",{className:"text-3xl font-bold text-gray-900 mb-2",children:n}),p&&e.jsx("p",{className:"text-sm text-gray-500",children:p}),g&&e.jsx("div",{className:"mt-3",children:g})]}),e.jsx("div",{className:`p-4 rounded-xl border ${h[w]}`,children:x})]})})};return a?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Partner Portfolio"}),e.jsx("p",{className:"text-gray-600",children:"Manage your assigned companies and inventory"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})]}):e.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Partner Portfolio"}),e.jsx("p",{className:"text-gray-600",children:"Comprehensive view of your assigned companies and inventory management"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsx(c,{title:"Assigned Companies",value:s.assigned_companies.length,subtitle:"Active partnerships",icon:e.jsx(P,{className:"h-8 w-8"}),color:"blue"}),e.jsx(c,{title:"Total Items",value:s.inventory.total_items,subtitle:"Across all companies",icon:e.jsx(M,{className:"h-8 w-8"}),color:"green"}),e.jsx(c,{title:"Portfolio Value",value:`$${s.inventory.total_value.toLocaleString()}`,subtitle:"Total inventory worth",icon:e.jsx(z,{className:"h-8 w-8"}),color:"purple"}),e.jsx(c,{title:"Low Stock Alerts",value:s.inventory.low_stock_count,subtitle:"Items need restocking",icon:e.jsx(q,{className:"h-8 w-8"}),color:"red",actionButton:s.inventory.low_stock_count>0&&e.jsx("button",{className:"text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full hover:bg-red-200 transition-colors",children:"View Details"})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[e.jsxs("div",{className:"lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(P,{className:"h-5 w-5 text-blue-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Your Companies"})]}),e.jsx("button",{className:"text-sm text-blue-600 hover:text-blue-800 font-medium",children:"View All"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:d.slice(0,4).map((r,n)=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4",children:e.jsx(P,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-semibold text-gray-900",children:r.name||`Company ${n+1}`}),e.jsx("p",{className:"text-sm text-gray-500",children:r.industry||"Technology"}),e.jsxs("p",{className:"text-xs text-gray-400",children:[r.inventory_count||0," inventory items"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${r.status==="active"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:r.status||"Active"}),e.jsx("button",{className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",children:e.jsx(K,{className:"h-4 w-4"})})]})]},n))})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Quick Actions"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ze,{className:"h-5 w-5 text-blue-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Upload Inventory"})]}),e.jsx(U,{className:"h-4 w-4 text-blue-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(pe,{className:"h-5 w-5 text-green-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-green-900",children:"Update Stock"})]}),e.jsx(U,{className:"h-4 w-4 text-green-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(B,{className:"h-5 w-5 text-purple-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-purple-900",children:"View Reports"})]}),e.jsx(U,{className:"h-4 w-4 text-purple-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(q,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-orange-900",children:"Stock Alerts"})]}),e.jsx(U,{className:"h-4 w-4 text-orange-600 transform rotate-45"})]})]})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(de,{className:"h-5 w-5 text-purple-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Inventory Categories"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:s.top_categories.slice(0,5).map((r,n)=>{var x;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-3 h-3 rounded-full mr-3 ${["bg-blue-500","bg-green-500","bg-purple-500","bg-orange-500","bg-red-500"][n]}`}),e.jsx("span",{className:"font-medium text-gray-900",children:r.category||"Uncategorized"})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-sm font-semibold text-gray-900",children:[r.item_count," items"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["$",(x=r.total_value)==null?void 0:x.toLocaleString()]})]})]},n)})})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(me,{className:"h-5 w-5 text-blue-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Activity"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:s.recent_activity.slice(0,6).map((r,n)=>e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsxs("div",{className:"flex-shrink-0",children:[r.action==="create"&&e.jsx(Z,{className:"h-5 w-5 text-green-500"}),r.action==="update"&&e.jsx(pe,{className:"h-5 w-5 text-blue-500"}),r.action==="delete"&&e.jsx(Ve,{className:"h-5 w-5 text-red-500"}),!["create","update","delete"].includes(r.action)&&e.jsx(Be,{className:"h-5 w-5 text-gray-500"})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:[r.action==="create"&&"Created ",r.action==="update"&&"Updated ",r.action==="delete"&&"Deleted ",r.entity_type]}),e.jsxs("p",{className:"text-xs text-gray-500",children:[new Date(r.created_at).toLocaleDateString()," • ",new Date(r.created_at).toLocaleTimeString()]})]})]},n))})})]})]})]})},_e=({isOpen:o,onClose:s,onSubmit:t,userRole:a="superadmin"})=>{const[i,d]=l.useState({name:"",email:"",role:"",password:"",confirmPassword:""}),[m,c]=l.useState(!1),[r,n]=l.useState({}),x=h=>{const{name:b,value:v}=h.target;d(j=>({...j,[b]:v})),r[b]&&n(j=>({...j,[b]:""}))},w=()=>{const h={};return i.name.trim()||(h.name="Name is required"),i.email.trim()?/\S+@\S+\.\S+/.test(i.email)||(h.email="Email is invalid"):h.email="Email is required",i.role||(h.role="Role is required"),i.password?i.password.length<6&&(h.password="Password must be at least 6 characters"):h.password="Password is required",i.password!==i.confirmPassword&&(h.confirmPassword="Passwords do not match"),n(h),Object.keys(h).length===0},p=async h=>{if(h.preventDefault(),!!w()){c(!0);try{const b={name:i.name,email:i.email,role:i.role,password:i.password};await t(b),g()}catch(b){console.error("Failed to add user:",b),n({submit:"Failed to add user. Please try again."})}finally{c(!1)}}},g=()=>{d({name:"",email:"",role:"",password:"",confirmPassword:""}),n({}),c(!1),s()};return o?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:g}),e.jsxs("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[e.jsx("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Add New User"}),e.jsx("button",{onClick:g,className:"text-gray-400 hover:text-gray-600",children:e.jsx(V,{className:"h-6 w-6"})})]})}),e.jsxs("form",{onSubmit:p,children:[e.jsxs("div",{className:"px-4 pb-4 sm:px-6 sm:pb-6",children:[r.submit&&e.jsx("div",{className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:r.submit}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Full Name *"}),e.jsx("input",{type:"text",id:"name",name:"name",value:i.name,onChange:x,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.name?"border-red-300":"border-gray-300"}`,placeholder:"Enter full name"}),r.name&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.name})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email *"}),e.jsx("input",{type:"email",id:"email",name:"email",value:i.email,onChange:x,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.email?"border-red-300":"border-gray-300"}`,placeholder:"<EMAIL>"}),r.email&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.email})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700",children:"Role *"}),e.jsxs("select",{id:"role",name:"role",value:i.role,onChange:x,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.role?"border-red-300":"border-gray-300"}`,children:[e.jsx("option",{value:"",children:"Select role"}),a==="superadmin"&&e.jsxs(e.Fragment,{children:[e.jsx("option",{value:"superadmin",children:"Super Admin"}),e.jsx("option",{value:"manager",children:"Manager"})]}),e.jsx("option",{value:"partner",children:"Partner"})]}),r.role&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.role})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password *"}),e.jsx("input",{type:"password",id:"password",name:"password",value:i.password,onChange:x,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.password?"border-red-300":"border-gray-300"}`,placeholder:"Enter password"}),r.password&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.password})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password *"}),e.jsx("input",{type:"password",id:"confirmPassword",name:"confirmPassword",value:i.confirmPassword,onChange:x,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.confirmPassword?"border-red-300":"border-gray-300"}`,placeholder:"Confirm password"}),r.confirmPassword&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.confirmPassword})]})]})]}),e.jsxs("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[e.jsx("button",{type:"submit",disabled:m,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:m?"Adding...":"Add User"}),e.jsx("button",{type:"button",onClick:g,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"Cancel"})]})]})]})]})}):null},Ss=()=>{const{userRole:o}=l.useContext(A),[s,t]=l.useState([{id:"1",name:"John Admin",email:"<EMAIL>",role:"superadmin",status:"active"},{id:"2",name:"Jane Manager",email:"<EMAIL>",role:"manager",status:"active"},{id:"3",name:"Bob Partner",email:"<EMAIL>",role:"partner",status:"active"}]),[a,i]=l.useState(!1),d=async m=>{try{const c={id:String(s.length+1),...m,status:"active"};t(r=>[...r,c]),i(!1)}catch(c){throw console.error("Failed to add user:",c),c}};return e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"User Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage system users and their permissions"})]}),e.jsxs("button",{onClick:()=>i(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(X,{className:"h-4 w-4 mr-2"}),"Add User"]})]})}),e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx(W,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search users...",className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"})]}),e.jsxs("button",{className:"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),"Filter"]})]})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(m=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:m.name}),e.jsx("div",{className:"text-sm text-gray-500",children:m.email})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800",children:m.role})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:m.status})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[e.jsx("button",{className:"text-primary hover:text-primary-dark mr-3",children:"Edit"}),e.jsx("button",{className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},m.id))})]})})]}),e.jsx(_e,{isOpen:a,onClose:()=>i(!1),onSubmit:d,userRole:o})]})},Ee=({isOpen:o,onClose:s,onSubmit:t,userRole:a="superadmin",currentUserId:i=null})=>{const[d,m]=l.useState({name:"",description:"",industry:"",email:"",phone:"",address:"",website:"",managerId:a==="manager"?i:""}),[c,r]=l.useState(!1),[n,x]=l.useState({}),[w,p]=l.useState([]);l.useEffect(()=>{(async()=>{if(a==="superadmin"&&o)try{p([{id:"2",name:"Jane Manager",email:"<EMAIL>"}])}catch(k){console.error("Failed to load managers:",k),p([])}})()},[a,o]);const g=j=>{const{name:k,value:F}=j.target;m(R=>({...R,[k]:F})),n[k]&&x(R=>({...R,[k]:""}))},h=()=>{const j={};return d.name.trim()||(j.name="Company name is required"),d.email.trim()?/\S+@\S+\.\S+/.test(d.email)||(j.email="Email is invalid"):j.email="Email is required",d.industry.trim()||(j.industry="Industry is required"),x(j),Object.keys(j).length===0},b=async j=>{if(j.preventDefault(),!!h()){r(!0);try{await t(d),v()}catch(k){console.error("Failed to add company:",k),x({submit:"Failed to add company. Please try again."})}finally{r(!1)}}},v=()=>{m({name:"",description:"",industry:"",email:"",phone:"",address:"",website:"",managerId:a==="manager"?i:""}),x({}),r(!1),s()};return o?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:v}),e.jsxs("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[e.jsx("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Add New Company"}),e.jsx("button",{onClick:v,className:"text-gray-400 hover:text-gray-600",children:e.jsx(V,{className:"h-6 w-6"})})]})}),e.jsxs("form",{onSubmit:b,children:[e.jsxs("div",{className:"px-4 pb-4 sm:px-6 sm:pb-6",children:[n.submit&&e.jsx("div",{className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:n.submit}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Company Name *"}),e.jsx("input",{type:"text",id:"name",name:"name",value:d.name,onChange:g,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${n.name?"border-red-300":"border-gray-300"}`,placeholder:"Enter company name"}),n.name&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:n.name})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email *"}),e.jsx("input",{type:"email",id:"email",name:"email",value:d.email,onChange:g,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${n.email?"border-red-300":"border-gray-300"}`,placeholder:"<EMAIL>"}),n.email&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:n.email})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"industry",className:"block text-sm font-medium text-gray-700",children:"Industry *"}),e.jsxs("select",{id:"industry",name:"industry",value:d.industry,onChange:g,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${n.industry?"border-red-300":"border-gray-300"}`,children:[e.jsx("option",{value:"",children:"Select industry"}),e.jsx("option",{value:"Technology",children:"Technology"}),e.jsx("option",{value:"Manufacturing",children:"Manufacturing"}),e.jsx("option",{value:"Retail",children:"Retail"}),e.jsx("option",{value:"Healthcare",children:"Healthcare"}),e.jsx("option",{value:"Finance",children:"Finance"}),e.jsx("option",{value:"Education",children:"Education"}),e.jsx("option",{value:"Other",children:"Other"})]}),n.industry&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:n.industry})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone"}),e.jsx("input",{type:"tel",id:"phone",name:"phone",value:d.phone,onChange:g,className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"+****************"})]}),a==="superadmin"&&e.jsxs("div",{children:[e.jsx("label",{htmlFor:"managerId",className:"block text-sm font-medium text-gray-700",children:"Assign Manager (Optional)"}),e.jsxs("select",{id:"managerId",name:"managerId",value:d.managerId,onChange:g,className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",children:[e.jsx("option",{value:"",children:"No manager assigned"}),w.map(j=>e.jsxs("option",{value:j.id,children:[j.name," (",j.email,")"]},j.id))]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700",children:"Description"}),e.jsx("textarea",{id:"description",name:"description",rows:3,value:d.description,onChange:g,className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Brief description of the company"})]})]})]}),e.jsxs("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[e.jsx("button",{type:"submit",disabled:c,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:c?"Adding...":"Add Company"}),e.jsx("button",{type:"button",onClick:v,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"Cancel"})]})]})]})]})}):null},ks=()=>{const{user:o,userRole:s}=l.useContext(A),[t,a]=l.useState([]),[i,d]=l.useState(!0),[m,c]=l.useState(!1);l.useEffect(()=>{o&&(async()=>{try{d(!0);const x=await O.getCompanies();a(x)}catch(x){console.error("Failed to load companies:",x),a([])}finally{d(!1)}})()},[o]);const r=async n=>{try{const x=await O.createCompany(n);a(w=>[...w,x]),c(!1)}catch(x){throw console.error("Failed to add company:",x),x}};return i?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Company Oversight"}),e.jsx("p",{className:"text-gray-600",children:"Monitor and manage all companies in the system"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})})]}):e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Company Oversight"}),e.jsx("p",{className:"text-gray-600",children:"Monitor and manage all companies in the system"})]}),e.jsxs("button",{onClick:()=>c(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(X,{className:"h-4 w-4 mr-2"}),"Add Company"]})]})}),e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(W,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search companies...",className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary w-64"})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6",children:t.map(n=>e.jsxs("div",{className:"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:n.name}),e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:"Active"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsxs("p",{children:["ID: ",n.id]}),e.jsx("p",{children:"Inventory Items: 5"}),e.jsx("p",{children:"Total Value: $15,000"})]}),e.jsxs("div",{className:"mt-4 flex space-x-2",children:[e.jsx("button",{className:"text-primary hover:text-primary-dark text-sm",children:"View Details"}),e.jsx("button",{className:"text-gray-600 hover:text-gray-900 text-sm",children:"Edit"})]})]},n.id))})]}),e.jsx(Ee,{isOpen:m,onClose:()=>c(!1),onSubmit:r,userRole:s,currentUserId:o==null?void 0:o.id})]})},se=({selectedCompany:o,onCompanyChange:s,companies:t,className:a="",showAllOption:i=!0,disabled:d=!1})=>{const{user:m,userRole:c}=l.useContext(A),[r,n]=l.useState([]),[x,w]=l.useState([]);l.useEffect(()=>{m&&(async()=>{try{if(t)n(t);else{const h=await O.getCompanies();w(h);let b=[];switch(c){case"superadmin":case"manager":b=h;break;case"partner":b=(m==null?void 0:m.assigned_companies)||[];break;default:b=[]}n(b),!o&&b.length>0&&!i&&s(b[0].id.toString())}}catch(h){console.error("Failed to load companies:",h),n([])}})()},[m,c,t,o,i,s]);const p=g=>{s(g.target.value)};return e.jsx("div",{className:`relative ${a}`,children:e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:o,onChange:p,disabled:d,className:`
            appearance-none w-full bg-white border border-gray-300 rounded-lg px-4 py-2 pr-10
            text-sm font-medium text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary 
            focus:border-primary disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed
          `,children:[i&&e.jsx("option",{value:"all",children:"All Companies"}),r.map(g=>e.jsx("option",{value:g.id,children:g.name},g.id))]}),e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:e.jsx(xe,{className:"h-4 w-4 text-gray-400"})}),e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(P,{className:"h-4 w-4 text-gray-400"})})]})})};se.propTypes={selectedCompany:y.string.isRequired,onCompanyChange:y.func.isRequired,companies:y.arrayOf(y.shape({id:y.string.isRequired,name:y.string.isRequired})),className:y.string,showAllOption:y.bool,disabled:y.bool};y.arrayOf(y.string).isRequired,y.func.isRequired,y.arrayOf(y.shape({id:y.string.isRequired,name:y.string.isRequired})),y.string,y.string,y.bool,y.bool,y.number;const Ae=({onFileSelect:o,acceptedTypes:s=[".xlsx",".xls",".pdf"],maxSize:t=10*1024*1024,multiple:a=!1,disabled:i=!1,className:d=""})=>{const[m,c]=l.useState(!1),[r,n]=l.useState([]),[x,w]=l.useState([]),p=l.useRef(null),g=u=>{var C;const f=[],S="."+((C=u.name.split(".").pop())==null?void 0:C.toLowerCase());if(s.includes(S)||f.push(`Invalid file type. Accepted types: ${s.join(", ")}`),u.size>t){const E=Math.round(t/1048576);f.push(`File size exceeds ${E}MB limit`)}return u.size===0&&f.push("File is empty"),f},h=u=>{const f=Array.from(u),S=[],C=[];f.forEach(E=>{const ue=g(E);ue.length===0?S.push(E):C.push(`${E.name}: ${ue.join(", ")}`)}),w(C),a?(n(E=>[...E,...S]),o([...r,...S])):(n(S.slice(0,1)),o(S[0]||null))},b=u=>{u.preventDefault(),u.stopPropagation(),u.type==="dragenter"||u.type==="dragover"?c(!0):u.type==="dragleave"&&c(!1)},v=u=>{u.preventDefault(),u.stopPropagation(),c(!1),!i&&u.dataTransfer.files&&u.dataTransfer.files[0]&&h(u.dataTransfer.files)},j=u=>{u.preventDefault(),!i&&u.target.files&&u.target.files[0]&&h(u.target.files)},k=u=>{const f=r.filter((S,C)=>C!==u);n(f),o(a?f:null)},F=()=>{!i&&p.current&&p.current.click()},R=u=>{if(u===0)return"0 Bytes";const f=1024,S=["Bytes","KB","MB","GB"],C=Math.floor(Math.log(u)/Math.log(f));return parseFloat((u/Math.pow(f,C)).toFixed(2))+" "+S[C]};return e.jsxs("div",{className:`w-full ${d}`,children:[e.jsxs("div",{className:`
          relative border-2 border-dashed rounded-lg p-6 text-center transition-colors
          ${m?"border-primary bg-primary bg-opacity-5":"border-gray-300"}
          ${i?"bg-gray-100 cursor-not-allowed":"hover:border-primary hover:bg-primary hover:bg-opacity-5 cursor-pointer"}
        `,onDragEnter:b,onDragLeave:b,onDragOver:b,onDrop:v,onClick:F,children:[e.jsx("input",{ref:p,type:"file",multiple:a,accept:s.join(","),onChange:j,disabled:i,className:"hidden"}),e.jsx(G,{className:`mx-auto h-12 w-12 ${i?"text-gray-400":"text-gray-500"}`}),e.jsxs("div",{className:"mt-4",children:[e.jsx("p",{className:`text-sm font-medium ${i?"text-gray-400":"text-gray-900"}`,children:m?"Drop files here":"Click to upload or drag and drop"}),e.jsxs("p",{className:`text-xs mt-1 ${i?"text-gray-400":"text-gray-500"}`,children:[s.join(", ")," up to ",Math.round(t/(1024*1024)),"MB"]})]})]}),x.length>0&&e.jsx("div",{className:"mt-3 space-y-1",children:x.map((u,f)=>e.jsxs("div",{className:"flex items-center text-sm text-red-600",children:[e.jsx(Y,{className:"h-4 w-4 mr-2 flex-shrink-0"}),u]},f))}),r.length>0&&e.jsxs("div",{className:"mt-4 space-y-2",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Selected Files:"}),r.map((u,f)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center min-w-0 flex-1",children:[e.jsx(be,{className:"h-5 w-5 text-gray-400 mr-3 flex-shrink-0"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:u.name}),e.jsx("p",{className:"text-xs text-gray-500",children:R(u.size)})]})]}),e.jsxs("div",{className:"flex items-center ml-4",children:[e.jsx(Z,{className:"h-5 w-5 text-green-500 mr-2"}),e.jsx("button",{type:"button",onClick:S=>{S.stopPropagation(),k(f)},className:"p-1 hover:bg-gray-200 rounded",disabled:i,children:e.jsx(V,{className:"h-4 w-4 text-gray-400"})})]})]},f))]})]})};Ae.propTypes={onFileSelect:y.func.isRequired,acceptedTypes:y.arrayOf(y.string),maxSize:y.number,multiple:y.bool,disabled:y.bool,className:y.string};const te=({isOpen:o,onClose:s,onUpload:t})=>{var b;const[a,i]=l.useState(null),[d,m]=l.useState("select"),[c,r]=l.useState(0),[n,x]=l.useState(null),w=v=>{i(v),m("select")},p=async()=>{if(a){m("uploading"),r(0);try{const v=setInterval(()=>{r(k=>k>=90?(clearInterval(v),90):k+10)},200),j=await t(a);clearInterval(v),r(100),setTimeout(()=>{x(j),m(j.success?"success":"error")},500)}catch(v){m("error"),x({success:!1,error:v.message||"Upload failed"})}}},g=()=>{i(null),m("select"),r(0),x(null),s()},h=()=>{i(null),m("select"),r(0),x(null)};return o?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:g}),e.jsxs("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[e.jsx("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Upload Inventory File"}),e.jsx("button",{onClick:g,className:"text-gray-400 hover:text-gray-600",children:e.jsx(V,{className:"h-6 w-6"})})]})}),e.jsxs("div",{className:"px-4 pb-4 sm:px-6 sm:pb-6",children:[d==="select"&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Upload Excel (.xlsx, .xls) or PDF files containing inventory data. The system will automatically detect and parse the content."}),e.jsx(Ae,{onFileSelect:w,acceptedTypes:[".xlsx",".xls",".pdf"],maxSize:10*1024*1024,multiple:!1}),a&&e.jsx("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(be,{className:"h-5 w-5 text-blue-500 mr-2"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-blue-900",children:a.name}),e.jsxs("p",{className:"text-xs text-blue-700",children:[(a.size/1024/1024).toFixed(2)," MB"]})]})]})})]}),d==="uploading"&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(G,{className:"mx-auto h-12 w-12 text-blue-500 animate-pulse"}),e.jsx("p",{className:"mt-2 text-sm font-medium text-gray-900",children:"Uploading and processing..."}),e.jsx("p",{className:"text-xs text-gray-500",children:"Please wait while we process your file"})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${c}%`}})}),e.jsxs("p",{className:"text-center text-sm text-gray-600",children:[c,"% complete"]})]}),d==="success"&&n&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(Z,{className:"mx-auto h-12 w-12 text-green-500"}),e.jsx("p",{className:"mt-2 text-sm font-medium text-gray-900",children:"Upload Successful!"})]}),e.jsx("div",{className:"bg-green-50 p-4 rounded-lg",children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-900",children:"Processed Items:"}),e.jsx("p",{className:"text-green-700",children:n.processedItems||0})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-900",children:"Created Items:"}),e.jsx("p",{className:"text-green-700",children:n.createdItems||0})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-900",children:"Updated Items:"}),e.jsx("p",{className:"text-green-700",children:n.updatedItems||0})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-900",children:"Errors:"}),e.jsx("p",{className:"text-green-700",children:((b=n.errors)==null?void 0:b.length)||0})]})]})}),n.errors&&n.errors.length>0&&e.jsxs("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[e.jsx("p",{className:"font-medium text-yellow-900 mb-2",children:"Warnings:"}),e.jsxs("ul",{className:"text-sm text-yellow-700 space-y-1",children:[n.errors.slice(0,5).map((v,j)=>e.jsxs("li",{children:["• ",v]},j)),n.errors.length>5&&e.jsxs("li",{children:["• ... and ",n.errors.length-5," more"]})]})]})]}),d==="error"&&n&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(Y,{className:"mx-auto h-12 w-12 text-red-500"}),e.jsx("p",{className:"mt-2 text-sm font-medium text-gray-900",children:"Upload Failed"})]}),e.jsx("div",{className:"bg-red-50 p-4 rounded-lg",children:e.jsx("p",{className:"text-sm text-red-700",children:n.error||"An error occurred during upload"})})]})]}),e.jsxs("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[d==="select"&&e.jsxs(e.Fragment,{children:[e.jsx("button",{type:"button",onClick:p,disabled:!a,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm disabled:bg-gray-300 disabled:cursor-not-allowed",children:"Upload File"}),e.jsx("button",{type:"button",onClick:g,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"Cancel"})]}),d==="uploading"&&e.jsx("button",{type:"button",onClick:g,className:"w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:w-auto sm:text-sm",children:"Cancel"}),(d==="success"||d==="error")&&e.jsxs(e.Fragment,{children:[e.jsx("button",{type:"button",onClick:g,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm",children:"Done"}),e.jsx("button",{type:"button",onClick:h,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"Upload Another"})]})]})]})]})}):null};te.propTypes={isOpen:y.bool.isRequired,onClose:y.func.isRequired,onUpload:y.func.isRequired};const Q=({items:o=[],onItemSelect:s,selectedItems:t=[],showCompanyColumn:a=!0,showActions:i=!0,className:d=""})=>{const[m,c]=l.useState("name"),[r,n]=l.useState("asc"),[x,w]=l.useState(""),[p,g]=l.useState("all"),h=l.useMemo(()=>{let u=o.filter(f=>{var E;const S=f.name.toLowerCase().includes(x.toLowerCase())||((E=f.sku)==null?void 0:E.toLowerCase().includes(x.toLowerCase()))||f.category.toLowerCase().includes(x.toLowerCase()),C=p==="all"||f.status===p;return S&&C});return u.sort((f,S)=>{let C=f[m],E=S[m];return typeof C=="string"&&(C=C.toLowerCase(),E=E.toLowerCase()),r==="asc"?C<E?-1:C>E?1:0:C>E?-1:C<E?1:0}),u},[o,x,p,m,r]),b=u=>{m===u?n(r==="asc"?"desc":"asc"):(c(u),n("asc"))},v=u=>{s(u?h.map(f=>f.id):[])},j=(u,f)=>{s(f?[...t,u]:t.filter(S=>S!==u))},k=({field:u,children:f})=>e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>b(u),children:e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("span",{children:f}),m===u&&(r==="asc"?e.jsx(He,{className:"h-4 w-4"}):e.jsx(xe,{className:"h-4 w-4"}))]})}),F=u=>{const f={active:"bg-green-100 text-green-800",inactive:"bg-yellow-100 text-yellow-800",discontinued:"bg-red-100 text-red-800"};return e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${f[u]||"bg-gray-100 text-gray-800"}`,children:u})},R=u=>u.currentQuantity<=u.minStockLevel;return e.jsxs("div",{className:`bg-white shadow rounded-lg ${d}`,children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx(W,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search items...",value:x,onChange:u=>w(u.target.value),className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"})]}),e.jsxs("select",{value:p,onChange:u=>g(u.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"}),e.jsx("option",{value:"discontinued",children:"Discontinued"})]})]}),e.jsxs("div",{className:"text-sm text-gray-500",children:[h.length," of ",o.length," items"]})]})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[i&&e.jsx("th",{className:"px-6 py-3 text-left",children:e.jsx("input",{type:"checkbox",checked:t.length===h.length&&h.length>0,onChange:u=>v(u.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"})}),e.jsx(k,{field:"name",children:"Name"}),e.jsx(k,{field:"sku",children:"SKU"}),e.jsx(k,{field:"category",children:"Category"}),e.jsx(k,{field:"currentQuantity",children:"Quantity"}),e.jsx(k,{field:"unitPrice",children:"Unit Price"}),e.jsx(k,{field:"totalValue",children:"Total Value"}),a&&e.jsx(k,{field:"companyName",children:"Company"}),e.jsx(k,{field:"status",children:"Status"}),e.jsx(k,{field:"lastUpdated",children:"Last Updated"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:h.map(u=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[i&&e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("input",{type:"checkbox",checked:t.includes(u.id),onChange:f=>j(u.id,f.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:u.name}),u.description&&e.jsx("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:u.description})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:u.sku||"-"}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:u.category}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.jsxs("div",{className:`${R(u)?"text-red-600 font-semibold":""}`,children:[u.currentQuantity,R(u)&&e.jsx("span",{className:"ml-1 text-xs text-red-500",children:"(Low)"})]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Min: ",u.minStockLevel]})]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",u.unitPrice.toFixed(2)]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",u.totalValue.toFixed(2)]}),a&&e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:u.companyName}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:F(u.status)}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(u.lastUpdated).toLocaleDateString()})]},u.id))})]})}),h.length===0&&e.jsx("div",{className:"text-center py-12",children:e.jsx("div",{className:"text-gray-500",children:"No items found"})})]})};Q.propTypes={items:y.arrayOf(y.object),onItemSelect:y.func,selectedItems:y.arrayOf(y.string),showCompanyColumn:y.bool,showActions:y.bool,className:y.string};const Fe=({stats:o,className:s=""})=>{const t=d=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(d),a=d=>new Intl.NumberFormat("en-US").format(d),i=({title:d,value:m,icon:c,trend:r,trendValue:n,color:x="blue"})=>{const w={blue:"bg-blue-50 text-blue-600",green:"bg-green-50 text-green-600",yellow:"bg-yellow-50 text-yellow-600",red:"bg-red-50 text-red-600",purple:"bg-purple-50 text-purple-600"};return e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:d}),e.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:m}),r&&e.jsxs("div",{className:"flex items-center mt-2",children:[r==="up"?e.jsx(ce,{className:"h-4 w-4 text-green-500 mr-1"}):e.jsx(We,{className:"h-4 w-4 text-red-500 mr-1"}),e.jsx("span",{className:`text-sm ${r==="up"?"text-green-600":"text-red-600"}`,children:n})]})]}),e.jsx("div",{className:`p-3 rounded-full ${w[x]}`,children:c})]})})};return e.jsxs("div",{className:`space-y-6 ${s}`,children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.jsx(i,{title:"Total Items",value:a(o.totalItems),icon:e.jsx(M,{className:"h-6 w-6"}),color:"blue"}),e.jsx(i,{title:"Total Value",value:t(o.totalValue),icon:e.jsx(z,{className:"h-6 w-6"}),color:"green"}),e.jsx(i,{title:"Low Stock Items",value:a(o.lowStockItems),icon:e.jsx(q,{className:"h-6 w-6"}),color:"red"}),e.jsx(i,{title:"Companies",value:a(o.companiesCount),icon:e.jsx(J,{className:"h-6 w-6"}),color:"purple"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Top Categories"})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:o.topCategories.map((d,m)=>{const c=d.value/o.totalValue*100;return e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:`w-3 h-3 rounded-full ${m===0?"bg-blue-500":m===1?"bg-green-500":m===2?"bg-yellow-500":m===3?"bg-purple-500":"bg-gray-500"}`})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:d.category}),e.jsxs("p",{className:"text-xs text-gray-500",children:[d.count," items"]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:t(d.value)}),e.jsxs("p",{className:"text-xs text-gray-500",children:[c.toFixed(1),"%"]})]})]},d.category)})})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Average Item Value"}),e.jsx("p",{className:"text-xl font-semibold text-gray-900",children:t(o.totalValue/o.totalItems||0)})]}),e.jsx(z,{className:"h-8 w-8 text-gray-400"})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Low Stock Rate"}),e.jsxs("p",{className:"text-xl font-semibold text-gray-900",children:[(o.lowStockItems/o.totalItems*100||0).toFixed(1),"%"]})]}),e.jsx(q,{className:"h-8 w-8 text-gray-400"})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Recent Transactions"}),e.jsx("p",{className:"text-xl font-semibold text-gray-900",children:a(o.recentTransactions)})]}),e.jsx(ce,{className:"h-8 w-8 text-gray-400"})]})})]})]})};Fe.propTypes={stats:y.shape({totalItems:y.number.isRequired,totalValue:y.number.isRequired,lowStockItems:y.number.isRequired,companiesCount:y.number.isRequired,recentTransactions:y.number.isRequired,topCategories:y.arrayOf(y.shape({category:y.string.isRequired,count:y.number.isRequired,value:y.number.isRequired})).isRequired}).isRequired,className:y.string};const Cs=()=>{const{user:o}=l.useContext(A),[s,t]=l.useState([]),[a,i]=l.useState("all"),[d,m]=l.useState([]),[c,r]=l.useState(!1),[n,x]=l.useState([]),[w,p]=l.useState(!0),[g,h]=l.useState(null);l.useEffect(()=>{o&&(async()=>{try{p(!0);const j=await T.getInventoryItems();t(j);const k={totalItems:j.length,totalValue:j.reduce((F,R)=>F+(R.total_value||0),0),lowStockItems:j.filter(F=>F.current_quantity<=F.min_stock_level).length,categories:[...new Set(j.map(F=>F.category))].length};h(k)}catch(j){console.error("Failed to load inventory data:",j),t([]),h(null)}finally{p(!1)}})()},[o]),l.useEffect(()=>{x(a==="all"?s:s.filter(v=>v.companyId===a))},[s,a]);const b=async v=>new Promise(j=>{setTimeout(()=>{j({success:!0,processedItems:15,createdItems:8,updatedItems:7,errors:['Row 3: Missing SKU for item "Test Item"']})},2e3)});return w?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Inventory Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage inventory across all companies"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})})]}):e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Inventory Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage inventory across all companies"})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsxs("button",{onClick:()=>r(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(G,{className:"h-4 w-4 mr-2"}),"Upload Inventory"]}),e.jsxs("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[e.jsx(we,{className:"h-4 w-4 mr-2"}),"Export"]})]})]})}),g&&e.jsx("div",{className:"mb-8",children:e.jsx(Fe,{stats:g})}),e.jsxs("div",{className:"mb-6 flex items-center space-x-4",children:[e.jsx(se,{selectedCompany:a,onCompanyChange:i,className:"w-64"}),e.jsxs("button",{className:"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),"More Filters"]})]}),e.jsx(Q,{items:n,selectedItems:d,onItemSelect:m,showCompanyColumn:a==="all"}),e.jsx(te,{isOpen:c,onClose:()=>r(!1),onUpload:b})]})},Is=()=>{const{userRole:o}=l.useContext(A),[s,t]=l.useState([{id:"1",name:"Bob Partner",email:"<EMAIL>",companies:["Acme Corp","TechStart Inc"],status:"active"},{id:"2",name:"Alice Partner",email:"<EMAIL>",companies:["Global Ventures"],status:"active"}]),[a,i]=l.useState(!1),d=async m=>{try{const c={...m,role:"partner"},r=await js.createUser(c);t(n=>[...n,{id:r.id,name:r.name,email:r.email,companies:[],status:r.status||"active"}]),i(!1)}catch(c){throw console.error("Failed to add partner:",c),c}};return e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Partner Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage partners and their company assignments"})]}),e.jsxs("button",{onClick:()=>i(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(X,{className:"h-4 w-4 mr-2"}),"Add Partner"]})]})}),e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(W,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search partners...",className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary w-64"})]})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Partner"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Assigned Companies"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(m=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:m.name}),e.jsx("div",{className:"text-sm text-gray-500",children:m.email})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:m.companies.join(", ")})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:m.status})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[e.jsx("button",{className:"text-primary hover:text-primary-dark mr-3",children:"Edit"}),e.jsx("button",{className:"text-red-600 hover:text-red-900",children:"Remove"})]})]},m.id))})]})})]}),e.jsx(_e,{isOpen:a,onClose:()=>i(!1),onSubmit:d,userRole:o})]})},_s=()=>{const{user:o,userRole:s}=l.useContext(A),[t,a]=l.useState([]),[i,d]=l.useState(!0),[m,c]=l.useState(!1);l.useEffect(()=>{o&&(async()=>{try{d(!0);const x=await O.getCompanies();a(x)}catch(x){console.error("Failed to load companies:",x),a([])}finally{d(!1)}})()},[o]);const r=async n=>{try{const x={...n,managerId:s==="manager"?o.id:n.managerId},w=await O.createCompany(x);a(p=>[...p,w]),c(!1)}catch(x){throw console.error("Failed to add company:",x),x}};return i?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Company Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage companies and their details"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})})]}):e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Company Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage companies and their details"})]}),e.jsxs("button",{onClick:()=>c(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(X,{className:"h-4 w-4 mr-2"}),"Add Company"]})]})}),e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(W,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search companies...",className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary w-64"})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6",children:t.map(n=>e.jsxs("div",{className:"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:n.name}),e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:"Active"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsxs("p",{children:["ID: ",n.id]}),e.jsx("p",{children:"Inventory Items: 3"}),e.jsx("p",{children:"Assigned Partners: 1"})]}),e.jsxs("div",{className:"mt-4 flex space-x-2",children:[e.jsx("button",{className:"text-primary hover:text-primary-dark text-sm",children:"View Details"}),e.jsx("button",{className:"text-gray-600 hover:text-gray-900 text-sm",children:"Edit"})]})]},n.id))})]}),e.jsx(Ee,{isOpen:m,onClose:()=>c(!1),onSubmit:r,userRole:s,currentUserId:o==null?void 0:o.id})]})},Es=()=>{const{user:o}=l.useContext(A),[s,t]=l.useState([]),[a,i]=l.useState("all"),[d,m]=l.useState([]),[c,r]=l.useState(!1),[n,x]=l.useState([]),[w,p]=l.useState(!0);l.useEffect(()=>{o&&(async()=>{try{p(!0);const b=await T.getInventoryItems();t(b)}catch(b){console.error("Failed to load inventory items:",b),t([])}finally{p(!1)}})()},[o]),l.useEffect(()=>{x(a==="all"?s:s.filter(h=>h.companyId===a))},[s,a]);const g=async h=>new Promise(b=>{setTimeout(()=>{b({success:!0,processedItems:10,createdItems:5,updatedItems:5,errors:[]})},2e3)});return e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Inventory Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage inventory for your companies"})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsxs("button",{onClick:()=>r(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(G,{className:"h-4 w-4 mr-2"}),"Upload Inventory"]}),e.jsxs("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[e.jsx(we,{className:"h-4 w-4 mr-2"}),"Export"]})]})]})}),e.jsx("div",{className:"mb-6",children:e.jsx(se,{selectedCompany:a,onCompanyChange:i,className:"w-64"})}),e.jsx(Q,{items:n,selectedItems:d,onItemSelect:m,showCompanyColumn:a==="all"}),e.jsx(te,{isOpen:c,onClose:()=>r(!1),onUpload:g})]})},As=()=>{const o=H(),[s]=l.useState([{id:"1",name:"Acme Corp",inventoryItems:3,totalValue:17899.67,lastUpdate:"2024-01-15"},{id:"2",name:"TechStart Inc",inventoryItems:2,totalValue:2949.83,lastUpdate:"2024-01-13"}]);return e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Your Companies"}),e.jsx("p",{className:"text-gray-600",children:"Companies assigned to your portfolio"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:s.map(t=>e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:t.name}),e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:"Active"})]}),e.jsxs("div",{className:"space-y-3 mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Inventory Items"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(M,{className:"h-4 w-4 text-gray-400 mr-1"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:t.inventoryItems})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Total Value"}),e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["$",t.totalValue.toLocaleString()]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Last Updated"}),e.jsx("span",{className:"text-sm text-gray-500",children:new Date(t.lastUpdate).toLocaleDateString()})]})]}),e.jsxs("button",{onClick:()=>o(`/partner/company/${t.id}`),className:"w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(K,{className:"h-4 w-4 mr-2"}),"View Details"]})]},t.id))})]})},Fs=()=>{const{id:o}=De(),s=H(),{user:t}=l.useContext(A),[a,i]=l.useState(null),[d,m]=l.useState([]),[c,r]=l.useState(!0);if(l.useEffect(()=>{t&&o&&(async()=>{try{r(!0);const p=await O.getCompany(o);i(p);const g=await T.getInventoryItems({company_id:o});m(g)}catch(p){console.error("Failed to load company data:",p),i(null),m([])}finally{r(!1)}})()},[o,t]),c)return e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})})});if(!a)return e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-gray-500",children:"Company not found"})})});const n=d.reduce((w,p)=>w+p.totalValue,0),x=d.filter(w=>w.currentQuantity<=w.minStockLevel).length;return e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("button",{onClick:()=>s("/partner/companies"),className:"inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mb-4",children:[e.jsx(fe,{className:"h-4 w-4 mr-1"}),"Back to Companies"]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:a.name}),e.jsx("p",{className:"text-gray-600",children:"Company inventory and details"})]}),e.jsx("span",{className:"inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800",children:"Active"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Items"}),e.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:d.length})]}),e.jsx("div",{className:"p-3 rounded-full bg-blue-50 text-blue-600",children:e.jsx(M,{className:"h-6 w-6"})})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Value"}),e.jsxs("p",{className:"text-2xl font-semibold text-gray-900",children:["$",n.toLocaleString()]})]}),e.jsx("div",{className:"p-3 rounded-full bg-green-50 text-green-600",children:e.jsx(z,{className:"h-6 w-6"})})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Low Stock Items"}),e.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:x})]}),e.jsx("div",{className:"p-3 rounded-full bg-red-50 text-red-600",children:e.jsx(ce,{className:"h-6 w-6"})})]})})]}),e.jsx(Q,{items:d,showCompanyColumn:!1,showActions:!1})]})},Us=()=>{const{user:o}=l.useContext(A),[s,t]=l.useState([]),[a,i]=l.useState("all"),[d,m]=l.useState([]),[c,r]=l.useState(!1),[n,x]=l.useState([]),[w,p]=l.useState(!0);l.useEffect(()=>{o&&(async()=>{var b;try{p(!0);const v=await T.getInventoryItems();t(v),((b=o==null?void 0:o.assigned_companies)==null?void 0:b.length)>0&&i(o.assigned_companies[0].id.toString())}catch(v){console.error("Failed to load inventory items:",v),t([])}finally{p(!1)}})()},[o]),l.useEffect(()=>{x(a==="all"?s:s.filter(h=>h.companyId===a))},[s,a]);const g=async h=>new Promise(b=>{setTimeout(()=>{b({success:!0,processedItems:8,createdItems:3,updatedItems:5,errors:[]})},2e3)});return e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Inventory Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage inventory for your assigned companies"})]}),e.jsxs("button",{onClick:()=>r(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(G,{className:"h-4 w-4 mr-2"}),"Upload Inventory"]})]})}),e.jsx("div",{className:"mb-6",children:e.jsx(se,{selectedCompany:a,onCompanyChange:i,className:"w-64",showAllOption:!0})}),e.jsx(Q,{items:n,selectedItems:d,onItemSelect:m,showCompanyColumn:a==="all"}),e.jsx(te,{isOpen:c,onClose:()=>r(!1),onUpload:g})]})};class Rs{async getNotifications(s={}){try{const t=await N.getNotifications(s);if(t.success)return t.data||[];throw new Error(t.message||"Failed to fetch notifications")}catch(t){throw console.error("Error fetching notifications:",t),t}}async markAsRead(s){try{const t=await N.markNotificationAsRead(s);if(t.success)return!0;throw new Error(t.message||"Failed to mark notification as read")}catch(t){throw console.error("Error marking notification as read:",t),t}}async markAllAsRead(){try{const s=await N.markAllNotificationsAsRead();if(s.success)return s.data;throw new Error(s.message||"Failed to mark all notifications as read")}catch(s){throw console.error("Error marking all notifications as read:",s),s}}async deleteNotification(s){try{const t=await N.deleteNotification(s);if(t.success)return!0;throw new Error(t.message||"Failed to delete notification")}catch(t){throw console.error("Error deleting notification:",t),t}}async getUnreadCount(){try{return(await this.getNotifications({unread_only:!0})).unread_count||0}catch(s){return console.error("Error fetching unread count:",s),0}}}const Ds=new Rs,_=({children:o})=>{const[s,t]=l.useState(!1),[a,i]=l.useState(0),{user:d,userRole:m,userEmail:c,logout:r}=l.useContext(A),n=H(),x=Pe();l.useEffect(()=>{(async()=>{try{if(d){const b=await Ds.getNotifications({is_read:!1});i(b.length)}}catch(b){console.error("Failed to load notification count:",b),i(0)}})()},[d]);const w=()=>{r(),n("/login")},g=(()=>{switch(m){case"superadmin":return[{name:"Dashboard",icon:e.jsx(re,{size:20}),path:"/superadmin"},{name:"User Management",icon:e.jsx(oe,{size:20}),path:"/superadmin/users"},{name:"Company Oversight",icon:e.jsx(P,{size:20}),path:"/superadmin/companies"},{name:"Inventory",icon:e.jsx(M,{size:20}),path:"/superadmin/inventory"},{name:"Notifications",icon:e.jsx(L,{size:20}),path:"/superadmin/notifications",badge:a},{name:"Settings",icon:e.jsx(ae,{size:20}),path:"/superadmin/settings"}];case"manager":return[{name:"Dashboard",icon:e.jsx(re,{size:20}),path:"/manager"},{name:"Partners",icon:e.jsx(oe,{size:20}),path:"/manager/partners"},{name:"Companies",icon:e.jsx(P,{size:20}),path:"/manager/companies"},{name:"Inventory",icon:e.jsx(M,{size:20}),path:"/manager/inventory"},{name:"Notifications",icon:e.jsx(L,{size:20}),path:"/manager/notifications",badge:a},{name:"Settings",icon:e.jsx(ae,{size:20}),path:"/manager/settings"}];case"partner":return[{name:"Portfolio",icon:e.jsx(re,{size:20}),path:"/partner"},{name:"Companies",icon:e.jsx(P,{size:20}),path:"/partner/companies"},{name:"Inventory",icon:e.jsx(M,{size:20}),path:"/partner/inventory"},{name:"Notifications",icon:e.jsx(L,{size:20}),path:"/partner/notifications",badge:a},{name:"Settings",icon:e.jsx(ae,{size:20}),path:"/partner/settings"}];default:return[]}})();return e.jsxs("div",{className:"flex h-screen bg-neutral-light overflow-hidden",children:[s&&e.jsx("div",{className:"fixed inset-0 z-20 bg-black bg-opacity-50 transition-opacity md:hidden",onClick:()=>t(!1)}),e.jsx("div",{className:`fixed inset-y-0 left-0 z-30 w-64 transform bg-secondary transition duration-300 ease-in-out md:relative md:translate-x-0 ${s?"translate-x-0":"-translate-x-full"}`,children:e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between px-4 py-5 border-b border-secondary-dark",children:[e.jsx("div",{className:"flex items-center",children:e.jsx("span",{className:"text-xl font-bold text-white",children:"Enterprise Portal"})}),e.jsx("button",{onClick:()=>t(!1),className:"text-white md:hidden",children:e.jsx(V,{size:24})})]}),e.jsx("div",{className:"px-4 py-4 border-b border-secondary-dark",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"h-10 w-10 rounded-full bg-primary flex items-center justify-center text-white font-bold",children:m==null?void 0:m.charAt(0).toUpperCase()}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm font-medium text-white",children:m==="superadmin"?"Super Admin":m==="manager"?"Manager":"Partner"}),e.jsx("p",{className:"text-xs text-white text-opacity-70",children:c||"No email"})]}),e.jsx(xe,{size:16,className:"ml-auto text-white text-opacity-70"})]})}),e.jsx("nav",{className:"flex-1 overflow-y-auto px-2 py-4",children:e.jsx("ul",{className:"space-y-1",children:g.map(h=>e.jsx("li",{children:e.jsxs("a",{href:h.path,onClick:b=>{b.preventDefault(),n(h.path),t(!1)},className:`flex items-center px-4 py-3 text-sm rounded-lg ${x.pathname===h.path?"bg-primary text-white":"text-white text-opacity-80 hover:bg-secondary-dark"}`,children:[e.jsx("span",{className:"mr-3",children:h.icon}),h.name,h.badge&&e.jsx("span",{className:"ml-auto bg-red-500 text-white text-xs font-bold px-2 py-0.5 rounded-full",children:h.badge})]})},h.name))})}),e.jsx("div",{className:"border-t border-secondary-dark p-4",children:e.jsxs("button",{onClick:w,className:"flex w-full items-center px-4 py-3 text-sm text-white text-opacity-80 rounded-lg hover:bg-secondary-dark",children:[e.jsx(Ge,{size:20,className:"mr-3"}),"Sign Out"]})})]})}),e.jsxs("div",{className:"flex flex-1 flex-col overflow-hidden",children:[e.jsx("header",{className:"bg-white shadow-sm z-10",children:e.jsxs("div",{className:"px-4 py-4 flex items-center justify-between",children:[e.jsx("button",{onClick:()=>t(!0),className:"text-neutral-dark md:hidden",children:e.jsx(Qe,{size:24})}),e.jsx("div",{className:"md:hidden font-montserrat font-bold text-lg",children:"Enterprise Portal"}),e.jsx("div",{className:"flex items-center space-x-4",children:e.jsxs("button",{className:"relative p-1 rounded-full text-gray-500 hover:bg-gray-100 hover:text-gray-600",onClick:()=>n(`/${m}/notifications`),children:[e.jsx(L,{size:20}),a>0&&e.jsx("span",{className:"absolute top-0 right-0 block h-4 w-4 rounded-full bg-red-500 text-white text-xs font-bold flex items-center justify-center",children:a})]})})]})}),e.jsx("main",{className:"flex-1 overflow-y-auto bg-neutral-light",children:o})]})]})};_.propTypes={children:y.node.isRequired};const ne=()=>{const[o,s]=l.useState([{id:"1",type:"warning",title:"Low Stock Alert",message:"Conference Tables at TechStart Inc is running low (2 remaining, minimum 1)",timestamp:"2024-01-15T10:30:00Z",read:!1},{id:"2",type:"info",title:"Inventory Upload Completed",message:"Successfully processed 15 items from purchases_january_2024.xlsx",timestamp:"2024-01-15T09:15:00Z",read:!1},{id:"3",type:"success",title:"New Partner Added",message:"Alice Partner has been successfully added to the system",timestamp:"2024-01-14T16:45:00Z",read:!0}]),t=c=>{s(r=>r.map(n=>n.id===c?{...n,read:!0}:n))},a=()=>{s(c=>c.map(r=>({...r,read:!0})))},i=c=>{s(r=>r.filter(n=>n.id!==c))},d=c=>{switch(c){case"warning":return e.jsx(q,{className:"h-5 w-5 text-yellow-500"});case"info":return e.jsx(Je,{className:"h-5 w-5 text-blue-500"});case"success":return e.jsx(Ye,{className:"h-5 w-5 text-green-500"});default:return e.jsx(L,{className:"h-5 w-5 text-gray-500"})}},m=o.filter(c=>!c.read).length;return e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"}),e.jsx("p",{className:"text-gray-600",children:m>0?`${m} unread notifications`:"All notifications read"})]}),m>0&&e.jsx("button",{onClick:a,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:"Mark all as read"})]})}),e.jsx("div",{className:"bg-white shadow rounded-lg",children:o.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(L,{className:"mx-auto h-12 w-12 text-gray-400"}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No notifications"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"You're all caught up!"})]}):e.jsx("div",{className:"divide-y divide-gray-200",children:o.map(c=>e.jsx("div",{className:`p-6 hover:bg-gray-50 ${c.read?"":"bg-blue-50"}`,children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:d(c.type)}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("p",{className:`text-sm font-medium ${c.read?"text-gray-700":"text-gray-900"}`,children:c.title}),!c.read&&e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})]}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:c.message}),e.jsx("p",{className:"mt-2 text-xs text-gray-500",children:new Date(c.timestamp).toLocaleString()})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[!c.read&&e.jsx("button",{onClick:()=>t(c.id),className:"text-blue-600 hover:text-blue-800 text-sm",children:"Mark as read"}),e.jsx("button",{onClick:()=>i(c.id),className:"text-gray-400 hover:text-gray-600",children:e.jsx(V,{className:"h-4 w-4"})})]})]})},c.id))})})]})},ie=()=>{const{userEmail:o,userRole:s}=l.useContext(A),[t,a]=l.useState({emailNotifications:!0,pushNotifications:!1,lowStockAlerts:!0,weeklyReports:!0,theme:"light",language:"en"}),i=(c,r)=>{a(n=>({...n,[c]:r}))},d=({title:c,icon:r,children:n})=>e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-2 bg-gray-100 rounded-lg mr-3",children:r}),e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:c})]})}),e.jsx("div",{className:"p-6",children:n})]}),m=({label:c,description:r,checked:n,onChange:x})=>e.jsxs("div",{className:"flex items-center justify-between py-3",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:c}),r&&e.jsx("p",{className:"text-sm text-gray-500",children:r})]}),e.jsx("button",{onClick:()=>x(!n),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${n?"bg-primary":"bg-gray-200"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${n?"translate-x-6":"translate-x-1"}`})})]});return e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Settings"}),e.jsx("p",{className:"text-gray-600",children:"Manage your account preferences and system settings"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(d,{title:"Profile",icon:e.jsx(Ke,{className:"h-5 w-5 text-gray-600"}),children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),e.jsx("input",{type:"email",value:o,disabled:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Role"}),e.jsx("input",{type:"text",value:s,disabled:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm"})]})]})}),e.jsx(d,{title:"Notifications",icon:e.jsx(L,{className:"h-5 w-5 text-gray-600"}),children:e.jsxs("div",{className:"space-y-1",children:[e.jsx(m,{label:"Email Notifications",description:"Receive notifications via email",checked:t.emailNotifications,onChange:c=>i("emailNotifications",c)}),e.jsx(m,{label:"Push Notifications",description:"Receive browser push notifications",checked:t.pushNotifications,onChange:c=>i("pushNotifications",c)}),e.jsx(m,{label:"Low Stock Alerts",description:"Get notified when inventory is running low",checked:t.lowStockAlerts,onChange:c=>i("lowStockAlerts",c)}),e.jsx(m,{label:"Weekly Reports",description:"Receive weekly inventory summary reports",checked:t.weeklyReports,onChange:c=>i("weeklyReports",c)})]})}),e.jsx(d,{title:"Security",icon:e.jsx(Ze,{className:"h-5 w-5 text-gray-600"}),children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:"Change Password"}),e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-600",children:["Last login: ",new Date().toLocaleDateString()," at ",new Date().toLocaleTimeString()]})})]})}),(s==="superadmin"||s==="manager")&&e.jsx(d,{title:"System",icon:e.jsx(Xe,{className:"h-5 w-5 text-gray-600"}),children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Default File Upload Size Limit"}),e.jsxs("select",{className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",children:[e.jsx("option",{children:"10 MB"}),e.jsx("option",{children:"25 MB"}),e.jsx("option",{children:"50 MB"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Data Retention Period"}),e.jsxs("select",{className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",children:[e.jsx("option",{children:"1 Year"}),e.jsx("option",{children:"2 Years"}),e.jsx("option",{children:"5 Years"})]})]})]})})]}),e.jsx("div",{className:"mt-8 flex justify-end",children:e.jsx("button",{className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:"Save Changes"})})]})},A=l.createContext({isAuthenticated:!1,user:null,userRole:null,userEmail:"",login:()=>{},logout:()=>{}});function Ps(){const[o,s]=l.useState(!1),[t,a]=l.useState(null),[i,d]=l.useState(null),[m,c]=l.useState(""),[r,n]=l.useState(!0);l.useEffect(()=>{(async()=>{try{if($.isAuthenticated()){await $.initializeFromToken();const h=$.getCurrentUser();h&&(s(!0),a(h),d(h.role),c(h.email))}}catch(h){console.error("Auth initialization failed:",h),$.logout()}finally{n(!1)}})()},[]);const x=g=>(s(!0),a(g),d(g.role),c(g.email),!0),w=async()=>{try{await $.logout()}catch(g){console.error("Logout error:",g)}finally{s(!1),a(null),d(null),c("")}};if(r)return e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"}),e.jsx("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})});const p=({children:g,requiredRole:h})=>o?h&&i!==h?i==="superadmin"?e.jsx(D,{to:"/superadmin",replace:!0}):i==="manager"?e.jsx(D,{to:"/manager",replace:!0}):e.jsx(D,{to:"/partner",replace:!0}):g:e.jsx(D,{to:"/login",replace:!0});return p.propTypes={children:y.node.isRequired,requiredRole:y.oneOf(["superadmin","manager","partner"])},e.jsx(A.Provider,{value:{isAuthenticated:o,user:t,userRole:i,userEmail:m,login:x,logout:w},children:e.jsx(Te,{children:e.jsxs($e,{children:[e.jsx(I,{path:"/",element:o?i==="superadmin"?e.jsx(D,{to:"/superadmin",replace:!0}):i==="manager"?e.jsx(D,{to:"/manager",replace:!0}):e.jsx(D,{to:"/partner",replace:!0}):e.jsx(D,{to:"/login",replace:!0})}),e.jsx(I,{path:"/login",element:e.jsx(ps,{})}),e.jsx(I,{path:"/reset-password",element:e.jsx(gs,{})}),e.jsx(I,{path:"/superadmin",element:e.jsx(p,{requiredRole:"superadmin",children:e.jsx(_,{children:e.jsx(ws,{})})})}),e.jsx(I,{path:"/superadmin/users",element:e.jsx(p,{requiredRole:"superadmin",children:e.jsx(_,{children:e.jsx(Ss,{})})})}),e.jsx(I,{path:"/superadmin/companies",element:e.jsx(p,{requiredRole:"superadmin",children:e.jsx(_,{children:e.jsx(ks,{})})})}),e.jsx(I,{path:"/superadmin/inventory",element:e.jsx(p,{requiredRole:"superadmin",children:e.jsx(_,{children:e.jsx(Cs,{})})})}),e.jsx(I,{path:"/superadmin/notifications",element:e.jsx(p,{requiredRole:"superadmin",children:e.jsx(_,{children:e.jsx(ne,{})})})}),e.jsx(I,{path:"/superadmin/settings",element:e.jsx(p,{requiredRole:"superadmin",children:e.jsx(_,{children:e.jsx(ie,{})})})}),e.jsx(I,{path:"/manager",element:e.jsx(p,{requiredRole:"manager",children:e.jsx(_,{children:e.jsx(Ns,{})})})}),e.jsx(I,{path:"/manager/partners",element:e.jsx(p,{requiredRole:"manager",children:e.jsx(_,{children:e.jsx(Is,{})})})}),e.jsx(I,{path:"/manager/companies",element:e.jsx(p,{requiredRole:"manager",children:e.jsx(_,{children:e.jsx(_s,{})})})}),e.jsx(I,{path:"/manager/inventory",element:e.jsx(p,{requiredRole:"manager",children:e.jsx(_,{children:e.jsx(Es,{})})})}),e.jsx(I,{path:"/manager/notifications",element:e.jsx(p,{requiredRole:"manager",children:e.jsx(_,{children:e.jsx(ne,{})})})}),e.jsx(I,{path:"/manager/settings",element:e.jsx(p,{requiredRole:"manager",children:e.jsx(_,{children:e.jsx(ie,{})})})}),e.jsx(I,{path:"/partner",element:e.jsx(p,{requiredRole:"partner",children:e.jsx(_,{children:e.jsx(vs,{})})})}),e.jsx(I,{path:"/partner/companies",element:e.jsx(p,{requiredRole:"partner",children:e.jsx(_,{children:e.jsx(As,{})})})}),e.jsx(I,{path:"/partner/company/:id",element:e.jsx(p,{requiredRole:"partner",children:e.jsx(_,{children:e.jsx(Fs,{})})})}),e.jsx(I,{path:"/partner/inventory",element:e.jsx(p,{requiredRole:"partner",children:e.jsx(_,{children:e.jsx(Us,{})})})}),e.jsx(I,{path:"/partner/notifications",element:e.jsx(p,{requiredRole:"partner",children:e.jsx(_,{children:e.jsx(ne,{})})})}),e.jsx(I,{path:"/partner/settings",element:e.jsx(p,{requiredRole:"partner",children:e.jsx(_,{children:e.jsx(ie,{})})})}),e.jsx(I,{path:"*",element:e.jsx(D,{to:"/login",replace:!0})})]})})})}const Ts=Se(document.getElementById("root"));Ts.render(e.jsx(Ps,{}));
