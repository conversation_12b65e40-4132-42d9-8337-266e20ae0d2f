#!/usr/bin/env node

/**
 * cPanel Deployment Script for Investment System
 * Automates the deployment process to cPanel hosting
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Investment System - cPanel Deployment Tool\n');

// Configuration
const CONFIG = {
    domain: 'eskillvisor.wallistry.pk',
    localDistPath: './dist',
    frontendPath: './dist/frontend',
    backendPath: './dist/backend',
    databasePath: './dist/database'
};

// Helper functions
function checkFileExists(filePath) {
    return fs.existsSync(filePath);
}

function createZipArchive(sourcePath, outputPath) {
    try {
        console.log(`📦 Creating archive: ${outputPath}`);
        
        // Use PowerShell to create zip on Windows
        const command = `powershell -command "Compress-Archive -Path '${sourcePath}\\*' -DestinationPath '${outputPath}' -Force"`;
        execSync(command, { stdio: 'inherit' });
        
        console.log(`✅ Archive created: ${outputPath}`);
        return true;
    } catch (error) {
        console.error(`❌ Failed to create archive: ${error.message}`);
        return false;
    }
}

function generateDeploymentPackage() {
    console.log('📦 Generating deployment package...\n');
    
    // Check if dist folder exists
    if (!checkFileExists(CONFIG.localDistPath)) {
        console.error('❌ Dist folder not found. Run "npm run build:production" first.');
        return false;
    }
    
    // Create deployment archives
    const deploymentDir = path.join(CONFIG.localDistPath, 'deployment');
    if (!fs.existsSync(deploymentDir)) {
        fs.mkdirSync(deploymentDir, { recursive: true });
    }
    
    // Create frontend archive
    if (checkFileExists(CONFIG.frontendPath)) {
        createZipArchive(
            CONFIG.frontendPath,
            path.join(deploymentDir, 'frontend-deployment.zip')
        );
    }
    
    // Create backend archive
    if (checkFileExists(CONFIG.backendPath)) {
        createZipArchive(
            CONFIG.backendPath,
            path.join(deploymentDir, 'backend-deployment.zip')
        );
    }
    
    return true;
}

function generateDeploymentInstructions() {
    const instructions = `
# 🚀 cPanel Deployment Instructions for Investment System

## 📁 **Upload Instructions**

### **1. Frontend Deployment (public_html/)**
1. Login to your cPanel File Manager
2. Navigate to public_html/
3. Upload and extract: \`deployment/frontend-deployment.zip\`
4. Ensure the structure is:
   \`\`\`
   public_html/
   ├── index.html
   ├── .htaccess
   └── assets/
   \`\`\`

### **2. Backend Deployment (public_html/backend/)**
1. Create folder: public_html/backend/
2. Upload and extract: \`deployment/backend-deployment.zip\`
3. Set permissions:
   - uploads/ → 755
   - logs/ → 755
   - config/ → 644

### **3. Database Setup**
1. Create MySQL database in cPanel
2. Import: \`database/investment_system_schema.sql\`
3. Update credentials in: \`backend/config/config.php\`

## 🔧 **Configuration Updates**

### **Database Configuration:**
Edit \`public_html/backend/config/config.php\`:
\`\`\`php
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database_name');
define('DB_USER', 'your_database_user');
define('DB_PASS', 'your_database_password');
\`\`\`

### **Security Settings:**
Update these in \`config.php\`:
\`\`\`php
define('JWT_SECRET', 'your_unique_jwt_secret');
define('ENCRYPTION_KEY', 'your_unique_encryption_key');
\`\`\`

## ✅ **Testing**

After deployment, test:
1. **Frontend:** https://${CONFIG.domain}
2. **Backend API:** https://${CONFIG.domain}/backend/companies.php
3. **Login:** <EMAIL> / password (change immediately)

## 🔄 **Updates**

For future updates:
1. Run: \`npm run build:production\`
2. Upload only changed files
3. Clear any caches

---
**Deployment configured for: ${CONFIG.domain}**
**Build time: ${new Date().toISOString()}**
`;

    const instructionsPath = path.join(CONFIG.localDistPath, 'CPANEL_DEPLOYMENT_INSTRUCTIONS.md');
    fs.writeFileSync(instructionsPath, instructions);
    console.log(`📝 Deployment instructions created: ${instructionsPath}`);
}

function validateDeployment() {
    console.log('🔍 Validating deployment package...\n');
    
    const checks = [
        {
            name: 'Frontend files',
            path: path.join(CONFIG.frontendPath, 'index.html'),
            required: true
        },
        {
            name: 'Frontend .htaccess',
            path: path.join(CONFIG.frontendPath, '.htaccess'),
            required: true
        },
        {
            name: 'Backend config',
            path: path.join(CONFIG.backendPath, 'config/config.php'),
            required: true
        },
        {
            name: 'Backend .htaccess',
            path: path.join(CONFIG.backendPath, '.htaccess'),
            required: true
        },
        {
            name: 'Database schema',
            path: path.join(CONFIG.databasePath, 'investment_system_schema.sql'),
            required: true
        }
    ];
    
    let allValid = true;
    
    checks.forEach(check => {
        const exists = checkFileExists(check.path);
        const status = exists ? '✅' : '❌';
        console.log(`${status} ${check.name}: ${check.path}`);
        
        if (check.required && !exists) {
            allValid = false;
        }
    });
    
    console.log('');
    
    if (allValid) {
        console.log('✅ All required files present');
        return true;
    } else {
        console.log('❌ Some required files are missing');
        return false;
    }
}

function showDeploymentSummary() {
    console.log('\n🎯 **Deployment Summary**\n');
    console.log(`Domain: ${CONFIG.domain}`);
    console.log(`Build time: ${new Date().toISOString()}`);
    console.log(`Deployment package: ${CONFIG.localDistPath}/deployment/`);
    
    console.log('\n📋 **Next Steps:**');
    console.log('1. Upload frontend-deployment.zip to public_html/');
    console.log('2. Upload backend-deployment.zip to public_html/backend/');
    console.log('3. Import database schema');
    console.log('4. Update configuration files');
    console.log('5. Test deployment');
    
    console.log('\n📖 **Documentation:**');
    console.log('- CPANEL_DEPLOYMENT_INSTRUCTIONS.md');
    console.log('- PRODUCTION_DEPLOYMENT_GUIDE.md');
    
    console.log('\n🌐 **Production URLs:**');
    console.log(`- Frontend: https://${CONFIG.domain}`);
    console.log(`- Backend: https://${CONFIG.domain}/backend/`);
    console.log(`- Login: https://${CONFIG.domain}/login`);
}

// Main deployment process
function main() {
    try {
        // Step 1: Validate deployment package
        if (!validateDeployment()) {
            console.log('\n❌ Deployment validation failed. Please run "npm run build:production" first.');
            process.exit(1);
        }
        
        // Step 2: Generate deployment package
        if (!generateDeploymentPackage()) {
            console.log('\n❌ Failed to generate deployment package.');
            process.exit(1);
        }
        
        // Step 3: Generate instructions
        generateDeploymentInstructions();
        
        // Step 4: Show summary
        showDeploymentSummary();
        
        console.log('\n🎉 Deployment package ready for cPanel upload!');
        
    } catch (error) {
        console.error('\n❌ Deployment failed:', error.message);
        process.exit(1);
    }
}

// Run the deployment process
main();
