const fs = require('fs-extra');
const path = require('path');
const archiver = require('archiver');
const { v4: uuidv4 } = require('uuid');
const CodeAnalyzer = require('../analyzers/CodeAnalyzer');

class DeploymentManager {
    constructor() {
        this.codeAnalyzer = new CodeAnalyzer();
        this.outputDir = path.join(__dirname, '../../output');
        this.ensureOutputDirectory();
    }

    async ensureOutputDirectory() {
        try {
            await fs.ensureDir(this.outputDir);
            await fs.ensureDir(path.join(this.outputDir, 'packages'));
            await fs.ensureDir(path.join(this.outputDir, 'documentation'));
            await fs.ensureDir(path.join(this.outputDir, 'history'));
        } catch (error) {
            console.error('Error creating output directories:', error);
        }
    }

    /**
     * Create a deployment package from detected changes
     */
    async createDeployment(projectPath, changes, config) {
        try {
            console.log('Creating deployment package...');
            
            const deploymentId = uuidv4();
            const timestamp = new Date().toISOString();
            const packageName = this.generatePackageName(config.projectName, timestamp);
            
            const deployment = {
                id: deploymentId,
                timestamp,
                projectPath,
                projectName: config.projectName,
                packageName,
                packagePath: path.join(this.outputDir, 'packages', packageName),
                documentationPath: null,
                config,
                changes: changes.changes,
                summary: {
                    totalFiles: changes.totalFiles,
                    changedFiles: changes.changedFiles,
                    addedFiles: changes.addedFiles,
                    modifiedFiles: changes.modifiedFiles,
                    categories: changes.categories
                },
                analysis: null,
                packageSize: 0,
                fileCount: 0,
                status: 'creating'
            };

            // Analyze code changes for feature descriptions
            console.log('Analyzing code changes...');
            deployment.analysis = await this.codeAnalyzer.analyzeChanges(
                changes.changes, 
                projectPath, 
                config
            );

            // Create the deployment package
            console.log('Building deployment package...');
            const packageResult = await this.createPackage(deployment, projectPath);
            
            deployment.packageSize = packageResult.size;
            deployment.fileCount = packageResult.fileCount;
            deployment.status = 'completed';

            // Save deployment history
            await this.saveDeploymentHistory(deployment);

            console.log(`Deployment package created: ${packageName}`);
            return deployment;

        } catch (error) {
            console.error('Error creating deployment:', error);
            throw new Error(`Deployment creation failed: ${error.message}`);
        }
    }

    /**
     * Create the actual ZIP package
     */
    async createPackage(deployment, projectPath) {
        return new Promise((resolve, reject) => {
            try {
                const output = fs.createWriteStream(deployment.packagePath);
                const archive = archiver('zip', {
                    zlib: { level: deployment.config.deployment?.compressionLevel || 6 }
                });

                let fileCount = 0;
                let totalSize = 0;

                output.on('close', () => {
                    resolve({
                        size: archive.pointer(),
                        fileCount: fileCount
                    });
                });

                archive.on('error', (err) => {
                    reject(err);
                });

                archive.on('entry', (entry) => {
                    if (entry.type === 'file') {
                        fileCount++;
                        totalSize += entry.stats.size;
                    }
                });

                archive.pipe(output);

                // Add changed files to the archive
                this.addFilesToArchive(archive, deployment.changes, projectPath, deployment.config);

                // Add deployment metadata
                this.addDeploymentMetadata(archive, deployment);

                archive.finalize();

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Add files to the archive based on changes
     */
    addFilesToArchive(archive, changes, projectPath, config) {
        const serverPath = config.deployment?.serverPath || '/public_html';
        
        changes.forEach(change => {
            if (change.status !== 'deleted') {
                const sourcePath = change.fullPath || path.join(projectPath, change.path);
                
                if (fs.existsSync(sourcePath)) {
                    // Determine target path in the archive
                    const targetPath = this.getTargetPath(change.path, change.category, config);
                    
                    archive.file(sourcePath, { 
                        name: targetPath,
                        stats: fs.statSync(sourcePath)
                    });
                }
            }
        });
    }

    /**
     * Determine the target path for a file in the deployment
     */
    getTargetPath(filePath, category, config) {
        const serverPath = config.deployment?.serverPath || '/public_html';
        
        // Remove leading slash from server path for archive
        const cleanServerPath = serverPath.replace(/^\/+/, '');
        
        // For different project types, handle paths differently
        switch (config.projectType) {
            case 'react-application':
            case 'vue-application':
            case 'angular-application':
                // For SPA applications, files from build/dist go to root
                if (filePath.startsWith('build/') || filePath.startsWith('dist/')) {
                    return path.join(cleanServerPath, filePath.replace(/^(build|dist)\//, ''));
                }
                break;
                
            case 'php-application':
            case 'laravel-application':
                // PHP files go directly to server path
                if (category === 'backend') {
                    return path.join(cleanServerPath, filePath);
                }
                break;
                
            case 'wordpress-site':
                // WordPress files maintain their structure
                return path.join(cleanServerPath, filePath);
                
            default:
                // Default behavior - maintain file structure
                return path.join(cleanServerPath, filePath);
        }
        
        return path.join(cleanServerPath, filePath);
    }

    /**
     * Add deployment metadata to the archive
     */
    addDeploymentMetadata(archive, deployment) {
        const metadata = {
            deployment: {
                id: deployment.id,
                timestamp: deployment.timestamp,
                projectName: deployment.projectName,
                version: '1.0.0'
            },
            summary: deployment.summary,
            analysis: deployment.analysis,
            instructions: this.generateUploadInstructions(deployment)
        };

        archive.append(JSON.stringify(metadata, null, 2), { 
            name: 'deployment-metadata.json' 
        });

        // Add a README file with basic instructions
        const readme = this.generateReadmeContent(deployment);
        archive.append(readme, { name: 'README.txt' });
    }

    /**
     * Generate upload instructions
     */
    generateUploadInstructions(deployment) {
        const serverPath = deployment.config.deployment?.serverPath || '/public_html';
        const domain = deployment.config.deployment?.domain || 'your-domain.com';
        
        return {
            steps: [
                {
                    step: 1,
                    title: 'Access cPanel File Manager',
                    description: `Log into your cPanel account for ${domain}`,
                    action: 'Navigate to File Manager in cPanel'
                },
                {
                    step: 2,
                    title: 'Navigate to Web Root',
                    description: `Go to your web root directory: ${serverPath}`,
                    action: `Click on "${serverPath}" folder`
                },
                {
                    step: 3,
                    title: 'Upload Package',
                    description: 'Upload the deployment ZIP file',
                    action: 'Click "Upload" button and select the ZIP file'
                },
                {
                    step: 4,
                    title: 'Extract Files',
                    description: 'Extract the uploaded ZIP file',
                    action: 'Right-click the ZIP file and select "Extract"'
                },
                {
                    step: 5,
                    title: 'Verify Deployment',
                    description: 'Check that files are in the correct locations',
                    action: `Visit https://${domain} to verify the deployment`
                }
            ],
            notes: [
                'Always backup your existing files before deploying',
                'Check file permissions after extraction (typically 644 for files, 755 for directories)',
                'Clear any caches if your site uses caching',
                'Test all functionality after deployment'
            ]
        };
    }

    /**
     * Generate README content
     */
    generateReadmeContent(deployment) {
        const serverPath = deployment.config.deployment?.serverPath || '/public_html';
        const domain = deployment.config.deployment?.domain || 'your-domain.com';
        
        return `
DEPLOYMENT PACKAGE: ${deployment.packageName}
Generated: ${new Date(deployment.timestamp).toLocaleString()}
Project: ${deployment.projectName}

DEPLOYMENT SUMMARY:
- Total Files: ${deployment.summary.totalFiles}
- Changed Files: ${deployment.summary.changedFiles}
- Added Files: ${deployment.summary.addedFiles}
- Modified Files: ${deployment.summary.modifiedFiles}

FEATURES INCLUDED:
${deployment.analysis?.features?.map(feature => `- ${feature}`).join('\n') || '- General improvements and updates'}

UPLOAD INSTRUCTIONS:
1. Log into cPanel for ${domain}
2. Open File Manager
3. Navigate to ${serverPath}
4. Upload this ZIP file
5. Extract the ZIP file
6. Verify deployment at https://${domain}

IMPORTANT NOTES:
- Backup existing files before deployment
- Check file permissions after extraction
- Clear caches if applicable
- Test functionality after deployment

For detailed instructions, see the HTML deployment guide.
        `.trim();
    }

    /**
     * Generate package name with timestamp
     */
    generatePackageName(projectName, timestamp) {
        const date = new Date(timestamp);
        const dateStr = date.toISOString().replace(/[:.]/g, '-').split('T')[0];
        const timeStr = date.toISOString().replace(/[:.]/g, '-').split('T')[1].split('.')[0];
        
        const safeName = projectName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
        return `${safeName}-deployment-${dateStr}-${timeStr}.zip`;
    }

    /**
     * Save deployment to history
     */
    async saveDeploymentHistory(deployment) {
        try {
            const historyFile = path.join(this.outputDir, 'history', 'deployments.json');
            
            let history = [];
            if (await fs.pathExists(historyFile)) {
                history = await fs.readJson(historyFile);
            }

            // Add current deployment to history
            history.unshift({
                id: deployment.id,
                timestamp: deployment.timestamp,
                projectName: deployment.projectName,
                packageName: deployment.packageName,
                packagePath: deployment.packagePath,
                summary: deployment.summary,
                analysis: deployment.analysis
            });

            // Keep only last 50 deployments
            history = history.slice(0, 50);

            await fs.writeJson(historyFile, history, { spaces: 2 });
            
        } catch (error) {
            console.error('Error saving deployment history:', error);
        }
    }

    /**
     * Get deployment history
     */
    async getDeploymentHistory() {
        try {
            const historyFile = path.join(this.outputDir, 'history', 'deployments.json');
            
            if (await fs.pathExists(historyFile)) {
                return await fs.readJson(historyFile);
            }
            
            return [];
        } catch (error) {
            console.error('Error loading deployment history:', error);
            return [];
        }
    }

    /**
     * Create rollback package from previous deployment
     */
    async createRollback(deploymentId) {
        try {
            const history = await this.getDeploymentHistory();
            const deployment = history.find(d => d.id === deploymentId);
            
            if (!deployment) {
                throw new Error('Deployment not found in history');
            }

            // Create rollback package (implementation would depend on backup strategy)
            console.log(`Creating rollback package for deployment ${deploymentId}`);
            
            // This would typically involve:
            // 1. Finding the previous state of files
            // 2. Creating a package to restore that state
            // 3. Generating rollback instructions
            
            return {
                success: true,
                message: 'Rollback package created',
                packagePath: `rollback-${deploymentId}.zip`
            };
            
        } catch (error) {
            console.error('Error creating rollback:', error);
            throw error;
        }
    }

    /**
     * Clean up old deployment packages
     */
    async cleanupOldPackages(keepCount = 10) {
        try {
            const packagesDir = path.join(this.outputDir, 'packages');
            const files = await fs.readdir(packagesDir);
            
            // Sort by creation time (newest first)
            const fileStats = await Promise.all(
                files.map(async file => {
                    const filePath = path.join(packagesDir, file);
                    const stats = await fs.stat(filePath);
                    return { file, path: filePath, created: stats.birthtime };
                })
            );
            
            fileStats.sort((a, b) => b.created - a.created);
            
            // Remove old packages
            const toDelete = fileStats.slice(keepCount);
            for (const item of toDelete) {
                await fs.remove(item.path);
                console.log(`Cleaned up old package: ${item.file}`);
            }
            
        } catch (error) {
            console.error('Error cleaning up old packages:', error);
        }
    }
}

module.exports = DeploymentManager;
