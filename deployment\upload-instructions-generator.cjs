#!/usr/bin/env node

/**
 * EskillVisor Upload Instructions Generator
 * Creates detailed, step-by-step upload instructions with exact file paths
 */

const fs = require('fs');
const path = require('path');
const DynamicChangeAnalyzer = require('./dynamic-change-analyzer.cjs');

class UploadInstructionsGenerator {
    constructor() {
        this.sourceDir = process.cwd();
        this.timestamp = new Date().toISOString();
    }

    /**
     * Generate comprehensive upload instructions
     */
    generateInstructions(packageInfo, changes, manifest) {
        console.log('📋 Generating Comprehensive Upload Instructions');
        console.log('=' .repeat(50));

        try {
            // Initialize dynamic change analyzer
            const analyzer = new DynamicChangeAnalyzer(this.sourceDir);
            const dynamicChanges = analyzer.analyzeChanges();
            const migrationInfo = analyzer.generateMigrationInfo();
            // Generate HTML instructions
            const htmlInstructions = this.generateHTMLInstructions(packageInfo, changes, manifest, dynamicChanges, migrationInfo);
            const htmlPath = path.join(this.sourceDir, 'deployment-upload-instructions.html');
            fs.writeFileSync(htmlPath, htmlInstructions);
            console.log(`✅ Created: deployment-upload-instructions.html`);

            // Generate text instructions
            const textInstructions = this.generateTextInstructions(packageInfo, changes, manifest, dynamicChanges, migrationInfo);
            const textPath = path.join(this.sourceDir, 'deployment-upload-instructions.txt');
            fs.writeFileSync(textPath, textInstructions);
            console.log(`✅ Created: deployment-upload-instructions.txt`);

            // Generate deployment summary
            const summary = this.generateDeploymentSummary(packageInfo, changes, manifest);
            const summaryPath = path.join(this.sourceDir, 'deployment-summary.json');
            fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
            console.log(`✅ Created: deployment-summary.json`);

            return {
                htmlPath,
                textPath,
                summaryPath,
                summary
            };

        } catch (error) {
            throw new Error(`Instructions generation failed: ${error.message}`);
        }
    }

    /**
     * Generate HTML instructions
     */
    generateHTMLInstructions(packageInfo, changes, manifest, dynamicChanges, migrationInfo) {
        const zipFileName = packageInfo.zipPath ? path.basename(packageInfo.zipPath) : 'No ZIP created';
        const absoluteZipPath = packageInfo.zipPath || 'N/A';
        const absolutePackagePath = path.resolve(packageInfo.packagePath);

        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EskillVisor Enhanced Dashboards - Deployment Instructions</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; line-height: 1.6; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center; }
        .section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 10px; border-left: 5px solid #007bff; }
        .success { background: #d4edda; border-color: #28a745; }
        .warning { background: #fff3cd; border-color: #ffc107; }
        .info { background: #d1ecf1; border-color: #17a2b8; }
        .code { background: #f1f3f4; padding: 15px; border-radius: 8px; font-family: 'Courier New', monospace; font-size: 14px; overflow-x: auto; }
        .path { background: #e9ecef; padding: 8px 12px; border-radius: 5px; font-family: monospace; font-weight: bold; color: #495057; }
        .step { margin: 15px 0; padding: 15px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .step h4 { margin-top: 0; color: #007bff; }
        .btn { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 6px; margin: 8px 4px; font-weight: bold; }
        .btn:hover { background: #0056b3; }
        .changes-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0; }
        .change-card { background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
        .change-card.added { border-color: #28a745; }
        .change-card.modified { border-color: #ffc107; }
        .change-card.deleted { border-color: #dc3545; }
        .file-list { max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; }
        .highlight { background: #fff3cd; padding: 2px 6px; border-radius: 3px; font-weight: bold; }
        .deployment-breakdown { display: grid; grid-template-columns: 1fr; gap: 15px; margin-top: 15px; }
        .deployment-section { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 3px solid #007bff; }
        .deployment-section h4 { margin-top: 0; color: #007bff; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 EskillVisor Enhanced Dashboards</h1>
        <h2>Deployment Instructions</h2>
        <p>Beautiful dashboards with business intelligence and analytics ready for production</p>
        <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
    </div>

    <div class="section success">
        <h3>✅ Deployment Package Ready</h3>
        <p>Your enhanced dashboards with beautiful designs, gradient cards, and analytics APIs are packaged and ready for deployment to production!</p>
        <div class="changes-grid">
            <div class="change-card">
                <h4>📊 Total Changes</h4>
                <p><strong>${changes.summary.totalChanges}</strong> files changed</p>
                <p>Deployment Type: <span class="highlight">${changes.summary.deploymentType}</span></p>
            </div>
            <div class="change-card added">
                <h4>➕ Added Files</h4>
                <p><strong>${changes.summary.added}</strong> new files</p>
            </div>
            <div class="change-card modified">
                <h4>📝 Modified Files</h4>
                <p><strong>${changes.summary.modified}</strong> updated files</p>
            </div>
        </div>
    </div>

    <div class="section info">
        <h3>📦 Package Information</h3>
        <div class="step">
            <h4>📁 Source Location (Your Computer)</h4>
            <div class="path">${absolutePackagePath}</div>
            <p>This directory contains all the files ready for upload to your server.</p>
        </div>
        <div class="step">
            <h4>📦 ZIP Package (Recommended Upload Method)</h4>
            <div class="path">${absoluteZipPath}</div>
            <p>Upload this ZIP file to cPanel for quick deployment.</p>
        </div>
        <div class="step">
            <h4>🎯 Target Location (cPanel Server)</h4>
            <div class="path">/home9/wallistry/eskillvisor.wallistry.pk/</div>
            <p>This is where files should be uploaded on your cPanel server.</p>
        </div>
    </div>

    <div class="section">
        <h3>🚀 Upload Methods</h3>
        
        <div class="step">
            <h4>Method 1: ZIP Upload (Recommended)</h4>
            <ol>
                <li><strong>Login to cPanel:</strong> Access your hosting provider's cPanel</li>
                <li><strong>Open File Manager:</strong> Click on "File Manager" in cPanel</li>
                <li><strong>Navigate to Target:</strong> Go to <code>/home9/wallistry/eskillvisor.wallistry.pk/</code></li>
                <li><strong>Upload ZIP:</strong> Upload the file <code>${zipFileName}</code></li>
                <li><strong>Extract ZIP:</strong> Right-click the ZIP file and select "Extract"</li>
                <li><strong>Overwrite Files:</strong> Choose "Yes" when prompted to overwrite existing files</li>
                <li><strong>Delete ZIP:</strong> Remove the ZIP file after extraction</li>
            </ol>
        </div>

        <div class="step">
            <h4>Method 2: Direct File Upload</h4>
            <ol>
                <li><strong>Login to cPanel:</strong> Access your hosting provider's cPanel</li>
                <li><strong>Open File Manager:</strong> Click on "File Manager" in cPanel</li>
                <li><strong>Navigate to Target:</strong> Go to <code>/home9/wallistry/eskillvisor.wallistry.pk/</code></li>
                <li><strong>Select All Files:</strong> From your local directory: <code>${absolutePackagePath}</code></li>
                <li><strong>Upload Files:</strong> Drag and drop or use the upload button</li>
                <li><strong>Maintain Structure:</strong> Ensure the <code>api/</code> and <code>assets/</code> directories are preserved</li>
                <li><strong>Overwrite Files:</strong> Replace existing files when prompted</li>
            </ol>
        </div>
    </div>

    <div class="section warning">
        <h3>⚠️ Important Notes</h3>
        <ul>
            <li><strong>Backup:</strong> Consider backing up your current website before deployment</li>
            <li><strong>Overwrite:</strong> This deployment will replace existing files with enhanced versions</li>
            <li><strong>Directory Structure:</strong> Maintain the exact directory structure during upload</li>
            <li><strong>File Permissions:</strong> cPanel will set appropriate permissions automatically</li>
        </ul>
    </div>

    <div class="section">
        <h3>🚀 Features Being Deployed</h3>
        <div class="changes-grid">
            ${this.generateDynamicFeatureCards(dynamicChanges)}
        </div>
    </div>

    <div class="section">
        <h3>🔍 Verification Steps</h3>
        <div class="step">
            <h4>After Upload, Test These URLs:</h4>
            <ol>
                <li><strong>Main Website:</strong> <a href="https://eskillvisor.wallistry.pk" target="_blank">https://eskillvisor.wallistry.pk</a></li>
                <li><strong>API Test:</strong> <a href="https://eskillvisor.wallistry.pk/api/test" target="_blank">https://eskillvisor.wallistry.pk/api/test</a></li>
                <li><strong>Login Page:</strong> <a href="https://eskillvisor.wallistry.pk/login" target="_blank">https://eskillvisor.wallistry.pk/login</a></li>
                <li><strong>Pending Users API:</strong> <a href="https://eskillvisor.wallistry.pk/api/users/pending" target="_blank">https://eskillvisor.wallistry.pk/api/users/pending</a></li>
                <li><strong>Pending Companies API:</strong> <a href="https://eskillvisor.wallistry.pk/api/companies/pending" target="_blank">https://eskillvisor.wallistry.pk/api/companies/pending</a></li>
            </ol>
        </div>
        <div class="step">
            <h4>Test Approval Workflow Features:</h4>
            <ol>
                <li>Login as Super Admin and check User Management approval tabs</li>
                <li>Login as Manager and verify Partner dashboard is read-only</li>
                <li>Test company creation with document upload functionality</li>
                <li>Verify approval workflow modals and status indicators</li>
                <li>Check that Partners cannot see Upload/Update buttons</li>
            </ol>
        </div>
    </div>

    <div class="section success">
        <h3>🎉 Deployment Complete</h3>
        <p>Once uploaded, your approval workflow system will be live with:</p>
        <ul>
            <li>🔐 Role-Based Access Control Implementation</li>
            <li>✅ Super Admin Approval Workflow for Users & Companies</li>
            <li>📋 Partner Role Restrictions (Read-Only Inventory Access)</li>
            <li>📄 Document Upload & Verification System for Companies</li>
            <li>🎯 Enhanced User Management with Approval Tabs</li>
            <li>🏢 Company Oversight with Approval Status Tracking</li>
            <li>📊 Approval History & Audit Trail System</li>
            <li>🔄 Manager-Created Entities Require Super Admin Approval</li>
            <li>📱 Beautiful Approval Modal Interfaces</li>
            <li>🚫 Removed Upload/Update Buttons from Partner Dashboard</li>
        </ul>
        <div style="text-align: center; margin-top: 30px;">
            <a href="https://eskillvisor.wallistry.pk" class="btn" target="_blank">🌐 Visit Enhanced Website</a>
            <a href="https://eskillvisor.wallistry.pk/api/test" class="btn" target="_blank">🔧 Test API</a>
        </div>
    </div>

    ${this.generateDynamicMigrationSection(migrationInfo)}

    <div class="section info">
        <h3>📦 What's Being Deployed Where</h3>
        <div class="deployment-breakdown">
            <div class="deployment-section">
                <h4>🌐 Frontend Files (cPanel File Manager)</h4>
                <ul>
                    <li><strong>index.html</strong> - Updated with approval workflow UI</li>
                    <li><strong>assets/</strong> - New JavaScript and CSS with approval components</li>
                    <li><strong>.htaccess</strong> - URL rewrite rules for React Router (fixes 404 on reload)</li>
                    <li>Location: Root directory of eskillvisor.wallistry.pk</li>
                </ul>
            </div>

            <div class="deployment-section">
                <h4>🔧 Backend Files (cPanel File Manager)</h4>
                <ul>
                    <li><strong>api/controllers/UserController.php</strong> - Added approval methods</li>
                    <li><strong>api/controllers/CompanyController.php</strong> - Added approval methods</li>
                    <li><strong>api/index.php</strong> - New approval endpoints</li>
                    <li>Location: api/ directory</li>
                </ul>
            </div>

            <div class="deployment-section">
                <h4>🗄️ Database Changes (phpMyAdmin)</h4>
                <ul>
                    <li><strong>Migration file:</strong> backend/migrations/012_add_approval_workflow.sql</li>
                    <li><strong>Action required:</strong> Manual execution in phpMyAdmin</li>
                    <li><strong>⚠️ Critical:</strong> Must be run after file upload</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section">
        <h3>🔗 New API Endpoints</h3>
        <p>The following new endpoints are now available:</p>
        <ul>
            <li><strong>GET /api/users/pending</strong> - Get pending users for approval</li>
            <li><strong>POST /api/users/{id}/approve</strong> - Approve a pending user</li>
            <li><strong>POST /api/users/{id}/reject</strong> - Reject a pending user</li>
            <li><strong>GET /api/companies/pending</strong> - Get pending companies for approval</li>
            <li><strong>POST /api/companies/{id}/approve</strong> - Approve a pending company</li>
            <li><strong>POST /api/companies/{id}/reject</strong> - Reject a pending company</li>
        </ul>
    </div>

    <div class="section info">
        <h3>📞 Support Information</h3>
        <p>If you encounter any issues during deployment:</p>
        <ul>
            <li>Check that all files were uploaded to the correct directory</li>
            <li>Verify file permissions are set correctly (cPanel handles this automatically)</li>
            <li>Clear your browser cache and try accessing the website again</li>
            <li>Check the API test endpoint to ensure backend functionality</li>
        </ul>
    </div>
</body>
</html>`;
    }

    /**
     * Generate text instructions
     */
    generateTextInstructions(packageInfo, changes, manifest, dynamicChanges, migrationInfo) {
        const zipFileName = packageInfo.zipPath ? path.basename(packageInfo.zipPath) : 'No ZIP created';
        const absoluteZipPath = packageInfo.zipPath || 'N/A';
        const absolutePackagePath = path.resolve(packageInfo.packagePath);

        return `
ESKILLVISOR ENHANCED DASHBOARDS - DEPLOYMENT INSTRUCTIONS
=========================================================

Generated: ${new Date().toLocaleString()}
Deployment Type: ${changes.summary.deploymentType}
Total Changes: ${changes.summary.totalChanges} files

PACKAGE INFORMATION
==================
Source Directory: ${absolutePackagePath}
ZIP Package: ${absoluteZipPath}
Target Server: /home9/wallistry/eskillvisor.wallistry.pk/
Website URL: https://eskillvisor.wallistry.pk

UPLOAD INSTRUCTIONS
==================

METHOD 1: ZIP UPLOAD (RECOMMENDED)
----------------------------------
1. Login to your cPanel at your hosting provider
2. Open "File Manager" in cPanel
3. Navigate to: /home9/wallistry/eskillvisor.wallistry.pk/
4. Upload the ZIP file: ${zipFileName}
5. Right-click the ZIP file and select "Extract"
6. Choose "Yes" to overwrite existing files
7. Delete the ZIP file after extraction

METHOD 2: DIRECT FILE UPLOAD
----------------------------
1. Login to your cPanel at your hosting provider
2. Open "File Manager" in cPanel
3. Navigate to: /home9/wallistry/eskillvisor.wallistry.pk/
4. Select all files from: ${absolutePackagePath}
5. Upload all files maintaining directory structure
6. Ensure api/ and assets/ directories are preserved
7. Overwrite existing files when prompted

${this.generateDynamicFeaturesText(dynamicChanges)}

⚠️ CRITICAL: DATABASE MIGRATION REQUIRED
========================================
After uploading files, you MUST run the database migration!

📍 Location: backend/migrations/012_add_approval_workflow.sql

How to run:
1. Go to cPanel → phpMyAdmin
2. Select your database
3. Click "SQL" tab
4. Copy and paste the contents of backend/migrations/012_add_approval_workflow.sql
5. Click "Go" to execute

📦 DEPLOYMENT BREAKDOWN
======================
🌐 Frontend Files (cPanel File Manager):
   - index.html (Updated with approval workflow UI)
   - assets/ (New JavaScript and CSS with approval components)
   - .htaccess (URL rewrite rules for React Router - fixes 404 on reload)
   - Location: Root directory of eskillvisor.wallistry.pk

🔧 Backend Files (cPanel File Manager):
   - api/controllers/UserController.php (Added approval methods)
   - api/controllers/CompanyController.php (Added approval methods)
   - api/index.php (New approval endpoints)
   - Location: api/ directory

🗄️ Database Changes (phpMyAdmin):
   - Migration file: backend/migrations/012_add_approval_workflow.sql
   - Action required: Manual execution in phpMyAdmin
   - ⚠️ Critical: Must be run after file upload

VERIFICATION STEPS
=================
After upload, test these URLs:
- Main Website: https://eskillvisor.wallistry.pk
- API Test: https://eskillvisor.wallistry.pk/api/test
- Login Page: https://eskillvisor.wallistry.pk/login
- Pending Users API: https://eskillvisor.wallistry.pk/api/users/pending
- Pending Companies API: https://eskillvisor.wallistry.pk/api/companies/pending

Test Approval Workflow Features:
1. Login as Super Admin and check User Management approval tabs
2. Login as Manager and verify Partner dashboard is read-only
3. Test company creation with document upload functionality
4. Verify approval workflow modals and status indicators
5. Check that Partners cannot see Upload/Update buttons

IMPORTANT NOTES
==============
- This deployment will replace existing files with approval workflow features
- Maintain exact directory structure during upload
- cPanel will set appropriate file permissions automatically
- Consider backing up current website before deployment
- ⚠️ Database migration is REQUIRED for approval workflow to function

DEPLOYMENT COMPLETE
==================
Your approval workflow system will be live with role-based access control,
document verification, and comprehensive approval management!
`;
    }

    /**
     * Generate deployment summary
     */
    generateDeploymentSummary(packageInfo, changes, manifest) {
        return {
            timestamp: this.timestamp,
            deployment: {
                type: changes.summary.deploymentType,
                totalChanges: changes.summary.totalChanges,
                added: changes.summary.added,
                modified: changes.summary.modified,
                deleted: changes.summary.deleted,
                deploymentRequired: changes.deploymentRequired
            },
            package: {
                sourceDirectory: path.resolve(packageInfo.packagePath),
                zipFile: packageInfo.zipPath,
                totalFiles: manifest.packageInfo.totalFiles,
                totalSize: this.formatFileSize(manifest.packageInfo.totalSize)
            },
            target: {
                website: 'https://eskillvisor.wallistry.pk',
                serverPath: '/home9/wallistry/eskillvisor.wallistry.pk/',
                method: 'cPanel File Manager'
            },
            features: [
                '🔐 Role-Based Access Control Implementation',
                '✅ Super Admin Approval Workflow for Users & Companies',
                '📋 Partner Role Restrictions (Read-Only Inventory Access)',
                '📄 Document Upload & Verification System for Companies',
                '🎯 Enhanced User Management with Approval Tabs',
                '🏢 Company Oversight with Approval Status Tracking',
                '📊 Approval History & Audit Trail System',
                '🔄 Manager-Created Entities Require Super Admin Approval',
                '📱 Beautiful Approval Modal Interfaces',
                '🚫 Removed Upload/Update Buttons from Partner Dashboard'
            ],
            databaseChanges: [
                'Added approval_status, created_by, approved_by fields to users table',
                'Added approval_status, approved_by, rejection_reason fields to companies table',
                'Created company_documents table for document verification',
                'Created approval_history table for audit trail'
            ],
            newEndpoints: [
                'GET /api/users/pending - Get pending users for approval',
                'POST /api/users/{id}/approve - Approve a pending user',
                'POST /api/users/{id}/reject - Reject a pending user',
                'GET /api/companies/pending - Get pending companies for approval',
                'POST /api/companies/{id}/approve - Approve a pending company',
                'POST /api/companies/{id}/reject - Reject a pending company'
            ],
            verification: {
                urls: [
                    'https://eskillvisor.wallistry.pk',
                    'https://eskillvisor.wallistry.pk/api/test',
                    'https://eskillvisor.wallistry.pk/login',
                    'https://eskillvisor.wallistry.pk/api/users/pending',
                    'https://eskillvisor.wallistry.pk/api/companies/pending'
                ]
            }
        };
    }

    /**
     * Generate dynamic feature cards for HTML
     */
    generateDynamicFeatureCards(dynamicChanges) {
        if (!dynamicChanges.features || dynamicChanges.features.length === 0) {
            return `
            <div class="change-card">
                <h4>📦 System Updates</h4>
                <p>Various system improvements and enhancements</p>
            </div>`;
        }

        return dynamicChanges.features.map(feature => `
            <div class="change-card">
                <h4>✅ ${feature.description}</h4>
                <p>Files updated: ${feature.files.join(', ')}</p>
                ${feature.details.length > 0 ? `<p class="text-sm text-gray-600">Key changes: ${feature.details.map(d => d.match).join(', ')}</p>` : ''}
            </div>
        `).join('');
    }

    /**
     * Generate dynamic migration section for HTML
     */
    generateDynamicMigrationSection(migrationInfo) {
        if (!migrationInfo || migrationInfo.length === 0) {
            return `
            <div class="section info">
                <h3>ℹ️ No Database Migrations Required</h3>
                <p>This deployment does not require any database changes.</p>
            </div>`;
        }

        const latestMigration = migrationInfo[0];

        return `
        <div class="section warning">
            <h3>🗄️ CRITICAL: Database Migration Required</h3>
            <p><strong>⚠️ IMPORTANT:</strong> After uploading files, you MUST run the database migration!</p>

            <div class="step">
                <h4>📍 Location: ${latestMigration.path}</h4>
                <p><strong>Description:</strong> ${latestMigration.description}</p>
                <p><strong>How to run:</strong></p>
                <ol>
                    <li>Go to cPanel → phpMyAdmin</li>
                    <li>Select your database</li>
                    <li>Click "SQL" tab</li>
                    <li>Copy and paste the contents of <code>${latestMigration.path}</code></li>
                    <li>Click "Go" to execute</li>
                </ol>

                <h4>📋 What this migration adds:</h4>
                <ul>
                    ${latestMigration.changes.map(change => `<li>✅ ${change}</li>`).join('')}
                </ul>
            </div>
        </div>`;
    }

    /**
     * Generate dynamic features text for text version
     */
    generateDynamicFeaturesText(dynamicChanges) {
        if (!dynamicChanges.features || dynamicChanges.features.length === 0) {
            return `SYSTEM UPDATES BEING DEPLOYED
=====================================
📦 Various system improvements and enhancements`;
        }

        const title = this.generateDynamicTitle(dynamicChanges);
        const features = dynamicChanges.features.map(feature =>
            `✅ ${feature.description}`
        ).join('\n');

        return `${title}
${'='.repeat(title.length)}
${features}`;
    }

    /**
     * Generate dynamic title based on detected changes
     */
    generateDynamicTitle(dynamicChanges) {
        if (dynamicChanges.frontend.length > 0 && dynamicChanges.backend.length > 0) {
            return 'FULL-STACK ENHANCEMENTS BEING DEPLOYED';
        } else if (dynamicChanges.frontend.length > 0) {
            return 'FRONTEND ENHANCEMENTS BEING DEPLOYED';
        } else if (dynamicChanges.backend.length > 0) {
            return 'BACKEND IMPROVEMENTS BEING DEPLOYED';
        } else if (dynamicChanges.database.length > 0) {
            return 'DATABASE UPDATES BEING DEPLOYED';
        } else {
            return 'SYSTEM UPDATES BEING DEPLOYED';
        }
    }

    /**
     * Format file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
}

module.exports = UploadInstructionsGenerator;
