-- Add extended fields to users table for enhanced user management
ALTER TABLE users 
ADD COLUMN first_name <PERSON><PERSON><PERSON><PERSON>(255) NULL AFTER name,
ADD COLUMN last_name <PERSON><PERSON><PERSON><PERSON>(255) NULL AFTER first_name,
ADD COLUMN mobile VARCHAR(20) NULL AFTER email,
ADD COLUMN address TEXT NULL AFTER mobile,
ADD COLUMN business_model VARCHAR(255) NULL AFTER address,
ADD COLUMN cnic_document_path VARCHAR(500) NULL AFTER business_model,
ADD INDEX idx_mobile (mobile),
ADD INDEX idx_business_model (business_model);

-- Update existing users to split name into first_name and last_name
UPDATE users 
SET 
    first_name = SUBSTRING_INDEX(name, ' ', 1),
    last_name = CASE 
        WHEN LOCATE(' ', name) > 0 THEN SUBSTRING(name, LOCATE(' ', name) + 1)
        ELSE ''
    END
WHERE first_name IS NULL OR last_name IS NULL;

-- <PERSON>reate uploads directory structure (Note: This should be done via file system)
-- mkdir -p uploads/documents/
-- chmod 755 uploads/documents/
