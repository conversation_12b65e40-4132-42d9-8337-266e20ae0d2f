#!/usr/bin/env node

/**
 * EskillVisor Deployment CLI
 * Main interface for the deployment workflow system
 */

const fs = require('fs');
const path = require('path');
const { program } = require('commander');

const ChangeDetector = require('./change-detector.cjs');
const DecisionEngine = require('./decision-engine.cjs');
const DeploymentExecutor = require('./executor.cjs');

class DeploymentCLI {
    constructor() {
        this.detector = new ChangeDetector();
        this.engine = new DecisionEngine();
        this.executor = new DeploymentExecutor();
    }

    /**
     * Initialize CLI commands
     */
    initializeCLI() {
        program
            .name('eskillvisor-deploy')
            .description('EskillVisor Deployment Workflow System')
            .version('1.0.0');

        // Detect changes command
        program
            .command('detect')
            .description('Detect changes since last deployment')
            .option('-f, --format <type>', 'Output format (json|table)', 'table')
            .action(async (options) => {
                await this.detectChanges(options);
            });

        // Analyze command
        program
            .command('analyze')
            .description('Analyze changes and generate deployment plan')
            .option('-f, --format <type>', 'Output format (json|table)', 'table')
            .option('-s, --save <file>', 'Save plan to file')
            .action(async (options) => {
                await this.analyzeChanges(options);
            });

        // Deploy command
        program
            .command('deploy')
            .description('Execute deployment')
            .option('-m, --mode <type>', 'Deployment mode (manual|semi-automatic|fully-automatic)', 'semi-automatic')
            .option('-y, --yes', 'Skip confirmation prompts')
            .option('-p, --plan <file>', 'Use existing deployment plan file')
            .action(async (options) => {
                await this.executeDeploy(options);
            });

        // Status command
        program
            .command('status')
            .description('Show deployment status and history')
            .option('-l, --limit <number>', 'Limit number of deployments to show', '10')
            .action(async (options) => {
                await this.showStatus(options);
            });

        // Rollback command
        program
            .command('rollback')
            .description('Rollback to previous deployment')
            .option('-t, --target <id>', 'Target deployment ID')
            .option('-y, --yes', 'Skip confirmation prompts')
            .action(async (options) => {
                await this.executeRollback(options);
            });

        // Init command
        program
            .command('init')
            .description('Initialize deployment system')
            .action(async () => {
                await this.initializeDeployment();
            });

        return program;
    }

    /**
     * Detect changes command
     */
    async detectChanges(options) {
        try {
            console.log('🔍 Detecting changes...\n');
            const changes = await this.detector.detectChanges();

            if (options.format === 'json') {
                console.log(JSON.stringify(changes, null, 2));
            } else {
                this.displayChangesTable(changes);
            }

        } catch (error) {
            console.error('❌ Error detecting changes:', error.message);
            process.exit(1);
        }
    }

    /**
     * Analyze changes command
     */
    async analyzeChanges(options) {
        try {
            console.log('🔍 Detecting changes...');
            const changes = await this.detector.detectChanges();
            
            console.log('📊 Analyzing deployment requirements...\n');
            const plan = this.engine.analyzeChanges(changes);

            if (options.format === 'json') {
                console.log(JSON.stringify(plan, null, 2));
            } else {
                this.displayPlanTable(plan);
            }

            if (options.save) {
                fs.writeFileSync(options.save, JSON.stringify(plan, null, 2));
                console.log(`\n💾 Plan saved to: ${options.save}`);
            }

        } catch (error) {
            console.error('❌ Error analyzing changes:', error.message);
            process.exit(1);
        }
    }

    /**
     * Execute deployment command
     */
    async executeDeploy(options) {
        try {
            let plan;

            if (options.plan) {
                console.log(`📋 Loading deployment plan from: ${options.plan}`);
                plan = JSON.parse(fs.readFileSync(options.plan, 'utf8'));
            } else {
                console.log('🔍 Detecting changes...');
                const changes = await this.detector.detectChanges();
                
                console.log('📊 Generating deployment plan...');
                plan = this.engine.analyzeChanges(changes);
            }

            if (plan.summary.totalChanges === 0) {
                console.log('✅ No changes detected. Deployment not required.');
                return;
            }

            console.log('🚀 Executing deployment...\n');
            const execution = await this.executor.executePlan(plan, {
                mode: options.mode,
                requireConfirmation: !options.yes
            });

            this.displayExecutionResult(execution);

            // Save deployment state if successful
            if (execution.status === 'completed') {
                await this.detector.saveDeploymentState();
                console.log('💾 Deployment state saved.');
            }

        } catch (error) {
            console.error('❌ Deployment failed:', error.message);
            process.exit(1);
        }
    }

    /**
     * Show status command
     */
    async showStatus(options) {
        try {
            console.log('📊 Deployment Status\n');
            
            // Show current changes
            const changes = await this.detector.detectChanges();
            console.log(`Current Changes: ${changes.summary.totalChanges} files`);
            console.log(`Last Deployment: ${changes.lastDeployment}`);
            console.log(`Deployment Required: ${changes.summary.requiresDeployment ? 'Yes' : 'No'}`);
            
            if (changes.summary.requiresDeployment) {
                console.log(`Recommended Type: ${changes.summary.deploymentType}`);
            }

            // Show deployment history
            console.log('\n📜 Recent Deployments:');
            this.showDeploymentHistory(parseInt(options.limit));

        } catch (error) {
            console.error('❌ Error showing status:', error.message);
            process.exit(1);
        }
    }

    /**
     * Execute rollback command
     */
    async executeRollback(options) {
        try {
            console.log('⏪ Rollback functionality');
            console.log('This would execute rollback procedures.');
            console.log('Implementation depends on backup and versioning strategy.');
            
            if (!options.yes) {
                console.log('Use --yes to confirm rollback execution.');
            }

        } catch (error) {
            console.error('❌ Rollback failed:', error.message);
            process.exit(1);
        }
    }

    /**
     * Initialize deployment system
     */
    async initializeDeployment() {
        try {
            console.log('🚀 Initializing EskillVisor Deployment System...\n');

            // Create directory structure
            const directories = [
                'deployment/logs',
                'deployment/backups',
                'deployment/plans'
            ];

            directories.forEach(dir => {
                if (!fs.existsSync(dir)) {
                    fs.mkdirSync(dir, { recursive: true });
                    console.log(`✅ Created directory: ${dir}`);
                }
            });

            // Initialize deployment state
            await this.detector.saveDeploymentState();
            console.log('✅ Initial deployment state saved');

            // Create package.json entry for commander if needed
            this.ensurePackageJsonDependencies();

            console.log('\n🎉 Deployment system initialized successfully!');
            console.log('\nAvailable commands:');
            console.log('  npm run deploy:detect   - Detect changes');
            console.log('  npm run deploy:analyze  - Analyze and plan');
            console.log('  npm run deploy:execute  - Execute deployment');
            console.log('  npm run deploy:status   - Show status');

        } catch (error) {
            console.error('❌ Initialization failed:', error.message);
            process.exit(1);
        }
    }

    /**
     * Display changes in table format
     */
    displayChangesTable(changes) {
        console.log('📋 Change Detection Report');
        console.log('='.repeat(50));
        console.log(`Last Deployment: ${changes.lastDeployment}`);
        console.log(`Total Changes: ${changes.summary.totalChanges}`);
        console.log(`Deployment Required: ${changes.summary.requiresDeployment ? 'Yes' : 'No'}`);
        
        if (changes.summary.requiresDeployment) {
            console.log(`Recommended Type: ${changes.summary.deploymentType}`);
        }

        console.log('\nChanges by Category:');
        Object.entries(changes.summary.categories).forEach(([category, stats]) => {
            if (stats.total > 0) {
                console.log(`  ${category.toUpperCase()}:`);
                console.log(`    Added: ${stats.added}, Modified: ${stats.modified}, Deleted: ${stats.deleted}`);
            }
        });

        // Show detailed file changes
        Object.entries(changes.changes).forEach(([category, categoryChanges]) => {
            const totalChanges = categoryChanges.added.length + categoryChanges.modified.length + categoryChanges.deleted.length;
            if (totalChanges > 0) {
                console.log(`\n${category.toUpperCase()} Changes:`);
                
                if (categoryChanges.added.length > 0) {
                    console.log('  Added:');
                    categoryChanges.added.forEach(file => console.log(`    + ${file.path}`));
                }
                
                if (categoryChanges.modified.length > 0) {
                    console.log('  Modified:');
                    categoryChanges.modified.forEach(file => console.log(`    ~ ${file.path}`));
                }
                
                if (categoryChanges.deleted.length > 0) {
                    console.log('  Deleted:');
                    categoryChanges.deleted.forEach(file => console.log(`    - ${file.path}`));
                }
            }
        });
    }

    /**
     * Display deployment plan in table format
     */
    displayPlanTable(plan) {
        console.log('📋 Deployment Plan');
        console.log('='.repeat(50));
        console.log(`Type: ${plan.recommendations.deploymentType}`);
        console.log(`Estimated Time: ${plan.estimatedTime} minutes`);
        console.log(`Total Steps: ${plan.deploymentSteps.length}`);

        if (plan.risks.length > 0) {
            console.log('\n⚠️  Risks:');
            plan.risks.forEach(risk => {
                console.log(`  ${risk.level.toUpperCase()}: ${risk.description}`);
            });
        }

        console.log('\n📝 Deployment Steps:');
        plan.deploymentSteps.forEach(step => {
            console.log(`  ${step.step}. ${step.description} (${step.estimatedTime}min)`);
            console.log(`     Target: ${step.target}`);
        });

        if (plan.recommendations.warnings.length > 0) {
            console.log('\n⚠️  Warnings:');
            plan.recommendations.warnings.forEach(warning => {
                console.log(`  ${warning.type}: ${warning.message}`);
            });
        }
    }

    /**
     * Display execution result
     */
    displayExecutionResult(execution) {
        console.log('\n📊 Deployment Execution Result');
        console.log('='.repeat(50));
        console.log(`Plan ID: ${execution.planId}`);
        console.log(`Status: ${execution.status.toUpperCase()}`);
        console.log(`Mode: ${execution.mode}`);
        console.log(`Started: ${execution.timestamp}`);

        if (execution.steps.length > 0) {
            console.log('\n📝 Executed Steps:');
            execution.steps.forEach(step => {
                const status = step.status === 'completed' ? '✅' : 
                              step.status === 'failed' ? '❌' : '⏳';
                console.log(`  ${status} Step ${step.step}: ${step.type}`);
            });
        }

        if (execution.errors.length > 0) {
            console.log('\n❌ Errors:');
            execution.errors.forEach(error => {
                console.log(`  ${error.message}`);
            });
        }

        if (execution.warnings.length > 0) {
            console.log('\n⚠️  Warnings:');
            execution.warnings.forEach(warning => {
                console.log(`  ${warning.message}`);
            });
        }
    }

    /**
     * Show deployment history
     */
    showDeploymentHistory(limit) {
        const logsDir = 'deployment/logs';
        if (!fs.existsSync(logsDir)) {
            console.log('  No deployment history found.');
            return;
        }

        const logFiles = fs.readdirSync(logsDir)
            .filter(file => file.startsWith('execution_') && file.endsWith('.json'))
            .sort()
            .reverse()
            .slice(0, limit);

        if (logFiles.length === 0) {
            console.log('  No deployment history found.');
            return;
        }

        logFiles.forEach(file => {
            try {
                const execution = JSON.parse(fs.readFileSync(path.join(logsDir, file), 'utf8'));
                const status = execution.status === 'completed' ? '✅' : 
                              execution.status === 'failed' ? '❌' : '⏳';
                console.log(`  ${status} ${execution.planId} - ${execution.timestamp} (${execution.mode})`);
            } catch (error) {
                console.log(`  ❓ ${file} - Could not read`);
            }
        });
    }

    /**
     * Ensure package.json has required dependencies
     */
    ensurePackageJsonDependencies() {
        try {
            const packagePath = 'package.json';
            if (fs.existsSync(packagePath)) {
                const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
                
                if (!packageJson.scripts) {
                    packageJson.scripts = {};
                }

                // Add deployment scripts
                packageJson.scripts['deploy:detect'] = 'node deployment/deploy.cjs detect';
                packageJson.scripts['deploy:analyze'] = 'node deployment/deploy.cjs analyze';
                packageJson.scripts['deploy:execute'] = 'node deployment/deploy.cjs deploy';
                packageJson.scripts['deploy:status'] = 'node deployment/deploy.cjs status';

                fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
                console.log('✅ Added deployment scripts to package.json');
            }
        } catch (error) {
            console.log('⚠️  Could not update package.json:', error.message);
        }
    }
}

// CLI execution
if (require.main === module) {
    const cli = new DeploymentCLI();
    const program = cli.initializeCLI();
    program.parse();
}

module.exports = DeploymentCLI;
