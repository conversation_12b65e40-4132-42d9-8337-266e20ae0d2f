<?php
/**
 * Production Configuration for Investment System
 * This file contains production-specific settings
 */

// Production Environment Settings
define('ENVIRONMENT', 'production');
define('DEBUG_MODE', false);
define('DISPLAY_ERRORS', false);

// Production Domain Configuration
define('FRONTEND_URL', 'https://eskillvisor.wallistry.pk');
define('BACKEND_URL', 'https://eskillvisor.wallistry.pk/backend');

// Production CORS Settings
define('ALLOWED_ORIGINS', [
    'https://eskillvisor.wallistry.pk',
    'https://www.eskillvisor.wallistry.pk'
]);

// Production Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'wallistry_investment_system');
define('DB_USER', 'wallistry_investment_user');
define('DB_PASS', 'your_production_password_here'); // Update this with actual password

// Production Security Settings
define('JWT_SECRET', 'your_production_jwt_secret_key_here'); // Update this with actual secret
define('ENCRYPTION_KEY', 'your_production_encryption_key_here'); // Update this with actual key

// Production File Upload Settings
define('UPLOAD_MAX_SIZE', 10 * 1024 * 1024); // 10MB
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('ALLOWED_FILE_TYPES', ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'txt']);

// Production Email Settings (if needed)
define('SMTP_HOST', 'your_smtp_host');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', 'your_smtp_username');
define('SMTP_PASSWORD', 'your_smtp_password');

// Production Logging Settings
define('LOG_ERRORS', true);
define('LOG_PATH', __DIR__ . '/../logs/');

// Production Session Settings
define('SESSION_LIFETIME', 3600); // 1 hour
define('SESSION_SECURE', true); // HTTPS only
define('SESSION_HTTPONLY', true);

// Production Rate Limiting
define('RATE_LIMIT_REQUESTS', 100);
define('RATE_LIMIT_WINDOW', 3600); // 1 hour

// Production Cache Settings
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // 1 hour

// Error Reporting for Production
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', LOG_PATH . 'php_errors.log');

// Set timezone
date_default_timezone_set('UTC');

// Production Headers
function setProductionHeaders() {
    // Security headers
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    // CORS headers for production
    $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
    if (in_array($origin, ALLOWED_ORIGINS)) {
        header('Access-Control-Allow-Origin: ' . $origin);
    }
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
    header('Access-Control-Allow-Credentials: true');
    header('Access-Control-Max-Age: 86400');
}

// Auto-set production headers
setProductionHeaders();
?>
