{"deployment": {"project_name": "EskillVisor", "environment": "production", "auto_deploy_enabled": true, "verify_deployment": true, "create_backups": true, "rollback_on_failure": true}, "targets": {"frontend": {"enabled": true, "local_path": "dist/", "remote_path": "/home9/wallistry/eskillvisor.wallistry.pk/", "build_command": "npm run build", "file_permissions": "644", "directory_permissions": "755", "exclude_patterns": ["*.map", "*.log", ".DS_Store", "Thumbs.db"]}, "backend": {"enabled": true, "local_path": "backend/", "remote_path": "/home9/wallistry/eskillvisor.wallistry.pk/api/", "file_permissions": "644", "directory_permissions": "755", "exclude_patterns": ["config.local.php", "*.log", "uploads/*", ".env.local", "temp/*"]}}, "connection": {"protocol": "ftp", "host": "${CPANEL_HOST}", "port": "${CPANEL_PORT}", "username": "${CPANEL_USERNAME}", "password": "${CPANEL_PASSWORD}", "secure": false, "timeout": 60000, "retry_attempts": 3, "retry_delay": 5000, "keep_alive": 30000, "passive_mode": false}, "verification": {"endpoints": [{"url": "https://eskillvisor.wallistry.pk/", "expected_status": 200, "timeout": 10000}, {"url": "https://eskillvisor.wallistry.pk/api/test", "expected_status": 200, "expected_content": "success", "timeout": 10000}], "max_verification_attempts": 3, "verification_delay": 5000}, "backup": {"enabled": true, "local_backup_path": "deployment/backups/", "remote_backup_path": "/home9/wallistry/backups/", "max_backups": 5, "backup_before_deploy": true}, "notifications": {"enabled": false, "webhook_url": "", "email_notifications": false, "slack_webhook": ""}, "performance": {"parallel_uploads": true, "max_concurrent_uploads": 5, "chunk_size": 1024, "compression_enabled": true}}