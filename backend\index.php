<?php
/**
 * EskillVisor Investment System API
 * Main entry point for the PHP backend
 */

// Enable CORS for frontend communication
$allowedOrigins = [
    'https://eskillvisor.wallistry.pk',
    'https://wallistry.pk',
    'https://www.wallistry.pk',
    'https://inventory-system-e-skill-visor.vercel.app',
    'http://localhost:5173',
    'http://localhost:3000'
];

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, $allowedOrigins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    header('Access-Control-Allow-Origin: https://eskillvisor.wallistry.pk');
}

header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Set content type to JSON
header('Content-Type: application/json');

// Error reporting for production
error_reporting(0);
ini_set('display_errors', 0);

// Include autoloader and configuration
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/core/Response.php';
require_once __DIR__ . '/core/Controller.php';
require_once __DIR__ . '/controllers/AnalyticsController.php';
require_once __DIR__ . '/models/User.php';
require_once __DIR__ . '/models/Company.php';
require_once __DIR__ . '/models/InventoryItem.php';

try {
    // Get the request path
    $requestUri = $_SERVER['REQUEST_URI'] ?? '';
    $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';

    // Remove script name from request URI to get the path
    $path = str_replace(dirname($scriptName), '', $requestUri);
    $path = trim($path, '/');

    // Remove query string
    if (strpos($path, '?') !== false) {
        $path = substr($path, 0, strpos($path, '?'));
    }

    // Handle test route
    if ($path === 'test' || $path === 'api/test' || $path === '') {
        Response::success([
            'message' => 'API endpoint is working!',
            'timestamp' => date('c'),
            'path' => $path,
            'uri' => $requestUri,
            'script' => $scriptName
        ]);
        exit;
    }

    // Handle analytics routes
    if (strpos($path, 'api/analytics/') === 0) {
        $analyticsController = new AnalyticsController();
        $route = str_replace('api/analytics/', '', $path);

        switch ($route) {
            case 'dashboard':
                $analyticsController->dashboard();
                break;
            case 'inventory-stats':
                $analyticsController->inventoryStats();
                break;
            case 'company-stats':
                $analyticsController->companyStats();
                break;
            case 'trends':
                $analyticsController->trends();
                break;
            default:
                Response::error('Analytics endpoint not found', 404);
        }
        exit;
    }

    // Handle companies routes
    if (strpos($path, 'api/companies/') === 0) {
        require_once __DIR__ . '/controllers/CompanyController.php';
        $companyController = new CompanyController();
        $route = str_replace('api/companies/', '', $path);

        if ($route === 'assigned') {
            // Handle assigned companies for partners
            $companyController->getAssignedCompanies();
            exit;
        } elseif ($route === 'pending') {
            // Handle pending companies for approval
            $companyController->pending();
            exit;
        } elseif (preg_match('/^(\d+)\/approve$/', $route, $matches)) {
            // Handle company approval
            $companyController->approve(['id' => $matches[1]]);
            exit;
        } elseif (preg_match('/^(\d+)\/reject$/', $route, $matches)) {
            // Handle company rejection
            $companyController->reject(['id' => $matches[1]]);
            exit;
        }
    }

    // Handle users routes
    if (strpos($path, 'api/users') === 0) {
        require_once __DIR__ . '/controllers/UserController.php';
        $userController = new UserController();

        if ($path === 'api/users' && $_SERVER['REQUEST_METHOD'] === 'GET') {
            // Handle get all users
            $userController->index();
            exit;
        } elseif ($path === 'api/users' && $_SERVER['REQUEST_METHOD'] === 'POST') {
            // Handle create user
            try {
                error_log("API: Creating user via UserController");
                $userController->store();
            } catch (Exception $e) {
                error_log("API: Error in user creation: " . $e->getMessage());
                Response::error('User creation failed: ' . $e->getMessage(), 500);
            }
            exit;
        }

        $route = str_replace('api/users/', '', $path);

        if ($route === 'pending') {
            // Handle pending users for approval
            $userController->pending();
            exit;
        } elseif (preg_match('/^(\d+)\/approve$/', $route, $matches)) {
            // Handle user approval
            $userController->approve(['id' => $matches[1]]);
            exit;
        } elseif (preg_match('/^(\d+)\/reject$/', $route, $matches)) {
            // Handle user rejection
            $userController->reject(['id' => $matches[1]]);
            exit;
        }
    }

    // For now, return a simple response for any other route
    Response::success([
        'message' => 'EskillVisor API is running!',
        'timestamp' => date('c'),
        'requested_path' => $path,
        'available_endpoints' => [
            '/test' => 'API test endpoint',
            '/api/analytics/dashboard' => 'Dashboard analytics',
            '/api/analytics/trends' => 'Trends data',
            '/api/companies/assigned' => 'Assigned companies',
            '/api/companies/pending' => 'Pending companies for approval',
            '/api/companies/{id}/approve' => 'Approve company',
            '/api/companies/{id}/reject' => 'Reject company',
            '/api/users/pending' => 'Pending users for approval',
            '/api/users/{id}/approve' => 'Approve user',
            '/api/users/{id}/reject' => 'Reject user',
            '/login.php' => 'User authentication',
            '/companies.php' => 'Company management',
            '/users.php' => 'User management'
        ]
    ]);

} catch (Exception $e) {
    Response::error('Internal server error: ' . $e->getMessage(), 500);
}
