@echo off
echo Pushing EskillVisor with User Management Updates to GitHub...

echo.
echo Adding all changes to Git...
git add .

echo.
echo Committing changes...
git commit -m "feat: Complete user management system with separated lists and profile modals

- Add UserProfileModal component with 3-tab interface (Profile, Companies, Inventory)
- Implement separated Manager and Partner lists in UserManagement
- Add clickable user rows that open profile modals
- Enhance AddCompanyModal with new required fields:
  * Company Director (dropdown from partners)
  * Registration Territory (country selection)
  * EIN Number
  * Marketplace (Amazon, Walmart, TikTok, Shopify, Others)
  * Custom marketplace field for 'Others' option
- Update CompanyController with new field handling and approval workflow
- Update UserController with getProfile endpoint for user profiles
- Extend Company model with new methods and field support
- Add database migration 014_add_company_extended_fields.sql
- Implement role-based company creation (Add Company vs Assign Company)
- Add comprehensive form validation and error handling
- Update CompanyOversight and CompanyManagement components
- Include production deployment packages and documentation

Backend Changes:
- Enhanced CompanyController with new field validation
- Added user profile API endpoint in UserController
- Extended Company model with getUserCompanies method
- Database migration for company extended fields and approval workflow

Frontend Changes:
- Separated Manager/Partner tabs with individual filtering
- User profile modal system with comprehensive data display
- Enhanced company creation with all required fields
- Improved search and filtering capabilities
- Role-based access control and button visibility

Deployment:
- Complete production deployment package ready
- Database migration included
- Updated API endpoints functional
- Frontend source code with all updates"

echo.
echo Pushing to GitHub...
git push origin main

echo.
echo ✅ Successfully pushed to GitHub!
echo.
echo Repository: https://github.com/ihsansaif313/Inventory-System-eSkillVisor.git
echo.
echo You can now clone this repository on your second laptop:
echo git clone https://github.com/ihsansaif313/Inventory-System-eSkillVisor.git
echo.
echo After cloning, run:
echo cd Inventory-System-eSkillVisor
echo npm install
echo npm run build
echo.
pause
