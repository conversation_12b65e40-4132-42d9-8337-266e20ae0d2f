<?php
/**
 * EskillVisor Database Migration System
 * Handles database schema changes and data migrations
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';

class MigrationManager {
    private $db;
    private $migrationsPath;
    private $migrationsTable = 'migrations';

    public function __construct() {
        $this->db = Database::getInstance();
        $this->migrationsPath = __DIR__;
        $this->ensureMigrationsTable();
    }

    /**
     * Ensure migrations table exists
     */
    private function ensureMigrationsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS {$this->migrationsTable} (
            id INT AUTO_INCREMENT PRIMARY KEY,
            migration VARCHAR(255) NOT NULL UNIQUE,
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            execution_time_ms INT DEFAULT 0,
            checksum VARCHAR(32) NOT NULL,
            INDEX idx_migration (migration),
            INDEX idx_executed_at (executed_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        $this->db->execute($sql);
    }

    /**
     * Get list of available migrations
     */
    public function getAvailableMigrations() {
        $migrations = [];
        $files = glob($this->migrationsPath . '/*.sql');
        
        foreach ($files as $file) {
            $filename = basename($file);
            if (preg_match('/^(\d{4}_\d{2}_\d{2}_\d{6})_(.+)\.sql$/', $filename, $matches)) {
                $migrations[] = [
                    'file' => $filename,
                    'timestamp' => $matches[1],
                    'name' => $matches[2],
                    'path' => $file,
                    'checksum' => md5_file($file)
                ];
            }
        }

        // Sort by timestamp
        usort($migrations, function($a, $b) {
            return strcmp($a['timestamp'], $b['timestamp']);
        });

        return $migrations;
    }

    /**
     * Get executed migrations
     */
    public function getExecutedMigrations() {
        $sql = "SELECT migration, executed_at, execution_time_ms, checksum 
                FROM {$this->migrationsTable} 
                ORDER BY executed_at";
        
        return $this->db->fetchAll($sql);
    }

    /**
     * Get pending migrations
     */
    public function getPendingMigrations() {
        $available = $this->getAvailableMigrations();
        $executed = $this->getExecutedMigrations();
        
        $executedMap = [];
        foreach ($executed as $migration) {
            $executedMap[$migration['migration']] = $migration;
        }

        $pending = [];
        foreach ($available as $migration) {
            if (!isset($executedMap[$migration['file']])) {
                $pending[] = $migration;
            } else {
                // Check if file has been modified
                $executedMigration = $executedMap[$migration['file']];
                if ($executedMigration['checksum'] !== $migration['checksum']) {
                    $migration['status'] = 'modified';
                    $migration['warning'] = 'Migration file has been modified after execution';
                    $pending[] = $migration;
                }
            }
        }

        return $pending;
    }

    /**
     * Execute a single migration
     */
    public function executeMigration($migration) {
        $startTime = microtime(true);
        
        try {
            echo "Executing migration: {$migration['file']}\n";
            
            // Read migration file
            $sql = file_get_contents($migration['path']);
            if ($sql === false) {
                throw new Exception("Could not read migration file: {$migration['path']}");
            }

            // Begin transaction
            $this->db->beginTransaction();

            // Execute migration SQL
            $statements = $this->splitSqlStatements($sql);
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $this->db->execute($statement);
                }
            }

            // Record migration execution
            $executionTime = round((microtime(true) - $startTime) * 1000);
            $this->recordMigrationExecution($migration, $executionTime);

            // Commit transaction
            $this->db->commit();

            echo "✅ Migration completed in {$executionTime}ms\n";
            return true;

        } catch (Exception $e) {
            // Rollback transaction
            $this->db->rollback();
            echo "❌ Migration failed: " . $e->getMessage() . "\n";
            throw $e;
        }
    }

    /**
     * Execute all pending migrations
     */
    public function executeAllPending() {
        $pending = $this->getPendingMigrations();
        
        if (empty($pending)) {
            echo "✅ No pending migrations\n";
            return true;
        }

        echo "Found " . count($pending) . " pending migrations\n";
        
        foreach ($pending as $migration) {
            if (isset($migration['warning'])) {
                echo "⚠️  Warning: {$migration['warning']}\n";
                echo "Continue? (y/N): ";
                $handle = fopen("php://stdin", "r");
                $line = fgets($handle);
                fclose($handle);
                
                if (trim(strtolower($line)) !== 'y') {
                    echo "Skipping migration: {$migration['file']}\n";
                    continue;
                }
            }

            $this->executeMigration($migration);
        }

        echo "✅ All migrations completed\n";
        return true;
    }

    /**
     * Create a new migration file
     */
    public function createMigration($name) {
        $timestamp = date('Y_m_d_His');
        $filename = "{$timestamp}_{$name}.sql";
        $filepath = $this->migrationsPath . '/' . $filename;

        $template = "-- Migration: {$name}\n";
        $template .= "-- Created: " . date('Y-m-d H:i:s') . "\n";
        $template .= "-- Description: Add description here\n\n";
        $template .= "-- Add your SQL statements here\n";
        $template .= "-- Example:\n";
        $template .= "-- ALTER TABLE users ADD COLUMN new_field VARCHAR(255);\n";
        $template .= "-- CREATE INDEX idx_new_field ON users(new_field);\n\n";

        if (file_put_contents($filepath, $template) !== false) {
            echo "✅ Created migration: {$filename}\n";
            echo "Edit the file to add your SQL statements\n";
            return $filepath;
        } else {
            throw new Exception("Could not create migration file: {$filepath}");
        }
    }

    /**
     * Show migration status
     */
    public function showStatus() {
        $available = $this->getAvailableMigrations();
        $executed = $this->getExecutedMigrations();
        $pending = $this->getPendingMigrations();

        echo "📊 Migration Status\n";
        echo "==================\n";
        echo "Available migrations: " . count($available) . "\n";
        echo "Executed migrations: " . count($executed) . "\n";
        echo "Pending migrations: " . count($pending) . "\n\n";

        if (!empty($pending)) {
            echo "📋 Pending Migrations:\n";
            foreach ($pending as $migration) {
                $status = isset($migration['warning']) ? '⚠️ ' : '📄 ';
                echo "  {$status}{$migration['file']} - {$migration['name']}\n";
                if (isset($migration['warning'])) {
                    echo "     Warning: {$migration['warning']}\n";
                }
            }
            echo "\n";
        }

        if (!empty($executed)) {
            echo "✅ Recent Executed Migrations:\n";
            $recent = array_slice(array_reverse($executed), 0, 5);
            foreach ($recent as $migration) {
                echo "  {$migration['migration']} - {$migration['executed_at']} ({$migration['execution_time_ms']}ms)\n";
            }
        }
    }

    /**
     * Record migration execution
     */
    private function recordMigrationExecution($migration, $executionTime) {
        $sql = "INSERT INTO {$this->migrationsTable} 
                (migration, execution_time_ms, checksum) 
                VALUES (?, ?, ?)";
        
        $this->db->execute($sql, [
            $migration['file'],
            $executionTime,
            $migration['checksum']
        ]);
    }

    /**
     * Split SQL into individual statements
     */
    private function splitSqlStatements($sql) {
        // Remove comments
        $sql = preg_replace('/--.*$/m', '', $sql);
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);
        
        // Split by semicolon (simple approach)
        $statements = explode(';', $sql);
        
        return array_filter($statements, function($statement) {
            return !empty(trim($statement));
        });
    }
}

// CLI usage
if (php_sapi_name() === 'cli') {
    $manager = new MigrationManager();
    
    $command = $argv[1] ?? 'status';
    
    try {
        switch ($command) {
            case 'status':
                $manager->showStatus();
                break;
                
            case 'execute':
            case 'migrate':
                $manager->executeAllPending();
                break;
                
            case 'create':
                $name = $argv[2] ?? null;
                if (!$name) {
                    echo "Usage: php migrate.php create <migration_name>\n";
                    exit(1);
                }
                $manager->createMigration($name);
                break;
                
            default:
                echo "Usage: php migrate.php <command>\n";
                echo "Commands:\n";
                echo "  status   - Show migration status\n";
                echo "  execute  - Execute all pending migrations\n";
                echo "  create   - Create a new migration\n";
                exit(1);
        }
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
        exit(1);
    }
}
?>
