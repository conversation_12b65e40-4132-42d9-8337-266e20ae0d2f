#!/usr/bin/env node

/**
 * EskillVisor HTTP-based Deployment Module
 * Alternative to FTP for reliable automated deployment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class HTTPDeployer {
    constructor(config, credentials) {
        this.config = config;
        this.credentials = credentials;
    }

    /**
     * Deploy using HTTP upload methods
     */
    async deploy() {
        try {
            console.log('🌐 Starting HTTP-based deployment...');
            
            // Method 1: Try cPanel File Manager API if available
            const success = await this.tryMultipleMethods();
            
            if (success) {
                console.log('✅ HTTP deployment completed successfully!');
                return true;
            } else {
                console.log('⚠️ HTTP deployment methods not available');
                return false;
            }
            
        } catch (error) {
            console.log(`❌ HTTP deployment failed: ${error.message}`);
            return false;
        }
    }

    /**
     * Try multiple deployment methods
     */
    async tryMultipleMethods() {
        const methods = [
            () => this.tryRsync(),
            () => this.tryScp(),
            () => this.tryGitDeploy(),
            () => this.createZipUpload()
        ];

        for (const method of methods) {
            try {
                const result = await method();
                if (result) return true;
            } catch (error) {
                console.log(`   Method failed: ${error.message}`);
                continue;
            }
        }

        return false;
    }

    /**
     * Try rsync if available
     */
    async tryRsync() {
        try {
            console.log('   🔄 Trying rsync deployment...');
            
            const rsyncCommand = `rsync -avz --delete cpanel-deployment/public_html/ ${this.credentials.CPANEL_USERNAME}@${this.credentials.CPANEL_HOST}:/home9/wallistry/eskillvisor.wallistry.pk/`;
            
            execSync(rsyncCommand, { stdio: 'pipe' });
            console.log('   ✅ Rsync deployment successful');
            return true;
            
        } catch (error) {
            console.log('   ⚠️ Rsync not available or failed');
            return false;
        }
    }

    /**
     * Try SCP if available
     */
    async tryScp() {
        try {
            console.log('   🔄 Trying SCP deployment...');
            
            const scpCommand = `scp -r cpanel-deployment/public_html/* ${this.credentials.CPANEL_USERNAME}@${this.credentials.CPANEL_HOST}:/home9/wallistry/eskillvisor.wallistry.pk/`;
            
            execSync(scpCommand, { stdio: 'pipe' });
            console.log('   ✅ SCP deployment successful');
            return true;
            
        } catch (error) {
            console.log('   ⚠️ SCP not available or failed');
            return false;
        }
    }

    /**
     * Try Git-based deployment
     */
    async tryGitDeploy() {
        try {
            console.log('   🔄 Trying Git deployment...');
            
            // This would require Git hooks on the server
            // For now, just return false
            return false;
            
        } catch (error) {
            return false;
        }
    }

    /**
     * Create ZIP file for manual upload
     */
    async createZipUpload() {
        try {
            console.log('   📦 Creating optimized deployment ZIP...');
            
            const zipFile = 'deployment-package.zip';
            const zipCommand = `cd cpanel-deployment && zip -r ../${zipFile} public_html/`;
            
            execSync(zipCommand, { stdio: 'pipe' });
            
            if (fs.existsSync(zipFile)) {
                const stats = fs.statSync(zipFile);
                console.log(`   ✅ Created ${zipFile} (${this.formatFileSize(stats.size)})`);
                console.log('   📋 Upload this ZIP file to cPanel and extract it');
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.log('   ⚠️ ZIP creation failed');
            return false;
        }
    }

    /**
     * Format file size for display
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
}

module.exports = HTTPDeployer;
