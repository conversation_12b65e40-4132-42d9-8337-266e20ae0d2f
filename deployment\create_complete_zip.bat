@echo off
echo Creating complete deployment ZIP package...

:: Create directory structure
mkdir zip_package\src\components\modals 2>nul
mkdir zip_package\src\pages\superadmin 2>nul
mkdir zip_package\src\pages\manager 2>nul
mkdir zip_package\backend\controllers 2>nul
mkdir zip_package\backend\models 2>nul
mkdir zip_package\backend\migrations 2>nul

:: Copy frontend files
echo Copying frontend files...
copy "files\src\components\modals\UserProfileModal.jsx" "zip_package\src\components\modals\"
copy "files\src\pages\superadmin\UserManagement.jsx" "zip_package\src\pages\superadmin\"
copy "files\src\components\modals\AddCompanyModal.jsx" "zip_package\src\components\modals\"
copy "files\src\pages\superadmin\CompanyOversight.jsx" "zip_package\src\pages\superadmin\"
copy "files\src\pages\manager\CompanyManagement.jsx" "zip_package\src\pages\manager\"

:: Copy backend files
echo Copying backend files...
copy "files\backend\controllers\CompanyController.php" "zip_package\backend\controllers\"
copy "files\backend\controllers\UserController.php" "zip_package\backend\controllers\"
copy "files\backend\models\Company.php" "zip_package\backend\models\"

:: Copy database migration
echo Copying database migration...
copy "database\014_add_company_extended_fields.sql" "zip_package\backend\migrations\"

:: Create ZIP file
echo Creating ZIP file...
powershell "Compress-Archive -Path 'zip_package\*' -DestinationPath 'user-management-updates-2025-07-18.zip' -Force"

:: Clean up
echo Cleaning up...
rmdir /s /q zip_package

echo.
echo ✅ ZIP package created successfully!
echo 📦 File: user-management-updates-2025-07-18.zip
echo 📁 Location: deployment/user-management-updates-2025-07-18.zip
echo.
echo Ready for upload to cPanel at: /home9/wallistry/eskillvisor.wallistry.pk/
echo.
pause
