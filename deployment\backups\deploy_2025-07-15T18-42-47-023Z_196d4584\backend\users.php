<?php
// Enable CORS for custom domain
$allowedOrigins = [
    'https://eskillvisor.wallistry.pk',
    'https://wallistry.pk',
    'https://www.wallistry.pk',
    'https://inventory-system-e-skill-visor.vercel.app',
    'http://localhost:5173',
    'http://localhost:3000'
];

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, $allowedOrigins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    header('Access-Control-Allow-Origin: https://eskillvisor.wallistry.pk');
}

header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

try {
    $db = Database::getInstance();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get all users
        $users = $db->fetchAll("SELECT id, name, email, role, status, created_at, updated_at FROM users ORDER BY name");
        
        echo json_encode([
            'success' => true,
            'data' => $users
        ]);
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Create new user
        $input = json_decode(file_get_contents('php://input'), true);
        
        $name = $input['name'] ?? '';
        $email = $input['email'] ?? '';
        $password = $input['password'] ?? '';
        $role = $input['role'] ?? '';
        
        if (empty($name) || empty($email) || empty($password) || empty($role)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Name, email, password, and role are required']);
            exit();
        }
        
        // Check if email already exists
        $existingUser = $db->fetchOne("SELECT id FROM users WHERE email = ?", [$email]);
        if ($existingUser) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Email already exists']);
            exit();
        }
        
        // Hash password
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        $result = $db->execute(
            "INSERT INTO users (name, email, password, role, status, created_at, updated_at) VALUES (?, ?, ?, ?, 'active', NOW(), NOW())",
            [$name, $email, $hashedPassword, $role]
        );
        
        if ($result) {
            $userId = $db->getLastInsertId();
            $user = $db->fetchOne("SELECT id, name, email, role, status, created_at, updated_at FROM users WHERE id = ?", [$userId]);
            
            echo json_encode([
                'success' => true,
                'data' => $user,
                'message' => 'User created successfully'
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to create user']);
        }
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
