<?php
/**
 * Debug Company Creation
 */

// Enable CORS for local development
$allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:3000',
    'http://127.0.0.1:5173',
    'http://127.0.0.1:3000'
];

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, $allowedOrigins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    header('Access-Control-Allow-Origin: http://localhost:5173');
}

header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

try {
    $db = Database::getInstance();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        echo json_encode([
            'debug' => true,
            'input_received' => $input,
            'step' => 'Input validation',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
        // Test simple company creation
        $name = $input['name'] ?? 'Debug Test Company';
        $companyDirector = $input['companyDirector'] ?? 'Debug Director';
        
        $db->beginTransaction();
        
        try {
            // Simple insert without foreign keys first
            $result = $db->execute(
                "INSERT INTO companies (name, company_director, description, status, created_at, updated_at) VALUES (?, ?, ?, 'active', NOW(), NOW())",
                [$name, $companyDirector, 'Debug test company']
            );
            
            if (!$result) {
                throw new Exception('Failed to create company');
            }
            
            $companyId = $db->lastInsertId();
            
            // Get the created company
            $company = $db->fetch(
                "SELECT * FROM companies WHERE id = ?",
                [$companyId]
            );
            
            $db->commit();
            
            echo json_encode([
                'success' => true,
                'debug' => true,
                'company_id' => $companyId,
                'insert_result' => $result,
                'company_data' => $company[0] ?? null,
                'message' => 'Debug company created successfully'
            ]);
            
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        
    } else {
        echo json_encode([
            'debug' => true,
            'message' => 'Debug endpoint ready',
            'method' => $_SERVER['REQUEST_METHOD']
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'debug' => true,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>
