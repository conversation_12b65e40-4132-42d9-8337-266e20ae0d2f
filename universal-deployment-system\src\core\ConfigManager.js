const fs = require('fs-extra');
const path = require('path');

class ConfigManager {
    constructor() {
        this.defaultConfig = {
            projectName: '',
            projectType: 'web-application',
            deployment: {
                target: 'cpanel',
                domain: '',
                username: '',
                serverPath: '/public_html',
                ftpHost: '',
                ftpPort: 21
            },
            fileCategories: {
                frontend: {
                    extensions: ['.html', '.css', '.js', '.jsx', '.ts', '.tsx', '.vue', '.scss', '.sass', '.less'],
                    directories: ['src', 'public', 'dist', 'build', 'assets', 'static'],
                    description: 'Frontend application files'
                },
                backend: {
                    extensions: ['.php', '.py', '.rb', '.java', '.cs', '.go', '.rs'],
                    directories: ['api', 'server', 'backend', 'app', 'controllers', 'models', 'routes'],
                    description: 'Backend application logic'
                },
                database: {
                    extensions: ['.sql', '.db', '.sqlite', '.mdb'],
                    directories: ['migrations', 'seeds', 'database', 'sql'],
                    description: 'Database scripts and migrations'
                },
                config: {
                    extensions: ['.json', '.yaml', '.yml', '.toml', '.ini', '.env', '.config'],
                    directories: ['config', 'configs', 'settings'],
                    description: 'Configuration files'
                },
                assets: {
                    extensions: ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.webp', '.pdf', '.doc', '.docx'],
                    directories: ['images', 'img', 'media', 'uploads', 'files', 'documents'],
                    description: 'Static assets and media files'
                }
            },
            excludePatterns: [
                'node_modules/**',
                '.git/**',
                '.vscode/**',
                '.idea/**',
                '*.log',
                '.env',
                '.env.local',
                'dist/**',
                'build/**',
                'coverage/**',
                '.nyc_output/**',
                'tmp/**',
                'temp/**'
            ],
            changeDetection: {
                method: 'content-hash', // 'content-hash', 'timestamp', 'size'
                remoteCheck: true,
                timeout: 30000
            },
            deployment: {
                createBackup: true,
                generateDocs: true,
                openDocsAfterDeploy: true,
                compressionLevel: 6
            }
        };
    }

    /**
     * Load project configuration from file or create default
     */
    async loadProjectConfig(projectPath) {
        try {
            const configPath = path.join(projectPath, '.deployment-config.json');
            
            if (await fs.pathExists(configPath)) {
                const config = await fs.readJson(configPath);
                return this.mergeWithDefaults(config);
            } else {
                // Create default config based on project structure
                const detectedConfig = await this.detectProjectType(projectPath);
                return this.mergeWithDefaults(detectedConfig);
            }
        } catch (error) {
            console.error('Error loading project config:', error);
            return this.defaultConfig;
        }
    }

    /**
     * Save project configuration to file
     */
    async saveProjectConfig(projectPath, config) {
        try {
            const configPath = path.join(projectPath, '.deployment-config.json');
            await fs.writeJson(configPath, config, { spaces: 2 });
            return { success: true, path: configPath };
        } catch (error) {
            console.error('Error saving project config:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Detect project type based on files and structure
     */
    async detectProjectType(projectPath) {
        const config = { ...this.defaultConfig };
        
        try {
            // Check for package.json (Node.js/React/Vue/Angular)
            if (await fs.pathExists(path.join(projectPath, 'package.json'))) {
                const packageJson = await fs.readJson(path.join(projectPath, 'package.json'));
                config.projectName = packageJson.name || path.basename(projectPath);
                
                if (packageJson.dependencies) {
                    if (packageJson.dependencies.react) {
                        config.projectType = 'react-application';
                    } else if (packageJson.dependencies.vue) {
                        config.projectType = 'vue-application';
                    } else if (packageJson.dependencies['@angular/core']) {
                        config.projectType = 'angular-application';
                    } else if (packageJson.dependencies.express) {
                        config.projectType = 'node-application';
                    }
                }
            }
            
            // Check for PHP files
            else if (await this.hasFilesWithExtension(projectPath, '.php')) {
                config.projectType = 'php-application';
                
                // Check for specific PHP frameworks
                if (await fs.pathExists(path.join(projectPath, 'artisan'))) {
                    config.projectType = 'laravel-application';
                } else if (await fs.pathExists(path.join(projectPath, 'wp-config.php'))) {
                    config.projectType = 'wordpress-site';
                }
            }
            
            // Check for Python files
            else if (await this.hasFilesWithExtension(projectPath, '.py')) {
                config.projectType = 'python-application';
                
                if (await fs.pathExists(path.join(projectPath, 'manage.py'))) {
                    config.projectType = 'django-application';
                } else if (await fs.pathExists(path.join(projectPath, 'app.py'))) {
                    config.projectType = 'flask-application';
                }
            }
            
            // Static website
            else if (await this.hasFilesWithExtension(projectPath, '.html')) {
                config.projectType = 'static-website';
            }
            
            // Set project name if not set
            if (!config.projectName) {
                config.projectName = path.basename(projectPath);
            }
            
        } catch (error) {
            console.error('Error detecting project type:', error);
        }
        
        return config;
    }

    /**
     * Check if directory has files with specific extension
     */
    async hasFilesWithExtension(dirPath, extension) {
        try {
            const files = await fs.readdir(dirPath);
            return files.some(file => path.extname(file) === extension);
        } catch (error) {
            return false;
        }
    }

    /**
     * Merge user config with defaults
     */
    mergeWithDefaults(userConfig) {
        return {
            ...this.defaultConfig,
            ...userConfig,
            fileCategories: {
                ...this.defaultConfig.fileCategories,
                ...userConfig.fileCategories
            },
            deployment: {
                ...this.defaultConfig.deployment,
                ...userConfig.deployment
            },
            changeDetection: {
                ...this.defaultConfig.changeDetection,
                ...userConfig.changeDetection
            }
        };
    }

    /**
     * Get project templates for different types
     */
    getProjectTemplates() {
        return {
            'react-application': {
                name: 'React Application',
                buildCommand: 'npm run build',
                buildOutput: 'build',
                serverPath: '/public_html'
            },
            'vue-application': {
                name: 'Vue Application',
                buildCommand: 'npm run build',
                buildOutput: 'dist',
                serverPath: '/public_html'
            },
            'angular-application': {
                name: 'Angular Application',
                buildCommand: 'ng build --prod',
                buildOutput: 'dist',
                serverPath: '/public_html'
            },
            'php-application': {
                name: 'PHP Application',
                serverPath: '/public_html'
            },
            'laravel-application': {
                name: 'Laravel Application',
                serverPath: '/public_html',
                excludePatterns: ['vendor/**', 'storage/logs/**', 'bootstrap/cache/**']
            },
            'wordpress-site': {
                name: 'WordPress Site',
                serverPath: '/public_html',
                excludePatterns: ['wp-content/uploads/**', 'wp-content/cache/**']
            },
            'static-website': {
                name: 'Static Website',
                serverPath: '/public_html'
            }
        };
    }

    /**
     * Validate configuration
     */
    validateConfig(config) {
        const errors = [];
        
        if (!config.projectName) {
            errors.push('Project name is required');
        }
        
        if (!config.deployment.domain) {
            errors.push('Deployment domain is required');
        }
        
        if (!config.deployment.serverPath) {
            errors.push('Server path is required');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
}

module.exports = ConfigManager;
