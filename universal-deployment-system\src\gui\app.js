// Universal Deployment System - Main Application Logic
const { ipcRenderer } = require('electron');

class DeploymentApp {
    constructor() {
        this.currentProject = null;
        this.currentConfig = null;
        this.lastChanges = null;
        this.lastDeployment = null;
        
        this.initializeApp();
        this.setupEventListeners();
    }

    initializeApp() {
        console.log('Initializing Universal Deployment System...');
        this.updateStatus('Ready - Select a project to begin');
        this.showWelcomeScreen();
    }

    setupEventListeners() {
        // Project selection
        document.getElementById('selectProjectBtn').addEventListener('click', () => {
            this.selectProject();
        });

        // Configuration
        document.getElementById('saveConfigBtn').addEventListener('click', () => {
            this.saveConfiguration();
        });

        // Actions
        document.getElementById('checkChangesBtn').addEventListener('click', () => {
            this.checkChanges();
        });

        document.getElementById('deployBtn').addEventListener('click', () => {
            this.createDeployment();
        });

        document.getElementById('openDocsBtn').addEventListener('click', () => {
            this.openDeploymentGuide();
        });

        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Deployment results actions
        document.getElementById('openPackageBtn')?.addEventListener('click', () => {
            this.openPackageLocation();
        });

        document.getElementById('openGuideBtn')?.addEventListener('click', () => {
            this.openDeploymentGuide();
        });

        document.getElementById('newDeploymentBtn')?.addEventListener('click', () => {
            this.startNewDeployment();
        });

        // Configuration form changes
        ['projectName', 'projectType', 'deploymentDomain', 'serverPath'].forEach(id => {
            document.getElementById(id).addEventListener('change', () => {
                this.onConfigurationChange();
            });
        });
    }

    async selectProject() {
        try {
            this.showLoading('Selecting project folder...');
            
            const projectPath = await ipcRenderer.invoke('select-project-folder');
            
            if (projectPath) {
                this.currentProject = projectPath;
                document.getElementById('projectPath').textContent = projectPath;
                
                // Load project configuration
                await this.loadProjectConfiguration(projectPath);
                
                this.updateStatus(`Project loaded: ${projectPath}`);
                this.enableActions();
            }
        } catch (error) {
            console.error('Error selecting project:', error);
            this.showError('Failed to select project folder');
        } finally {
            this.hideLoading();
        }
    }

    async loadProjectConfiguration(projectPath) {
        try {
            this.showLoading('Loading project configuration...');
            
            const config = await ipcRenderer.invoke('load-project-config', projectPath);
            this.currentConfig = config;
            
            // Update form fields
            document.getElementById('projectName').value = config.projectName || '';
            document.getElementById('projectType').value = config.projectType || 'web-application';
            document.getElementById('deploymentDomain').value = config.deployment?.domain || '';
            document.getElementById('serverPath').value = config.deployment?.serverPath || '/public_html';
            
            this.updateStatus('Project configuration loaded');
        } catch (error) {
            console.error('Error loading project config:', error);
            this.showError('Failed to load project configuration');
        } finally {
            this.hideLoading();
        }
    }

    async saveConfiguration() {
        try {
            if (!this.currentProject) {
                this.showError('Please select a project first');
                return;
            }

            this.showLoading('Saving configuration...');

            // Gather form data
            const config = {
                ...this.currentConfig,
                projectName: document.getElementById('projectName').value,
                projectType: document.getElementById('projectType').value,
                deployment: {
                    ...this.currentConfig.deployment,
                    domain: document.getElementById('deploymentDomain').value,
                    serverPath: document.getElementById('serverPath').value
                }
            };

            const result = await ipcRenderer.invoke('save-project-config', this.currentProject, config);
            
            if (result.success) {
                this.currentConfig = config;
                this.updateStatus('Configuration saved successfully');
                this.enableActions();
            } else {
                this.showError('Failed to save configuration: ' + result.error);
            }
        } catch (error) {
            console.error('Error saving configuration:', error);
            this.showError('Failed to save configuration');
        } finally {
            this.hideLoading();
        }
    }

    async checkChanges() {
        try {
            if (!this.currentProject || !this.currentConfig) {
                this.showError('Please select a project and configure settings first');
                return;
            }

            this.showLoading('Detecting changes...');
            this.updateStatus('Scanning files and detecting changes...');

            const changes = await ipcRenderer.invoke('detect-changes', this.currentProject, this.currentConfig);
            this.lastChanges = changes;

            this.displayChanges(changes);
            this.updateStatus(`Found ${changes.changedFiles} changes out of ${changes.totalFiles} files`);
            
            // Enable deployment if changes found
            if (changes.changedFiles > 0) {
                document.getElementById('deployBtn').disabled = false;
            }

        } catch (error) {
            console.error('Error checking changes:', error);
            this.showError('Failed to detect changes: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async createDeployment() {
        try {
            if (!this.lastChanges || this.lastChanges.changedFiles === 0) {
                this.showError('No changes detected. Please check for changes first.');
                return;
            }

            this.showLoading('Creating deployment package...');
            this.updateStatus('Building deployment package...');

            const deployment = await ipcRenderer.invoke('create-deployment', 
                this.currentProject, this.lastChanges, this.currentConfig);
            
            this.lastDeployment = deployment;

            // Generate documentation
            this.updateStatus('Generating deployment documentation...');
            const documentation = await ipcRenderer.invoke('generate-documentation', deployment);

            this.displayDeploymentResults(deployment);
            this.updateStatus('Deployment package created successfully');

            // Enable documentation button
            document.getElementById('openDocsBtn').disabled = false;

        } catch (error) {
            console.error('Error creating deployment:', error);
            this.showError('Failed to create deployment: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    displayChanges(changes) {
        this.hideWelcomeScreen();
        
        // Update summary
        document.getElementById('totalFiles').textContent = changes.totalFiles;
        document.getElementById('changedFiles').textContent = changes.changedFiles;
        document.getElementById('addedFiles').textContent = changes.addedFiles;
        document.getElementById('modifiedFiles').textContent = changes.modifiedFiles;

        // Display categories
        this.displayCategories(changes.categories);
        
        // Display all files
        this.displayFilesList(changes.changes);

        // Display features (if analysis available)
        if (changes.analysis) {
            this.displayFeatures(changes.analysis.features);
        }

        // Show changes display
        document.getElementById('changesDisplay').style.display = 'block';
        document.getElementById('deploymentResults').style.display = 'none';
    }

    displayCategories(categories) {
        const container = document.getElementById('categoriesList');
        container.innerHTML = '';

        Object.entries(categories).forEach(([categoryName, categoryData]) => {
            if (categoryData.count > 0) {
                const categoryElement = this.createCategoryElement(categoryName, categoryData);
                container.appendChild(categoryElement);
            }
        });
    }

    createCategoryElement(name, data) {
        const element = document.createElement('div');
        element.className = 'category-item';
        
        element.innerHTML = `
            <div class="category-header" onclick="this.parentElement.querySelector('.category-files').style.display = this.parentElement.querySelector('.category-files').style.display === 'none' ? 'block' : 'none'">
                <div class="category-title">
                    <i class="fas fa-${this.getCategoryIcon(name)}"></i>
                    ${this.formatCategoryName(name)}
                </div>
                <span class="category-count">${data.count}</span>
            </div>
            <div class="category-files" style="display: none;">
                ${data.files.map(file => `
                    <div class="file-item">
                        <div class="file-status ${file.status}"></div>
                        <div class="file-path">${file.path}</div>
                        <div class="file-size">${this.formatFileSize(file.size)}</div>
                    </div>
                `).join('')}
            </div>
        `;

        return element;
    }

    displayFilesList(files) {
        const container = document.getElementById('filesList');
        container.innerHTML = '';

        files.forEach(file => {
            const fileElement = document.createElement('div');
            fileElement.className = 'file-item';
            fileElement.innerHTML = `
                <div class="file-status ${file.status}"></div>
                <div class="file-path">${file.path}</div>
                <div class="file-size">${this.formatFileSize(file.size)}</div>
            `;
            container.appendChild(fileElement);
        });
    }

    displayFeatures(features) {
        const container = document.getElementById('featuresList');
        container.innerHTML = '';

        features.forEach(feature => {
            const featureElement = document.createElement('div');
            featureElement.className = 'feature-item';
            featureElement.innerHTML = `
                <div class="feature-title">${feature}</div>
            `;
            container.appendChild(featureElement);
        });
    }

    displayDeploymentResults(deployment) {
        // Update deployment info
        document.getElementById('packageFile').textContent = deployment.packagePath;
        document.getElementById('packageSize').textContent = this.formatFileSize(deployment.packageSize);
        document.getElementById('packageFiles').textContent = deployment.fileCount;
        document.getElementById('packageCreated').textContent = new Date(deployment.timestamp).toLocaleString();

        // Show deployment results
        document.getElementById('changesDisplay').style.display = 'none';
        document.getElementById('deploymentResults').style.display = 'block';
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        document.getElementById(`${tabName}Tab`).classList.add('active');
    }

    // Utility methods
    getCategoryIcon(category) {
        const icons = {
            frontend: 'code',
            backend: 'server',
            database: 'database',
            config: 'cog',
            assets: 'image',
            other: 'file'
        };
        return icons[category] || 'file';
    }

    formatCategoryName(name) {
        return name.charAt(0).toUpperCase() + name.slice(1).replace(/([A-Z])/g, ' $1');
    }

    formatFileSize(bytes) {
        if (!bytes) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    enableActions() {
        document.getElementById('checkChangesBtn').disabled = false;
        document.getElementById('saveConfigBtn').disabled = false;
    }

    onConfigurationChange() {
        // Enable save button when configuration changes
        document.getElementById('saveConfigBtn').disabled = false;
    }

    showWelcomeScreen() {
        document.getElementById('welcomeScreen').style.display = 'block';
        document.getElementById('changesDisplay').style.display = 'none';
        document.getElementById('deploymentResults').style.display = 'none';
    }

    hideWelcomeScreen() {
        document.getElementById('welcomeScreen').style.display = 'none';
    }

    showLoading(message) {
        document.getElementById('loadingText').textContent = message;
        document.getElementById('loadingOverlay').style.display = 'flex';
        document.getElementById('progressBar').style.display = 'block';
    }

    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
        document.getElementById('progressBar').style.display = 'none';
    }

    updateStatus(message) {
        document.getElementById('statusText').textContent = message;
        document.getElementById('lastUpdate').textContent = `Last updated: ${new Date().toLocaleTimeString()}`;
    }

    showError(message) {
        this.updateStatus(`Error: ${message}`);
        console.error(message);
        // You could add a toast notification here
    }

    async openPackageLocation() {
        if (this.lastDeployment) {
            await ipcRenderer.invoke('open-file-explorer', this.lastDeployment.packagePath);
        }
    }

    async openDeploymentGuide() {
        if (this.lastDeployment) {
            await ipcRenderer.invoke('open-external', this.lastDeployment.documentationPath);
        }
    }

    startNewDeployment() {
        this.lastChanges = null;
        this.lastDeployment = null;
        document.getElementById('deployBtn').disabled = true;
        document.getElementById('openDocsBtn').disabled = true;
        this.showWelcomeScreen();
        this.updateStatus('Ready for new deployment');
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new DeploymentApp();
});
