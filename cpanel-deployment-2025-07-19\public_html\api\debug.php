<?php
// Debug script to check API functionality
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://eskillvisor.wallistry.pk');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

$debug_info = [
    'timestamp' => date('c'),
    'server_name' => $_SERVER['SERVER_NAME'] ?? 'unknown',
    'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
    'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
    'php_version' => phpversion(),
    'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'unknown',
    'script_filename' => $_SERVER['SCRIPT_FILENAME'] ?? 'unknown'
];

// Test file includes
$config_exists = file_exists(__DIR__ . '/config/config.php');
$database_exists = file_exists(__DIR__ . '/config/database.php');

$debug_info['config_file_exists'] = $config_exists;
$debug_info['database_file_exists'] = $database_exists;

// Test config loading
if ($config_exists) {
    try {
        require_once __DIR__ . '/config/config.php';
        $debug_info['config_loaded'] = true;
        $debug_info['app_name'] = defined('APP_NAME') ? APP_NAME : 'not defined';
        $debug_info['app_env'] = defined('APP_ENV') ? APP_ENV : 'not defined';
        $debug_info['db_host'] = defined('DB_HOST') ? DB_HOST : 'not defined';
        $debug_info['db_name'] = defined('DB_NAME') ? DB_NAME : 'not defined';
    } catch (Exception $e) {
        $debug_info['config_loaded'] = false;
        $debug_info['config_error'] = $e->getMessage();
    }
} else {
    $debug_info['config_loaded'] = false;
    $debug_info['config_error'] = 'Config file not found';
}

// Test database connection
if ($database_exists && $config_exists) {
    try {
        require_once __DIR__ . '/config/database.php';
        $db = Database::getInstance();
        $connection = $db->getConnection();
        $stmt = $connection->query("SELECT 1 as test");
        $result = $stmt->fetch();
        
        $debug_info['database_connection'] = true;
        $debug_info['database_test_result'] = $result['test'];
    } catch (Exception $e) {
        $debug_info['database_connection'] = false;
        $debug_info['database_error'] = $e->getMessage();
    }
} else {
    $debug_info['database_connection'] = false;
    $debug_info['database_error'] = 'Database file not found or config not loaded';
}

// Test directory permissions
$uploads_dir = __DIR__ . '/uploads';
$debug_info['uploads_dir_exists'] = is_dir($uploads_dir);
$debug_info['uploads_dir_writable'] = is_writable($uploads_dir);

echo json_encode([
    'success' => true,
    'message' => 'Debug information collected',
    'debug' => $debug_info
], JSON_PRETTY_PRINT);
?>
