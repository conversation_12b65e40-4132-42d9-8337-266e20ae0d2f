# cPanel Database Setup Guide for EskillVisor

## 🗄️ Database Configuration for wallistry.pk

### Step 1: Create Database in cPanel

1. **Login to cPanel**
   - URL: https://wallistry.pk:2083
   - Username: wallistry
   - Password: +GlESn;lJteQ%VXf

2. **Navigate to MySQL Databases**
   - Find "Databases" section
   - Click "MySQL Databases"

3. **Create New Database**
   - Database Name: `eskillvisor_db`
   - Full Database Name will be: `wallistry_eskillvisor_db`
   - Click "Create Database"

4. **Create Database User**
   - Username: `eskill`
   - Full Username will be: `wallistry_eskill`
   - Password: `EskillVisor2024!`
   - Click "Create User"

5. **Add User to Database**
   - Select User: `wallistry_eskill`
   - Select Database: `wallistry_eskillvisor_db`
   - Grant ALL PRIVILEGES
   - Click "Add"

### Step 2: Update Database Configuration

The production configuration files are already created with these settings:

```php
// Database configuration for cPanel hosting
define('DB_HOST', 'localhost');
define('DB_NAME', 'wallistry_eskillvisor_db');
define('DB_USER', 'wallistry_eskill');
define('DB_PASS', 'EskillVisor2024!');
define('DB_CHARSET', 'utf8mb4');
```

### Step 3: Upload Backend Files

1. **Prepare Files for Upload**
   - The backend folder is ready for deployment
   - All configuration files are set for production

2. **Upload to cPanel**
   - Access File Manager in cPanel
   - Navigate to `public_html`
   - Create folder named `api`
   - Upload entire `backend` folder contents to `public_html/api/`

3. **Set Permissions**
   - Set `api/uploads/` folder to 755 permissions
   - Set `api/logs/` folder to 755 permissions
   - Ensure all PHP files have 644 permissions

### Step 4: Run Database Installation

1. **Access Installation Script**
   - URL: https://wallistry.pk/api/install.php

2. **Expected Installation Process**
   ```
   ✓ PHP version check
   ✓ Required extensions loaded
   ✓ Database connection established
   ✓ Database created successfully
   ✓ All migrations executed
   ✓ Default users created
   ✓ Sample data inserted
   ```

3. **Default Login Accounts**
   | Role | Email | Password |
   |------|-------|----------|
   | Super Admin | <EMAIL> | password |
   | Manager | <EMAIL> | password |
   | Partner | <EMAIL> | password |

### Step 5: Test API Endpoints

1. **Basic API Test**
   - URL: https://wallistry.pk/api/test
   - Expected Response:
   ```json
   {
     "success": true,
     "data": {
       "message": "API endpoint is working!",
       "timestamp": "2024-01-07T10:30:00+00:00"
     }
   }
   ```

2. **Authentication Test**
   - URL: https://wallistry.pk/api/auth/login
   - Method: POST
   - Body:
   ```json
   {
     "email": "<EMAIL>",
     "password": "password"
   }
   ```

### Step 6: Security Checklist

- ✅ Database user has limited privileges
- ✅ Strong database password set
- ✅ JWT secret key configured
- ✅ Error reporting disabled in production
- ✅ CORS properly configured
- ✅ File upload restrictions in place
- ✅ .htaccess security headers configured

### Troubleshooting

**Database Connection Issues:**
1. Verify database name includes cPanel prefix
2. Check username includes cPanel prefix
3. Ensure database user has proper privileges
4. Confirm password is correct

**Permission Issues:**
1. Set uploads folder to 755
2. Set logs folder to 755
3. Ensure PHP files are 644

**API Not Responding:**
1. Check .htaccess file exists
2. Verify mod_rewrite is enabled
3. Check PHP error logs in cPanel

### Next Steps

After successful database setup:
1. Test all API endpoints
2. Verify frontend can connect to backend
3. Test user authentication
4. Verify file upload functionality
5. Check audit trail logging
6. Test notification system

### Support

For issues:
1. Check cPanel Error Logs
2. Review API logs at `/api/logs/`
3. Test individual endpoints
4. Verify database connectivity
