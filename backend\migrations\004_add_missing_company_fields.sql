-- Add missing fields to companies table to match AddCompanyModal
ALTER TABLE companies 
ADD COLUMN company_director <PERSON><PERSON><PERSON><PERSON>(255) NULL AFTER name,
ADD COLUMN registration_territory VARCHAR(100) NULL AFTER company_director,
ADD COLUMN ein_number VARCHAR(50) NULL AFTER registration_territory,
ADD COLUMN marketplace VARCHAR(100) NULL AFTER ein_number,
ADD COLUMN custom_marketplace VARCHAR(100) NULL AFTER marketplace,
ADD COLUMN manager_id INT NULL AFTER created_by;

-- Add foreign key constraint for manager_id
ALTER TABLE companies 
ADD CONSTRAINT fk_companies_manager_id FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL;

-- Add indexes for better performance
ALTER TABLE companies 
ADD INDEX idx_company_director (company_director),
ADD INDEX idx_registration_territory (registration_territory),
ADD INDEX idx_ein_number (ein_number),
ADD INDEX idx_marketplace (marketplace),
ADD INDEX idx_manager_id (manager_id);
