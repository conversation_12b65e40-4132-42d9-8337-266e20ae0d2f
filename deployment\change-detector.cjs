#!/usr/bin/env node

/**
 * EskillVisor Change Detection System
 * Analyzes file changes since last deployment
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { execSync } = require('child_process');

class ChangeDetector {
    constructor(configPath = './deployment/config.json') {
        this.config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        this.lastDeploymentFile = this.config.git.deploymentStateFile;
        this.projectRoot = process.cwd();
    }

    /**
     * Detect all changes since last deployment
     */
    async detectChanges() {
        const lastDeployment = this.getLastDeploymentState();
        const currentState = await this.getCurrentState();
        
        const changes = {
            frontend: this.detectFrontendChanges(lastDeployment, currentState),
            backend: this.detectBackendChanges(lastDeployment, currentState),
            database: this.detectDatabaseChanges(lastDeployment, currentState),
            config: this.detectConfigChanges(lastDeployment, currentState)
        };

        return {
            timestamp: new Date().toISOString(),
            lastDeployment: lastDeployment.timestamp || 'Never',
            changes,
            summary: this.generateChangeSummary(changes)
        };
    }

    /**
     * Get last deployment state
     */
    getLastDeploymentState() {
        try {
            if (fs.existsSync(this.lastDeploymentFile)) {
                return JSON.parse(fs.readFileSync(this.lastDeploymentFile, 'utf8'));
            }
        } catch (error) {
            console.warn('Could not read last deployment state:', error.message);
        }
        return { files: {}, timestamp: null };
    }

    /**
     * Get current file state
     */
    async getCurrentState() {
        const state = { files: {} };
        
        // Frontend files
        await this.scanDirectory('src', state.files, 'frontend');
        await this.scanDirectory('public', state.files, 'frontend');
        this.scanFile('package.json', state.files, 'frontend');
        this.scanFile('vite.config.js', state.files, 'frontend');
        this.scanFile('index.html', state.files, 'frontend');

        // Backend files
        await this.scanDirectory('backend', state.files, 'backend');

        // Database files
        await this.scanDirectory('backend/migrations', state.files, 'database');
        this.scanFile('backend/database.sql', state.files, 'database');

        // Config files
        this.scanFile('deployment/config.json', state.files, 'config');
        this.scanFile('.env', state.files, 'config');

        return state;
    }

    /**
     * Scan directory for changes
     */
    async scanDirectory(dirPath, files, category) {
        const fullPath = path.join(this.projectRoot, dirPath);
        if (!fs.existsSync(fullPath)) return;

        const items = fs.readdirSync(fullPath, { withFileTypes: true });
        
        for (const item of items) {
            const itemPath = path.join(dirPath, item.name);
            
            if (item.isDirectory()) {
                await this.scanDirectory(itemPath, files, category);
            } else {
                this.scanFile(itemPath, files, category);
            }
        }
    }

    /**
     * Scan individual file
     */
    scanFile(filePath, files, category) {
        const fullPath = path.join(this.projectRoot, filePath);
        if (!fs.existsSync(fullPath)) return;

        const stats = fs.statSync(fullPath);
        const content = fs.readFileSync(fullPath);
        const hash = crypto.createHash('md5').update(content).digest('hex');

        files[filePath] = {
            category,
            hash,
            size: stats.size,
            modified: stats.mtime.toISOString(),
            permissions: stats.mode.toString(8)
        };
    }

    /**
     * Detect frontend changes
     */
    detectFrontendChanges(lastState, currentState) {
        return this.detectCategoryChanges('frontend', lastState, currentState);
    }

    /**
     * Detect backend changes
     */
    detectBackendChanges(lastState, currentState) {
        return this.detectCategoryChanges('backend', lastState, currentState);
    }

    /**
     * Detect database changes
     */
    detectDatabaseChanges(lastState, currentState) {
        return this.detectCategoryChanges('database', lastState, currentState);
    }

    /**
     * Detect config changes
     */
    detectConfigChanges(lastState, currentState) {
        return this.detectCategoryChanges('config', lastState, currentState);
    }

    /**
     * Detect changes for specific category
     */
    detectCategoryChanges(category, lastState, currentState) {
        const changes = {
            added: [],
            modified: [],
            deleted: [],
            unchanged: []
        };

        // Find current files in category
        const currentFiles = Object.entries(currentState.files)
            .filter(([_, file]) => file.category === category);

        // Find last files in category
        const lastFiles = Object.entries(lastState.files || {})
            .filter(([_, file]) => file.category === category);

        const lastFileMap = new Map(lastFiles);
        const currentFileMap = new Map(currentFiles);

        // Check for added and modified files
        for (const [filePath, currentFile] of currentFiles) {
            const lastFile = lastFileMap.get(filePath);
            
            if (!lastFile) {
                changes.added.push({
                    path: filePath,
                    size: currentFile.size,
                    modified: currentFile.modified
                });
            } else if (lastFile.hash !== currentFile.hash) {
                changes.modified.push({
                    path: filePath,
                    size: currentFile.size,
                    modified: currentFile.modified,
                    previousModified: lastFile.modified
                });
            } else {
                changes.unchanged.push({
                    path: filePath,
                    size: currentFile.size,
                    modified: currentFile.modified
                });
            }
        }

        // Check for deleted files
        for (const [filePath, lastFile] of lastFiles) {
            if (!currentFileMap.has(filePath)) {
                changes.deleted.push({
                    path: filePath,
                    size: lastFile.size,
                    modified: lastFile.modified
                });
            }
        }

        return changes;
    }

    /**
     * Generate change summary
     */
    generateChangeSummary(changes) {
        const summary = {
            totalChanges: 0,
            categories: {},
            requiresDeployment: false,
            deploymentType: 'none'
        };

        for (const [category, categoryChanges] of Object.entries(changes)) {
            const total = categoryChanges.added.length + 
                         categoryChanges.modified.length + 
                         categoryChanges.deleted.length;
            
            summary.categories[category] = {
                added: categoryChanges.added.length,
                modified: categoryChanges.modified.length,
                deleted: categoryChanges.deleted.length,
                total
            };
            
            summary.totalChanges += total;
        }

        // Determine deployment requirements
        if (summary.totalChanges > 0) {
            summary.requiresDeployment = true;
            
            if (summary.categories.database.total > 0) {
                summary.deploymentType = 'full';
            } else if (summary.categories.backend.total > 0) {
                summary.deploymentType = 'backend-frontend';
            } else if (summary.categories.frontend.total > 0) {
                summary.deploymentType = 'frontend-only';
            } else {
                summary.deploymentType = 'config-only';
            }
        }

        return summary;
    }

    /**
     * Save current state as last deployment
     */
    async saveDeploymentState() {
        const currentState = await this.getCurrentState();
        currentState.timestamp = new Date().toISOString();
        
        // Ensure deployment directory exists
        const deploymentDir = path.dirname(this.lastDeploymentFile);
        if (!fs.existsSync(deploymentDir)) {
            fs.mkdirSync(deploymentDir, { recursive: true });
        }
        
        fs.writeFileSync(this.lastDeploymentFile, JSON.stringify(currentState, null, 2));
        return currentState;
    }
}

module.exports = ChangeDetector;

// CLI usage
if (require.main === module) {
    const detector = new ChangeDetector();
    
    detector.detectChanges().then(changes => {
        console.log(JSON.stringify(changes, null, 2));
    }).catch(error => {
        console.error('Error detecting changes:', error);
        process.exit(1);
    });
}
