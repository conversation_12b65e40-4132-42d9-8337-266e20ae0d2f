#!/usr/bin/env node

/**
 * EskillVisor FTP/SFTP Deployment Module
 * Handles actual file uploads to cPanel
 */

const fs = require('fs');
const path = require('path');
const { Client } = require('basic-ftp');
const { NodeSSH } = require('node-ssh');

class FTPDeployer {
    constructor(config, credentials) {
        this.config = config;
        this.credentials = credentials;
        this.client = null;
        this.protocol = credentials.CPANEL_PROTOCOL || 'ftp';
    }

    /**
     * Connect to cPanel server
     */
    async connect() {
        try {
            if (this.protocol === 'sftp') {
                await this.connectSFTP();
            } else {
                await this.connectFTP();
            }
            console.log(`✅ Connected to ${this.credentials.CPANEL_HOST} via ${this.protocol.toUpperCase()}`);
        } catch (error) {
            throw new Error(`Connection failed: ${error.message}`);
        }
    }

    /**
     * Connect via FTP
     */
    async connectFTP() {
        this.client = new Client();
        this.client.ftp.verbose = this.credentials.DEBUG_MODE === 'true';
        
        await this.client.access({
            host: this.credentials.CPANEL_HOST,
            port: parseInt(this.credentials.CPANEL_PORT) || 21,
            user: this.credentials.CPANEL_USERNAME,
            password: this.credentials.CPANEL_PASSWORD,
            secure: this.config.connection.secure
        });
    }

    /**
     * Connect via SFTP
     */
    async connectSFTP() {
        this.client = new NodeSSH();
        
        await this.client.connect({
            host: this.credentials.CPANEL_HOST,
            port: parseInt(this.credentials.CPANEL_PORT) || 22,
            username: this.credentials.CPANEL_USERNAME,
            password: this.credentials.CPANEL_PASSWORD
        });
    }

    /**
     * Upload frontend files
     */
    async uploadFrontend() {
        if (!this.config.targets.frontend.enabled) {
            console.log('⏭️ Frontend deployment disabled');
            return;
        }

        const localPath = this.config.targets.frontend.local_path;
        const remotePath = this.config.targets.frontend.remote_path;

        if (!fs.existsSync(localPath)) {
            throw new Error(`Frontend build directory not found: ${localPath}`);
        }

        console.log(`📁 Uploading frontend: ${localPath} → ${remotePath}`);

        if (this.protocol === 'sftp') {
            await this.uploadDirectorySFTP(localPath, remotePath);
        } else {
            await this.uploadDirectoryFTP(localPath, remotePath);
        }

        console.log('✅ Frontend upload completed');
    }

    /**
     * Upload backend files
     */
    async uploadBackend(changedFiles = []) {
        if (!this.config.targets.backend.enabled) {
            console.log('⏭️ Backend deployment disabled');
            return;
        }

        const localPath = this.config.targets.backend.local_path;
        const remotePath = this.config.targets.backend.remote_path;

        if (!fs.existsSync(localPath)) {
            throw new Error(`Backend directory not found: ${localPath}`);
        }

        console.log(`🔧 Uploading backend: ${localPath} → ${remotePath}`);

        // If we have specific changed files, upload only those
        if (changedFiles.length > 0) {
            await this.uploadChangedFiles(changedFiles, localPath, remotePath);
        } else {
            // Upload entire backend directory
            if (this.protocol === 'sftp') {
                await this.uploadDirectorySFTP(localPath, remotePath);
            } else {
                await this.uploadDirectoryFTP(localPath, remotePath);
            }
        }

        console.log('✅ Backend upload completed');
    }

    /**
     * Upload directory via FTP
     */
    async uploadDirectoryFTP(localDir, remoteDir) {
        try {
            // Ensure remote directory exists
            await this.ensureRemoteDirectoryFTP(remoteDir);
            
            // Upload directory contents
            await this.client.uploadFromDir(localDir, remoteDir);
        } catch (error) {
            throw new Error(`FTP upload failed: ${error.message}`);
        }
    }

    /**
     * Upload directory via SFTP
     */
    async uploadDirectorySFTP(localDir, remoteDir) {
        try {
            // Ensure remote directory exists
            await this.ensureRemoteDirectorySFTP(remoteDir);
            
            // Upload directory contents
            await this.client.putDirectory(localDir, remoteDir, {
                recursive: true,
                concurrency: parseInt(this.credentials.MAX_CONCURRENT_UPLOADS) || 5,
                validate: false,
                tick: (localPath, remotePath, error) => {
                    if (error) {
                        console.error(`❌ Failed to upload ${localPath}: ${error.message}`);
                    } else {
                        console.log(`   → ${path.basename(localPath)}`);
                    }
                }
            });
        } catch (error) {
            throw new Error(`SFTP upload failed: ${error.message}`);
        }
    }

    /**
     * Upload only changed files
     */
    async uploadChangedFiles(changedFiles, localBase, remoteBase) {
        for (const fileInfo of changedFiles) {
            const localFile = path.join(localBase, fileInfo.path.replace(localBase, ''));
            const remoteFile = path.join(remoteBase, fileInfo.path.replace(localBase, '')).replace(/\\/g, '/');
            
            if (fs.existsSync(localFile)) {
                console.log(`   → ${fileInfo.path}`);
                
                if (this.protocol === 'sftp') {
                    await this.client.putFile(localFile, remoteFile);
                } else {
                    await this.client.uploadFrom(localFile, remoteFile);
                }
            }
        }
    }

    /**
     * Set file permissions
     */
    async setPermissions() {
        if (this.protocol !== 'sftp') {
            console.log('⏭️ Permission setting only available via SFTP');
            return;
        }

        try {
            const frontendPath = this.config.targets.frontend.remote_path;
            const backendPath = this.config.targets.backend.remote_path;

            // Set frontend permissions
            if (this.config.targets.frontend.enabled) {
                console.log('   → Setting frontend permissions...');
                await this.setDirectoryPermissions(frontendPath, '755', '644');
            }

            // Set backend permissions
            if (this.config.targets.backend.enabled) {
                console.log('   → Setting backend permissions...');
                await this.setDirectoryPermissions(backendPath, '755', '644');
            }

            console.log('✅ Permissions set successfully');
        } catch (error) {
            console.warn(`⚠️ Permission setting failed: ${error.message}`);
        }
    }

    /**
     * Set permissions recursively
     */
    async setDirectoryPermissions(remotePath, dirPerm, filePerm) {
        try {
            // Set directory permission
            await this.client.execCommand(`find ${remotePath} -type d -exec chmod ${dirPerm} {} \\;`);
            
            // Set file permissions
            await this.client.execCommand(`find ${remotePath} -type f -exec chmod ${filePerm} {} \\;`);
        } catch (error) {
            throw new Error(`Permission setting failed: ${error.message}`);
        }
    }

    /**
     * Ensure remote directory exists (FTP)
     */
    async ensureRemoteDirectoryFTP(remotePath) {
        try {
            await this.client.ensureDir(remotePath);
        } catch (error) {
            // Directory might already exist, ignore error
        }
    }

    /**
     * Ensure remote directory exists (SFTP)
     */
    async ensureRemoteDirectorySFTP(remotePath) {
        try {
            await this.client.mkdir(remotePath, 'sftp');
        } catch (error) {
            // Directory might already exist, ignore error
        }
    }

    /**
     * Create backup of remote files
     */
    async createRemoteBackup(backupPath) {
        if (this.protocol !== 'sftp') {
            console.log('⏭️ Remote backup only available via SFTP');
            return;
        }

        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupDir = `${backupPath}/backup_${timestamp}`;

            console.log(`💾 Creating remote backup: ${backupDir}`);

            // Create backup directory
            await this.client.mkdir(backupDir, 'sftp');

            // Backup frontend
            if (this.config.targets.frontend.enabled) {
                const frontendBackup = `${backupDir}/frontend`;
                await this.client.execCommand(`cp -r ${this.config.targets.frontend.remote_path} ${frontendBackup}`);
            }

            // Backup backend
            if (this.config.targets.backend.enabled) {
                const backendBackup = `${backupDir}/backend`;
                await this.client.execCommand(`cp -r ${this.config.targets.backend.remote_path} ${backendBackup}`);
            }

            console.log('✅ Remote backup created');
            return backupDir;
        } catch (error) {
            console.warn(`⚠️ Remote backup failed: ${error.message}`);
        }
    }

    /**
     * Verify deployment by checking files
     */
    async verifyDeployment() {
        try {
            console.log('🔍 Verifying deployed files...');

            // Check if key files exist
            const keyFiles = [
                this.config.targets.frontend.remote_path + 'index.html',
                this.config.targets.backend.remote_path + 'index.php'
            ];

            for (const file of keyFiles) {
                if (this.protocol === 'sftp') {
                    const exists = await this.client.exists(file);
                    if (!exists) {
                        throw new Error(`Key file missing: ${file}`);
                    }
                } else {
                    // For FTP, try to get file size
                    try {
                        await this.client.size(file);
                    } catch (error) {
                        throw new Error(`Key file missing: ${file}`);
                    }
                }
                console.log(`   ✅ ${path.basename(file)} exists`);
            }

            console.log('✅ File verification completed');
        } catch (error) {
            throw new Error(`Deployment verification failed: ${error.message}`);
        }
    }

    /**
     * Disconnect from server
     */
    async disconnect() {
        try {
            if (this.client) {
                if (this.protocol === 'sftp') {
                    this.client.dispose();
                } else {
                    this.client.close();
                }
                console.log('📡 Disconnected from server');
            }
        } catch (error) {
            console.warn(`⚠️ Disconnect warning: ${error.message}`);
        }
    }
}

module.exports = FTPDeployer;
