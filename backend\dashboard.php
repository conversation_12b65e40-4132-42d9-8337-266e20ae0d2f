<?php
/**
 * Dashboard data endpoint
 */

// Enable CORS for local development
$allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:3000',
    'http://127.0.0.1:5173',
    'http://127.0.0.1:3000'
];

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, $allowedOrigins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    header('Access-Control-Allow-Origin: http://localhost:5173');
}

header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

try {
    // Get authorization header
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? '';
    
    if (strpos($authHeader, 'Bearer ') !== 0) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Authorization token required']);
        exit();
    }
    
    $db = Database::getInstance();
    
    // Get user statistics
    $userStats = $db->fetch("
        SELECT 
            COUNT(*) as total_users,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
            SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as new_users_today
        FROM users
    ");
    
    // Get company statistics (with error handling)
    $companyStats = ['total_companies' => 0, 'active_companies' => 0, 'new_companies_today' => 0];
    try {
        $companyStats = $db->fetch("
            SELECT
                COUNT(*) as total_companies,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_companies,
                SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as new_companies_today
            FROM companies
        ");
    } catch (Exception $e) {
        // Companies table might not exist, use defaults
    }

    // Get inventory statistics (with error handling)
    $inventoryStats = ['total_items' => 0, 'total_value' => 0, 'low_stock_count' => 0];
    try {
        $inventoryStats = $db->fetch("
            SELECT
                COUNT(*) as total_items,
                COALESCE(SUM(price * quantity), 0) as total_value,
                SUM(CASE WHEN quantity < 10 THEN 1 ELSE 0 END) as low_stock_count
            FROM inventory_items
            WHERE status = 'active'
        ");
    } catch (Exception $e) {
        // Inventory table might not exist, use defaults
    }
    
    // Get top categories (mock data for now)
    $topCategories = [
        ['name' => 'Electronics', 'count' => 45, 'value' => 125000],
        ['name' => 'Furniture', 'count' => 32, 'value' => 89000],
        ['name' => 'Office Supplies', 'count' => 28, 'value' => 15000],
        ['name' => 'Equipment', 'count' => 19, 'value' => 67000],
        ['name' => 'Software', 'count' => 15, 'value' => 23000]
    ];
    
    // Get recent activity (mock data for now)
    $recentActivity = [
        [
            'id' => 1,
            'type' => 'user_created',
            'description' => 'New user registered',
            'user' => 'John Doe',
            'timestamp' => date('c', strtotime('-2 hours'))
        ],
        [
            'id' => 2,
            'type' => 'inventory_updated',
            'description' => 'Inventory item updated',
            'user' => 'Jane Smith',
            'timestamp' => date('c', strtotime('-4 hours'))
        ],
        [
            'id' => 3,
            'type' => 'company_created',
            'description' => 'New company added',
            'user' => 'Admin User',
            'timestamp' => date('c', strtotime('-6 hours'))
        ]
    ];
    
    // Return dashboard data
    echo json_encode([
        'success' => true,
        'data' => [
            'users' => [
                'total_users' => (int)($userStats['total_users'] ?? 0),
                'active_users' => (int)($userStats['active_users'] ?? 0),
                'new_users_today' => (int)($userStats['new_users_today'] ?? 0)
            ],
            'companies' => [
                'total_companies' => (int)($companyStats['total_companies'] ?? 0),
                'active_companies' => (int)($companyStats['active_companies'] ?? 0),
                'new_companies_today' => (int)($companyStats['new_companies_today'] ?? 0)
            ],
            'inventory' => [
                'total_items' => (int)($inventoryStats['total_items'] ?? 0),
                'total_value' => (float)($inventoryStats['total_value'] ?? 0),
                'low_stock_count' => (int)($inventoryStats['low_stock_count'] ?? 0)
            ],
            'top_categories' => $topCategories,
            'recent_activity' => $recentActivity
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}
?>
