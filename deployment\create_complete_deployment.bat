@echo off
echo Creating complete EskillVisor deployment package...

:: Create the deployment directory structure
mkdir complete_project_deployment 2>nul
mkdir complete_project_deployment\backend 2>nul
mkdir complete_project_deployment\src 2>nul
mkdir complete_project_deployment\src\components 2>nul
mkdir complete_project_deployment\src\components\modals 2>nul
mkdir complete_project_deployment\src\pages 2>nul
mkdir complete_project_deployment\src\pages\superadmin 2>nul
mkdir complete_project_deployment\src\pages\manager 2>nul

echo Copying root files...
copy "..\index.html" "complete_project_deployment\"
copy "..\package.json" "complete_project_deployment\"
copy "..\vite.config.js" "complete_project_deployment\"
copy "..\tailwind.config.js" "complete_project_deployment\"
copy "..\postcss.config.js" "complete_project_deployment\"

echo Copying backend directory...
robocopy "..\backend" "complete_project_deployment\backend" /E /XD node_modules .git

echo Copying src directory...
robocopy "..\src" "complete_project_deployment\src" /E

echo Copying updated files...
copy "files\src\components\modals\UserProfileModal.jsx" "complete_project_deployment\src\components\modals\"
copy "files\src\pages\superadmin\UserManagement.jsx" "complete_project_deployment\src\pages\superadmin\"
copy "files\src\components\modals\AddCompanyModal.jsx" "complete_project_deployment\src\components\modals\"
copy "files\src\pages\superadmin\CompanyOversight.jsx" "complete_project_deployment\src\pages\superadmin\"
copy "files\src\pages\manager\CompanyManagement.jsx" "complete_project_deployment\src\pages\manager\"

echo Copying updated backend files...
copy "files\backend\controllers\CompanyController.php" "complete_project_deployment\backend\controllers\"
copy "files\backend\controllers\UserController.php" "complete_project_deployment\backend\controllers\"
copy "files\backend\models\Company.php" "complete_project_deployment\backend\models\"

echo Creating database migration...
mkdir complete_project_deployment\backend\migrations 2>nul
copy "database\014_add_company_extended_fields.sql" "complete_project_deployment\backend\migrations\"

echo Creating ZIP package...
powershell "Compress-Archive -Path 'complete_project_deployment\*' -DestinationPath 'eskillvisor-complete-deployment-2025-07-18.zip' -Force"

echo Cleaning up...
rmdir /s /q complete_project_deployment

echo.
echo ✅ Complete deployment package created!
echo 📦 File: eskillvisor-complete-deployment-2025-07-18.zip
echo 📁 Location: deployment/eskillvisor-complete-deployment-2025-07-18.zip
echo.
echo This package contains the complete project structure including:
echo - Frontend files (src/, index.html, package.json, etc.)
echo - Backend files (backend/ directory)
echo - Updated user management files
echo - Database migration
echo.
echo Ready for upload to cPanel at: /home9/wallistry/eskillvisor.wallistry.pk/
echo.
pause
