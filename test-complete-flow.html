<!DOCTYPE html>
<html>
<head>
    <title>Complete Login Flow Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-result { margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 4px solid #ccc; }
        .success { background: #d4edda; border-left-color: #28a745; }
        .error { background: #f8d7da; border-left-color: #dc3545; }
        .info { background: #d1ecf1; border-left-color: #17a2b8; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Complete Login Flow Test</h1>
        
        <button onclick="testCompleteFlow()">🚀 Test Complete Admin Flow</button>
        <button onclick="clearResults()">🧹 Clear Results</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost/Investment-System-eSkillVisor/backend';
        
        function addResult(title, status, message, details = null) {
            const resultsDiv = document.getElementById('results');
            const statusClass = status === 'success' ? 'success' : status === 'error' ? 'error' : 'info';
            const icon = status === 'success' ? '✅' : status === 'error' ? '❌' : 'ℹ️';
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${statusClass}`;
            resultDiv.innerHTML = `
                <h4>${icon} ${title}</h4>
                <p>${message}</p>
                ${details ? `<pre>${JSON.stringify(details, null, 2)}</pre>` : ''}
            `;
            
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testCompleteFlow() {
            clearResults();
            addResult('Complete Flow Test', 'info', 'Testing complete admin login and dashboard flow...');
            
            try {
                // Step 1: Login
                addResult('Step 1', 'info', 'Testing login...');
                const loginResponse = await fetch(`${API_BASE}/login.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': 'http://localhost:5173'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password'
                    })
                });
                
                const loginData = await loginResponse.json();
                
                if (!loginData.success) {
                    addResult('Login Failed', 'error', loginData.message);
                    return;
                }
                
                const token = loginData.data.access_token;
                const user = loginData.data.user;
                
                addResult('Login Success', 'success', `Logged in as ${user.email} with role: ${user.role}`, user);
                
                // Step 2: Test getCurrentUser
                addResult('Step 2', 'info', 'Testing getCurrentUser...');
                const userResponse = await fetch(`${API_BASE}/me.php`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Origin': 'http://localhost:5173'
                    }
                });
                
                const userData = await userResponse.json();
                
                if (!userData.success) {
                    addResult('Get User Failed', 'error', userData.message);
                    return;
                }
                
                addResult('Get User Success', 'success', 'User data retrieved successfully', userData.data);
                
                // Step 3: Test Dashboard Data
                addResult('Step 3', 'info', 'Testing dashboard data...');
                const dashboardResponse = await fetch(`${API_BASE}/dashboard.php`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Origin': 'http://localhost:5173'
                    }
                });
                
                const dashboardData = await dashboardResponse.json();
                
                if (!dashboardData.success) {
                    addResult('Dashboard Failed', 'error', dashboardData.message);
                    return;
                }
                
                addResult('Dashboard Success', 'success', 'Dashboard data loaded successfully', {
                    users: dashboardData.data.users,
                    companies: dashboardData.data.companies,
                    inventory: dashboardData.data.inventory
                });
                
                // Step 4: Role Routing Test
                addResult('Step 4', 'info', 'Testing role routing logic...');
                
                let expectedRoute;
                if (user.role === 'super_admin' || user.role === 'superadmin') {
                    expectedRoute = '/superadmin';
                } else if (user.role === 'manager') {
                    expectedRoute = '/manager';
                } else {
                    expectedRoute = '/partner';
                }
                
                addResult('Role Routing', 'success', `Role ${user.role} should route to ${expectedRoute}`, {
                    userRole: user.role,
                    expectedRoute: expectedRoute,
                    routingLogic: 'Working correctly'
                });
                
                // Final Summary
                addResult('🎉 Complete Flow Test', 'success', 
                    'All tests passed! The admin login flow is working correctly. ' +
                    'You can now login at http://localhost:5173 with <EMAIL> / password ' +
                    'and should be redirected to the SuperAdmin dashboard without any blank screens.'
                );
                
            } catch (error) {
                addResult('Flow Test Failed', 'error', `Error: ${error.message}`);
            }
        }
    </script>
</body>
</html>
