import React, { useState, useContext } from 'react';
import { PlusIcon, SearchIcon, FilterIcon, CheckIcon, XIcon, ClockIcon, UserIcon, AlertCircleIcon } from 'lucide-react';
import { AuthContext } from '../../App.jsx';
import AddUserModal from '../../components/modals/AddUserModal.jsx';
import UserApprovalModal from '../../components/modals/UserApprovalModal.jsx';
import UserProfileModal from '../../components/modals/UserProfileModal.jsx';
import userService from '../../services/userService.js';

const UserManagement = () => {
  const { userRole, user } = useContext(AuthContext);
  const [users, setUsers] = useState([
    { id: '1', name: '<PERSON>', email: '<EMAIL>', role: 'superadmin', status: 'active', approval_status: 'approved' },
    { id: '2', name: '<PERSON> <PERSON>', email: '<EMAIL>', role: 'manager', status: 'active', approval_status: 'approved' },
    { id: '3', name: 'Bob Partner', email: '<EMAIL>', role: 'partner', status: 'active', approval_status: 'approved' },
    { id: '4', name: 'Alice Smith', email: '<EMAIL>', role: 'partner', status: 'active', approval_status: 'pending', created_by: '2', created_at: '2025-07-15T10:30:00Z' },
    { id: '5', name: 'Mike Johnson', email: '<EMAIL>', role: 'manager', status: 'active', approval_status: 'pending', created_by: '2', created_at: '2025-07-15T09:15:00Z' }
  ]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showAddManagerModal, setShowAddManagerModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [activeTab, setActiveTab] = useState('all');
  const [activeUserTab, setActiveUserTab] = useState('managers');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [roleFilter, setRoleFilter] = useState('all');

  const handleAddUser = async (userData) => {
    try {
      console.log('Creating user with data:', userData);

      // Call the real API
      const newUser = await userService.createUser(userData);
      console.log('User created successfully:', newUser);

      // Add to local state for immediate UI update
      setUsers(prev => [...prev, newUser]);

      // Close the modal
      setShowAddModal(false);
      setShowAddManagerModal(false);

      // Optionally refresh the user list to ensure sync
      // await loadUsers();

    } catch (error) {
      console.error('Failed to add user:', error);
      throw error; // Re-throw to let modal handle the error
    }
  };

  const handleApproveUser = async (userId, approvalData) => {
    try {
      setUsers(prev => prev.map(user =>
        user.id === userId
          ? {
              ...user,
              approval_status: 'approved',
              approved_by: user.id,
              approved_at: new Date().toISOString()
            }
          : user
      ));
      setShowApprovalModal(false);
      setSelectedUser(null);
    } catch (error) {
      console.error('Failed to approve user:', error);
      throw error;
    }
  };

  const handleRejectUser = async (userId, rejectionReason) => {
    try {
      setUsers(prev => prev.map(user =>
        user.id === userId
          ? {
              ...user,
              approval_status: 'rejected',
              rejection_reason: rejectionReason,
              rejected_at: new Date().toISOString()
            }
          : user
      ));
      setShowApprovalModal(false);
      setSelectedUser(null);
    } catch (error) {
      console.error('Failed to reject user:', error);
      throw error;
    }
  };

  const handleUserClick = (user) => {
    setSelectedUser(user);
    setShowProfileModal(true);
  };

  const handleRefreshUsers = () => {
    // Refresh user data if needed
    console.log('Refreshing user data...');
  };

  // Filter users by role
  const managers = users.filter(user => user.role === 'manager');
  const partners = users.filter(user => user.role === 'partner');

  // Apply search and status filters
  const filterUsers = (userList) => {
    return userList.filter(user => {
      const matchesSearch = searchTerm === '' ||
        user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = statusFilter === 'all' || user.status === statusFilter;

      return matchesSearch && matchesStatus;
    });
  };

  const filteredManagers = filterUsers(managers);
  const filteredPartners = filterUsers(partners);

  const openApprovalModal = (user) => {
    setSelectedUser(user);
    setShowApprovalModal(true);
  };

  const filteredUsers = users.filter(user => {
    if (activeTab === 'all') return true;
    if (activeTab === 'pending') return user.approval_status === 'pending';
    if (activeTab === 'approved') return user.approval_status === 'approved';
    if (activeTab === 'rejected') return user.approval_status === 'rejected';
    return true;
  });

  const pendingCount = users.filter(user => user.approval_status === 'pending').length;

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
            <p className="text-gray-600">Manage system users and their permissions</p>
          </div>
          <div className="flex items-center space-x-3">
            {pendingCount > 0 && (
              <div className="bg-orange-100 border border-orange-200 rounded-lg px-3 py-2">
                <div className="flex items-center">
                  <ClockIcon className="h-4 w-4 text-orange-600 mr-2" />
                  <span className="text-sm font-medium text-orange-800">
                    {pendingCount} pending approval{pendingCount !== 1 ? 's' : ''}
                  </span>
                </div>
              </div>
            )}
            <button
              onClick={() => setShowAddManagerModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 mr-3"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Manager
            </button>
            <button
              onClick={() => setShowAddModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Partner
            </button>
          </div>
        </div>
      </div>

      {/* User Type Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'managers', name: 'Managers', count: managers.length },
              { id: 'partners', name: 'Partners', count: partners.length },
              { id: 'pending', name: 'Pending Approval', count: pendingCount }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveUserTab(tab.id)}
                className={`${
                  activeUserTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`}
              >
                {tab.name}
                <span className={`ml-2 py-0.5 px-2 rounded-full text-xs ${
                  activeUserTab === tab.id ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600'
                }`}>
                  {tab.count}
                </span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="suspended">Suspended</option>
              </select>
            </div>
            <div className="text-sm text-gray-500">
              {activeUserTab === 'managers' && `${filteredManagers.length} managers`}
              {activeUserTab === 'partners' && `${filteredPartners.length} partners`}
              {activeUserTab === 'pending' && `${pendingCount} pending approval`}
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approval</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {(() => {
                let displayUsers = [];
                if (activeUserTab === 'managers') {
                  displayUsers = filteredManagers;
                } else if (activeUserTab === 'partners') {
                  displayUsers = filteredPartners;
                } else if (activeUserTab === 'pending') {
                  displayUsers = users.filter(user => user.approval_status === 'pending');
                }

                return displayUsers.map((user) => (
                  <tr
                    key={user.id}
                    className="hover:bg-gray-50 cursor-pointer"
                    onClick={() => handleUserClick(user)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                            <UserIcon className="h-5 w-5 text-gray-500" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{user.name || `${user.firstName} ${user.lastName}`}</div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.role === 'superadmin' ? 'bg-purple-100 text-purple-800' :
                        user.role === 'manager' ? 'bg-blue-100 text-blue-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {user.role}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.status === 'active' ? 'bg-green-100 text-green-800' :
                        user.status === 'inactive' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {user.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.approval_status === 'approved' ? 'bg-green-100 text-green-800' :
                        user.approval_status === 'pending' ? 'bg-orange-100 text-orange-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {user.approval_status === 'approved' && <CheckIcon className="h-3 w-3 mr-1" />}
                        {user.approval_status === 'pending' && <ClockIcon className="h-3 w-3 mr-1" />}
                        {user.approval_status === 'rejected' && <XIcon className="h-3 w-3 mr-1" />}
                        {user.approval_status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.created_at ? new Date(user.created_at).toLocaleDateString() : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {user.approval_status === 'pending' && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            openApprovalModal(user);
                          }}
                          className="text-primary hover:text-primary-dark mr-3"
                        >
                          Review
                        </button>
                      )}
                      <button
                        onClick={(e) => e.stopPropagation()}
                        className="text-gray-600 hover:text-gray-900 mr-3"
                      >
                        Edit
                      </button>
                      <button
                        onClick={(e) => e.stopPropagation()}
                        className="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ));
              })()}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Manager Modal */}
      <AddUserModal
        isOpen={showAddManagerModal}
        onClose={() => setShowAddManagerModal(false)}
        onSubmit={handleAddUser}
        userRole={userRole}
        modalType="manager"
      />

      {/* Add Partner Modal */}
      <AddUserModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSubmit={handleAddUser}
        userRole={userRole}
        modalType="partner"
      />

      {/* User Approval Modal */}
      <UserApprovalModal
        isOpen={showApprovalModal}
        onClose={() => setShowApprovalModal(false)}
        user={selectedUser}
        onApprove={handleApproveUser}
        onReject={handleRejectUser}
      />

      {/* User Profile Modal */}
      <UserProfileModal
        isOpen={showProfileModal}
        onClose={() => setShowProfileModal(false)}
        user={selectedUser}
        onRefresh={handleRefreshUsers}
      />
    </div>
  );
};

export default UserManagement;
