# 🚀 EskillVisor User Management Updates - EXACT Deployment Instructions

## 📦 Deployment Package: `user-management-updates-2025-07-18.zip`

---

## **METHOD 1: ZIP Upload (Recommended)**

### Step 1: Login to cPanel
- **URL:** Access your hosting provider's cPanel for wallistry.pk
- **Credentials:** Use your cPanel username and password

### Step 2: Open File Manager
- Click on **"File Manager"** in the Files section of cPanel

### Step 3: Navigate to Target Directory
- **EXACT PATH:** `/home9/wallistry/eskillvisor.wallistry.pk/`
- Navigate to this exact location in File Manager

### Step 4: Upload ZIP File
- **FILE TO UPLOAD:** `user-management-updates-2025-07-18.zip`
- Click **"Upload"** button in File Manager
- Select the ZIP file from your computer
- Wait for upload to complete (should show 100%)

### Step 5: Extract ZIP File
- **Right-click** on `user-management-updates-2025-07-18.zip`
- Select **"Extract"** from the context menu
- Choose **"Extract to current directory"**
- Click **"Extract Files"**

### Step 6: Overwrite Existing Files
- When prompted **"File already exists, overwrite?"**
- Choose **"Yes"** or **"Yes to All"** to overwrite existing files
- This will update all modified files with new versions

### Step 7: Clean Up
- **Delete** the ZIP file `user-management-updates-2025-07-18.zip` after extraction
- Right-click → Delete to remove the ZIP file

---

## **DATABASE CHANGES REQUIRED**

### Step 1: Access phpMyAdmin
- Go to **cPanel → phpMyAdmin**
- Click on **phpMyAdmin** in the Databases section

### Step 2: Select Database
- Select your **EskillVisor database** from the left sidebar
- (Usually named something like `wallistry_eskillvisor` or similar)

### Step 3: Open SQL Tab
- Click the **"SQL"** tab at the top of phpMyAdmin

### Step 4: Execute Database Migration
- **Copy and paste** the following SQL migration:

```sql
-- Add extended fields to companies table for enhanced company management
-- Migration: 014_add_company_extended_fields.sql
-- Date: 2025-07-18

-- Add new required fields for company registration
ALTER TABLE companies 
ADD COLUMN company_director_id INT NULL AFTER name,
ADD COLUMN registration_territory VARCHAR(255) NULL AFTER company_director_id,
ADD COLUMN ein_number VARCHAR(50) NULL AFTER registration_territory,
ADD COLUMN marketplace VARCHAR(100) NULL AFTER ein_number,
ADD COLUMN custom_marketplace VARCHAR(255) NULL AFTER marketplace,
ADD COLUMN approval_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' AFTER status,
ADD COLUMN approved_by INT NULL AFTER approval_status,
ADD COLUMN approved_at TIMESTAMP NULL AFTER approved_by,
ADD COLUMN rejected_by INT NULL AFTER approved_at,
ADD COLUMN rejected_at TIMESTAMP NULL AFTER rejected_by,
ADD COLUMN rejection_reason TEXT NULL AFTER rejected_at,
ADD INDEX idx_company_director (company_director_id),
ADD INDEX idx_registration_territory (registration_territory),
ADD INDEX idx_ein_number (ein_number),
ADD INDEX idx_marketplace (marketplace),
ADD INDEX idx_approval_status (approval_status),
ADD FOREIGN KEY (company_director_id) REFERENCES users(id) ON DELETE SET NULL,
ADD FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
ADD FOREIGN KEY (rejected_by) REFERENCES users(id) ON DELETE SET NULL;

-- Update existing companies to have approved status
UPDATE companies SET approval_status = 'approved' WHERE approval_status IS NULL OR approval_status = 'pending';
```

### Step 5: Execute Migration
- Click **"Go"** button to execute the SQL
- You should see a success message like "Query executed successfully"

---

## **FILES INCLUDED IN ZIP PACKAGE**

### NEW FILE:
```
src/components/modals/UserProfileModal.jsx
```

### UPDATED FILES:
```
src/pages/superadmin/UserManagement.jsx
src/components/modals/AddCompanyModal.jsx
src/pages/superadmin/CompanyOversight.jsx
src/pages/manager/CompanyManagement.jsx
```

---

## **POST-DEPLOYMENT VERIFICATION**

### Step 1: Clear Browser Cache
- Press **Ctrl+F5** (Windows) or **Cmd+Shift+R** (Mac)
- Or clear browser cache manually

### Step 2: Test User Management
- Navigate to **User Management** page
- ✅ Check Manager and Partner tabs work
- ✅ Click on users to open profile modal
- ✅ Verify 3 tabs in user profile (Profile, Companies, Inventory)

### Step 3: Test Company Creation
- Open a user profile
- ✅ Click "Add Company" button (Super Admin) or "Assign Company" (Manager)
- ✅ Verify new fields are present:
  - Company Name ✅
  - Company Director (dropdown) ✅
  - Registration Territory (country dropdown) ✅
  - EIN Number ✅
  - Marketplace (Amazon, Walmart, TikTok, Shopify, Others) ✅
  - Custom marketplace field (when "Others" selected) ✅

### Step 4: Test Form Validation
- ✅ Try submitting empty form - should show validation errors
- ✅ Fill required fields - should allow submission
- ✅ Select "Others" marketplace - custom field should appear

---

## **TROUBLESHOOTING**

### Issue: "Column already exists" error in database
**Solution:** The migration is safe to run multiple times. If you get this error, the columns already exist and you can ignore it.

### Issue: User Management page not loading
**Solution:** 
1. Check that `UserProfileModal.jsx` was uploaded to `src/components/modals/`
2. Clear browser cache completely
3. Check browser console for JavaScript errors

### Issue: Company modal missing new fields
**Solution:**
1. Verify `AddCompanyModal.jsx` was updated correctly
2. Clear browser cache
3. Check file permissions are 644

### Issue: Database connection errors
**Solution:**
1. Verify database migration was executed successfully
2. Check that all foreign key constraints are satisfied
3. Ensure database user has ALTER privileges

---

## **ROLLBACK PLAN**

If issues occur:

### Files Rollback:
1. Restore previous versions of updated files from backup
2. Remove `UserProfileModal.jsx` if it causes issues

### Database Rollback:
```sql
-- Only run if you need to rollback database changes
ALTER TABLE companies 
DROP FOREIGN KEY companies_ibfk_2,
DROP FOREIGN KEY companies_ibfk_3, 
DROP FOREIGN KEY companies_ibfk_4,
DROP COLUMN company_director_id,
DROP COLUMN registration_territory,
DROP COLUMN ein_number,
DROP COLUMN marketplace,
DROP COLUMN custom_marketplace,
DROP COLUMN approval_status,
DROP COLUMN approved_by,
DROP COLUMN approved_at,
DROP COLUMN rejected_by,
DROP COLUMN rejected_at,
DROP COLUMN rejection_reason;
```

---

## **DEPLOYMENT CHECKLIST**

- [ ] **1. Backup current files**
- [ ] **2. Upload ZIP to `/home9/wallistry/eskillvisor.wallistry.pk/`**
- [ ] **3. Extract ZIP file**
- [ ] **4. Overwrite existing files**
- [ ] **5. Delete ZIP file**
- [ ] **6. Run database migration in phpMyAdmin**
- [ ] **7. Clear browser cache**
- [ ] **8. Test user management functionality**
- [ ] **9. Test company creation workflow**
- [ ] **10. Verify all new features work**

---

**Deployment Package:** `user-management-updates-2025-07-18.zip`  
**Database Migration:** `014_add_company_extended_fields.sql`  
**Generated:** July 18, 2025  
**Impact:** Medium - UI/UX improvements + Database schema updates
