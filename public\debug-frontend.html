<!DOCTYPE html>
<html>
<head>
    <title>Frontend Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-result { margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 4px solid #ccc; }
        .success { background: #d4edda; border-left-color: #28a745; }
        .error { background: #f8d7da; border-left-color: #dc3545; }
        .info { background: #d1ecf1; border-left-color: #17a2b8; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; max-height: 300px; }
        .fix-section { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Frontend Debug Tool</h1>
        <p>Testing from the same domain as the frontend (http://localhost:5173)</p>
        
        <div class="fix-section">
            <h3>🚀 Quick Fixes</h3>
            <button onclick="clearStorageAndReload()">Clear Storage & Reload</button>
            <button onclick="testDirectLogin()">Test Direct Login</button>
            <button onclick="checkLocalStorage()">Check Local Storage</button>
        </div>
        
        <button onclick="testFrontendAPIs()">Test Frontend API Calls</button>
        <button onclick="testBackendDirectly()">Test Backend Directly</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost/Investment-System-eSkillVisor/backend';
        
        function addResult(title, status, message, details = null) {
            const resultsDiv = document.getElementById('results');
            const statusClass = status === 'success' ? 'success' : status === 'error' ? 'error' : 'info';
            const icon = status === 'success' ? '✅' : status === 'error' ? '❌' : 'ℹ️';
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${statusClass}`;
            resultDiv.innerHTML = `
                <h4>${icon} ${title}</h4>
                <p>${message}</p>
                ${details ? `<pre>${JSON.stringify(details, null, 2)}</pre>` : ''}
            `;
            
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function clearStorageAndReload() {
            addResult('Storage Clear', 'info', 'Clearing all browser storage...');
            
            // Clear all storage
            localStorage.clear();
            sessionStorage.clear();
            
            // Clear cookies for this domain
            document.cookie.split(";").forEach(function(c) { 
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
            });
            
            addResult('Storage Cleared', 'success', 'All storage cleared. Reloading page in 2 seconds...');
            
            setTimeout(() => {
                location.reload();
            }, 2000);
        }
        
        function checkLocalStorage() {
            clearResults();
            
            const storageInfo = {
                localStorage: {},
                sessionStorage: {},
                cookies: document.cookie
            };
            
            // Check localStorage
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                storageInfo.localStorage[key] = localStorage.getItem(key);
            }
            
            // Check sessionStorage
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                storageInfo.sessionStorage[key] = sessionStorage.getItem(key);
            }
            
            addResult('Storage Check', 'info', 'Current browser storage contents:', storageInfo);
        }
        
        async function testDirectLogin() {
            clearResults();
            addResult('Direct Login Test', 'info', 'Testing login directly...');
            
            try {
                const response = await fetch(`${API_BASE}/login.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password'
                    })
                });
                
                const responseText = await response.text();
                console.log('Login response:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    addResult('Login Parse Error', 'error', 'Invalid JSON response', {
                        responseText: responseText.substring(0, 500),
                        parseError: parseError.message
                    });
                    return;
                }
                
                if (data.success) {
                    // Store token in localStorage like the app does
                    localStorage.setItem('auth_token', data.data.access_token);
                    localStorage.setItem('user', JSON.stringify(data.data.user));
                    
                    addResult('Login Success', 'success', 'Login successful and token stored', {
                        user: data.data.user,
                        tokenStored: true
                    });
                } else {
                    addResult('Login Failed', 'error', data.message, data);
                }
                
            } catch (error) {
                addResult('Login Error', 'error', `Network error: ${error.message}`, {
                    error: error.toString()
                });
            }
        }
        
        async function testFrontendAPIs() {
            clearResults();
            addResult('Frontend API Test', 'info', 'Testing APIs the same way the frontend does...');
            
            // Check if we have a token
            const token = localStorage.getItem('auth_token');
            if (!token) {
                addResult('No Token', 'error', 'No auth token found. Run "Test Direct Login" first.');
                return;
            }
            
            try {
                // Test user profile API
                const response = await fetch(`${API_BASE}/user-profile.php?id=1`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const responseText = await response.text();
                console.log('Profile response:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    addResult('Profile Parse Error', 'error', 'Invalid JSON response', {
                        responseText: responseText.substring(0, 500),
                        parseError: parseError.message
                    });
                    return;
                }
                
                if (data.success) {
                    addResult('Profile Success', 'success', 'User profile loaded successfully', {
                        user: data.data.user,
                        hasCompanies: !!data.data.assigned_companies,
                        companiesCount: data.data.assigned_companies?.length || 0
                    });
                } else {
                    addResult('Profile Failed', 'error', data.message, data);
                }
                
            } catch (error) {
                addResult('Profile Error', 'error', `Network error: ${error.message}`, {
                    error: error.toString()
                });
            }
        }
        
        async function testBackendDirectly() {
            clearResults();
            addResult('Backend Direct Test', 'info', 'Testing backend endpoints directly...');
            
            try {
                // Test connection endpoint
                const response = await fetch(`${API_BASE}/test-connection.php`);
                const data = await response.json();
                
                if (data.success) {
                    addResult('Backend Connection', 'success', 'Backend is responding correctly', {
                        message: data.message,
                        database: data.database,
                        phpVersion: data.php_version
                    });
                } else {
                    addResult('Backend Connection', 'error', 'Backend error', data);
                }
                
            } catch (error) {
                addResult('Backend Error', 'error', `Cannot connect to backend: ${error.message}`, {
                    error: error.toString()
                });
            }
        }
        
        // Auto-run basic checks on page load
        window.onload = function() {
            addResult('Debug Tool Loaded', 'info', 'Frontend debug tool loaded from http://localhost:5173');
            
            setTimeout(() => {
                testBackendDirectly();
            }, 1000);
        };
    </script>
</body>
</html>
