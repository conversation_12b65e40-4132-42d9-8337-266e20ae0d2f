<?php
/**
 * Check existing table structure
 */

header('Content-Type: text/plain');

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

echo "Database Table Structure Check\n";
echo "==============================\n\n";

try {
    $db = Database::getInstance();
    $connection = $db->getConnection();
    
    // Show all tables
    echo "Existing tables:\n";
    $stmt = $connection->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
        echo "- $table\n";
    }
    
    echo "\n";
    
    // Check users table structure
    if (in_array('users', $tables)) {
        echo "Users table structure:\n";
        $stmt = $connection->query("DESCRIBE users");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($columns as $column) {
            echo "- {$column['Field']} ({$column['Type']}) {$column['Null']} {$column['Key']}\n";
        }
        
        echo "\nSample users data:\n";
        $stmt = $connection->query("SELECT * FROM users LIMIT 3");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($users as $user) {
            echo "- ID: {$user['id']}, Email: {$user['email']}, Role: {$user['role']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
