import React, { useState, useEffect } from 'react';
import { XIcon, UploadIcon, FileTextIcon, XCircleIcon } from 'lucide-react';
import FileUpload from '../inventory/FileUpload.jsx';

const AddCompanyModal = ({ isOpen, onClose, onSubmit, userRole = 'superadmin', currentUserId = null, targetUserId = null }) => {
  const [formData, setFormData] = useState({
    name: '',
    companyDirector: '',
    registrationTerritory: '',
    einNumber: '',
    marketplace: '',
    customMarketplace: '',
    description: '',
    industry: '',
    email: '',
    phone: '',
    address: '',
    managerId: userRole === 'manager' ? currentUserId : ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});
  const [managers, setManagers] = useState([]);
  const [partners, setPartners] = useState([]);
  const [documents, setDocuments] = useState([]);
  const [activeTab, setActiveTab] = useState('basic');

  // Countries list
  const countries = [
    'United States', 'Canada', 'United Kingdom', 'Germany', 'France', 'Italy', 'Spain', 'Netherlands',
    'Australia', 'Japan', 'South Korea', 'Singapore', 'Hong Kong', 'India', 'China', 'Brazil',
    'Mexico', 'Argentina', 'Chile', 'Colombia', 'Peru', 'South Africa', 'Nigeria', 'Kenya',
    'Egypt', 'Morocco', 'Turkey', 'Russia', 'Poland', 'Czech Republic', 'Hungary', 'Romania',
    'Greece', 'Portugal', 'Belgium', 'Switzerland', 'Austria', 'Denmark', 'Sweden', 'Norway',
    'Finland', 'Ireland', 'New Zealand', 'Thailand', 'Malaysia', 'Indonesia', 'Philippines',
    'Vietnam', 'Pakistan', 'Bangladesh', 'Sri Lanka', 'Nepal', 'Afghanistan', 'Iran', 'Iraq',
    'Saudi Arabia', 'UAE', 'Qatar', 'Kuwait', 'Bahrain', 'Oman', 'Jordan', 'Lebanon', 'Israel'
  ].sort();

  // Marketplace options
  const marketplaceOptions = [
    'Amazon',
    'Walmart',
    'TikTok',
    'Shopify',
    'Others'
  ];

  // Load managers and partners
  useEffect(() => {
    const loadUsersData = async () => {
      if (isOpen) {
        try {
          // Load managers from API
          const response = await fetch('http://localhost/Investment-System-eSkillVisor/backend/managers.php');
          if (response.ok) {
            const data = await response.json();
            if (data.success) {
              setManagers(data.data);
            }
          } else {
            console.error('Failed to fetch managers');
            setManagers([]);
          }

          // Load partners for company director dropdown (keeping mock for now)
          const mockPartners = [
            { id: '3', name: 'Bob Partner', email: '<EMAIL>' },
            { id: '4', name: 'Alice Smith', email: '<EMAIL>' },
            { id: '6', name: 'Sarah Wilson', email: '<EMAIL>' },
            { id: '7', name: 'David Brown', email: '<EMAIL>' }
          ];
          setPartners(mockPartners);
        } catch (error) {
          console.error('Failed to load users data:', error);
          setManagers([]);
          setPartners([]);
        }
      }
    };

    loadUsersData();
  }, [userRole, isOpen]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Company name is required';
    }

    if (!formData.companyDirector.trim()) {
      newErrors.companyDirector = 'Company director is required';
    }

    if (!formData.registrationTerritory.trim()) {
      newErrors.registrationTerritory = 'Registration territory is required';
    }

    if (!formData.einNumber.trim()) {
      newErrors.einNumber = 'EIN number is required';
    }

    if (!formData.marketplace.trim()) {
      newErrors.marketplace = 'Marketplace is required';
    }

    if (formData.marketplace === 'Others' && !formData.customMarketplace.trim()) {
      newErrors.customMarketplace = 'Please specify the marketplace';
    }

    // Email is optional, but if provided, must be valid
    if (formData.email.trim() && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleDocumentUpload = (file, documentType) => {
    if (file) {
      const newDocument = {
        id: Date.now(),
        file: file,
        type: documentType,
        name: file.name,
        size: file.size,
        status: 'pending'
      };
      setDocuments(prev => [...prev, newDocument]);
    }
  };

  const removeDocument = (documentId) => {
    setDocuments(prev => prev.filter(doc => doc.id !== documentId));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // First create the company
      const submissionData = {
        ...formData
      };
      const createdCompany = await onSubmit(submissionData);

      // Then upload documents if any
      if (documents.length > 0 && createdCompany && createdCompany.id) {
        await uploadDocuments(createdCompany.id);
      }

      handleClose();
    } catch (error) {
      console.error('Failed to add company:', error);
      setErrors({ submit: 'Failed to add company. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const uploadDocuments = async (companyId) => {
    if (documents.length === 0) return;

    const formData = new FormData();
    formData.append('company_id', companyId);
    formData.append('uploaded_by', currentUserId);

    documents.forEach((doc, index) => {
      formData.append('documents[]', doc.file);
    });

    try {
      const response = await fetch('http://localhost/Investment-System-eSkillVisor/backend/company-documents.php', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      if (!result.success) {
        console.error('Document upload failed:', result.message);
      }
    } catch (error) {
      console.error('Document upload error:', error);
    }
  };

  const handleClose = () => {
    setFormData({
      name: '',
      companyDirector: '',
      registrationTerritory: '',
      einNumber: '',
      marketplace: '',
      customMarketplace: '',
      description: '',
      industry: '',
      email: '',
      phone: '',
      address: '',
      managerId: userRole === 'manager' ? currentUserId : ''
    });
    setErrors({});
    setDocuments([]);
    setActiveTab('basic');
    setIsSubmitting(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={handleClose}
        ></div>

        {/* Modal */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          {/* Header */}
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Add New Company
              </h3>
              <button
                onClick={handleClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <XIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Tabs */}
            <div className="mt-4">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                  <button
                    type="button"
                    onClick={() => setActiveTab('basic')}
                    className={`${
                      activeTab === 'basic'
                        ? 'border-primary text-primary'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`}
                  >
                    Basic Information
                  </button>
                  <button
                    type="button"
                    onClick={() => setActiveTab('documents')}
                    className={`${
                      activeTab === 'documents'
                        ? 'border-primary text-primary'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`}
                  >
                    <FileTextIcon className="h-4 w-4 mr-1" />
                    Documents
                    {documents.length > 0 && (
                      <span className="ml-1 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                        {documents.length}
                      </span>
                    )}
                  </button>
                </nav>
              </div>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit}>
            <div className="px-4 pb-4 sm:px-6 sm:pb-6">
              {errors.submit && (
                <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                  {errors.submit}
                </div>
              )}

              {/* Basic Information Tab */}
              {activeTab === 'basic' && (
                <div className="space-y-4">
                  {/* Company Name */}
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                      Company Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className={`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${
                        errors.name ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="Enter company name"
                    />
                    {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                  </div>

                  {/* Company Director */}
                  <div>
                    <label htmlFor="companyDirector" className="block text-sm font-medium text-gray-700">
                      Company Director *
                    </label>
                    <select
                      id="companyDirector"
                      name="companyDirector"
                      value={formData.companyDirector}
                      onChange={handleInputChange}
                      className={`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${
                        errors.companyDirector ? 'border-red-300' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select company director</option>
                      {partners.map((partner) => (
                        <option key={partner.id} value={partner.id}>
                          {partner.name} ({partner.email})
                        </option>
                      ))}
                    </select>
                    {errors.companyDirector && <p className="mt-1 text-sm text-red-600">{errors.companyDirector}</p>}
                  </div>

                  {/* Registration Territory */}
                  <div>
                    <label htmlFor="registrationTerritory" className="block text-sm font-medium text-gray-700">
                      Company Registration Territory *
                    </label>
                    <select
                      id="registrationTerritory"
                      name="registrationTerritory"
                      value={formData.registrationTerritory}
                      onChange={handleInputChange}
                      className={`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${
                        errors.registrationTerritory ? 'border-red-300' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select country</option>
                      {countries.map((country) => (
                        <option key={country} value={country}>
                          {country}
                        </option>
                      ))}
                    </select>
                    {errors.registrationTerritory && <p className="mt-1 text-sm text-red-600">{errors.registrationTerritory}</p>}
                  </div>

                  {/* EIN Number */}
                  <div>
                    <label htmlFor="einNumber" className="block text-sm font-medium text-gray-700">
                      EIN Number *
                    </label>
                    <input
                      type="text"
                      id="einNumber"
                      name="einNumber"
                      value={formData.einNumber}
                      onChange={handleInputChange}
                      className={`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${
                        errors.einNumber ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="Enter EIN number"
                    />
                    {errors.einNumber && <p className="mt-1 text-sm text-red-600">{errors.einNumber}</p>}
                  </div>

                  {/* Marketplace */}
                  <div>
                    <label htmlFor="marketplace" className="block text-sm font-medium text-gray-700">
                      Marketplace *
                    </label>
                    <select
                      id="marketplace"
                      name="marketplace"
                      value={formData.marketplace}
                      onChange={handleInputChange}
                      className={`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${
                        errors.marketplace ? 'border-red-300' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select marketplace</option>
                      {marketplaceOptions.map((marketplace) => (
                        <option key={marketplace} value={marketplace}>
                          {marketplace}
                        </option>
                      ))}
                    </select>
                    {errors.marketplace && <p className="mt-1 text-sm text-red-600">{errors.marketplace}</p>}
                  </div>

                  {/* Custom Marketplace (if Others is selected) */}
                  {formData.marketplace === 'Others' && (
                    <div>
                      <label htmlFor="customMarketplace" className="block text-sm font-medium text-gray-700">
                        Specify Marketplace *
                      </label>
                      <input
                        type="text"
                        id="customMarketplace"
                        name="customMarketplace"
                        value={formData.customMarketplace}
                        onChange={handleInputChange}
                        className={`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${
                          errors.customMarketplace ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Enter marketplace name"
                      />
                      {errors.customMarketplace && <p className="mt-1 text-sm text-red-600">{errors.customMarketplace}</p>}
                    </div>
                  )}

                  {/* Additional Information Section */}
                  <div className="pt-6 border-t border-gray-200">
                    <h4 className="text-sm font-medium text-gray-900 mb-4">Additional Information (Optional)</h4>

                    <div className="space-y-4">
                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                          Company Email
                        </label>
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          className={`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${
                            errors.email ? 'border-red-300' : 'border-gray-300'
                          }`}
                          placeholder="<EMAIL>"
                        />
                        {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
                      </div>

                      <div>
                        <label htmlFor="industry" className="block text-sm font-medium text-gray-700">
                          Industry
                        </label>
                        <select
                          id="industry"
                          name="industry"
                          value={formData.industry}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                        >
                          <option value="">Select industry</option>
                          <option value="Technology">Technology</option>
                          <option value="Manufacturing">Manufacturing</option>
                          <option value="Retail">Retail</option>
                          <option value="Healthcare">Healthcare</option>
                          <option value="Finance">Finance</option>
                          <option value="Education">Education</option>
                          <option value="Other">Other</option>
                        </select>
                      </div>

                      <div>
                        <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                          Phone
                        </label>
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                          placeholder="+****************"
                        />
                      </div>

                      <div>
                        <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                          Address
                        </label>
                        <textarea
                          id="address"
                          name="address"
                          rows={2}
                          value={formData.address}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                          placeholder="Enter company address"
                        />
                      </div>



                      {/* Manager Assignment - Visible to SuperAdmin and Manager */}
                      <div>
                        <label htmlFor="managerId" className="block text-sm font-medium text-gray-700">
                          Assign Manager
                        </label>
                        <select
                          id="managerId"
                          name="managerId"
                          value={formData.managerId}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                          disabled={userRole === 'manager'}
                        >
                          <option value="">No manager assigned (create manager first)</option>
                          {managers.map((manager) => (
                            <option key={manager.id} value={manager.id}>
                              {manager.display_name}
                            </option>
                          ))}
                        </select>
                        {userRole === 'manager' && (
                          <p className="mt-1 text-xs text-gray-500">
                            As a manager, you are automatically assigned to companies you create.
                          </p>
                        )}
                      </div>

                      <div>
                        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                          Description
                        </label>
                        <textarea
                          id="description"
                          name="description"
                          rows={3}
                          value={formData.description}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                          placeholder="Enter company description"
                        />
                      </div>
                    </div>
                  </div>

                </div>
              )}

              {/* Documents Tab */}
              {activeTab === 'documents' && (
                <div className="space-y-6">
                  <div className="text-center">
                    <h4 className="text-lg font-medium text-gray-900 mb-2">Company Documents</h4>
                    <p className="text-sm text-gray-600 mb-6">
                      Upload required documents for company verification. These will be reviewed during the approval process.
                    </p>
                  </div>

                  {/* Document Upload Sections */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Registration Document */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h5 className="text-sm font-medium text-gray-900 mb-3">Company Registration</h5>
                      <FileUpload
                        onFileSelect={(file) => handleDocumentUpload(file, 'registration')}
                        acceptedTypes={['.pdf', '.jpg', '.jpeg', '.png']}
                        maxSize={5 * 1024 * 1024}
                        multiple={false}
                        className="mb-3"
                      />
                      <p className="text-xs text-gray-500">Upload company registration certificate</p>
                    </div>

                    {/* Tax Certificate */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h5 className="text-sm font-medium text-gray-900 mb-3">Tax Certificate</h5>
                      <FileUpload
                        onFileSelect={(file) => handleDocumentUpload(file, 'tax_certificate')}
                        acceptedTypes={['.pdf', '.jpg', '.jpeg', '.png']}
                        maxSize={5 * 1024 * 1024}
                        multiple={false}
                        className="mb-3"
                      />
                      <p className="text-xs text-gray-500">Upload tax registration certificate</p>
                    </div>

                    {/* Business License */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h5 className="text-sm font-medium text-gray-900 mb-3">Business License</h5>
                      <FileUpload
                        onFileSelect={(file) => handleDocumentUpload(file, 'business_license')}
                        acceptedTypes={['.pdf', '.jpg', '.jpeg', '.png']}
                        maxSize={5 * 1024 * 1024}
                        multiple={false}
                        className="mb-3"
                      />
                      <p className="text-xs text-gray-500">Upload business license (optional)</p>
                    </div>

                    {/* Other Documents */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h5 className="text-sm font-medium text-gray-900 mb-3">Other Documents</h5>
                      <FileUpload
                        onFileSelect={(file) => handleDocumentUpload(file, 'other')}
                        acceptedTypes={['.pdf', '.jpg', '.jpeg', '.png']}
                        maxSize={5 * 1024 * 1024}
                        multiple={false}
                        className="mb-3"
                      />
                      <p className="text-xs text-gray-500">Upload any additional documents</p>
                    </div>
                  </div>

                  {/* Uploaded Documents List */}
                  {documents.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-900 mb-3">Uploaded Documents</h5>
                      <div className="space-y-2">
                        {documents.map((doc) => (
                          <div key={doc.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center">
                              <FileTextIcon className="h-5 w-5 text-gray-400 mr-3" />
                              <div>
                                <p className="text-sm font-medium text-gray-900">{doc.name}</p>
                                <p className="text-xs text-gray-500 capitalize">{doc.type.replace('_', ' ')}</p>
                              </div>
                            </div>
                            <button
                              type="button"
                              onClick={() => removeDocument(doc.id)}
                              className="text-red-600 hover:text-red-800"
                            >
                              <XCircleIcon className="h-5 w-5" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Adding...' : 'Add Company'}
              </button>
              <button
                type="button"
                onClick={handleClose}
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddCompanyModal;
