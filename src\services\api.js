/**
 * API Service for EskillVisor Backend Integration
 */

// API URL configuration for local development
const getApiBaseUrl = () => {
  // Local development configuration
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    return 'http://localhost/Investment-System-eSkillVisor/backend';
  }

  // Production fallback (if needed)
  return 'http://localhost/Investment-System-eSkillVisor/backend';
};

const API_BASE_URL = getApiBaseUrl();

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
    this.token = localStorage.getItem('auth_token');
  }

  setToken(token) {
    this.token = token;
    if (token) {
      localStorage.setItem('auth_token', token);
    } else {
      localStorage.removeItem('auth_token');
    }
  }

  getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: this.getHeaders(),
      ...options,
    };

    try {
      const response = await fetch(url, config);

      // Check if response is ok first
      if (!response.ok) {
        // Try to get error message from response
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      // Get the response text
      const responseText = await response.text();

      // Check if response is empty
      if (!responseText.trim()) {
        throw new Error('Empty response from server');
      }

      // Try to parse as JSON
      try {
        const data = JSON.parse(responseText);
        return data;
      } catch (parseError) {
        // If JSON parsing fails, log the actual response for debugging
        console.error('JSON parse failed. Response was:', responseText);
        throw new Error(`Invalid JSON response from server`);
      }

    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  async get(endpoint) {
    return this.request(endpoint, { method: 'GET' });
  }

  async post(endpoint, data, options = {}) {
    const requestOptions = {
      method: 'POST',
      body: data instanceof FormData ? data : JSON.stringify(data),
      ...options
    };

    // Don't set Content-Type for FormData, let browser handle it
    if (!(data instanceof FormData)) {
      requestOptions.headers = {
        ...requestOptions.headers,
        'Content-Type': 'application/json'
      };
    }

    return this.request(endpoint, requestOptions);
  }

  async put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  }

  // Authentication endpoints
  async login(email, password) {
    const response = await this.post('/login.php', { email, password });
    if (response.success && response.data.access_token) {
      this.setToken(response.data.access_token);
    }
    return response;
  }

  async logout() {
    try {
      await this.post('/auth/logout');
    } finally {
      this.setToken(null);
    }
  }

  async getCurrentUser() {
    return this.get('/me.php');
  }

  async refreshToken(refreshToken) {
    const response = await this.post('/auth/refresh', { refresh_token: refreshToken });
    if (response.success && response.data.access_token) {
      this.setToken(response.data.access_token);
    }
    return response;
  }

  // User management endpoints
  async getUsers(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.get(`/users.php${queryString ? `?${queryString}` : ''}`);
  }

  async getUser(id) {
    return this.get(`/users.php?id=${id}`);
  }

  async getUserProfile(id) {
    return this.get(`/user-profile.php?id=${id}`);
  }

  async createUser(userData) {
    // Handle FormData for file uploads
    const options = {};
    if (userData instanceof FormData) {
      options.headers = {}; // Let browser set Content-Type for FormData
    }
    return this.post('/users.php', userData, options);
  }

  async updateUser(id, userData) {
    return this.put(`/users.php?id=${id}`, userData);
  }

  async updateUserProfile(id, userData) {
    return this.put(`/user-profile.php?id=${id}`, userData);
  }

  async deleteUser(id) {
    return this.delete(`/users.php?id=${id}`);
  }

  // Company management endpoints
  async getCompanies(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.get(`/companies.php${queryString ? `?${queryString}` : ''}`);
  }

  async getCompany(id) {
    return this.get(`/companies.php?id=${id}`);
  }

  async createCompany(companyData) {
    return this.post('/companies.php', companyData);
  }

  async updateCompany(id, companyData) {
    return this.put(`/companies.php?id=${id}`, companyData);
  }

  async deleteCompany(id) {
    return this.delete(`/companies.php?id=${id}`);
  }

  async assignPartner(companyId, userId) {
    return this.post(`/companies/${companyId}/partners`, { user_id: userId });
  }

  async removePartner(companyId, partnerId) {
    return this.delete(`/companies/${companyId}/partners/${partnerId}`);
  }

  // Inventory management endpoints
  async getInventory(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.get(`/inventory${queryString ? `?${queryString}` : ''}`);
  }

  async getInventoryItem(id) {
    return this.get(`/inventory/${id}`);
  }

  async createInventoryItem(itemData) {
    return this.post('/inventory', itemData);
  }

  async updateInventoryItem(id, itemData) {
    return this.put(`/inventory/${id}`, itemData);
  }

  async deleteInventoryItem(id) {
    return this.delete(`/inventory/${id}`);
  }

  async getLowStockItems() {
    return this.get('/inventory/low-stock');
  }

  async getTransactions(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.get(`/transactions${queryString ? `?${queryString}` : ''}`);
  }

  async createTransaction(transactionData) {
    return this.post('/transactions', transactionData);
  }

  // File upload endpoints
  async uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${this.baseURL}/files/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`,
      },
      body: formData,
    });

    const data = await response.json();
    if (!response.ok) {
      throw new Error(data.message || 'File upload failed');
    }

    return data;
  }

  async getUploads(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.get(`/files/uploads${queryString ? `?${queryString}` : ''}`);
  }

  async processFile(fileId) {
    return this.post(`/files/process/${fileId}`);
  }

  // Analytics endpoints
  async getDashboardData() {
    return this.get('/dashboard.php');
  }

  async getInventoryStats() {
    return this.get('/analytics/inventory-stats');
  }

  async getTrends() {
    return this.get('/analytics/trends');
  }

  async getCompanyStats() {
    return this.get('/analytics/company-stats');
  }

  async getAssignedCompanies() {
    return this.get('/companies/assigned');
  }

  async exportData(type, format = 'csv') {
    return this.get(`/analytics/export?type=${type}&format=${format}`);
  }

  // Notification endpoints
  async getNotifications(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.get(`/notifications${queryString ? `?${queryString}` : ''}`);
  }

  async markNotificationAsRead(id) {
    return this.put(`/notifications/${id}/read`);
  }

  async markAllNotificationsAsRead() {
    return this.post('/notifications/mark-all-read');
  }

  async deleteNotification(id) {
    return this.delete(`/notifications/${id}`);
  }
}

// Create and export a singleton instance
const apiService = new ApiService();
export default apiService;
