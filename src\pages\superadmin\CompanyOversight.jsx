import React, { useState, useEffect, useContext } from 'react';
import { PlusIcon, SearchIcon, CheckIcon, XIcon, ClockIcon, BuildingIcon, AlertCircleIcon, FileTextIcon } from 'lucide-react';
import { AuthContext } from '../../App.jsx';
import companyService from '../../services/companyService.js';
import AddCompanyModal from '../../components/modals/AddCompanyModal.jsx';
import CompanyApprovalModal from '../../components/modals/CompanyApprovalModal.jsx';

const CompanyOversight = () => {
  const { user, userRole } = useContext(AuthContext);
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    const loadCompanies = async () => {
      try {
        setLoading(true);
        const companiesData = await companyService.getCompanies();
        // Use real approval status from database
        setCompanies(companiesData);
      } catch (error) {
        console.error('Failed to load companies:', error);
        setCompanies([]);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      loadCompanies();
    }
  }, [user]);

  const handleAddCompany = async (companyData) => {
    try {
      const newCompany = await companyService.createCompany(companyData);
      const companyWithApproval = {
        ...newCompany,
        approval_status: userRole === 'superadmin' ? 'approved' : 'pending',
        created_by: user.id,
        created_at: new Date().toISOString(),
        documents: []
      };
      setCompanies(prev => [...prev, companyWithApproval]);
      setShowAddModal(false);
    } catch (error) {
      console.error('Failed to add company:', error);
      throw error; // Re-throw to let modal handle the error
    }
  };

  const handleApproveCompany = async (companyId, approvalData) => {
    try {
      // Call API to approve company
      await companyService.approveCompany(companyId, user.id);

      // Update local state
      setCompanies(prev => prev.map(company =>
        company.id === companyId
          ? {
              ...company,
              approval_status: 'approved',
              approved_by: user.id,
              approved_at: new Date().toISOString()
            }
          : company
      ));
      setShowApprovalModal(false);
      setSelectedCompany(null);
    } catch (error) {
      console.error('Failed to approve company:', error);
      throw error;
    }
  };

  const handleRejectCompany = async (companyId, rejectionReason) => {
    try {
      // Call API to reject company
      await companyService.rejectCompany(companyId, user.id, rejectionReason);

      // Update local state
      setCompanies(prev => prev.map(company =>
        company.id === companyId
          ? {
              ...company,
              approval_status: 'rejected',
              rejection_reason: rejectionReason,
              rejected_by: user.id,
              rejected_at: new Date().toISOString()
            }
          : company
      ));
      setShowApprovalModal(false);
      setSelectedCompany(null);
    } catch (error) {
      console.error('Failed to reject company:', error);
      throw error;
    }
  };

  const openApprovalModal = (company) => {
    setSelectedCompany(company);
    setShowApprovalModal(true);
  };

  const filteredCompanies = companies.filter(company => {
    if (activeTab === 'all') return true;
    if (activeTab === 'pending') return company.approval_status === 'pending';
    if (activeTab === 'approved') return company.approval_status === 'approved';
    if (activeTab === 'rejected') return company.approval_status === 'rejected';
    return true;
  });

  const pendingCount = companies.filter(company => company.approval_status === 'pending').length;

  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Company Oversight</h1>
          <p className="text-gray-600">Monitor and manage all companies in the system</p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Company Oversight</h1>
            <p className="text-gray-600">Monitor and manage all companies in the system</p>
          </div>
          {pendingCount > 0 && (
            <div className="bg-orange-100 border border-orange-200 rounded-lg px-3 py-2">
              <div className="flex items-center">
                <ClockIcon className="h-4 w-4 text-orange-600 mr-2" />
                <span className="text-sm font-medium text-orange-800">
                  {pendingCount} pending approval{pendingCount !== 1 ? 's' : ''}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Approval Workflow Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'all', name: 'All Companies', count: companies.length },
              { id: 'pending', name: 'Pending Approval', count: pendingCount },
              { id: 'approved', name: 'Approved', count: companies.filter(c => c.approval_status === 'approved').length },
              { id: 'rejected', name: 'Rejected', count: companies.filter(c => c.approval_status === 'rejected').length }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`}
              >
                {tab.name}
                <span className={`ml-2 py-0.5 px-2 rounded-full text-xs ${
                  activeTab === tab.id ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600'
                }`}>
                  {tab.count}
                </span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg">
        <div className="p-4 border-b border-gray-200">
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search companies..."
              className="pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary w-64"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
          {filteredCompanies.map((company) => (
            <div key={company.id} className={`border rounded-lg p-6 hover:shadow-md transition-shadow ${
              company.approval_status === 'pending' ? 'border-orange-200 bg-orange-50' :
              company.approval_status === 'rejected' ? 'border-red-200 bg-red-50' :
              'border-gray-200 bg-white'
            }`}>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <BuildingIcon className="h-5 w-5 text-gray-400 mr-2" />
                  <h3 className="text-lg font-medium text-gray-900">{company.name}</h3>
                </div>
                <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${
                  company.approval_status === 'approved' ? 'bg-green-100 text-green-800' :
                  company.approval_status === 'pending' ? 'bg-orange-100 text-orange-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {company.approval_status === 'approved' && <CheckIcon className="h-3 w-3 mr-1" />}
                  {company.approval_status === 'pending' && <ClockIcon className="h-3 w-3 mr-1" />}
                  {company.approval_status === 'rejected' && <XIcon className="h-3 w-3 mr-1" />}
                  {company.approval_status}
                </span>
              </div>

              <div className="space-y-2 text-sm text-gray-600 mb-4">
                <p><span className="font-medium">ID:</span> {company.id}</p>
                {company.company_director && (
                  <p><span className="font-medium">Director:</span> {company.company_director}</p>
                )}
                {company.registration_territory && (
                  <p><span className="font-medium">Territory:</span> {company.registration_territory}</p>
                )}
                {company.ein_number && (
                  <p><span className="font-medium">EIN:</span> {company.ein_number}</p>
                )}
                {company.marketplace && (
                  <p><span className="font-medium">Marketplace:</span> {company.marketplace === 'Others' ? company.custom_marketplace : company.marketplace}</p>
                )}
                <p><span className="font-medium">Industry:</span> {company.industry || 'Not specified'}</p>
                {company.email && (
                  <p><span className="font-medium">Email:</span> {company.email}</p>
                )}
                {company.phone && (
                  <p><span className="font-medium">Phone:</span> {company.phone}</p>
                )}
                <p><span className="font-medium">Created:</span> {new Date(company.created_at).toLocaleDateString()}</p>
                {company.created_by_name && (
                  <p><span className="font-medium">Created by:</span> {company.created_by_name} {company.created_by_lastname}</p>
                )}
                {company.partner_count > 0 && (
                  <p><span className="font-medium">Partners:</span> {company.partner_count}</p>
                )}
                {company.documents && company.documents.length > 0 && (
                  <div className="flex items-center">
                    <FileTextIcon className="h-4 w-4 mr-1" />
                    <span>{company.documents.length} document{company.documents.length !== 1 ? 's' : ''}</span>
                  </div>
                )}
              </div>

              {company.approval_status === 'pending' && (
                <div className="border-t border-gray-200 pt-4">
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-3">
                    <div className="flex items-center">
                      <AlertCircleIcon className="h-4 w-4 text-orange-600 mr-2" />
                      <span className="text-xs font-medium text-orange-800">Requires approval</span>
                    </div>
                  </div>
                  <button
                    onClick={() => openApprovalModal(company)}
                    className="w-full bg-primary text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-primary-dark transition-colors"
                  >
                    Review & Approve
                  </button>
                </div>
              )}

              {company.approval_status === 'rejected' && company.rejection_reason && (
                <div className="border-t border-gray-200 pt-4">
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                    <p className="text-xs font-medium text-red-800 mb-1">Rejection Reason:</p>
                    <p className="text-xs text-red-700">{company.rejection_reason}</p>
                  </div>
                </div>
              )}

              {company.approval_status === 'approved' && (
                <div className="mt-4 flex space-x-2">
                  <button className="text-primary hover:text-primary-dark text-sm">View Details</button>
                  <button className="text-gray-600 hover:text-gray-900 text-sm">Edit</button>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Add Company Modal */}
      <AddCompanyModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSubmit={handleAddCompany}
        userRole={userRole}
        currentUserId={user?.id}
      />

      {/* Company Approval Modal */}
      <CompanyApprovalModal
        isOpen={showApprovalModal}
        onClose={() => setShowApprovalModal(false)}
        company={selectedCompany}
        onApprove={handleApproveCompany}
        onReject={handleRejectCompany}
      />
    </div>
  );
};

export default CompanyOversight;
