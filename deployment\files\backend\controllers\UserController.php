<?php
/**
 * User Management Controller
 */

class UserController extends Controller {
    
    public function index() {
        if (!$this->requireManager()) {
            return;
        }
        
        try {
            $pagination = $this->getPaginationParams();
            $search = $this->getQueryParam('search');
            $role = $this->getQueryParam('role');
            $status = $this->getQueryParam('status');
            
            $userModel = new User();
            
            // Build query
            $where = "1=1";
            $params = [];
            
            if ($search) {
                $where .= " AND (name LIKE :search OR email LIKE :search)";
                $params['search'] = "%{$search}%";
            }
            
            if ($role) {
                $where .= " AND role = :role";
                $params['role'] = $role;
            }
            
            if ($status) {
                $where .= " AND status = :status";
                $params['status'] = $status;
            }
            
            $result = $userModel->paginate(
                $pagination['page'],
                $pagination['limit'],
                'created_at',
                'DESC',
                $where,
                $params
            );
            
            Response::paginated($result['data'], $result['total'], $result['page'], $result['limit']);
        } catch (Exception $e) {
            logError('Get users failed: ' . $e->getMessage());
            Response::error('Failed to get users', 500);
        }
    }
    
    public function show($params) {
        if (!$this->requireManager()) {
            return;
        }
        
        try {
            $userModel = new User();
            $user = $userModel->find($params['id']);
            
            if (!$user) {
                Response::notFound('User not found');
                return;
            }
            
            // Get assigned companies for partners
            if ($user['role'] === 'partner') {
                $user['assigned_companies'] = $userModel->getUserCompanies($user['id']);
            }
            
            Response::success($user);
        } catch (Exception $e) {
            logError('Get user failed: ' . $e->getMessage());
            Response::error('Failed to get user', 500);
        }
    }
    
    public function getProfile($params) {
        if (!$this->requireAuth()) {
            return;
        }
        
        try {
            $userModel = new User();
            $companyModel = new Company();
            
            $user = $userModel->find($params['id']);
            
            if (!$user) {
                Response::notFound('User not found');
                return;
            }
            
            // Check access permissions
            if ($this->user['role'] !== 'superadmin' && 
                $this->user['role'] !== 'manager' && 
                $this->user['id'] != $params['id']) {
                Response::forbidden('Access denied');
                return;
            }
            
            // Remove sensitive data
            unset($user['password']);
            
            // Get user's companies
            $user['companies'] = $companyModel->getUserCompanies($params['id']);
            
            // Get user's inventory summary (mock data for now)
            $user['inventory_summary'] = [
                ['category' => 'Electronics', 'items' => 12, 'value' => 15000],
                ['category' => 'Clothing', 'items' => 8, 'value' => 3500],
                ['category' => 'Books', 'items' => 15, 'value' => 750]
            ];
            
            Response::success($user);
        } catch (Exception $e) {
            logError('Get user profile failed: ' . $e->getMessage());
            Response::error('Failed to get user profile', 500);
        }
    }
    
    public function store() {
        if (!$this->requireManager()) {
            return;
        }
        
        $data = $this->getRequestData();
        
        $rules = [
            'firstName' => 'required|max:100',
            'lastName' => 'required|max:100',
            'email' => 'required|email|max:255',
            'mobile' => 'max:20',
            'address' => 'max:500',
            'businessModel' => 'max:100',
            'role' => 'required|in:manager,partner',
            'password' => 'required|min:8',
            'status' => 'in:active,inactive'
        ];
        
        if (!$this->validateAndRespond($data, $rules)) {
            return;
        }
        
        try {
            $userModel = new User();
            
            // Check if email already exists
            if ($userModel->emailExists($data['email'])) {
                Response::error('Email already exists', 400);
                return;
            }
            
            // Role-based validation
            if ($this->user['role'] === 'manager' && $data['role'] !== 'partner') {
                Response::error('Managers can only create partner accounts', 403);
                return;
            }
            
            // Prepare user data
            $userData = [
                'firstName' => $data['firstName'],
                'lastName' => $data['lastName'],
                'name' => $data['firstName'] . ' ' . $data['lastName'],
                'email' => $data['email'],
                'mobile' => $data['mobile'] ?? '',
                'address' => $data['address'] ?? '',
                'businessModel' => $data['businessModel'] ?? '',
                'role' => $data['role'],
                'password' => password_hash($data['password'], PASSWORD_DEFAULT),
                'status' => $data['status'] ?? 'active',
                'created_by' => $this->user['id'],
                'approval_status' => $this->user['role'] === 'superadmin' ? 'approved' : 'pending'
            ];
            
            // If super admin is creating, auto-approve
            if ($this->user['role'] === 'superadmin') {
                $userData['approved_by'] = $this->user['id'];
                $userData['approved_at'] = date('Y-m-d H:i:s');
            }
            
            $user = $userModel->create($userData);
            
            // Remove password from response
            unset($user['password']);
            
            $this->logActivity('create', 'user', $user['id'], $userData);
            
            Response::created($user, 'User created successfully');
        } catch (Exception $e) {
            logError('Create user failed: ' . $e->getMessage());
            Response::error('Failed to create user', 500);
        }
    }
    
    public function update($params) {
        if (!$this->requireManager()) {
            return;
        }
        
        $data = $this->getRequestData();
        
        $rules = [
            'firstName' => 'max:100',
            'lastName' => 'max:100',
            'email' => 'email|max:255',
            'mobile' => 'max:20',
            'address' => 'max:500',
            'businessModel' => 'max:100',
            'role' => 'in:manager,partner',
            'status' => 'in:active,inactive,suspended'
        ];
        
        if (!$this->validateAndRespond($data, $rules)) {
            return;
        }
        
        try {
            $userModel = new User();
            $user = $userModel->find($params['id']);
            
            if (!$user) {
                Response::notFound('User not found');
                return;
            }
            
            // Check if email already exists (excluding current user)
            if (isset($data['email']) && $userModel->emailExists($data['email'], $params['id'])) {
                Response::error('Email already exists', 400);
                return;
            }
            
            // Role-based validation
            if ($this->user['role'] === 'manager' && isset($data['role']) && $data['role'] !== 'partner') {
                Response::error('Managers can only manage partner accounts', 403);
                return;
            }
            
            // Prepare update data
            $updateData = [];
            $allowedFields = ['firstName', 'lastName', 'email', 'mobile', 'address', 'businessModel', 'role', 'status'];
            
            foreach ($data as $key => $value) {
                if (in_array($key, $allowedFields)) {
                    $updateData[$key] = $value;
                }
            }
            
            // Update name if firstName or lastName changed
            if (isset($data['firstName']) || isset($data['lastName'])) {
                $firstName = $data['firstName'] ?? $user['firstName'];
                $lastName = $data['lastName'] ?? $user['lastName'];
                $updateData['name'] = $firstName . ' ' . $lastName;
            }
            
            $updatedUser = $userModel->update($params['id'], $updateData);
            
            // Remove password from response
            unset($updatedUser['password']);
            
            $this->logActivity('update', 'user', $params['id'], $updateData);
            
            Response::success($updatedUser, 'User updated successfully');
        } catch (Exception $e) {
            logError('Update user failed: ' . $e->getMessage());
            Response::error('Failed to update user', 500);
        }
    }
    
    public function approve($params) {
        if (!$this->requireSuperAdmin()) {
            return;
        }
        
        $data = $this->getRequestData();
        
        try {
            $userModel = new User();
            $user = $userModel->find($params['id']);
            
            if (!$user) {
                Response::notFound('User not found');
                return;
            }
            
            if ($user['approval_status'] !== 'pending') {
                Response::error('User is not pending approval', 400);
                return;
            }
            
            $updateData = [
                'approval_status' => 'approved',
                'approved_by' => $this->user['id'],
                'approved_at' => date('Y-m-d H:i:s'),
                'rejection_reason' => null,
                'rejected_at' => null
            ];
            
            $updatedUser = $userModel->update($params['id'], $updateData);
            
            // Remove password from response
            unset($updatedUser['password']);
            
            $this->logActivity('approve', 'user', $params['id'], $updateData);
            
            Response::success($updatedUser, 'User approved successfully');
        } catch (Exception $e) {
            logError('Approve user failed: ' . $e->getMessage());
            Response::error('Failed to approve user', 500);
        }
    }
    
    public function reject($params) {
        if (!$this->requireSuperAdmin()) {
            return;
        }
        
        $data = $this->getRequestData();
        
        $rules = [
            'rejectionReason' => 'required|max:1000'
        ];
        
        if (!$this->validateAndRespond($data, $rules)) {
            return;
        }
        
        try {
            $userModel = new User();
            $user = $userModel->find($params['id']);
            
            if (!$user) {
                Response::notFound('User not found');
                return;
            }
            
            if ($user['approval_status'] !== 'pending') {
                Response::error('User is not pending approval', 400);
                return;
            }
            
            $updateData = [
                'approval_status' => 'rejected',
                'rejected_by' => $this->user['id'],
                'rejected_at' => date('Y-m-d H:i:s'),
                'rejection_reason' => $data['rejectionReason'],
                'approved_by' => null,
                'approved_at' => null
            ];
            
            $updatedUser = $userModel->update($params['id'], $updateData);
            
            // Remove password from response
            unset($updatedUser['password']);
            
            $this->logActivity('reject', 'user', $params['id'], $updateData);
            
            Response::success($updatedUser, 'User rejected successfully');
        } catch (Exception $e) {
            logError('Reject user failed: ' . $e->getMessage());
            Response::error('Failed to reject user', 500);
        }
    }
    
    public function delete($params) {
        if (!$this->requireSuperAdmin()) {
            return;
        }
        
        try {
            $userModel = new User();
            $user = $userModel->find($params['id']);
            
            if (!$user) {
                Response::notFound('User not found');
                return;
            }
            
            if ($user['role'] === 'superadmin') {
                Response::error('Cannot delete super admin user', 400);
                return;
            }
            
            // Check if user has associated data
            $companyCount = $userModel->getUserCompanyCount($params['id']);
            if ($companyCount > 0) {
                Response::error('Cannot delete user with associated companies', 400);
                return;
            }
            
            $userModel->delete($params['id']);
            
            $this->logActivity('delete', 'user', $params['id'], ['name' => $user['name']]);
            
            Response::success(null, 'User deleted successfully');
        } catch (Exception $e) {
            logError('Delete user failed: ' . $e->getMessage());
            Response::error('Failed to delete user', 500);
        }
    }
}
