<?php
/**
 * Backend Deployment Script for wallistry.pk
 * This script prepares the backend for production deployment
 */

echo "EskillVisor Backend Deployment for wallistry.pk\n";
echo "===============================================\n\n";

// Check if we're in the right directory
if (!file_exists('backend/config/config.example.php')) {
    echo "❌ Error: Please run this script from the project root directory\n";
    exit(1);
}

// Step 1: Create production config files
echo "📁 Creating production configuration files...\n";

// Copy production config
if (file_exists('backend/config/config.production.php')) {
    if (!file_exists('backend/config/config.php')) {
        copy('backend/config/config.production.php', 'backend/config/config.php');
        echo "✅ Created backend/config/config.php\n";
    } else {
        echo "⚠️  backend/config/config.php already exists\n";
    }
} else {
    echo "❌ backend/config/config.production.php not found\n";
    exit(1);
}

// Copy production database config
if (file_exists('backend/config/database.production.php')) {
    if (!file_exists('backend/config/database.php')) {
        copy('backend/config/database.production.php', 'backend/config/database.php');
        echo "✅ Created backend/config/database.php\n";
    } else {
        echo "⚠️  backend/config/database.php already exists\n";
    }
} else {
    echo "❌ backend/config/database.production.php not found\n";
    exit(1);
}

// Step 2: Create necessary directories
echo "\n📂 Creating necessary directories...\n";

$directories = [
    'backend/uploads',
    'backend/logs'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "✅ Created directory: $dir\n";
    } else {
        echo "✅ Directory exists: $dir\n";
    }
}

// Step 3: Create .htaccess files for security
echo "\n🔒 Creating security files...\n";

// Backend .htaccess
$backendHtaccess = 'backend/.htaccess';
if (!file_exists($backendHtaccess)) {
    $htaccessContent = <<<EOT
# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Enable CORS for API
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"

# Handle preflight requests
RewriteEngine On
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# Redirect all requests to index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Deny access to sensitive files
<Files "*.php">
    <RequireAll>
        Require all granted
    </RequireAll>
</Files>

<FilesMatch "\.(env|log|sql|md)$">
    Require all denied
</FilesMatch>
EOT;
    
    file_put_contents($backendHtaccess, $htaccessContent);
    echo "✅ Created backend/.htaccess\n";
} else {
    echo "✅ backend/.htaccess already exists\n";
}

// Uploads .htaccess
$uploadsHtaccess = 'backend/uploads/.htaccess';
if (!file_exists($uploadsHtaccess)) {
    $uploadsHtaccessContent = <<<EOT
# Deny direct access to uploaded files
Require all denied

# Allow only specific file types
<FilesMatch "\.(jpg|jpeg|png|gif|pdf|xlsx|xls|csv)$">
    Require all granted
</FilesMatch>
EOT;
    
    file_put_contents($uploadsHtaccess, $uploadsHtaccessContent);
    echo "✅ Created backend/uploads/.htaccess\n";
} else {
    echo "✅ backend/uploads/.htaccess already exists\n";
}

// Step 4: Create deployment info file
echo "\n📋 Creating deployment information...\n";

$deploymentInfo = [
    'deployment_date' => date('Y-m-d H:i:s'),
    'version' => '1.0.0',
    'environment' => 'production',
    'domain' => 'wallistry.pk',
    'api_url' => 'https://wallistry.pk/api',
    'database' => 'wallistry_eskillvisor_db'
];

file_put_contents('backend/deployment.json', json_encode($deploymentInfo, JSON_PRETTY_PRINT));
echo "✅ Created deployment.json\n";

// Step 5: Create upload instructions
echo "\n📤 Creating upload instructions...\n";

$uploadInstructions = <<<EOT
# Backend Deployment Instructions for wallistry.pk

## Files to Upload to cPanel

Upload the entire `backend` folder to your cPanel public_html directory as `api`:

```
public_html/
├── api/                    # Upload backend folder here and rename to 'api'
│   ├── config/
│   │   ├── config.php
│   │   └── database.php
│   ├── controllers/
│   ├── core/
│   ├── models/
│   ├── services/
│   ├── migrations/
│   ├── uploads/
│   ├── logs/
│   ├── index.php
│   ├── install.php
│   └── .htaccess
```

## Database Setup

1. Create database in cPanel:
   - Database name: wallistry_eskillvisor_db
   - Username: wallistry_eskill
   - Password: EskillVisor2024!

2. Run installation:
   - Visit: https://wallistry.pk/api/install.php

## Testing

Test the API:
- https://wallistry.pk/api/test

Expected response:
```json
{
  "success": true,
  "data": {
    "message": "API endpoint is working!",
    "timestamp": "2024-01-07T10:30:00+00:00"
  }
}
```

## Security Notes

- Change database password in production
- Update JWT secret key
- Configure SMTP settings for email
- Set up SSL certificate
- Enable error logging
EOT;

file_put_contents('BACKEND_UPLOAD_INSTRUCTIONS.md', $uploadInstructions);
echo "✅ Created BACKEND_UPLOAD_INSTRUCTIONS.md\n";

echo "\n🎉 Backend deployment preparation complete!\n\n";
echo "Next steps:\n";
echo "1. Upload the 'backend' folder to cPanel as 'api'\n";
echo "2. Create the database in cPanel\n";
echo "3. Run the installation script\n";
echo "4. Test the API endpoints\n\n";
echo "For detailed instructions, see: BACKEND_UPLOAD_INSTRUCTIONS.md\n";
?>
