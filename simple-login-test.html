<!DOCTYPE html>
<html>
<head>
    <title>Simple Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        input { padding: 8px; margin: 5px; width: 200px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .result { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background: #d4edda; }
        .error { background: #f8d7da; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Simple Login Test</h1>
    
    <div class="form-group">
        <label>Email:</label>
        <input type="email" id="email" value="<EMAIL>">
    </div>
    
    <div class="form-group">
        <label>Password:</label>
        <input type="password" id="password" value="password">
    </div>
    
    <button onclick="testLogin()">Test Login</button>
    <button onclick="testCurrentUser()">Test Current User (after login)</button>
    
    <div id="result"></div>
    
    <script>
        let currentToken = null;
        
        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                console.log('Testing login...');
                
                const response = await fetch('http://localhost/Investment-System-eSkillVisor/backend/login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': 'http://localhost:5173'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                console.log('Response status:', response.status);
                
                const responseText = await response.text();
                console.log('Raw response:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error(`JSON Parse Error: ${parseError.message}. Response: ${responseText}`);
                }
                
                if (data.success) {
                    currentToken = data.data.access_token;
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>✅ Login Successful!</h3>
                        <p><strong>Token:</strong> ${currentToken.substring(0, 20)}...</p>
                        <p><strong>User:</strong> ${JSON.stringify(data.data.user, null, 2)}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h3>❌ Login Failed</h3>
                        <p><strong>Message:</strong> ${data.message}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
                
            } catch (error) {
                console.error('Login test failed:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ Login Test Failed</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }
        
        async function testCurrentUser() {
            const resultDiv = document.getElementById('result');
            
            if (!currentToken) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '<h3>❌ No token available. Please login first.</h3>';
                return;
            }
            
            try {
                console.log('Testing getCurrentUser...');
                
                const response = await fetch('http://localhost/Investment-System-eSkillVisor/backend/me.php', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Origin': 'http://localhost:5173'
                    }
                });
                
                console.log('Response status:', response.status);
                
                const responseText = await response.text();
                console.log('Raw response:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error(`JSON Parse Error: ${parseError.message}. Response: ${responseText}`);
                }
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>✅ Get Current User Successful!</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h3>❌ Get Current User Failed</h3>
                        <p><strong>Message:</strong> ${data.message}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
                
            } catch (error) {
                console.error('getCurrentUser test failed:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ Get Current User Test Failed</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
