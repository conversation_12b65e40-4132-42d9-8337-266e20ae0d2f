<?php
/**
 * Quick Setup Script for Local Development
 * Creates default users with proper passwords
 */

header('Content-Type: text/plain');

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

echo "EskillVisor Quick Setup\n";
echo "======================\n\n";

try {
    $db = Database::getInstance();
    $connection = $db->getConnection();
    
    echo "✓ Database connection successful\n\n";
    
    // Check if users table exists
    $stmt = $connection->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "Creating users table...\n";
        $createUsersTable = "
        CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHA<PERSON>(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            role ENUM('superadmin', 'manager', 'partner') NOT NULL,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        $connection->exec($createUsersTable);
        echo "✓ Users table created\n";
    } else {
        echo "✓ Users table already exists\n";
    }
    
    // Create default users
    echo "\nCreating default users...\n";
    
    $defaultUsers = [
        [
            'email' => '<EMAIL>',
            'password' => password_hash('password', PASSWORD_DEFAULT),
            'first_name' => 'System',
            'last_name' => 'Administrator',
            'role' => 'superadmin'
        ],
        [
            'email' => '<EMAIL>',
            'password' => password_hash('password', PASSWORD_DEFAULT),
            'first_name' => 'System',
            'last_name' => 'Manager',
            'role' => 'manager'
        ],
        [
            'email' => '<EMAIL>',
            'password' => password_hash('password', PASSWORD_DEFAULT),
            'first_name' => 'System',
            'last_name' => 'Partner',
            'role' => 'partner'
        ]
    ];
    
    foreach ($defaultUsers as $user) {
        // Check if user already exists
        $stmt = $connection->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$user['email']]);
        
        if ($stmt->rowCount() == 0) {
            // Insert new user
            $stmt = $connection->prepare("
                INSERT INTO users (email, password, first_name, last_name, role, status) 
                VALUES (?, ?, ?, ?, ?, 'active')
            ");
            $stmt->execute([
                $user['email'],
                $user['password'],
                $user['first_name'],
                $user['last_name'],
                $user['role']
            ]);
            echo "✓ Created user: {$user['email']} ({$user['role']})\n";
        } else {
            // Update existing user password
            $stmt = $connection->prepare("UPDATE users SET password = ? WHERE email = ?");
            $stmt->execute([$user['password'], $user['email']]);
            echo "✓ Updated user: {$user['email']} ({$user['role']})\n";
        }
    }
    
    // Create basic companies table if it doesn't exist
    $stmt = $connection->query("SHOW TABLES LIKE 'companies'");
    if ($stmt->rowCount() == 0) {
        echo "\nCreating companies table...\n";
        $createCompaniesTable = "
        CREATE TABLE companies (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255),
            phone VARCHAR(50),
            address TEXT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        $connection->exec($createCompaniesTable);
        echo "✓ Companies table created\n";
    }
    
    // Create basic inventory_items table if it doesn't exist
    $stmt = $connection->query("SHOW TABLES LIKE 'inventory_items'");
    if ($stmt->rowCount() == 0) {
        echo "Creating inventory_items table...\n";
        $createInventoryTable = "
        CREATE TABLE inventory_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            quantity INT DEFAULT 0,
            price DECIMAL(10,2) DEFAULT 0.00,
            company_id INT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        $connection->exec($createInventoryTable);
        echo "✓ Inventory items table created\n";
    }
    
    echo "\n======================\n";
    echo "✅ Quick setup completed successfully!\n\n";
    echo "Default login credentials:\n";
    echo "- Admin: <EMAIL> / password\n";
    echo "- Manager: <EMAIL> / password\n";
    echo "- Partner: <EMAIL> / password\n\n";
    echo "You can now test the login at: http://localhost:5173\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
