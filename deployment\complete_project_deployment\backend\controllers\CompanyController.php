<?php
/**
 * Company Controller
 */

class CompanyController extends BaseController {
    
    public function index() {
        if (!$this->requireAuth()) {
            return;
        }
        
        try {
            $companyModel = new Company();
            $page = (int)($_GET['page'] ?? 1);
            $limit = (int)($_GET['limit'] ?? 10);
            $search = $_GET['search'] ?? '';
            $status = $_GET['status'] ?? '';
            $approval_status = $_GET['approval_status'] ?? '';
            
            $result = $companyModel->getPaginated($page, $limit, $search, $status, $approval_status);
            
            Response::paginated($result['data'], $result['total'], $result['page'], $result['limit']);
        } catch (Exception $e) {
            logError('Get companies failed: ' . $e->getMessage());
            Response::error('Failed to get companies', 500);
        }
    }
    
    public function store() {
        if (!$this->requireManager()) {
            return;
        }
        
        $data = $this->getRequestData();
        
        $rules = [
            'name' => 'required|max:255',
            'companyDirector' => 'required|integer',
            'registrationTerritory' => 'required|max:255',
            'einNumber' => 'required|max:50',
            'marketplace' => 'required|max:100',
            'customMarketplace' => 'max:255',
            'description' => 'max:1000',
            'industry' => 'max:100',
            'website' => 'max:255',
            'email' => 'email|max:255',
            'phone' => 'max:50',
            'address' => 'max:1000',
            'status' => 'in:active,inactive'
        ];
        
        if (!$this->validateAndRespond($data, $rules)) {
            return;
        }
        
        // Validate that company director exists and is a partner
        if (!empty($data['companyDirector'])) {
            $userModel = new User();
            $director = $userModel->find($data['companyDirector']);
            if (!$director || $director['role'] !== 'partner') {
                Response::error('Invalid company director. Must be a partner.', 400);
                return;
            }
        }
        
        // Validate custom marketplace if "Others" is selected
        if ($data['marketplace'] === 'Others' && empty($data['customMarketplace'])) {
            Response::error('Custom marketplace is required when "Others" is selected.', 400);
            return;
        }
        
        try {
            $companyModel = new Company();
            
            // Prepare data for insertion
            $companyData = [
                'name' => $data['name'],
                'company_director_id' => $data['companyDirector'],
                'registration_territory' => $data['registrationTerritory'],
                'ein_number' => $data['einNumber'],
                'marketplace' => $data['marketplace'],
                'custom_marketplace' => $data['marketplace'] === 'Others' ? $data['customMarketplace'] : null,
                'description' => $data['description'] ?? '',
                'industry' => $data['industry'] ?? '',
                'website' => $data['website'] ?? '',
                'email' => $data['email'] ?? '',
                'phone' => $data['phone'] ?? '',
                'address' => $data['address'] ?? '',
                'created_by' => $this->user['id'],
                'status' => $data['status'] ?? 'active',
                'approval_status' => $this->user['role'] === 'superadmin' ? 'approved' : 'pending'
            ];
            
            // If super admin is creating, auto-approve
            if ($this->user['role'] === 'superadmin') {
                $companyData['approved_by'] = $this->user['id'];
                $companyData['approved_at'] = date('Y-m-d H:i:s');
            }
            
            $company = $companyModel->create($companyData);
            
            $this->logActivity('create', 'company', $company['id'], $companyData);
            
            Response::created($company, 'Company created successfully');
        } catch (Exception $e) {
            logError('Create company failed: ' . $e->getMessage());
            Response::error('Failed to create company', 500);
        }
    }
    
    public function approve($params) {
        if (!$this->requireSuperAdmin()) {
            return;
        }
        
        $data = $this->getRequestData();
        
        try {
            $companyModel = new Company();
            $company = $companyModel->find($params['id']);
            
            if (!$company) {
                Response::notFound('Company not found');
                return;
            }
            
            if ($company['approval_status'] !== 'pending') {
                Response::error('Company is not pending approval', 400);
                return;
            }
            
            $updateData = [
                'approval_status' => 'approved',
                'approved_by' => $this->user['id'],
                'approved_at' => date('Y-m-d H:i:s'),
                'rejection_reason' => null,
                'rejected_at' => null
            ];
            
            // Add manager assignment if provided
            if (!empty($data['managerId'])) {
                $updateData['manager_id'] = $data['managerId'];
            }
            
            $updatedCompany = $companyModel->update($params['id'], $updateData);
            
            $this->logActivity('approve', 'company', $params['id'], $updateData);
            
            Response::success($updatedCompany, 'Company approved successfully');
        } catch (Exception $e) {
            logError('Approve company failed: ' . $e->getMessage());
            Response::error('Failed to approve company', 500);
        }
    }
    
    public function reject($params) {
        if (!$this->requireSuperAdmin()) {
            return;
        }
        
        $data = $this->getRequestData();
        
        $rules = [
            'rejectionReason' => 'required|max:1000'
        ];
        
        if (!$this->validateAndRespond($data, $rules)) {
            return;
        }
        
        try {
            $companyModel = new Company();
            $company = $companyModel->find($params['id']);
            
            if (!$company) {
                Response::notFound('Company not found');
                return;
            }
            
            if ($company['approval_status'] !== 'pending') {
                Response::error('Company is not pending approval', 400);
                return;
            }
            
            $updateData = [
                'approval_status' => 'rejected',
                'rejected_by' => $this->user['id'],
                'rejected_at' => date('Y-m-d H:i:s'),
                'rejection_reason' => $data['rejectionReason'],
                'approved_by' => null,
                'approved_at' => null
            ];
            
            $updatedCompany = $companyModel->update($params['id'], $updateData);
            
            $this->logActivity('reject', 'company', $params['id'], $updateData);
            
            Response::success($updatedCompany, 'Company rejected successfully');
        } catch (Exception $e) {
            logError('Reject company failed: ' . $e->getMessage());
            Response::error('Failed to reject company', 500);
        }
    }
}
