<?php
// Enable CORS for custom domain
$allowedOrigins = [
    'https://eskillvisor.wallistry.pk',
    'https://wallistry.pk',
    'https://www.wallistry.pk',
    'https://inventory-system-e-skill-visor.vercel.app',
    'http://localhost:5173',
    'http://localhost:3000'
];

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, $allowedOrigins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    header('Access-Control-Allow-Origin: https://eskillvisor.wallistry.pk');
}

header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

try {
    $db = Database::getInstance();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get all companies
        $companies = $db->fetchAll("SELECT * FROM companies WHERE status = 'active' ORDER BY name");
        
        echo json_encode([
            'success' => true,
            'data' => $companies
        ]);
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Create new company
        $input = json_decode(file_get_contents('php://input'), true);
        
        $name = $input['name'] ?? '';
        $description = $input['description'] ?? '';
        $industry = $input['industry'] ?? '';
        $email = $input['email'] ?? '';
        $phone = $input['phone'] ?? '';
        $managerId = $input['managerId'] ?? null;
        
        if (empty($name) || empty($email) || empty($industry)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Name, email, and industry are required']);
            exit();
        }
        
        $result = $db->execute(
            "INSERT INTO companies (name, description, industry, email, phone, manager_id, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())",
            [$name, $description, $industry, $email, $phone, $managerId]
        );
        
        if ($result) {
            $companyId = $db->getLastInsertId();
            $company = $db->fetchOne("SELECT * FROM companies WHERE id = ?", [$companyId]);
            
            echo json_encode([
                'success' => true,
                'data' => $company,
                'message' => 'Company created successfully'
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to create company']);
        }
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
