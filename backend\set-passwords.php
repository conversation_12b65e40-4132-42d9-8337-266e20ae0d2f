<?php
/**
 * Set passwords for existing users
 */

header('Content-Type: text/plain');

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

echo "Setting User Passwords\n";
echo "=====================\n\n";

try {
    $db = Database::getInstance();
    $connection = $db->getConnection();
    
    // Set password for all existing users
    $defaultPassword = 'password';
    $hashedPassword = password_hash($defaultPassword, PASSWORD_DEFAULT);
    
    $users = [
        '<EMAIL>',
        '<EMAIL>', 
        '<EMAIL>'
    ];
    
    foreach ($users as $email) {
        $stmt = $connection->prepare("UPDATE users SET password_hash = ? WHERE email = ?");
        $result = $stmt->execute([$hashedPassword, $email]);
        
        if ($result) {
            echo "✓ Password set for: $email\n";
        } else {
            echo "✗ Failed to set password for: $email\n";
        }
    }
    
    echo "\n======================\n";
    echo "✅ Passwords updated successfully!\n\n";
    echo "All users now have password: 'password'\n";
    echo "You can now login at: http://localhost:5173\n\n";
    echo "Login credentials:\n";
    echo "- <EMAIL> / password\n";
    echo "- <EMAIL> / password\n";
    echo "- <EMAIL> / password\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}
?>
