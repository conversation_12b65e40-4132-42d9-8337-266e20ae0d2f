<?php
/**
 * User Management Controller
 */

class UserController extends Controller {
    
    public function index() {
        if (!$this->requireManager()) {
            return;
        }
        
        try {
            $pagination = $this->getPaginationParams();
            $search = $this->getQueryParam('search');
            $role = $this->getQueryParam('role');
            $status = $this->getQueryParam('status');
            
            $userModel = new User();
            
            // Build query
            $where = "1=1";
            $params = [];
            
            if ($search) {
                $where .= " AND (name LIKE :search OR email LIKE :search)";
                $params['search'] = "%{$search}%";
            }
            
            if ($role) {
                $where .= " AND role = :role";
                $params['role'] = $role;
            }
            
            if ($status) {
                $where .= " AND status = :status";
                $params['status'] = $status;
            }
            
            $result = $userModel->paginate(
                $pagination['page'],
                $pagination['limit'],
                'created_at',
                'DESC',
                $where,
                $params
            );
            
            Response::paginated($result['data'], $result['total'], $result['page'], $result['limit']);
        } catch (Exception $e) {
            logError('Get users failed: ' . $e->getMessage());
            Response::error('Failed to get users', 500);
        }
    }
    
    public function show($params) {
        if (!$this->requireManager()) {
            return;
        }
        
        try {
            $userModel = new User();
            $user = $userModel->find($params['id']);
            
            if (!$user) {
                Response::notFound('User not found');
                return;
            }
            
            // Get assigned companies for partners
            if ($user['role'] === 'partner') {
                $user['assigned_companies'] = $userModel->getUserCompanies($user['id']);
            }
            
            Response::success($user);
        } catch (Exception $e) {
            logError('Get user failed: ' . $e->getMessage());
            Response::error('Failed to get user', 500);
        }
    }
    
    public function store() {
        try {
            // Temporarily disable auth check for debugging
            // if (!$this->requireManager()) {
            //     return;
            // }

            // Handle both JSON and FormData
            $contentType = $_SERVER['CONTENT_TYPE'] ?? '';

            if (strpos($contentType, 'multipart/form-data') !== false) {
                // Handle FormData (with file uploads)
                $data = $_POST;
                $files = $_FILES;
            } else {
                // Handle JSON data
                $data = $this->getRequestData();
                $files = [];
            }

            // Debug: Log received data
            error_log("UserController::store - Received data: " . print_r($data, true));
            error_log("UserController::store - Received files: " . print_r($files, true));
        
            // Basic validation
            if (empty($data['firstName']) || empty($data['lastName']) || empty($data['email']) || empty($data['password'])) {
                Response::error('Required fields missing: firstName, lastName, email, password', 400);
                return;
            }

            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                Response::error('Invalid email format', 400);
                return;
            }
        
            // Create user with basic fields first
            $db = Database::getInstance();

            // Check if email already exists
            $existingUser = $db->fetchOne("SELECT id FROM users WHERE email = ?", [$data['email']]);
            if ($existingUser) {
                Response::error('Email already exists', 400);
                return;
            }

            // Hash password
            $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);

            // Prepare basic user data
            $name = $data['firstName'] . ' ' . $data['lastName'];
            $role = $data['role'] ?? 'partner';
            $mobile = $data['mobile'] ?? '';
            $address = $data['address'] ?? '';
            $businessModel = $data['businessModel'] ?? '';

            // Insert user
            $sql = "INSERT INTO users (name, first_name, last_name, email, mobile, address, business_model, password, role, status, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())";

            $result = $db->execute($sql, [
                $name,
                $data['firstName'],
                $data['lastName'],
                $data['email'],
                $mobile,
                $address,
                $businessModel,
                $hashedPassword,
                $role
            ]);

            if ($result) {
                $userId = $db->getLastInsertId();
                $user = $db->fetchOne("SELECT id, name, first_name, last_name, email, mobile, address, business_model, role, status, created_at FROM users WHERE id = ?", [$userId]);

                Response::created($user, 'User created successfully');
            } else {
                Response::error('Failed to create user in database', 500);
            }

        } catch (Exception $e) {
            error_log('Create user failed: ' . $e->getMessage());
            Response::error('Failed to create user: ' . $e->getMessage(), 500);
        }
    }
    
    public function update($params) {
        if (!$this->requireManager()) {
            return;
        }
        
        $data = $this->getRequestData();
        
        $rules = [
            'name' => 'max:255',
            'email' => 'email|unique:users,email,' . $params['id'],
            'role' => 'in:superadmin,manager,partner',
            'status' => 'in:active,inactive,suspended'
        ];
        
        if (!$this->validateAndRespond($data, $rules)) {
            return;
        }
        
        try {
            $userModel = new User();
            $oldUser = $userModel->find($params['id']);
            
            if (!$oldUser) {
                Response::notFound('User not found');
                return;
            }
            
            // Hash password if provided
            if (isset($data['password'])) {
                $data['password'] = Auth::hashPassword($data['password']);
            }
            
            $user = $userModel->update($params['id'], $data);
            
            $this->logActivity('update', 'user', $params['id'], [
                'old' => $oldUser,
                'new' => $data
            ]);
            
            Response::updated($user, 'User updated successfully');
        } catch (Exception $e) {
            logError('Update user failed: ' . $e->getMessage());
            Response::error('Failed to update user', 500);
        }
    }
    
    public function delete($params) {
        if (!$this->requireSuperAdmin()) {
            return;
        }
        
        try {
            $userModel = new User();
            $user = $userModel->find($params['id']);
            
            if (!$user) {
                Response::notFound('User not found');
                return;
            }
            
            // Don't allow deleting yourself
            if ($user['id'] == $this->user['id']) {
                Response::error('Cannot delete your own account', 400);
                return;
            }
            
            $userModel->delete($params['id']);
            
            $this->logActivity('delete', 'user', $params['id'], $user);
            
            Response::deleted('User deleted successfully');
        } catch (Exception $e) {
            logError('Delete user failed: ' . $e->getMessage());
            Response::error('Failed to delete user', 500);
        }
    }

    /**
     * Get pending users for approval
     */
    public function pending() {
        if (!$this->requireSuperAdmin()) {
            return;
        }

        try {
            $userModel = new User();
            $pendingUsers = $userModel->where('approval_status', 'pending');

            // Add creator information
            foreach ($pendingUsers as &$user) {
                if ($user['created_by']) {
                    $creator = $userModel->find($user['created_by']);
                    $user['created_by_name'] = $creator ? $creator['name'] : 'Unknown';
                }
            }

            Response::success($pendingUsers);
        } catch (Exception $e) {
            Response::error('Failed to get pending users: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Approve a user
     */
    public function approve() {
        if (!$this->requireSuperAdmin()) {
            return;
        }

        try {
            $userId = $this->getRouteParam('id');
            $data = $this->getJsonInput();

            if (!$userId) {
                Response::error('User ID is required', 400);
                return;
            }

            $userModel = new User();
            $user = $userModel->find($userId);

            if (!$user) {
                Response::error('User not found', 404);
                return;
            }

            if ($user['approval_status'] !== 'pending') {
                Response::error('User is not pending approval', 400);
                return;
            }

            // Update user approval status
            $updateData = [
                'approval_status' => 'approved',
                'approved_by' => $this->getCurrentUserId(),
                'approved_at' => date('Y-m-d H:i:s')
            ];

            $userModel->update($userId, $updateData);

            // Log approval history
            $this->logApprovalHistory('user', $userId, 'approved', $data['notes'] ?? null);

            Response::success(['message' => 'User approved successfully']);
        } catch (Exception $e) {
            Response::error('Failed to approve user: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Reject a user
     */
    public function reject() {
        if (!$this->requireSuperAdmin()) {
            return;
        }

        try {
            $userId = $this->getRouteParam('id');
            $data = $this->getJsonInput();

            if (!$userId) {
                Response::error('User ID is required', 400);
                return;
            }

            if (empty($data['reason'])) {
                Response::error('Rejection reason is required', 400);
                return;
            }

            $userModel = new User();
            $user = $userModel->find($userId);

            if (!$user) {
                Response::error('User not found', 404);
                return;
            }

            if ($user['approval_status'] !== 'pending') {
                Response::error('User is not pending approval', 400);
                return;
            }

            // Update user approval status
            $updateData = [
                'approval_status' => 'rejected',
                'rejection_reason' => $data['reason'],
                'rejected_at' => date('Y-m-d H:i:s')
            ];

            $userModel->update($userId, $updateData);

            // Log approval history
            $this->logApprovalHistory('user', $userId, 'rejected', $data['reason']);

            Response::success(['message' => 'User rejected successfully']);
        } catch (Exception $e) {
            Response::error('Failed to reject user: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Handle document upload
     */
    private function handleDocumentUpload($fileData, $type) {
        try {
            // Create uploads directory if it doesn't exist
            $uploadDir = __DIR__ . '/../uploads/documents/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Generate unique filename
            $extension = pathinfo($fileData['name'], PATHINFO_EXTENSION);
            $filename = $type . '_' . uniqid() . '_' . time() . '.' . $extension;
            $filePath = $uploadDir . $filename;

            // Move uploaded file
            if (move_uploaded_file($fileData['tmp_name'], $filePath)) {
                return 'uploads/documents/' . $filename;
            } else {
                throw new Exception('Failed to move uploaded file');
            }
        } catch (Exception $e) {
            error_log("Document upload failed: " . $e->getMessage());
            throw new Exception('Document upload failed');
        }
    }

    /**
     * Log approval history
     */
    private function logApprovalHistory($entityType, $entityId, $action, $reason = null) {
        try {
            $db = Database::getInstance();
            $sql = "INSERT INTO approval_history (entity_type, entity_id, action, performed_by, reason, created_at)
                    VALUES (?, ?, ?, ?, ?, NOW())";
            $db->execute($sql, [$entityType, $entityId, $action, $this->getCurrentUserId(), $reason]);
        } catch (Exception $e) {
            // Log error but don't fail the main operation
            error_log("Failed to log approval history: " . $e->getMessage());
        }
    }
}
