import React, { useState, useEffect } from 'react';
import { XIcon, DownloadIcon, FileTextIcon, UserIcon, CalendarIcon, CheckCircleIcon, XCircleIcon, ClockIcon } from 'lucide-react';

const CompanyDetailsModal = ({ isOpen, onClose, companyId }) => {
  const [companyDetails, setCompanyDetails] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('details');

  useEffect(() => {
    if (isOpen && companyId) {
      loadCompanyDetails();
    }
  }, [isOpen, companyId]);

  const loadCompanyDetails = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`http://localhost/Investment-System-eSkillVisor/backend/company-details.php?id=${companyId}`);
      const data = await response.json();
      
      if (data.success) {
        setCompanyDetails(data.data);
      } else {
        setError(data.message || 'Failed to load company details');
      }
    } catch (err) {
      setError('Network error: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadDocument = (documentId, fileName) => {
    const downloadUrl = `http://localhost/Investment-System-eSkillVisor/backend/download-document.php?id=${documentId}`;
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getApprovalStatusIcon = (status) => {
    switch (status) {
      case 'approved':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'rejected':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getApprovalStatusClass = (status) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Company Details</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2 text-gray-600">Loading company details...</span>
            </div>
          ) : error ? (
            <div className="p-6 text-center">
              <div className="text-red-600 mb-2">Error loading company details</div>
              <div className="text-gray-600 text-sm">{error}</div>
              <button
                onClick={loadCompanyDetails}
                className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
              >
                Retry
              </button>
            </div>
          ) : companyDetails ? (
            <>
              {/* Tabs */}
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 px-6">
                  {[
                    { id: 'details', label: 'Company Details', icon: UserIcon },
                    { id: 'documents', label: 'Documents', icon: FileTextIcon },
                    { id: 'history', label: 'Approval History', icon: CalendarIcon }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                        activeTab === tab.id
                          ? 'border-primary text-primary'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <tab.icon className="h-4 w-4 mr-2" />
                      {tab.label}
                    </button>
                  ))}
                </nav>
              </div>

              {/* Tab Content */}
              <div className="p-6">
                {activeTab === 'details' && (
                  <div className="space-y-6">
                    {/* Basic Information */}
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Company Name</label>
                          <p className="mt-1 text-sm text-gray-900">{companyDetails.company.name}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Company Director</label>
                          <p className="mt-1 text-sm text-gray-900">{companyDetails.company.company_director || 'Not specified'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Registration Territory</label>
                          <p className="mt-1 text-sm text-gray-900">{companyDetails.company.registration_territory || 'Not specified'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">EIN Number</label>
                          <p className="mt-1 text-sm text-gray-900">{companyDetails.company.ein_number || 'Not specified'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Marketplace</label>
                          <p className="mt-1 text-sm text-gray-900">
                            {companyDetails.company.marketplace === 'Others' 
                              ? companyDetails.company.custom_marketplace 
                              : companyDetails.company.marketplace || 'Not specified'}
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Industry</label>
                          <p className="mt-1 text-sm text-gray-900">{companyDetails.company.industry || 'Not specified'}</p>
                        </div>
                      </div>
                    </div>

                    {/* Contact Information */}
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Email</label>
                          <p className="mt-1 text-sm text-gray-900">{companyDetails.company.email || 'Not specified'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Phone</label>
                          <p className="mt-1 text-sm text-gray-900">{companyDetails.company.phone || 'Not specified'}</p>
                        </div>
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700">Address</label>
                          <p className="mt-1 text-sm text-gray-900">{companyDetails.company.address || 'Not specified'}</p>
                        </div>
                      </div>
                    </div>

                    {/* Management Information */}
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Management</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Assigned Manager</label>
                          <p className="mt-1 text-sm text-gray-900">
                            {companyDetails.company.manager_name 
                              ? `${companyDetails.company.manager_name} (${companyDetails.company.manager_email})`
                              : 'No manager assigned'}
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Created By</label>
                          <p className="mt-1 text-sm text-gray-900">
                            {companyDetails.company.created_by_name && companyDetails.company.created_by_lastname
                              ? `${companyDetails.company.created_by_name} ${companyDetails.company.created_by_lastname}`
                              : 'Unknown'}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Approval Status */}
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Approval Status</h3>
                      <div className="flex items-center space-x-3">
                        {getApprovalStatusIcon(companyDetails.company.approval_status)}
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getApprovalStatusClass(companyDetails.company.approval_status)}`}>
                          {companyDetails.company.approval_status || 'pending'}
                        </span>
                        <span className="text-sm text-gray-500">
                          Created on {new Date(companyDetails.company.created_at).toLocaleDateString()}
                        </span>
                      </div>
                      {companyDetails.company.rejection_reason && (
                        <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
                          <p className="text-sm text-red-800">
                            <strong>Rejection Reason:</strong> {companyDetails.company.rejection_reason}
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Partners */}
                    {companyDetails.partners && companyDetails.partners.length > 0 && (
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Assigned Partners</h3>
                        <div className="space-y-2">
                          {companyDetails.partners.map((partner) => (
                            <div key={partner.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                              <div>
                                <p className="text-sm font-medium text-gray-900">
                                  {partner.first_name} {partner.last_name}
                                </p>
                                <p className="text-sm text-gray-500">{partner.email}</p>
                              </div>
                              <div className="text-xs text-gray-500">
                                Assigned {new Date(partner.assigned_at).toLocaleDateString()}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {activeTab === 'documents' && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Company Documents</h3>
                    {companyDetails.documents && companyDetails.documents.length > 0 ? (
                      <div className="space-y-3">
                        {companyDetails.documents.map((document) => (
                          <div key={document.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div className="flex items-center space-x-3">
                              <FileTextIcon className="h-8 w-8 text-gray-400" />
                              <div>
                                <p className="text-sm font-medium text-gray-900">{document.original_name}</p>
                                <p className="text-xs text-gray-500">
                                  {formatFileSize(document.file_size)} • 
                                  Uploaded {new Date(document.uploaded_at).toLocaleDateString()} by {document.uploaded_by_name} {document.uploaded_by_lastname}
                                </p>
                              </div>
                            </div>
                            <button
                              onClick={() => handleDownloadDocument(document.id, document.original_name)}
                              className="flex items-center space-x-1 px-3 py-1 text-sm text-primary hover:bg-primary hover:text-white rounded-md transition-colors"
                            >
                              <DownloadIcon className="h-4 w-4" />
                              <span>Download</span>
                            </button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <FileTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500">No documents uploaded yet</p>
                      </div>
                    )}
                  </div>
                )}

                {activeTab === 'history' && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Approval History</h3>
                    {companyDetails.approval_history && companyDetails.approval_history.length > 0 ? (
                      <div className="space-y-4">
                        {companyDetails.approval_history.map((entry, index) => (
                          <div key={index} className="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg">
                            <CalendarIcon className="h-5 w-5 text-gray-400 mt-0.5" />
                            <div>
                              <p className="text-sm font-medium text-gray-900">{entry.action}</p>
                              <p className="text-xs text-gray-500">
                                {entry.first_name} {entry.last_name} • {new Date(entry.performed_at).toLocaleString()}
                              </p>
                              {entry.details && (
                                <p className="text-sm text-gray-600 mt-1">{entry.details}</p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <CalendarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500">No approval history available</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </>
          ) : null}
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default CompanyDetailsModal;
