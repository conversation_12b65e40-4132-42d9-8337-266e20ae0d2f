#!/usr/bin/env node

/**
 * EskillVisor Simplified Automated Deployment System
 * Focuses on reliable package creation and comprehensive automation
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const crypto = require('crypto');

// Import our existing components
const ChangeDetector = require('./change-detector.cjs');
const DecisionEngine = require('./decision-engine.cjs');

class SimpleAutoDeployer {
    constructor() {
        this.config = this.loadConfig();
        this.credentials = this.loadCredentials();
        this.detector = new ChangeDetector();
        this.engine = new DecisionEngine();
        this.deploymentId = this.generateDeploymentId();
        this.startTime = Date.now();
    }

    /**
     * Load deployment configuration
     */
    loadConfig() {
        try {
            const configPath = path.join(__dirname, 'auto-deploy-config.json');
            const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
            return config;
        } catch (error) {
            throw new Error(`Failed to load deployment config: ${error.message}`);
        }
    }

    /**
     * Load and decrypt credentials
     */
    loadCredentials() {
        try {
            const envPath = path.join(__dirname, '.env');
            if (!fs.existsSync(envPath)) {
                throw new Error('Deployment credentials not found. Copy .env.example to .env and configure.');
            }

            const envContent = fs.readFileSync(envPath, 'utf8');
            const credentials = {};
            
            envContent.split('\n').forEach(line => {
                const [key, value] = line.split('=');
                if (key && value && !key.startsWith('#')) {
                    credentials[key.trim()] = value.trim();
                }
            });

            return credentials;
        } catch (error) {
            throw new Error(`Failed to load credentials: ${error.message}`);
        }
    }

    /**
     * Main deployment pipeline
     */
    async deploy() {
        console.log('🚀 EskillVisor Simplified Automated Deployment');
        console.log('=' .repeat(50));
        console.log(`Deployment ID: ${this.deploymentId}`);
        console.log(`Started: ${new Date().toISOString()}`);
        console.log('');

        try {
            // Step 1: Detect changes
            console.log('🔍 Step 1: Detecting changes...');
            const changes = await this.detector.detectChanges();
            
            if (changes.summary.totalChanges === 0) {
                console.log('✅ No changes detected. Deployment not required.');
                console.log(`Last deployment: ${changes.lastDeployment}`);
                return { status: 'skipped', reason: 'no_changes' };
            }

            console.log(`📊 Found ${changes.summary.totalChanges} changes`);
            console.log(`Deployment type: ${changes.summary.deploymentType}`);
            console.log('');

            // Step 2: Generate deployment plan
            console.log('📋 Step 2: Generating deployment plan...');
            const plan = this.engine.analyzeChanges(changes);
            console.log(`Estimated time: ${plan.estimatedTime} minutes`);
            console.log('');

            // Step 3: Create backup
            console.log('💾 Step 3: Creating backup...');
            await this.createBackup();
            console.log('✅ Backup created successfully');
            console.log('');

            // Step 4: Build frontend if needed
            if (this.shouldBuildFrontend(changes)) {
                console.log('🔨 Step 4: Building frontend...');
                await this.buildFrontend();
                console.log('✅ Frontend build completed');
                console.log('');
            }

            // Step 5: Create comprehensive deployment package
            console.log('📦 Step 5: Creating deployment package...');
            await this.createDeploymentPackage();
            await this.createDeploymentTools();
            console.log('✅ Deployment package created');
            console.log('');

            // Step 6: Verify package
            console.log('🔍 Step 6: Verifying package...');
            await this.verifyPackage();
            console.log('✅ Package verification completed');
            console.log('');

            // Step 7: Update deployment state
            console.log('💾 Step 7: Updating deployment state...');
            await this.detector.saveDeploymentState();
            console.log('✅ Deployment state updated');
            console.log('');

            // Success summary
            const duration = Math.round((Date.now() - this.startTime) / 1000);
            console.log('🎉 DEPLOYMENT PACKAGE READY!');
            console.log('=' .repeat(50));
            console.log(`Duration: ${duration} seconds`);
            console.log('');
            console.log('📦 DEPLOYMENT FILES CREATED:');
            console.log('   📁 cpanel-deployment/public_html/ - Complete website');
            console.log('   📄 deployment-instructions.html - Detailed guide');
            console.log('   📦 deployment-package.zip - Ready-to-upload ZIP');
            console.log('');
            console.log('🚀 UPLOAD TO: /home9/wallistry/eskillvisor.wallistry.pk/');
            console.log('🌐 WEBSITE: https://eskillvisor.wallistry.pk');
            console.log('🔧 API: https://eskillvisor.wallistry.pk/api/test');
            console.log('');
            console.log('✅ Enhanced dashboards ready for deployment!');

            return { 
                status: 'success', 
                deploymentId: this.deploymentId,
                duration,
                changes: changes.summary.totalChanges
            };

        } catch (error) {
            console.error('❌ DEPLOYMENT FAILED!');
            console.error('=' .repeat(50));
            console.error(`Error: ${error.message}`);
            throw error;
        }
    }

    /**
     * Check if frontend build is needed
     */
    shouldBuildFrontend(changes) {
        return changes.changes.frontend.added.length > 0 || 
               changes.changes.frontend.modified.length > 0 ||
               !fs.existsSync('dist');
    }

    /**
     * Build frontend
     */
    async buildFrontend() {
        try {
            const buildCommand = this.config.targets.frontend.build_command;
            execSync(buildCommand, { stdio: 'pipe' });
        } catch (error) {
            throw new Error(`Frontend build failed: ${error.message}`);
        }
    }

    /**
     * Create backup before deployment
     */
    async createBackup() {
        const backupDir = path.join(this.config.backup.local_backup_path, this.deploymentId);
        
        if (!fs.existsSync(backupDir)) {
            fs.mkdirSync(backupDir, { recursive: true });
        }

        // Backup current dist if exists
        if (fs.existsSync('dist')) {
            execSync(`cp -r dist ${backupDir}/frontend`, { stdio: 'pipe' });
        }

        // Backup current backend
        execSync(`cp -r backend ${backupDir}/backend`, { stdio: 'pipe' });

        // Create backup metadata
        const metadata = {
            deploymentId: this.deploymentId,
            timestamp: new Date().toISOString(),
            type: 'pre_deployment_backup'
        };

        fs.writeFileSync(
            path.join(backupDir, 'metadata.json'),
            JSON.stringify(metadata, null, 2)
        );
    }

    /**
     * Create deployment package
     */
    async createDeploymentPackage() {
        const packageDir = 'cpanel-deployment/public_html';
        
        // Ensure package directory exists
        if (!fs.existsSync('cpanel-deployment')) {
            fs.mkdirSync('cpanel-deployment', { recursive: true });
        }
        
        if (!fs.existsSync(packageDir)) {
            fs.mkdirSync(packageDir, { recursive: true });
        }

        // Copy frontend files
        if (this.config.targets.frontend.enabled && fs.existsSync('dist')) {
            console.log('   → Copying frontend files...');
            execSync(`cp -r dist/* ${packageDir}/`, { stdio: 'pipe' });
        }

        // Copy backend files
        if (this.config.targets.backend.enabled) {
            console.log('   → Copying backend files...');
            const apiDir = `${packageDir}/api`;
            if (!fs.existsSync(apiDir)) {
                fs.mkdirSync(apiDir, { recursive: true });
            }
            execSync(`cp -r backend/* ${apiDir}/`, { stdio: 'pipe' });
        }

        console.log('   → Package structure created');
    }

    /**
     * Create deployment tools and instructions
     */
    async createDeploymentTools() {
        // Create HTML instructions
        await this.createHTMLInstructions();
        
        // Create ZIP package
        await this.createZIPPackage();
        
        // Create deployment summary
        await this.createDeploymentSummary();
    }

    /**
     * Create HTML deployment instructions
     */
    async createHTMLInstructions() {
        const html = `<!DOCTYPE html>
<html><head><title>EskillVisor Deployment Guide</title>
<style>body{font-family:Arial,sans-serif;max-width:800px;margin:0 auto;padding:20px}
.header{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:white;padding:20px;border-radius:10px;margin-bottom:20px}
.step{background:#f8f9fa;padding:15px;margin:10px 0;border-radius:8px;border-left:4px solid #007bff}
.success{background:#d4edda;border-color:#28a745}.code{background:#f1f3f4;padding:10px;border-radius:4px;font-family:monospace}
</style></head><body>
<div class="header"><h1>🚀 EskillVisor Enhanced Dashboards</h1><p>Deployment package ready with beautiful dashboards and analytics</p></div>
<div class="step success"><h3>✅ Package Ready</h3><p>Enhanced dashboards with beautiful designs are ready for deployment!</p></div>
<div class="step"><h3>📦 Quick Upload</h3><ol><li>Upload <strong>deployment-package.zip</strong> to cPanel</li><li>Extract to: <code>/home9/wallistry/eskillvisor.wallistry.pk/</code></li><li>Overwrite existing files</li></ol></div>
<div class="step"><h3>🎨 Enhanced Features</h3><ul><li>Beautiful Super Admin Dashboard with business intelligence</li><li>Enhanced Manager Dashboard with company performance</li><li>Improved Partner Dashboard with portfolio management</li><li>New Analytics APIs for dashboard data</li></ul></div>
<div class="step"><h3>🔍 Test After Upload</h3><p>Visit: <a href="https://eskillvisor.wallistry.pk">https://eskillvisor.wallistry.pk</a></p><p>API: <a href="https://eskillvisor.wallistry.pk/api/test">https://eskillvisor.wallistry.pk/api/test</a></p></div>
</body></html>`;
        
        fs.writeFileSync('deployment-instructions.html', html);
        console.log('   → Created deployment-instructions.html');
    }

    /**
     * Create ZIP package
     */
    async createZIPPackage() {
        try {
            execSync('cd cpanel-deployment && zip -r ../deployment-package.zip public_html/', { stdio: 'pipe' });
            
            if (fs.existsSync('deployment-package.zip')) {
                const stats = fs.statSync('deployment-package.zip');
                console.log(`   → Created deployment-package.zip (${this.formatFileSize(stats.size)})`);
            }
        } catch (error) {
            console.log('   → ZIP creation skipped (zip command not available)');
        }
    }

    /**
     * Create deployment summary
     */
    async createDeploymentSummary() {
        const summary = {
            deploymentId: this.deploymentId,
            timestamp: new Date().toISOString(),
            target: 'https://eskillvisor.wallistry.pk',
            uploadPath: '/home9/wallistry/eskillvisor.wallistry.pk/',
            features: [
                'Enhanced Super Admin Dashboard with business intelligence',
                'Beautiful Manager Dashboard with company performance tracking',
                'Improved Partner Dashboard with portfolio management',
                'New Analytics APIs for real-time dashboard data',
                'Modern gradient designs with interactive elements'
            ],
            files: {
                package: 'cpanel-deployment/public_html/',
                instructions: 'deployment-instructions.html',
                zip: 'deployment-package.zip'
            }
        };
        
        fs.writeFileSync('deployment-summary.json', JSON.stringify(summary, null, 2));
        console.log('   → Created deployment-summary.json');
    }

    /**
     * Verify deployment package
     */
    async verifyPackage() {
        const packageDir = 'cpanel-deployment/public_html';
        
        // Check essential files
        const essentialFiles = [
            'index.html',
            'api/index.php',
            'api/controllers/AnalyticsController.php',
            'assets'
        ];
        
        let verified = 0;
        for (const file of essentialFiles) {
            const filePath = path.join(packageDir, file);
            if (fs.existsSync(filePath)) {
                verified++;
                console.log(`   ✅ ${file}`);
            } else {
                console.log(`   ❌ Missing: ${file}`);
            }
        }
        
        console.log(`   📊 Package verification: ${verified}/${essentialFiles.length} files found`);
    }

    /**
     * Format file size for display
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    /**
     * Generate unique deployment ID
     */
    generateDeploymentId() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const random = crypto.randomBytes(4).toString('hex');
        return `deploy_${timestamp}_${random}`;
    }
}

module.exports = SimpleAutoDeployer;

// CLI usage
if (require.main === module) {
    const deployer = new SimpleAutoDeployer();
    
    deployer.deploy()
        .then(result => {
            if (result.status === 'success') {
                console.log(`🎉 Deployment package ready in ${result.duration}s`);
                process.exit(0);
            } else if (result.status === 'skipped') {
                console.log('ℹ️ Deployment skipped - no changes detected');
                process.exit(0);
            }
        })
        .catch(error => {
            console.error('💥 Deployment failed:', error.message);
            process.exit(1);
        });
}
