const fs = require('fs-extra');
const path = require('path');

class CodeAnalyzer {
    constructor() {
        this.patterns = {
            // Frontend patterns
            react: {
                component: /(?:class|function|const)\s+(\w+).*(?:extends\s+(?:React\.)?Component|=\s*\(\)|=>\s*{)/g,
                hook: /const\s+(\w+)\s*=\s*use\w+/g,
                jsx: /<(\w+)(?:\s+[^>]*)?\s*(?:\/>|>.*?<\/\1>)/gs
            },
            vue: {
                component: /<template>[\s\S]*?<\/template>|<script>[\s\S]*?<\/script>|<style[\s\S]*?>[\s\S]*?<\/style>/g,
                directive: /v-\w+/g
            },
            angular: {
                component: /@Component\s*\(\s*{[\s\S]*?}\s*\)/g,
                service: /@Injectable\s*\(\s*{[\s\S]*?}\s*\)/g,
                directive: /@Directive\s*\(\s*{[\s\S]*?}\s*\)/g
            },
            
            // Backend patterns
            php: {
                class: /class\s+(\w+)(?:\s+extends\s+\w+)?(?:\s+implements\s+[\w,\s]+)?\s*{/g,
                function: /(?:public|private|protected)?\s*function\s+(\w+)\s*\(/g,
                route: /Route::(get|post|put|delete|patch)\s*\(\s*['"]([^'"]+)['"]/g
            },
            javascript: {
                function: /(?:function\s+(\w+)|const\s+(\w+)\s*=\s*(?:async\s+)?(?:function|\(|\w+\s*=>))/g,
                class: /class\s+(\w+)(?:\s+extends\s+\w+)?\s*{/g,
                export: /export\s+(?:default\s+)?(?:class|function|const)\s+(\w+)/g
            },
            
            // Database patterns
            sql: {
                table: /CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?`?(\w+)`?/gi,
                alter: /ALTER\s+TABLE\s+`?(\w+)`?\s+ADD\s+(?:COLUMN\s+)?`?(\w+)`?/gi,
                insert: /INSERT\s+INTO\s+`?(\w+)`?/gi
            },
            
            // Configuration patterns
            config: {
                env: /(\w+)=(.+)/g,
                json: /"(\w+)"\s*:\s*([^,}\]]+)/g
            }
        };
    }

    /**
     * Analyze changes and generate feature descriptions
     */
    async analyzeChanges(changes, projectPath, config) {
        const analysis = {
            features: [],
            summary: {
                frontend: { changes: 0, features: [] },
                backend: { changes: 0, features: [] },
                database: { changes: 0, features: [] },
                config: { changes: 0, features: [] },
                assets: { changes: 0, features: [] }
            },
            technicalDetails: [],
            impactAssessment: 'low'
        };

        try {
            // Group changes by category
            const categorizedChanges = this.groupChangesByCategory(changes);

            // Analyze each category
            for (const [category, categoryChanges] of Object.entries(categorizedChanges)) {
                if (categoryChanges.length > 0) {
                    const categoryAnalysis = await this.analyzeCategoryChanges(
                        category, 
                        categoryChanges, 
                        projectPath, 
                        config
                    );
                    
                    analysis.summary[category] = categoryAnalysis;
                    analysis.features.push(...categoryAnalysis.features);
                    analysis.technicalDetails.push(...categoryAnalysis.technicalDetails);
                }
            }

            // Assess overall impact
            analysis.impactAssessment = this.assessImpact(analysis);

            return analysis;

        } catch (error) {
            console.error('Error in code analysis:', error);
            return this.getFallbackAnalysis(changes);
        }
    }

    /**
     * Group changes by category
     */
    groupChangesByCategory(changes) {
        const grouped = {
            frontend: [],
            backend: [],
            database: [],
            config: [],
            assets: [],
            other: []
        };

        changes.forEach(change => {
            const category = change.category || 'other';
            if (grouped[category]) {
                grouped[category].push(change);
            } else {
                grouped.other.push(change);
            }
        });

        return grouped;
    }

    /**
     * Analyze changes in a specific category
     */
    async analyzeCategoryChanges(category, changes, projectPath, config) {
        const analysis = {
            changes: changes.length,
            features: [],
            technicalDetails: []
        };

        switch (category) {
            case 'frontend':
                analysis.features = await this.analyzeFrontendChanges(changes, projectPath);
                break;
            case 'backend':
                analysis.features = await this.analyzeBackendChanges(changes, projectPath);
                break;
            case 'database':
                analysis.features = await this.analyzeDatabaseChanges(changes, projectPath);
                break;
            case 'config':
                analysis.features = await this.analyzeConfigChanges(changes, projectPath);
                break;
            case 'assets':
                analysis.features = await this.analyzeAssetChanges(changes, projectPath);
                break;
            default:
                analysis.features = await this.analyzeGenericChanges(changes, projectPath);
        }

        // Generate technical details
        analysis.technicalDetails = this.generateTechnicalDetails(changes, category);

        return analysis;
    }

    /**
     * Analyze frontend changes
     */
    async analyzeFrontendChanges(changes, projectPath) {
        const features = [];
        const componentChanges = [];
        const styleChanges = [];
        const scriptChanges = [];

        for (const change of changes) {
            try {
                const content = await this.getFileContent(change.fullPath || path.join(projectPath, change.path));
                
                if (!content) continue;

                const ext = path.extname(change.path).toLowerCase();
                
                if (['.jsx', '.tsx', '.vue'].includes(ext)) {
                    const components = this.extractComponents(content, ext);
                    componentChanges.push(...components);
                } else if (['.css', '.scss', '.sass', '.less'].includes(ext)) {
                    styleChanges.push(change.path);
                } else if (['.js', '.ts'].includes(ext)) {
                    const functions = this.extractFunctions(content);
                    scriptChanges.push(...functions);
                }
            } catch (error) {
                console.warn(`Error analyzing ${change.path}:`, error.message);
            }
        }

        // Generate feature descriptions
        if (componentChanges.length > 0) {
            features.push(`Updated ${componentChanges.length} UI component(s): ${componentChanges.slice(0, 3).join(', ')}${componentChanges.length > 3 ? '...' : ''}`);
        }

        if (styleChanges.length > 0) {
            features.push(`Modified styling in ${styleChanges.length} file(s) for improved visual design`);
        }

        if (scriptChanges.length > 0) {
            features.push(`Enhanced frontend functionality with ${scriptChanges.length} function update(s)`);
        }

        if (features.length === 0) {
            features.push(`Updated ${changes.length} frontend file(s) with interface improvements`);
        }

        return features;
    }

    /**
     * Analyze backend changes
     */
    async analyzeBackendChanges(changes, projectPath) {
        const features = [];
        const apiChanges = [];
        const classChanges = [];
        const routeChanges = [];

        for (const change of changes) {
            try {
                const content = await this.getFileContent(change.fullPath || path.join(projectPath, change.path));
                
                if (!content) continue;

                const ext = path.extname(change.path).toLowerCase();
                
                if (ext === '.php') {
                    const classes = this.extractPHPClasses(content);
                    const routes = this.extractPHPRoutes(content);
                    classChanges.push(...classes);
                    routeChanges.push(...routes);
                } else if (['.js', '.ts'].includes(ext)) {
                    const functions = this.extractFunctions(content);
                    apiChanges.push(...functions);
                }
            } catch (error) {
                console.warn(`Error analyzing ${change.path}:`, error.message);
            }
        }

        // Generate feature descriptions
        if (routeChanges.length > 0) {
            features.push(`Added/updated ${routeChanges.length} API endpoint(s) for enhanced functionality`);
        }

        if (classChanges.length > 0) {
            features.push(`Enhanced backend logic with ${classChanges.length} class update(s)`);
        }

        if (apiChanges.length > 0) {
            features.push(`Improved server-side functionality with ${apiChanges.length} function update(s)`);
        }

        if (features.length === 0) {
            features.push(`Updated ${changes.length} backend file(s) with server-side improvements`);
        }

        return features;
    }

    /**
     * Analyze database changes
     */
    async analyzeDatabaseChanges(changes, projectPath) {
        const features = [];
        const tableChanges = [];
        const migrationChanges = [];

        for (const change of changes) {
            try {
                const content = await this.getFileContent(change.fullPath || path.join(projectPath, change.path));
                
                if (!content) continue;

                const tables = this.extractSQLTables(content);
                const alters = this.extractSQLAlters(content);
                
                tableChanges.push(...tables);
                migrationChanges.push(...alters);
            } catch (error) {
                console.warn(`Error analyzing ${change.path}:`, error.message);
            }
        }

        // Generate feature descriptions
        if (tableChanges.length > 0) {
            features.push(`Database schema updates with ${tableChanges.length} table modification(s)`);
        }

        if (migrationChanges.length > 0) {
            features.push(`Applied ${migrationChanges.length} database migration(s) for data structure improvements`);
        }

        if (features.length === 0) {
            features.push(`Updated ${changes.length} database file(s) with data structure changes`);
        }

        return features;
    }

    /**
     * Analyze configuration changes
     */
    async analyzeConfigChanges(changes, projectPath) {
        const features = [];
        const configTypes = new Set();

        changes.forEach(change => {
            const ext = path.extname(change.path).toLowerCase();
            if (ext === '.env') {
                configTypes.add('environment variables');
            } else if (ext === '.json') {
                configTypes.add('JSON configuration');
            } else if (ext === '.yaml' || ext === '.yml') {
                configTypes.add('YAML configuration');
            } else {
                configTypes.add('configuration files');
            }
        });

        if (configTypes.size > 0) {
            features.push(`Updated ${Array.from(configTypes).join(', ')} for improved system configuration`);
        }

        return features;
    }

    /**
     * Analyze asset changes
     */
    async analyzeAssetChanges(changes, projectPath) {
        const features = [];
        const assetTypes = new Set();

        changes.forEach(change => {
            const ext = path.extname(change.path).toLowerCase();
            if (['.png', '.jpg', '.jpeg', '.gif', '.svg'].includes(ext)) {
                assetTypes.add('images');
            } else if (['.pdf', '.doc', '.docx'].includes(ext)) {
                assetTypes.add('documents');
            } else {
                assetTypes.add('media files');
            }
        });

        if (assetTypes.size > 0) {
            features.push(`Updated ${Array.from(assetTypes).join(', ')} for enhanced visual content`);
        }

        return features;
    }

    /**
     * Analyze generic changes
     */
    async analyzeGenericChanges(changes, projectPath) {
        return [`Updated ${changes.length} file(s) with various improvements`];
    }

    /**
     * Extract components from frontend files
     */
    extractComponents(content, extension) {
        const components = [];
        
        if (extension === '.jsx' || extension === '.tsx') {
            const matches = content.match(this.patterns.react.component);
            if (matches) {
                matches.forEach(match => {
                    const componentName = match.match(/(?:class|function|const)\s+(\w+)/);
                    if (componentName) {
                        components.push(componentName[1]);
                    }
                });
            }
        } else if (extension === '.vue') {
            // Vue component analysis
            if (content.includes('<template>')) {
                components.push('Vue Component');
            }
        }

        return components;
    }

    /**
     * Extract functions from JavaScript/TypeScript files
     */
    extractFunctions(content) {
        const functions = [];
        const matches = content.match(this.patterns.javascript.function);
        
        if (matches) {
            matches.forEach(match => {
                const funcName = match.match(/(?:function\s+(\w+)|const\s+(\w+))/);
                if (funcName) {
                    functions.push(funcName[1] || funcName[2]);
                }
            });
        }

        return functions;
    }

    /**
     * Extract PHP classes
     */
    extractPHPClasses(content) {
        const classes = [];
        const matches = content.match(this.patterns.php.class);
        
        if (matches) {
            matches.forEach(match => {
                const className = match.match(/class\s+(\w+)/);
                if (className) {
                    classes.push(className[1]);
                }
            });
        }

        return classes;
    }

    /**
     * Extract PHP routes
     */
    extractPHPRoutes(content) {
        const routes = [];
        const matches = content.match(this.patterns.php.route);
        
        if (matches) {
            matches.forEach(match => {
                const routeInfo = match.match(/Route::(get|post|put|delete|patch)\s*\(\s*['"]([^'"]+)['"]/);
                if (routeInfo) {
                    routes.push(`${routeInfo[1].toUpperCase()} ${routeInfo[2]}`);
                }
            });
        }

        return routes;
    }

    /**
     * Extract SQL tables
     */
    extractSQLTables(content) {
        const tables = [];
        const matches = content.match(this.patterns.sql.table);
        
        if (matches) {
            matches.forEach(match => {
                const tableName = match.match(/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?`?(\w+)`?/i);
                if (tableName) {
                    tables.push(tableName[1]);
                }
            });
        }

        return tables;
    }

    /**
     * Extract SQL alters
     */
    extractSQLAlters(content) {
        const alters = [];
        const matches = content.match(this.patterns.sql.alter);
        
        if (matches) {
            matches.forEach(match => {
                const alterInfo = match.match(/ALTER\s+TABLE\s+`?(\w+)`?\s+ADD\s+(?:COLUMN\s+)?`?(\w+)`?/i);
                if (alterInfo) {
                    alters.push(`${alterInfo[1]}.${alterInfo[2]}`);
                }
            });
        }

        return alters;
    }

    /**
     * Get file content safely
     */
    async getFileContent(filePath) {
        try {
            if (await fs.pathExists(filePath)) {
                return await fs.readFile(filePath, 'utf8');
            }
        } catch (error) {
            console.warn(`Could not read file ${filePath}:`, error.message);
        }
        return null;
    }

    /**
     * Generate technical details for changes
     */
    generateTechnicalDetails(changes, category) {
        const details = [];
        
        const statusCounts = {
            added: changes.filter(c => c.status === 'added').length,
            modified: changes.filter(c => c.status === 'modified').length,
            deleted: changes.filter(c => c.status === 'deleted').length
        };

        if (statusCounts.added > 0) {
            details.push(`${statusCounts.added} new ${category} file(s) added`);
        }
        
        if (statusCounts.modified > 0) {
            details.push(`${statusCounts.modified} existing ${category} file(s) modified`);
        }
        
        if (statusCounts.deleted > 0) {
            details.push(`${statusCounts.deleted} ${category} file(s) removed`);
        }

        return details;
    }

    /**
     * Assess overall impact of changes
     */
    assessImpact(analysis) {
        const totalChanges = analysis.features.length;
        const hasBackendChanges = analysis.summary.backend.changes > 0;
        const hasDatabaseChanges = analysis.summary.database.changes > 0;
        
        if (hasDatabaseChanges || (hasBackendChanges && totalChanges > 10)) {
            return 'high';
        } else if (hasBackendChanges || totalChanges > 5) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Fallback analysis when detailed analysis fails
     */
    getFallbackAnalysis(changes) {
        const categories = {};
        
        changes.forEach(change => {
            const category = change.category || 'other';
            if (!categories[category]) {
                categories[category] = 0;
            }
            categories[category]++;
        });

        const features = Object.entries(categories).map(([category, count]) => 
            `Updated ${count} ${category} file(s)`
        );

        return {
            features,
            summary: categories,
            technicalDetails: [`${changes.length} total file(s) modified`],
            impactAssessment: changes.length > 10 ? 'medium' : 'low'
        };
    }
}

module.exports = CodeAnalyzer;
