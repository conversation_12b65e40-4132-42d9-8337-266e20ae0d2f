<?php
/**
 * Notifications API endpoint
 */

// Enable CORS for local development
$allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:3000',
    'http://127.0.0.1:5173',
    'http://127.0.0.1:3000'
];

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, $allowedOrigins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    header('Access-Control-Allow-Origin: *');
}

header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

try {
    // Get authorization header
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? '';
    
    if (strpos($authHeader, 'Bearer ') !== 0) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Authorization token required']);
        exit();
    }
    
    $db = Database::getInstance();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $isRead = $_GET['is_read'] ?? null;
        
        // For now, return mock notifications
        $notifications = [
            [
                'id' => 1,
                'title' => 'New User Registration',
                'message' => 'A new partner has registered and is pending approval',
                'type' => 'user_registration',
                'is_read' => false,
                'created_at' => date('c', strtotime('-2 hours'))
            ],
            [
                'id' => 2,
                'title' => 'System Update',
                'message' => 'System maintenance scheduled for tonight',
                'type' => 'system',
                'is_read' => false,
                'created_at' => date('c', strtotime('-1 day'))
            ]
        ];
        
        // Filter by read status if specified
        if ($isRead !== null) {
            $isReadBool = $isRead === 'true' || $isRead === '1';
            $notifications = array_filter($notifications, function($notif) use ($isReadBool) {
                return $notif['is_read'] === $isReadBool;
            });
        }
        
        echo json_encode([
            'success' => true,
            'data' => array_values($notifications)
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log("Notifications error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}
?>
