# Dependencies
node_modules/
vendor/

# Production builds
dist/
dist-ssr/
build/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
backend/logs/*.log
backend/logs/*.txt

# Uploads and temporary files
backend/uploads/*
!backend/uploads/.gitkeep
temp/
tmp/

# Database
*.sql.backup
*.sql.bak
*.db

# PHP specific
composer.phar
composer.lock

# IDE files
.vscode/*
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Configuration files with sensitive data
backend/config/config.php
backend/config/database.php

# Backup files
*.bak
*.backup

# Test and debug files
test_*.php
debug_*.php
*_test.php

# Development scripts
deploy_*.bat
start_*.bat
*.sh

# Cache files
*.cache
.cache
.parcel-cache

# Temporary files
*.tmp
*.temp
*.local
