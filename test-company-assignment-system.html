<!DOCTYPE html>
<html>
<head>
    <title>Company Assignment System Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #333; margin: 0; font-size: 2.5em; }
        .test-result { margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 4px solid #ccc; }
        .success { background: #d4edda; border-left-color: #28a745; }
        .error { background: #f8d7da; border-left-color: #dc3545; }
        .info { background: #d1ecf1; border-left-color: #17a2b8; }
        button { padding: 12px 24px; background: #007bff; color: white; border: none; border-radius: 6px; cursor: pointer; margin: 5px; font-weight: bold; }
        button:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; max-height: 200px; }
        .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0; }
        .feature-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6; }
        .feature-card h4 { margin-top: 0; color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 Company Assignment System</h1>
            <p>Test the new company assignment and approval workflow</p>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h4>🎯 New Features</h4>
                <ul>
                    <li>Add Company button on Partner profiles</li>
                    <li>Company approval workflow</li>
                    <li>Manager vs SuperAdmin permissions</li>
                    <li>Company display on Partner profiles</li>
                    <li>Approval status tracking</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h4>🔄 Workflow</h4>
                <ol>
                    <li>Manager/SuperAdmin views Partner profile</li>
                    <li>Clicks "Add Company" button</li>
                    <li>Fills company details</li>
                    <li>Company created with approval status</li>
                    <li>SuperAdmin approves/rejects if created by Manager</li>
                    <li>Company appears on Partner profile</li>
                </ol>
            </div>
        </div>
        
        <button onclick="testCompanyCreation()">🏢 Test Company Creation</button>
        <button onclick="testApprovalWorkflow()">✅ Test Approval Workflow</button>
        <button onclick="testPartnerAssignment()">👤 Test Partner Assignment</button>
        <button onclick="testCompanyDisplay()">📋 Test Company Display</button>
        <button onclick="runCompleteTest()">🚀 Run Complete Test</button>
        <button onclick="clearResults()">🧹 Clear Results</button>
        
        <div id="results"></div>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="http://localhost:5173/superadmin/users" target="_blank" class="btn-success" style="text-decoration: none; display: inline-block;">
                🎯 Test in Frontend
            </a>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost/Investment-System-eSkillVisor/backend';
        let testToken = null;
        
        function addResult(title, status, message, details = null) {
            const resultsDiv = document.getElementById('results');
            const statusClass = status === 'success' ? 'success' : status === 'error' ? 'error' : 'info';
            const icon = status === 'success' ? '✅' : status === 'error' ? '❌' : 'ℹ️';
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${statusClass}`;
            resultDiv.innerHTML = `
                <h4>${icon} ${title}</h4>
                <p>${message}</p>
                ${details ? `<pre>${JSON.stringify(details, null, 2)}</pre>` : ''}
            `;
            
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function getAuthToken() {
            if (testToken) return testToken;
            
            try {
                const response = await fetch(`${API_BASE}/login.php`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password'
                    })
                });
                
                const data = await response.json();
                if (data.success) {
                    testToken = data.data.access_token;
                    return testToken;
                }
                throw new Error('Failed to get auth token');
            } catch (error) {
                addResult('Authentication', 'error', `Failed to get auth token: ${error.message}`);
                return null;
            }
        }
        
        async function testCompanyCreation() {
            clearResults();
            addResult('Company Creation Test', 'info', 'Testing company creation with approval workflow...');
            
            const token = await getAuthToken();
            if (!token) return;
            
            const companyData = {
                name: `Test Company ${Date.now()}`,
                description: 'Test company for approval workflow',
                industry: 'Technology',
                email: `test${Date.now()}@company.com`,
                phone: '+1234567890',
                website: 'https://testcompany.com',
                address: '123 Test Street',
                city: 'Test City',
                state: 'Test State',
                country: 'Test Country',
                postal_code: '12345',
                partner_id: 3, // Assuming partner with ID 3 exists
                created_by: 1 // SuperAdmin
            };
            
            try {
                const response = await fetch(`${API_BASE}/companies.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(companyData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    addResult('Company Creation', 'success', 'Company created successfully!', {
                        company: data.data,
                        approvalStatus: data.data.approval_status,
                        message: data.message
                    });
                    window.testCompanyId = data.data.id;
                } else {
                    addResult('Company Creation', 'error', data.message);
                }
            } catch (error) {
                addResult('Company Creation', 'error', error.message);
            }
        }
        
        async function testApprovalWorkflow() {
            clearResults();
            addResult('Approval Workflow Test', 'info', 'Testing company approval and rejection...');
            
            const token = await getAuthToken();
            if (!token) return;
            
            if (!window.testCompanyId) {
                addResult('Approval Test', 'error', 'No test company ID available. Run company creation test first.');
                return;
            }
            
            try {
                // Test approval
                const approveResponse = await fetch(`${API_BASE}/companies.php/${window.testCompanyId}/approve`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ approved_by: 1 })
                });
                
                const approveData = await approveResponse.json();
                
                if (approveData.success) {
                    addResult('Company Approval', 'success', 'Company approved successfully!');
                } else {
                    addResult('Company Approval', 'error', approveData.message);
                }
            } catch (error) {
                addResult('Company Approval', 'error', error.message);
            }
        }
        
        async function testPartnerAssignment() {
            clearResults();
            addResult('Partner Assignment Test', 'info', 'Testing partner assignment to companies...');
            
            const token = await getAuthToken();
            if (!token) return;
            
            try {
                // Get users to find a partner
                const usersResponse = await fetch(`${API_BASE}/users.php`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                const usersData = await usersResponse.json();
                
                if (usersData.success) {
                    const partners = usersData.data.filter(user => user.role === 'partner');
                    
                    if (partners.length > 0) {
                        addResult('Partner Assignment', 'success', `Found ${partners.length} partners for assignment`, {
                            partners: partners.map(p => ({ id: p.id, name: `${p.first_name} ${p.last_name}`, email: p.email }))
                        });
                    } else {
                        addResult('Partner Assignment', 'info', 'No partners found in system');
                    }
                } else {
                    addResult('Partner Assignment', 'error', usersData.message);
                }
            } catch (error) {
                addResult('Partner Assignment', 'error', error.message);
            }
        }
        
        async function testCompanyDisplay() {
            clearResults();
            addResult('Company Display Test', 'info', 'Testing company display on partner profiles...');
            
            const token = await getAuthToken();
            if (!token) return;
            
            try {
                // Get companies with approval status
                const companiesResponse = await fetch(`${API_BASE}/companies.php`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                const companiesData = await companiesResponse.json();
                
                if (companiesData.success) {
                    const companies = companiesData.data;
                    const approvalStats = companies.reduce((acc, company) => {
                        acc[company.approval_status || 'pending'] = (acc[company.approval_status || 'pending'] || 0) + 1;
                        return acc;
                    }, {});
                    
                    addResult('Company Display', 'success', `Retrieved ${companies.length} companies with approval status`, {
                        totalCompanies: companies.length,
                        approvalStats: approvalStats,
                        recentCompanies: companies.slice(0, 3).map(c => ({
                            id: c.id,
                            name: c.name,
                            approval_status: c.approval_status,
                            created_by_name: c.created_by_name
                        }))
                    });
                } else {
                    addResult('Company Display', 'error', companiesData.message);
                }
            } catch (error) {
                addResult('Company Display', 'error', error.message);
            }
        }
        
        async function runCompleteTest() {
            clearResults();
            addResult('Complete System Test', 'info', 'Running comprehensive company assignment system test...');
            
            await testCompanyCreation();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testApprovalWorkflow();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testPartnerAssignment();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testCompanyDisplay();
            
            addResult('🎉 System Test Complete', 'success', 
                'Company Assignment System is ready!', {
                    implementedFeatures: [
                        'Company creation with approval workflow',
                        'Partner assignment to companies',
                        'Approval status tracking',
                        'Manager vs SuperAdmin permissions',
                        'Company display on partner profiles'
                    ],
                    testingInstructions: [
                        '1. Login as SuperAdmin or Manager',
                        '2. Go to User Management',
                        '3. Click on a Partner profile',
                        '4. Click "Add Company" button',
                        '5. Fill company details and submit',
                        '6. Check approval status in Company Management',
                        '7. Verify company appears on Partner profile'
                    ]
                });
        }
    </script>
</body>
</html>
