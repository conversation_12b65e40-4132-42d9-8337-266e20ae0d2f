<?php
/**
 * Company Controller
 */

class CompanyController extends BaseController {
    
    public function index() {
        if (!$this->requireAuth()) {
            return;
        }
        
        try {
            $companyModel = new Company();
            $page = (int)($_GET['page'] ?? 1);
            $limit = (int)($_GET['limit'] ?? 10);
            $search = $_GET['search'] ?? '';
            $status = $_GET['status'] ?? '';
            $approval_status = $_GET['approval_status'] ?? '';
            
            $result = $companyModel->getPaginated($page, $limit, $search, $status, $approval_status);
            
            Response::paginated($result['data'], $result['total'], $result['page'], $result['limit']);
        } catch (Exception $e) {
            logError('Get companies failed: ' . $e->getMessage());
            Response::error('Failed to get companies', 500);
        }
    }
    
    public function getByManager() {
        if (!$this->requireManager()) {
            return;
        }
        
        try {
            $companyModel = new Company();
            $page = (int)($_GET['page'] ?? 1);
            $limit = (int)($_GET['limit'] ?? 10);
            $search = $_GET['search'] ?? '';
            $status = $_GET['status'] ?? '';
            
            $result = $companyModel->getByManager($this->user['id'], $page, $limit, $search, $status);
            
            Response::paginated($result['data'], $result['total'], $result['page'], $result['limit']);
        } catch (Exception $e) {
            logError('Get companies by manager failed: ' . $e->getMessage());
            Response::error('Failed to get companies', 500);
        }
    }
    
    public function getAll() {
        if (!$this->requireAuth()) {
            return;
        }
        
        try {
            $companyModel = new Company();
            
            if ($this->user['role'] === 'superadmin') {
                $companies = $companyModel->getAll();
            } else {
                $companies = $companyModel->getByManager($this->user['id']);
            }
            
            Response::success($companies);
        } catch (Exception $e) {
            logError('Get all companies failed: ' . $e->getMessage());
            Response::error('Failed to get companies', 500);
        }
    }
    
    public function show($params) {
        if (!$this->requireAuth()) {
            return;
        }
        
        try {
            $companyModel = new Company();
            $company = $companyModel->getWithStats($params['id']);
            
            if (!$company) {
                Response::notFound('Company not found');
                return;
            }
            
            if (!$this->canAccessCompany($params['id'])) {
                return;
            }
            
            // Get partners for this company
            $company['partners'] = $companyModel->getPartners($params['id']);
            
            Response::success($company);
        } catch (Exception $e) {
            logError('Get company failed: ' . $e->getMessage());
            Response::error('Failed to get company', 500);
        }
    }
    
    public function store() {
        if (!$this->requireManager()) {
            return;
        }
        
        $data = $this->getRequestData();
        
        $rules = [
            'name' => 'required|max:255',
            'companyDirector' => 'required|integer',
            'registrationTerritory' => 'required|max:255',
            'einNumber' => 'required|max:50',
            'marketplace' => 'required|max:100',
            'customMarketplace' => 'max:255',
            'description' => 'max:1000',
            'industry' => 'max:100',
            'website' => 'max:255',
            'email' => 'email|max:255',
            'phone' => 'max:50',
            'address' => 'max:1000',
            'status' => 'in:active,inactive'
        ];
        
        if (!$this->validateAndRespond($data, $rules)) {
            return;
        }
        
        // Validate that company director exists and is a partner
        if (!empty($data['companyDirector'])) {
            $userModel = new User();
            $director = $userModel->find($data['companyDirector']);
            if (!$director || $director['role'] !== 'partner') {
                Response::error('Invalid company director. Must be a partner.', 400);
                return;
            }
        }
        
        // Validate custom marketplace if "Others" is selected
        if ($data['marketplace'] === 'Others' && empty($data['customMarketplace'])) {
            Response::error('Custom marketplace is required when "Others" is selected.', 400);
            return;
        }
        
        try {
            $companyModel = new Company();
            
            // Prepare data for insertion
            $companyData = [
                'name' => $data['name'],
                'company_director_id' => $data['companyDirector'],
                'registration_territory' => $data['registrationTerritory'],
                'ein_number' => $data['einNumber'],
                'marketplace' => $data['marketplace'],
                'custom_marketplace' => $data['marketplace'] === 'Others' ? $data['customMarketplace'] : null,
                'description' => $data['description'] ?? '',
                'industry' => $data['industry'] ?? '',
                'website' => $data['website'] ?? '',
                'email' => $data['email'] ?? '',
                'phone' => $data['phone'] ?? '',
                'address' => $data['address'] ?? '',
                'created_by' => $this->user['id'],
                'status' => $data['status'] ?? 'active',
                'approval_status' => $this->user['role'] === 'superadmin' ? 'approved' : 'pending'
            ];
            
            // If super admin is creating, auto-approve
            if ($this->user['role'] === 'superadmin') {
                $companyData['approved_by'] = $this->user['id'];
                $companyData['approved_at'] = date('Y-m-d H:i:s');
            }
            
            $company = $companyModel->create($companyData);
            
            $this->logActivity('create', 'company', $company['id'], $companyData);
            
            Response::created($company, 'Company created successfully');
        } catch (Exception $e) {
            logError('Create company failed: ' . $e->getMessage());
            Response::error('Failed to create company', 500);
        }
    }
    
    public function update($params) {
        if (!$this->requireManager()) {
            return;
        }
        
        $data = $this->getRequestData();
        
        $rules = [
            'name' => 'max:255',
            'companyDirector' => 'integer',
            'registrationTerritory' => 'max:255',
            'einNumber' => 'max:50',
            'marketplace' => 'max:100',
            'customMarketplace' => 'max:255',
            'description' => 'max:1000',
            'industry' => 'max:100',
            'website' => 'max:255',
            'email' => 'email|max:255',
            'phone' => 'max:50',
            'address' => 'max:1000',
            'status' => 'in:active,inactive'
        ];
        
        if (!$this->validateAndRespond($data, $rules)) {
            return;
        }
        
        try {
            $companyModel = new Company();
            $company = $companyModel->find($params['id']);
            
            if (!$company) {
                Response::notFound('Company not found');
                return;
            }
            
            if (!$this->canAccessCompany($params['id'])) {
                return;
            }
            
            // Validate company director if provided
            if (!empty($data['companyDirector'])) {
                $userModel = new User();
                $director = $userModel->find($data['companyDirector']);
                if (!$director || $director['role'] !== 'partner') {
                    Response::error('Invalid company director. Must be a partner.', 400);
                    return;
                }
            }
            
            // Validate custom marketplace if "Others" is selected
            if (isset($data['marketplace']) && $data['marketplace'] === 'Others' && empty($data['customMarketplace'])) {
                Response::error('Custom marketplace is required when "Others" is selected.', 400);
                return;
            }
            
            // Prepare update data
            $updateData = [];
            $allowedFields = [
                'name', 'description', 'industry', 'website', 'email', 
                'phone', 'address', 'status'
            ];
            
            // Map frontend field names to database field names
            $fieldMapping = [
                'companyDirector' => 'company_director_id',
                'registrationTerritory' => 'registration_territory',
                'einNumber' => 'ein_number',
                'marketplace' => 'marketplace',
                'customMarketplace' => 'custom_marketplace'
            ];
            
            foreach ($data as $key => $value) {
                if (in_array($key, $allowedFields)) {
                    $updateData[$key] = $value;
                } elseif (isset($fieldMapping[$key])) {
                    $updateData[$fieldMapping[$key]] = $value;
                }
            }
            
            // Handle custom marketplace
            if (isset($data['marketplace'])) {
                $updateData['custom_marketplace'] = $data['marketplace'] === 'Others' ? ($data['customMarketplace'] ?? null) : null;
            }
            
            $updatedCompany = $companyModel->update($params['id'], $updateData);
            
            $this->logActivity('update', 'company', $params['id'], $updateData);
            
            Response::success($updatedCompany, 'Company updated successfully');
        } catch (Exception $e) {
            logError('Update company failed: ' . $e->getMessage());
            Response::error('Failed to update company', 500);
        }
    }
    
    public function approve($params) {
        if (!$this->requireSuperAdmin()) {
            return;
        }
        
        $data = $this->getRequestData();
        
        try {
            $companyModel = new Company();
            $company = $companyModel->find($params['id']);
            
            if (!$company) {
                Response::notFound('Company not found');
                return;
            }
            
            if ($company['approval_status'] !== 'pending') {
                Response::error('Company is not pending approval', 400);
                return;
            }
            
            $updateData = [
                'approval_status' => 'approved',
                'approved_by' => $this->user['id'],
                'approved_at' => date('Y-m-d H:i:s'),
                'rejection_reason' => null,
                'rejected_at' => null
            ];
            
            // Add manager assignment if provided
            if (!empty($data['managerId'])) {
                $updateData['manager_id'] = $data['managerId'];
            }
            
            $updatedCompany = $companyModel->update($params['id'], $updateData);
            
            $this->logActivity('approve', 'company', $params['id'], $updateData);
            
            Response::success($updatedCompany, 'Company approved successfully');
        } catch (Exception $e) {
            logError('Approve company failed: ' . $e->getMessage());
            Response::error('Failed to approve company', 500);
        }
    }
    
    public function reject($params) {
        if (!$this->requireSuperAdmin()) {
            return;
        }
        
        $data = $this->getRequestData();
        
        $rules = [
            'rejectionReason' => 'required|max:1000'
        ];
        
        if (!$this->validateAndRespond($data, $rules)) {
            return;
        }
        
        try {
            $companyModel = new Company();
            $company = $companyModel->find($params['id']);
            
            if (!$company) {
                Response::notFound('Company not found');
                return;
            }
            
            if ($company['approval_status'] !== 'pending') {
                Response::error('Company is not pending approval', 400);
                return;
            }
            
            $updateData = [
                'approval_status' => 'rejected',
                'rejected_by' => $this->user['id'],
                'rejected_at' => date('Y-m-d H:i:s'),
                'rejection_reason' => $data['rejectionReason'],
                'approved_by' => null,
                'approved_at' => null
            ];
            
            $updatedCompany = $companyModel->update($params['id'], $updateData);
            
            $this->logActivity('reject', 'company', $params['id'], $updateData);
            
            Response::success($updatedCompany, 'Company rejected successfully');
        } catch (Exception $e) {
            logError('Reject company failed: ' . $e->getMessage());
            Response::error('Failed to reject company', 500);
        }
    }
    
    public function delete($params) {
        if (!$this->requireSuperAdmin()) {
            return;
        }
        
        try {
            $companyModel = new Company();
            $company = $companyModel->find($params['id']);
            
            if (!$company) {
                Response::notFound('Company not found');
                return;
            }
            
            // Check if company has inventory items
            $inventoryCount = $companyModel->getInventoryCount($params['id']);
            if ($inventoryCount > 0) {
                Response::error('Cannot delete company with existing inventory items', 400);
                return;
            }
            
            $companyModel->delete($params['id']);
            
            $this->logActivity('delete', 'company', $params['id'], ['name' => $company['name']]);
            
            Response::success(null, 'Company deleted successfully');
        } catch (Exception $e) {
            logError('Delete company failed: ' . $e->getMessage());
            Response::error('Failed to delete company', 500);
        }
    }
    
    public function assignPartner($params) {
        if (!$this->requireManager()) {
            return;
        }
        
        $data = $this->getRequestData();
        
        $rules = [
            'userId' => 'required|integer'
        ];
        
        if (!$this->validateAndRespond($data, $rules)) {
            return;
        }
        
        try {
            if (!$this->canAccessCompany($params['id'])) {
                return;
            }
            
            $companyModel = new Company();
            $userModel = new User();
            
            // Validate user exists and is a partner
            $user = $userModel->find($data['userId']);
            if (!$user || $user['role'] !== 'partner') {
                Response::error('Invalid user. Must be a partner.', 400);
                return;
            }
            
            // Check if already assigned
            if ($companyModel->isPartnerAssigned($params['id'], $data['userId'])) {
                Response::error('Partner is already assigned to this company', 400);
                return;
            }
            
            $assignment = $companyModel->assignPartner($params['id'], $data['userId'], $this->user['id']);
            
            $this->logActivity('assign_partner', 'company', $params['id'], [
                'partner_id' => $data['userId'],
                'partner_name' => $user['name']
            ]);
            
            Response::created($assignment, 'Partner assigned successfully');
        } catch (Exception $e) {
            logError('Assign partner failed: ' . $e->getMessage());
            Response::error('Failed to assign partner', 500);
        }
    }
    
    public function removePartner($params) {
        if (!$this->requireManager()) {
            return;
        }
        
        try {
            if (!$this->canAccessCompany($params['id'])) {
                return;
            }
            
            $companyModel = new Company();
            $userModel = new User();
            
            $user = $userModel->find($params['userId']);
            if (!$user) {
                Response::notFound('User not found');
                return;
            }
            
            if (!$companyModel->isPartnerAssigned($params['id'], $params['userId'])) {
                Response::error('Partner is not assigned to this company', 400);
                return;
            }
            
            $companyModel->removePartner($params['id'], $params['userId']);
            
            $this->logActivity('remove_partner', 'company', $params['id'], [
                'partner_id' => $params['userId'],
                'partner_name' => $user['name']
            ]);
            
            Response::success(null, 'Partner removed successfully');
        } catch (Exception $e) {
            logError('Remove partner failed: ' . $e->getMessage());
            Response::error('Failed to remove partner', 500);
        }
    }
    
    private function canAccessCompany($companyId) {
        if ($this->user['role'] === 'superadmin') {
            return true;
        }
        
        $companyModel = new Company();
        $company = $companyModel->find($companyId);
        
        if (!$company) {
            Response::notFound('Company not found');
            return false;
        }
        
        if ($this->user['role'] === 'manager') {
            if ($company['created_by'] == $this->user['id'] || $company['manager_id'] == $this->user['id']) {
                return true;
            }
        }
        
        if ($this->user['role'] === 'partner') {
            if ($companyModel->isPartnerAssigned($companyId, $this->user['id'])) {
                return true;
            }
        }
        
        Response::forbidden('Access denied to this company');
        return false;
    }
}
