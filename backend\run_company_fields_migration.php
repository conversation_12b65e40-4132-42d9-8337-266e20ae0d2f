<?php
/**
 * Run database migration for missing company fields
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

try {
    $db = Database::getInstance();
    
    echo "Running migration: Add missing company fields...\n";
    
    // Check if columns already exist
    $result = $db->fetch("SHOW COLUMNS FROM companies LIKE 'company_director'");
    
    if (!$result) {
        // Add missing fields
        $sql = "ALTER TABLE companies
                ADD COLUMN company_director VARCHAR(255) NULL AFTER name,
                ADD COLUMN registration_territory VARCHAR(100) NULL AFTER company_director,
                ADD COLUMN ein_number VARCHAR(50) NULL AFTER registration_territory,
                ADD COLUMN marketplace VARCHAR(100) NULL AFTER ein_number,
                ADD COLUMN custom_marketplace VARCHAR(100) NULL AFTER marketplace,
                ADD COLUMN manager_id INT NULL,
                ADD COLUMN created_by INT NULL";
        
        $db->execute($sql);
        echo "✅ Added missing fields to companies table\n";
        
        // Add foreign key constraints
        $sql2 = "ALTER TABLE companies
                 ADD CONSTRAINT fk_companies_manager_id FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL,
                 ADD CONSTRAINT fk_companies_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL";
        
        $db->execute($sql2);
        echo "✅ Added foreign key constraints\n";
        
        // Add indexes
        $sql3 = "ALTER TABLE companies
                 ADD INDEX idx_company_director (company_director),
                 ADD INDEX idx_registration_territory (registration_territory),
                 ADD INDEX idx_ein_number (ein_number),
                 ADD INDEX idx_marketplace (marketplace),
                 ADD INDEX idx_manager_id (manager_id),
                 ADD INDEX idx_created_by (created_by)";
        
        $db->execute($sql3);
        echo "✅ Added indexes\n";
        
        echo "🎉 Company fields migration completed successfully!\n";
    } else {
        echo "ℹ️ Migration already applied - company fields exist\n";
    }
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
