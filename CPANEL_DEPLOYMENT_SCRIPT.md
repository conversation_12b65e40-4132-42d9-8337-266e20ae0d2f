# 🚀 cPanel Deployment Script for EskillVisor

## IMMEDIATE DEPLOYMENT STEPS

### Step 1: Login to cPanel
- URL: https://wallistry.pk:2083
- Username: wallistry
- Password: +GlESn;lJteQ%VXf

### Step 2: Clean Existing API Directory
1. Go to File Manager
2. Navigate to public_html/api/
3. **DELETE ALL CONTENTS** in the api folder (we're redeploying clean)

### Step 3: Upload Backend Files
Upload the entire `backend` folder contents to `public_html/api/`:

**Required Files Structure:**
```
public_html/api/
├── config/
│   ├── config.php (from backend/config/config.production.php)
│   └── database.php (from backend/config/database.production.php)
├── controllers/
├── core/
├── models/
├── services/
├── migrations/
├── uploads/ (create if not exists, set 755)
├── logs/ (create if not exists, set 755)
├── index.php
├── install.php
└── .htaccess
```

### Step 4: Set File Permissions
- uploads/ folder: 755
- logs/ folder: 755
- All PHP files: 644
- .htaccess: 644

### Step 5: Create Database
1. In cPanel, go to "MySQL Databases"
2. Create Database:
   - Name: `eskillvisor_db`
   - Full name will be: `wallistry_eskillvisor_db`
3. Create User:
   - Username: `eskill`
   - Full username will be: `wallistry_eskill`
   - Password: `EskillVisor2024!`
4. Add User to Database:
   - Grant ALL PRIVILEGES

### Step 6: Run Installation
Visit: https://wallistry.pk/api/install.php

Expected output:
```
✓ PHP version check passed
✓ Required extensions loaded
✓ Database connection successful
✓ Database created
✓ All migrations executed
✓ Default users created
✓ Installation completed successfully!
```

### Step 7: Test API
Visit: https://wallistry.pk/api/test

Expected response:
```json
{
  "success": true,
  "data": {
    "message": "API endpoint is working!",
    "timestamp": "2024-01-07T10:30:00+00:00"
  }
}
```

### Step 8: Test Authentication
POST to: https://wallistry.pk/api/auth/login
Body:
```json
{
  "email": "<EMAIL>",
  "password": "password"
}
```

Expected response:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "role": "super_admin"
    }
  }
}
```

## CRITICAL FILES TO UPLOAD

### 1. backend/config/config.php
```php
<?php
define('APP_ENV', 'production');
define('APP_DEBUG', false);
define('DB_HOST', 'localhost');
define('DB_NAME', 'wallistry_eskillvisor_db');
define('DB_USER', 'wallistry_eskill');
define('DB_PASS', 'EskillVisor2024!');
// ... rest of config
?>
```

### 2. backend/config/database.php
```php
<?php
// Only define if not already defined
if (!defined('DB_HOST')) {
    define('DB_HOST', 'localhost');
}
// ... rest with conditional defines
?>
```

## TROUBLESHOOTING

### If API returns PHP notices:
- Ensure config files use conditional defines
- Check file permissions
- Verify .htaccess exists

### If database connection fails:
- Verify database name includes wallistry_ prefix
- Check user has ALL privileges
- Confirm password is correct

### If routes not working:
- Ensure .htaccess file exists
- Check mod_rewrite is enabled
- Verify index.php handles routing

## SUCCESS VERIFICATION

✅ API test endpoint works
✅ No PHP notices in responses
✅ Authentication works
✅ Database connection successful
✅ Frontend can connect to backend

## DEFAULT LOGIN ACCOUNTS

| Role | Email | Password |
|------|-------|----------|
| Super Admin | <EMAIL> | password |
| Manager | <EMAIL> | password |
| Partner | <EMAIL> | password |

⚠️ **CHANGE THESE PASSWORDS AFTER DEPLOYMENT!**
