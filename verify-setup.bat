@echo off
echo ========================================
echo Investment System Setup Verification
echo ========================================
echo.

echo Checking Frontend Server...
curl -s http://localhost:5173 >nul 2>&1
if %errorlevel% == 0 (
    echo [✓] Frontend: Running at http://localhost:5173
) else (
    echo [✗] Frontend: Not accessible
)

echo.
echo Checking Apache Server...
curl -s http://localhost >nul 2>&1
if %errorlevel% == 0 (
    echo [✓] Apache: Running at http://localhost
) else (
    echo [✗] Apache: Not running - Start XAMPP Apache
)

echo.
echo Checking Backend API...
curl -s http://localhost/Investment-System-eSkillVisor/backend/ >nul 2>&1
if %errorlevel% == 0 (
    echo [✓] Backend: API accessible
) else (
    echo [✗] Backend: Not accessible - Check XAMPP and file copy
)

echo.
echo Checking MySQL Connection...
netstat -an | findstr ":3306" >nul 2>&1
if %errorlevel% == 0 (
    echo [✓] MySQL: Running on port 3306
) else (
    echo [✗] MySQL: Not running - Start XAMPP MySQL
)

echo.
echo ========================================
echo Next Steps:
echo 1. If Apache/MySQL not running: Start XAMPP services
echo 2. Create database: Visit http://localhost/phpmyadmin
echo 3. Run installation: http://localhost/Investment-System-eSkillVisor/backend/install.php
echo 4. Test system: http://localhost:5173
echo ========================================
pause
