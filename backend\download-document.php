<?php
/**
 * Document Download API - Secure file download
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

try {
    $db = Database::getInstance();
    
    $documentId = $_GET['id'] ?? null;
    
    if (!$documentId) {
        http_response_code(400);
        echo "Document ID is required";
        exit();
    }
    
    // Get document info
    $document = $db->fetch(
        "SELECT * FROM company_documents WHERE id = ? AND status = 'active'",
        [$documentId]
    );
    
    if (!$document) {
        http_response_code(404);
        echo "Document not found";
        exit();
    }
    
    $document = $document[0];
    $filePath = $document['file_path'];
    
    // Check if file exists
    if (!file_exists($filePath)) {
        http_response_code(404);
        echo "File not found on server";
        exit();
    }
    
    // Set appropriate headers for download
    $mimeType = mime_content_type($filePath);
    if (!$mimeType) {
        $mimeType = 'application/octet-stream';
    }
    
    header('Content-Type: ' . $mimeType);
    header('Content-Disposition: attachment; filename="' . $document['original_name'] . '"');
    header('Content-Length: ' . filesize($filePath));
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: 0');
    
    // Output file
    readfile($filePath);
    
} catch (Exception $e) {
    error_log("Document download error: " . $e->getMessage());
    http_response_code(500);
    echo "Server error";
}
?>
