# 🔧 Backend Fix Instructions for wallistry.pk

## 🚨 Current Issues Identified

The backend API at https://wallistry.pk/api has the following issues:
1. **Duplicate constant definitions** in database.php
2. **Route not found** errors for API endpoints
3. **PHP notices** appearing in responses

## 🛠️ Immediate Fixes Required

### Issue 1: Fix Duplicate Constants

The current `/api/config/database.php` file has duplicate constant definitions. 

**Solution**: Replace the current database.php file with our corrected version.

### Issue 2: Fix API Routing

The API endpoints are not being recognized properly.

**Solution**: Ensure the .htaccess file is properly configured and index.php is handling routes correctly.

## 📁 Files to Upload/Replace

### 1. Replace backend/config/database.php
Upload the corrected `backend/config/database.php` file that prevents duplicate constants.

### 2. Replace backend/config/config.php  
Upload the production-ready `backend/config/config.php` file.

### 3. Verify backend/index.php
Ensure the main index.php file has proper CORS and routing configuration.

### 4. Check backend/.htaccess
Verify the .htaccess file is properly configured for URL rewriting.

## 🚀 Quick Fix Steps

### Step 1: Download Corrected Files
The following files in your local project are ready for upload:
- `backend/config/config.php` ✅
- `backend/config/database.php` ✅  
- `backend/index.php` ✅
- `backend/.htaccess` ✅

### Step 2: Upload via cPanel File Manager
1. Login to cPanel: https://wallistry.pk:2083
2. Go to File Manager
3. Navigate to `public_html/api/config/`
4. Replace these files:
   - `config.php`
   - `database.php`
5. Navigate to `public_html/api/`
6. Replace `index.php` if needed

### Step 3: Test API After Upload
Test these endpoints after uploading:

1. **Basic Test**:
   ```
   GET https://wallistry.pk/api/test
   ```
   Expected: `{"success": true, "data": {"message": "API endpoint is working!"}}`

2. **Auth Test**:
   ```
   POST https://wallistry.pk/api/auth/login
   Content-Type: application/json
   
   {
     "email": "<EMAIL>", 
     "password": "password"
   }
   ```

## 🔍 Verification Steps

### Check 1: No PHP Notices
After uploading corrected files, API responses should not show PHP notices.

### Check 2: Routes Working
API endpoints should return proper JSON responses, not "Route not found" errors.

### Check 3: Database Connection
The installation script should work: https://wallistry.pk/api/install.php

## 📋 Database Setup (If Not Done)

If the database hasn't been set up yet:

1. **Create Database in cPanel**:
   - Database Name: `eskillvisor_db` (becomes `wallistry_eskillvisor_db`)
   - Username: `eskill` (becomes `wallistry_eskill`)  
   - Password: `EskillVisor2024!`

2. **Run Installation**:
   - Visit: https://wallistry.pk/api/install.php
   - This will create all tables and default users

3. **Test Login**:
   - Email: <EMAIL>
   - Password: password

## 🎯 Expected Results After Fix

### API Test Response
```json
{
  "success": true,
  "data": {
    "message": "API endpoint is working!",
    "timestamp": "2024-01-07T10:30:00+00:00"
  }
}
```

### Login Response
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "role": "super_admin"
    }
  }
}
```

## 🚨 Troubleshooting

### If API Still Not Working:

1. **Check File Permissions**:
   - PHP files: 644
   - Directories: 755
   - uploads/: 755
   - logs/: 755

2. **Check .htaccess**:
   - Ensure mod_rewrite is enabled
   - Verify .htaccess file exists in /api/ directory

3. **Check PHP Logs**:
   - Access cPanel Error Logs
   - Look for PHP errors in /api/logs/

4. **Database Connection**:
   - Verify database exists
   - Check user has proper privileges
   - Test connection manually

## 📞 Next Steps

1. Upload the corrected configuration files
2. Test the API endpoints
3. Run the database installation if needed
4. Verify frontend can connect to backend
5. Complete the deployment checklist

The backend should be fully functional after these fixes! 🚀
