-- Safe migration: Add extended fields to users table for enhanced user management
-- This script checks if columns exist before adding them to prevent duplicate column errors

-- Add first_name column if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'first_name') = 0,
    'ALTER TABLE users ADD COLUMN first_name VARCHAR(255) NULL AFTER name',
    'SELECT "first_name column already exists" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add last_name column if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'last_name') = 0,
    'ALTER TABLE users ADD COLUMN last_name VARCHAR(255) NULL AFTER first_name',
    'SELECT "last_name column already exists" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add mobile column if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'mobile') = 0,
    'ALTER TABLE users ADD COLUMN mobile VARCHAR(20) NULL AFTER email',
    'SELECT "mobile column already exists" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add address column if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'address') = 0,
    'ALTER TABLE users ADD COLUMN address TEXT NULL AFTER mobile',
    'SELECT "address column already exists" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add business_model column if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'business_model') = 0,
    'ALTER TABLE users ADD COLUMN business_model VARCHAR(255) NULL AFTER address',
    'SELECT "business_model column already exists" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add cnic_document_path column if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'cnic_document_path') = 0,
    'ALTER TABLE users ADD COLUMN cnic_document_path VARCHAR(500) NULL AFTER business_model',
    'SELECT "cnic_document_path column already exists" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add mobile index if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND INDEX_NAME = 'idx_mobile') = 0,
    'ALTER TABLE users ADD INDEX idx_mobile (mobile)',
    'SELECT "idx_mobile index already exists" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add business_model index if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND INDEX_NAME = 'idx_business_model') = 0,
    'ALTER TABLE users ADD INDEX idx_business_model (business_model)',
    'SELECT "idx_business_model index already exists" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update existing users to split name into first_name and last_name (only if they are empty)
UPDATE users 
SET 
    first_name = CASE 
        WHEN (first_name IS NULL OR first_name = '') THEN SUBSTRING_INDEX(name, ' ', 1)
        ELSE first_name
    END,
    last_name = CASE 
        WHEN (last_name IS NULL OR last_name = '') THEN 
            CASE 
                WHEN LOCATE(' ', name) > 0 THEN SUBSTRING(name, LOCATE(' ', name) + 1)
                ELSE ''
            END
        ELSE last_name
    END
WHERE name IS NOT NULL AND name != '';

-- Show final table structure
SELECT 'Migration completed successfully. Current users table structure:' AS message;
DESCRIBE users;
