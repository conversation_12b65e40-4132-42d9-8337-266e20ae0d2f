import{r as l,a as Oe,g as ze,u as X,b as Be,c as Ve,B as Ge,R as We,d as R,N as U}from"./vendor-CMsH-4Bd.js";import{L as we,M as ie,C as z,E as Je,a as ee,b as le,A as Ce,U as pe,B as T,D as H,T as B,c as he,d as V,e as ye,f as je,g as Y,h as E,i as He,F as Q,S as Qe,P as O,I as be,R as Ye,j as G,k as Z,X as P,l as se,m as ne,n as Ze,o as ke,p as te,q as K,r as re,s as _e,t as Ie,u as Ke,v as ve,w as Xe,x as ge,y as es,z as Ae,G as ss,H as ts,J as q,K as de,N as me,O as rs,Q as as}from"./ui-DUfY2fMy.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const m of document.querySelectorAll('link[rel="modulepreload"]'))a(m);new MutationObserver(m=>{for(const n of m)if(n.type==="childList")for(const u of n.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&a(u)}).observe(document,{childList:!0,subtree:!0});function s(m){const n={};return m.integrity&&(n.integrity=m.integrity),m.referrerPolicy&&(n.referrerPolicy=m.referrerPolicy),m.crossOrigin==="use-credentials"?n.credentials="include":m.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function a(m){if(m.ep)return;m.ep=!0;const n=s(m);fetch(m.href,n)}})();var Re={exports:{}},oe={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ns=l,is=Symbol.for("react.element"),ls=Symbol.for("react.fragment"),os=Object.prototype.hasOwnProperty,cs=ns.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ds={key:!0,ref:!0,__self:!0,__source:!0};function De(o,t,s){var a,m={},n=null,u=null;s!==void 0&&(n=""+s),t.key!==void 0&&(n=""+t.key),t.ref!==void 0&&(u=t.ref);for(a in t)os.call(t,a)&&!ds.hasOwnProperty(a)&&(m[a]=t[a]);if(o&&o.defaultProps)for(a in t=o.defaultProps,t)m[a]===void 0&&(m[a]=t[a]);return{$$typeof:is,type:o,key:n,ref:u,props:m,_owner:cs.current}}oe.Fragment=ls;oe.jsx=De;oe.jsxs=De;Re.exports=oe;var e=Re.exports,Fe,Se=Oe;Fe=Se.createRoot,Se.hydrateRoot;var Ee={exports:{}},ms="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",xs=ms,us=xs;function Pe(){}function Ue(){}Ue.resetWarningCache=Pe;var ps=function(){function o(a,m,n,u,d,i){if(i!==us){var r=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw r.name="Invariant Violation",r}}o.isRequired=o;function t(){return o}var s={array:o,bigint:o,bool:o,func:o,number:o,object:o,string:o,symbol:o,any:o,arrayOf:t,element:o,elementType:o,instanceOf:t,node:o,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:Ue,resetWarningCache:Pe};return s.PropTypes=s,s};Ee.exports=ps();var hs=Ee.exports;const v=ze(hs),gs=()=>window.location.hostname==="eskillvisor.wallistry.pk"||window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1"?"https://eskillvisor.wallistry.pk/api":"https://wallistry.pk/api",fs=gs();class ys{constructor(){this.baseURL=fs,this.token=localStorage.getItem("auth_token")}setToken(t){this.token=t,t?localStorage.setItem("auth_token",t):localStorage.removeItem("auth_token")}getHeaders(){const t={"Content-Type":"application/json"};return this.token&&(t.Authorization=`Bearer ${this.token}`),t}async request(t,s={}){const a=`${this.baseURL}${t}`,m={headers:this.getHeaders(),...s};try{const n=await fetch(a,m),u=await n.json();if(!n.ok)throw new Error(u.message||"API request failed");return u}catch(n){throw console.error("API request failed:",n),n}}async get(t){return this.request(t,{method:"GET"})}async post(t,s,a={}){const m={method:"POST",body:s instanceof FormData?s:JSON.stringify(s),...a};return s instanceof FormData||(m.headers={...m.headers,"Content-Type":"application/json"}),this.request(t,m)}async put(t,s){return this.request(t,{method:"PUT",body:JSON.stringify(s)})}async delete(t){return this.request(t,{method:"DELETE"})}async login(t,s){const a=await this.post("/login.php",{email:t,password:s});return a.success&&a.data.access_token&&this.setToken(a.data.access_token),a}async logout(){try{await this.post("/api/auth/logout")}finally{this.setToken(null)}}async getCurrentUser(){return this.get("/api/auth/me")}async refreshToken(t){const s=await this.post("/api/auth/refresh",{refresh_token:t});return s.success&&s.data.access_token&&this.setToken(s.data.access_token),s}async getUsers(t={}){const s=new URLSearchParams(t).toString();return this.get(`/users${s?`?${s}`:""}`)}async getUser(t){return this.get(`/users.php?id=${t}`)}async createUser(t){const s={};return t instanceof FormData&&(s.headers={}),this.post("/users",t,s)}async updateUser(t,s){return this.put(`/users.php?id=${t}`,s)}async deleteUser(t){return this.delete(`/users.php?id=${t}`)}async getCompanies(t={}){const s=new URLSearchParams(t).toString();return this.get(`/companies.php${s?`?${s}`:""}`)}async getCompany(t){return this.get(`/companies.php?id=${t}`)}async createCompany(t){return this.post("/companies.php",t)}async updateCompany(t,s){return this.put(`/companies.php?id=${t}`,s)}async deleteCompany(t){return this.delete(`/companies.php?id=${t}`)}async assignPartner(t,s){return this.post(`/api/companies/${t}/partners`,{user_id:s})}async removePartner(t,s){return this.delete(`/api/companies/${t}/partners/${s}`)}async getInventory(t={}){const s=new URLSearchParams(t).toString();return this.get(`/api/inventory${s?`?${s}`:""}`)}async getInventoryItem(t){return this.get(`/api/inventory/${t}`)}async createInventoryItem(t){return this.post("/api/inventory",t)}async updateInventoryItem(t,s){return this.put(`/api/inventory/${t}`,s)}async deleteInventoryItem(t){return this.delete(`/api/inventory/${t}`)}async getLowStockItems(){return this.get("/api/inventory/low-stock")}async getTransactions(t={}){const s=new URLSearchParams(t).toString();return this.get(`/api/transactions${s?`?${s}`:""}`)}async createTransaction(t){return this.post("/api/transactions",t)}async uploadFile(t){const s=new FormData;s.append("file",t);const a=await fetch(`${this.baseURL}/api/files/upload`,{method:"POST",headers:{Authorization:`Bearer ${this.token}`},body:s}),m=await a.json();if(!a.ok)throw new Error(m.message||"File upload failed");return m}async getUploads(t={}){const s=new URLSearchParams(t).toString();return this.get(`/api/files/uploads${s?`?${s}`:""}`)}async processFile(t){return this.post(`/api/files/process/${t}`)}async getDashboardData(){return this.get("/api/analytics/dashboard")}async getInventoryStats(){return this.get("/api/analytics/inventory-stats")}async getTrends(){return this.get("/api/analytics/trends")}async getCompanyStats(){return this.get("/api/analytics/company-stats")}async getAssignedCompanies(){return this.get("/api/companies/assigned")}async getCompanyStats(){return this.get("/api/analytics/company-stats")}async getTrends(){return this.get("/api/analytics/trends")}async exportData(t,s="csv"){return this.get(`/api/analytics/export?type=${t}&format=${s}`)}async getNotifications(t={}){const s=new URLSearchParams(t).toString();return this.get(`/api/notifications${s?`?${s}`:""}`)}async markNotificationAsRead(t){return this.put(`/api/notifications/${t}/read`)}async markAllNotificationsAsRead(){return this.post("/api/notifications/mark-all-read")}async deleteNotification(t){return this.delete(`/api/notifications/${t}`)}}const C=new ys;class js{constructor(){this.currentUser=null,this.token=localStorage.getItem("auth_token"),this.refreshToken=localStorage.getItem("refresh_token"),this.token&&this.initializeFromToken()}async initializeFromToken(){try{if(this.token){C.setToken(this.token);const t=await C.getCurrentUser();t.success?this.currentUser=t.data:this.logout()}}catch(t){console.error("Failed to initialize from token:",t),this.logout()}}async login(t,s){try{const a=await C.login(t,s);if(a.success)return this.token=a.data.access_token,this.refreshToken=a.data.refresh_token,this.currentUser=a.data.user,localStorage.setItem("auth_token",this.token),localStorage.setItem("refresh_token",this.refreshToken),{success:!0,user:this.currentUser,token:this.token,refreshToken:this.refreshToken};throw new Error(a.message||"Login failed")}catch(a){throw new Error(a.message||"Login failed")}}async logout(){try{this.token&&await C.logout()}catch(t){console.error("Logout error:",t)}finally{localStorage.removeItem("auth_token"),localStorage.removeItem("refresh_token"),this.token=null,this.refreshToken=null,this.currentUser=null,C.setToken(null)}return{success:!0}}async refreshAuthToken(){try{if(!this.refreshToken)throw new Error("No refresh token available");const t=await C.refreshToken(this.refreshToken);if(t.success)return this.token=t.data.access_token,localStorage.setItem("auth_token",this.token),C.setToken(this.token),{success:!0,token:this.token};throw new Error(t.message||"Token refresh failed")}catch{throw this.logout(),new Error("Token refresh failed")}}async resetPassword(t){try{return await C.post("/api/auth/forgot-password",{email:t})}catch(s){throw new Error(s.message||"Password reset failed")}}getCurrentUser(){return this.currentUser}getToken(){return this.token}isAuthenticated(){return!!this.token&&!!this.currentUser}hasRole(t){var s;return((s=this.currentUser)==null?void 0:s.role)===t}hasAnyRole(t){var s;return t.includes((s=this.currentUser)==null?void 0:s.role)}isSuperAdmin(){return this.hasRole("superadmin")}isManager(){return this.hasAnyRole(["superadmin","manager"])}isPartner(){return this.hasRole("partner")}canAccessCompany(t){var s;return this.currentUser?this.hasAnyRole(["superadmin","manager"])?!0:this.hasRole("partner")&&((s=this.currentUser.assigned_companies)==null?void 0:s.some(a=>a.id===t))||!1:!1}getAccessibleCompanies(){return this.currentUser?this.hasAnyRole(["superadmin","manager"])?"all":this.hasRole("partner")?this.currentUser.assigned_companies||[]:[]:[]}}const L=new js,bs=()=>{const[o,t]=l.useState(""),[s,a]=l.useState(""),[m,n]=l.useState(!1),[u,d]=l.useState(!1),[i,r]=l.useState(""),[p,N]=l.useState(""),[f,y]=l.useState(!1),{login:j,isAuthenticated:w,userRole:k}=l.useContext(F),b=X();l.useEffect(()=>{w&&b(k==="superadmin"?"/superadmin":k==="manager"?"/manager":"/partner")},[w,k,b]);const I=g=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(g),_=async g=>{if(g.preventDefault(),!I(o)){r("Please enter a valid email address");return}r(""),N(""),d(!0);try{const h=await L.login(o,s);h.success&&(j(h.user),h.user.role==="superadmin"?b("/superadmin"):h.user.role==="manager"?b("/manager"):b("/partner"))}catch(h){N(h.message||"Login failed. Please try again.")}finally{d(!1)}},c=async(g,h)=>{t(g),a(h),r(""),N(""),d(!0);try{const S=await L.login(g,h);S.success&&(j(S.user),S.user.role==="superadmin"?b("/superadmin"):S.user.role==="manager"?b("/manager"):b("/partner"))}catch(S){N(S.message||"Demo login failed. Please try again.")}finally{d(!1)}},x=[{email:"<EMAIL>",password:"password",role:"Super Admin",description:"Full system access"},{email:"<EMAIL>",password:"password",role:"Manager",description:"Manage partners and companies"},{email:"<EMAIL>",password:"password",role:"Partner",description:"View assigned companies"}];return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-primary to-secondary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-20"}),e.jsxs("div",{className:"max-w-md w-full space-y-8 relative z-10",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mx-auto h-16 w-16 bg-white rounded-full flex items-center justify-center shadow-lg",children:e.jsx(we,{className:"h-8 w-8 text-primary"})}),e.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Enterprise Portal"}),e.jsx("p",{className:"mt-2 text-center text-sm text-white text-opacity-80",children:"Sign in to your account"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-xl p-8",children:[e.jsxs("form",{className:"space-y-6",onSubmit:_,children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(ie,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:o,onChange:g=>t(g.target.value),className:`appearance-none relative block w-full pl-10 pr-3 py-2 border ${i?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm`,placeholder:"Enter your email"})]}),i&&e.jsxs("div",{className:"mt-1 flex items-center text-sm text-red-600",children:[e.jsx(z,{className:"h-4 w-4 mr-1"}),i]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(we,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{id:"password",name:"password",type:m?"text":"password",autoComplete:"current-password",required:!0,value:s,onChange:g=>a(g.target.value),className:"appearance-none relative block w-full pl-10 pr-10 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm",placeholder:"Enter your password"}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>n(!m),children:m?e.jsx(Je,{className:"h-5 w-5 text-gray-400"}):e.jsx(ee,{className:"h-5 w-5 text-gray-400"})})]})]}),p&&e.jsxs("div",{className:"flex items-center text-sm text-red-600 bg-red-50 p-3 rounded-md",children:[e.jsx(z,{className:"h-4 w-4 mr-2"}),p]}),e.jsx("div",{children:e.jsx("button",{type:"submit",disabled:u,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:u?"Signing in...":"Sign in"})}),e.jsx("div",{className:"text-center",children:e.jsxs("button",{type:"button",onClick:()=>y(!f),className:"text-sm text-primary hover:text-primary-dark",children:[f?"Hide":"Show"," Demo Accounts"]})})]}),f&&e.jsxs("div",{className:"mt-6 border-t border-gray-200 pt-6",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Demo Accounts:"}),e.jsx("div",{className:"space-y-2",children:x.map((g,h)=>e.jsx("button",{onClick:()=>c(g.email,g.password),className:"w-full text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:g.role}),e.jsx("p",{className:"text-xs text-gray-500",children:g.email})]}),e.jsx("p",{className:"text-xs text-gray-400",children:g.description})]})},h))})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsx("a",{href:"/reset-password",onClick:g=>{g.preventDefault(),b("/reset-password")},className:"text-sm text-primary hover:text-primary-dark",children:"Forgot your password?"})})]})]})]})},vs=()=>{const[o,t]=l.useState(""),[s,a]=l.useState(!1),[m,n]=l.useState(!1),u=X(),d=i=>{i.preventDefault(),a(!0),setTimeout(()=>{a(!1),n(!0)},1500)};return m?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary to-secondary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"max-w-md w-full space-y-8",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-xl p-8 text-center",children:[e.jsx(le,{className:"mx-auto h-16 w-16 text-green-500"}),e.jsx("h2",{className:"mt-6 text-2xl font-bold text-gray-900",children:"Check your email"}),e.jsxs("p",{className:"mt-2 text-sm text-gray-600",children:["We've sent a password reset link to ",o]}),e.jsx("button",{onClick:()=>u("/login"),className:"mt-6 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:"Back to Login"})]})})}):e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary to-secondary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-md w-full space-y-8",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Reset your password"}),e.jsx("p",{className:"mt-2 text-center text-sm text-white text-opacity-80",children:"Enter your email address and we'll send you a link to reset your password."})]}),e.jsx("div",{className:"bg-white rounded-lg shadow-xl p-8",children:e.jsxs("form",{className:"space-y-6",onSubmit:d,children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(ie,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:o,onChange:i=>t(i.target.value),className:"appearance-none relative block w-full pl-10 pr-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm",placeholder:"Enter your email"})]})]}),e.jsx("div",{children:e.jsx("button",{type:"submit",disabled:s,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:s?"Sending...":"Send reset link"})}),e.jsx("div",{className:"text-center",children:e.jsxs("button",{type:"button",onClick:()=>u("/login"),className:"inline-flex items-center text-sm text-primary hover:text-primary-dark",children:[e.jsx(Ce,{className:"h-4 w-4 mr-1"}),"Back to login"]})})]})})]})})};class Ns{async getInventoryItems(t={}){try{const s=await C.getInventory(t);if(s.success)return s.data||[];throw new Error(s.message||"Failed to fetch inventory items")}catch(s){throw console.error("Error fetching inventory items:",s),s}}async getInventoryItem(t){try{const s=await C.getInventoryItem(t);if(s.success)return s.data;throw new Error(s.message||"Failed to fetch inventory item")}catch(s){throw console.error("Error fetching inventory item:",s),s}}async createInventoryItem(t){try{const s=await C.createInventoryItem(t);if(s.success)return s.data;throw new Error(s.message||"Failed to create inventory item")}catch(s){throw console.error("Error creating inventory item:",s),s}}async updateInventoryItem(t,s){try{const a=await C.updateInventoryItem(t,s);if(a.success)return a.data;throw new Error(a.message||"Failed to update inventory item")}catch(a){throw console.error("Error updating inventory item:",a),a}}async deleteInventoryItem(t){try{const s=await C.deleteInventoryItem(t);if(s.success)return!0;throw new Error(s.message||"Failed to delete inventory item")}catch(s){throw console.error("Error deleting inventory item:",s),s}}async getLowStockItems(){try{const t=await C.getLowStockItems();if(t.success)return t.data||[];throw new Error(t.message||"Failed to fetch low stock items")}catch(t){throw console.error("Error fetching low stock items:",t),t}}async getTransactions(t={}){try{const s=await C.getTransactions(t);if(s.success)return s.data||[];throw new Error(s.message||"Failed to fetch transactions")}catch(s){throw console.error("Error fetching transactions:",s),s}}async createTransaction(t){try{const s=await C.createTransaction(t);if(s.success)return s.data;throw new Error(s.message||"Failed to create transaction")}catch(s){throw console.error("Error creating transaction:",s),s}}async uploadFile(t){try{const s=await C.uploadFile(t);if(s.success)return s.data;throw new Error(s.message||"Failed to upload file")}catch(s){throw console.error("Error uploading file:",s),s}}async processFile(t){try{const s=await C.processFile(t);if(s.success)return s.data;throw new Error(s.message||"Failed to process file")}catch(s){throw console.error("Error processing file:",s),s}}async getUploads(t={}){try{const s=await C.getUploads(t);if(s.success)return s.data||[];throw new Error(s.message||"Failed to fetch uploads")}catch(s){throw console.error("Error fetching uploads:",s),s}}async getInventoryStats(){try{const t=await C.getInventoryStats();if(t.success)return t.data;throw new Error(t.message||"Failed to fetch inventory stats")}catch(t){throw console.error("Error fetching inventory stats:",t),t}}async getDashboardData(){try{const t=await C.getDashboardData();if(t.success)return t.data;throw new Error(t.message||"Failed to fetch dashboard data")}catch(t){throw console.error("Error fetching dashboard data:",t),t}}async getTrends(){try{const t=await C.getTrends();if(t.success)return t.data;throw new Error(t.message||"Failed to fetch trends data")}catch(t){throw console.error("Get trends data failed:",t),t}}async getCompanyStats(){try{const t=await C.getCompanyStats();if(t.success)return t.data;throw new Error(t.message||"Failed to fetch company stats")}catch(t){throw console.error("Get company stats failed:",t),t}}async exportData(t="inventory",s="csv"){try{return await C.exportData(t,s)}catch(a){throw console.error("Error exporting data:",a),a}}}const M=new Ns;class ws{async getUsers(t={}){try{const s=await C.getUsers(t);if(s.success)return s.data||[];throw new Error(s.message||"Failed to fetch users")}catch(s){throw console.error("Error fetching users:",s),s}}async getUser(t){try{const s=await C.getUser(t);if(s.success)return s.data;throw new Error(s.message||"Failed to fetch user")}catch(s){throw console.error("Error fetching user:",s),s}}async createUser(t){try{const s=await C.createUser(t);if(s.success)return s.data;throw new Error(s.message||"Failed to create user")}catch(s){throw console.error("Error creating user:",s),s}}async updateUser(t,s){try{const a=await C.updateUser(t,s);if(a.success)return a.data;throw new Error(a.message||"Failed to update user")}catch(a){throw console.error("Error updating user:",a),a}}async deleteUser(t){try{const s=await C.deleteUser(t);if(s.success)return!0;throw new Error(s.message||"Failed to delete user")}catch(s){throw console.error("Error deleting user:",s),s}}}const Te=new ws;class Ss{async getCompanies(t={}){try{const s=await C.getCompanies(t);if(s.success)return s.data||[];throw new Error(s.message||"Failed to fetch companies")}catch(s){throw console.error("Error fetching companies:",s),s}}async getCompany(t){try{const s=await C.getCompany(t);if(s.success)return s.data;throw new Error(s.message||"Failed to fetch company")}catch(s){throw console.error("Error fetching company:",s),s}}async createCompany(t){try{const s=await C.createCompany(t);if(s.success)return s.data;throw new Error(s.message||"Failed to create company")}catch(s){throw console.error("Error creating company:",s),s}}async updateCompany(t,s){try{const a=await C.updateCompany(t,s);if(a.success)return a.data;throw new Error(a.message||"Failed to update company")}catch(a){throw console.error("Error updating company:",a),a}}async deleteCompany(t){try{const s=await C.deleteCompany(t);if(s.success)return!0;throw new Error(s.message||"Failed to delete company")}catch(s){throw console.error("Error deleting company:",s),s}}async assignPartner(t,s){try{const a=await C.assignPartner(t,s);if(a.success)return!0;throw new Error(a.message||"Failed to assign partner")}catch(a){throw console.error("Error assigning partner:",a),a}}async removePartner(t,s){try{const a=await C.removePartner(t,s);if(a.success)return!0;throw new Error(a.message||"Failed to remove partner")}catch(a){throw console.error("Error removing partner:",a),a}}async getCompanyStats(){try{const t=await C.getCompanyStats();if(t.success)return t.data;throw new Error(t.message||"Failed to fetch company stats")}catch(t){throw console.error("Error fetching company stats:",t),t}}async getAssignedCompanies(){try{const t=await C.getAssignedCompanies();if(t.success)return t.data||[];throw new Error(t.message||"Failed to fetch assigned companies")}catch(t){throw console.error("Error fetching assigned companies:",t),t}}}const W=new Ss,Cs=()=>{const{user:o}=l.useContext(F),[t,s]=l.useState({users:{total_users:0,active_users:0,new_users_today:0},companies:{total_companies:0,active_companies:0,new_companies_today:0},inventory:{total_items:0,total_value:0,low_stock_count:0},top_categories:[],recent_activity:[]}),[a,m]=l.useState(!0),[n,u]=l.useState([]);l.useEffect(()=>{o&&(async()=>{try{m(!0);const r=await M.getDashboardData();r&&s({users:r.users||{total_users:0,active_users:0,new_users_today:0},companies:r.companies||{total_companies:0,active_companies:0,new_companies_today:0},inventory:r.inventory||{total_items:0,total_value:0,low_stock_count:0},top_categories:r.top_categories||[],recent_activity:r.recent_activity||[]});const p=await M.getTrends();p&&u(p.trends||[])}catch(r){console.error("Failed to load dashboard data:",r)}finally{m(!1)}})()},[o]);const d=({title:i,value:r,icon:p,color:N="blue",subtitle:f,trend:y})=>{const j={blue:"bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 border-blue-200",green:"bg-gradient-to-br from-green-50 to-green-100 text-green-600 border-green-200",purple:"bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600 border-purple-200",orange:"bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600 border-orange-200",red:"bg-gradient-to-br from-red-50 to-red-100 text-red-600 border-red-200",indigo:"bg-gradient-to-br from-indigo-50 to-indigo-100 text-indigo-600 border-indigo-200"};return e.jsx("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:i}),e.jsx("p",{className:"text-3xl font-bold text-gray-900 mb-2",children:r}),f&&e.jsx("p",{className:"text-sm text-gray-500",children:f}),y&&e.jsxs("div",{className:"flex items-center mt-2",children:[y.direction==="up"?e.jsx(E,{className:"h-4 w-4 text-green-500 mr-1"}):e.jsx(He,{className:"h-4 w-4 text-red-500 mr-1"}),e.jsx("span",{className:`text-sm font-medium ${y.direction==="up"?"text-green-600":"text-red-600"}`,children:y.value}),e.jsx("span",{className:"text-sm text-gray-500 ml-1",children:"vs last month"})]})]}),e.jsx("div",{className:`p-4 rounded-xl border ${j[N]}`,children:p})]})})};return a?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Super Admin Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"System-wide overview and analytics"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})]}):e.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Super Admin Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Complete system overview and business intelligence - Enhanced v2.0"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[e.jsx(d,{title:"Total Users",value:t.users.total_users,subtitle:`${t.users.active_users} active users`,icon:e.jsx(pe,{className:"h-8 w-8"}),color:"blue",trend:{direction:"up",value:"+12%"}}),e.jsx(d,{title:"Companies",value:t.companies.total_companies,subtitle:`${t.companies.active_companies} active companies`,icon:e.jsx(T,{className:"h-8 w-8"}),color:"green",trend:{direction:"up",value:"+8%"}}),e.jsx(d,{title:"Total Inventory Value",value:`$${t.inventory.total_value.toLocaleString()}`,subtitle:`${t.inventory.total_items} total items`,icon:e.jsx(H,{className:"h-8 w-8"}),color:"purple",trend:{direction:"up",value:"+15%"}})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsx(d,{title:"Low Stock Alerts",value:t.inventory.low_stock_count,subtitle:"Items need restocking",icon:e.jsx(B,{className:"h-6 w-6"}),color:"red"}),e.jsx(d,{title:"New Users Today",value:t.users.new_users_today,subtitle:"User registrations",icon:e.jsx(he,{className:"h-6 w-6"}),color:"indigo"}),e.jsx(d,{title:"New Companies",value:t.companies.new_companies_today,subtitle:"Companies added today",icon:e.jsx(V,{className:"h-6 w-6"}),color:"orange"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ye,{className:"h-5 w-5 text-purple-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Top Inventory Categories"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:t.top_categories.slice(0,5).map((i,r)=>{var p;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-3 h-3 rounded-full mr-3 ${["bg-blue-500","bg-green-500","bg-purple-500","bg-orange-500","bg-red-500"][r]}`}),e.jsx("span",{className:"font-medium text-gray-900",children:i.category||"Uncategorized"})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-sm font-semibold text-gray-900",children:[i.item_count," items"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["$",(p=i.total_value)==null?void 0:p.toLocaleString()]})]})]},r)})})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(je,{className:"h-5 w-5 text-blue-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent System Activity"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:t.recent_activity.slice(0,6).map((i,r)=>e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:`w-2 h-2 rounded-full mt-2 ${i.action==="create"?"bg-green-500":i.action==="update"?"bg-blue-500":i.action==="delete"?"bg-red-500":"bg-gray-500"}`}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:[i.action==="create"&&"Created ",i.action==="update"&&"Updated ",i.action==="delete"&&"Deleted ",i.entity_type]}),e.jsxs("p",{className:"text-xs text-gray-500",children:[i.user_name," • ",new Date(i.created_at).toLocaleDateString()]})]})]},r))})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Y,{className:"h-5 w-5 text-green-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Growth Trends (Last 30 Days)"})]})}),e.jsx("div",{className:"p-6",children:n.length>0?e.jsx("div",{className:"space-y-4",children:n.slice(-7).map((i,r)=>{var p;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:new Date(i.date).toLocaleDateString()}),e.jsxs("div",{className:"text-xs text-gray-500",children:[i.items_added," items added"]})]}),e.jsx("div",{className:"text-right",children:e.jsxs("div",{className:"text-sm font-semibold text-green-600",children:["+$",(p=i.value_added)==null?void 0:p.toLocaleString()]})})]},r)})}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(Y,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500",children:"No trend data available"})]})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Quick Actions"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(he,{className:"h-5 w-5 text-blue-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Create New Partner"})]}),e.jsx(E,{className:"h-4 w-4 text-blue-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(V,{className:"h-5 w-5 text-green-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-green-900",children:"Add Company"})]}),e.jsx(E,{className:"h-4 w-4 text-green-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Y,{className:"h-5 w-5 text-purple-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-purple-900",children:"View Reports"})]}),e.jsx(E,{className:"h-4 w-4 text-purple-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ee,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-orange-900",children:"System Overview"})]}),e.jsx(E,{className:"h-4 w-4 text-orange-600 transform rotate-45"})]})]})})]})]})]})},ks=()=>{const{user:o}=l.useContext(F),[t,s]=l.useState({companies:{total_companies:0,active_companies:0},inventory:{total_items:0,total_value:0,low_stock_count:0},top_categories:[],recent_activity:[],assigned_companies:[]}),[a,m]=l.useState(!0),[n,u]=l.useState([]);l.useEffect(()=>{o&&(async()=>{try{m(!0);const r=await M.getDashboardData();r&&s({companies:r.companies||{total_companies:0,active_companies:0},inventory:r.inventory||{total_items:0,total_value:0,low_stock_count:0},top_categories:r.top_categories||[],recent_activity:r.recent_activity||[],assigned_companies:r.assigned_companies||[]});try{const p=await M.getCompanyStats();p&&u(p.top_companies||[])}catch(p){console.error("Failed to load company stats:",p)}}catch(r){console.error("Failed to load dashboard data:",r)}finally{m(!1)}})()},[o]);const d=({title:i,value:r,icon:p,color:N="blue",subtitle:f,trend:y})=>{const j={blue:"bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 border-blue-200",green:"bg-gradient-to-br from-green-50 to-green-100 text-green-600 border-green-200",purple:"bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600 border-purple-200",orange:"bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600 border-orange-200",red:"bg-gradient-to-br from-red-50 to-red-100 text-red-600 border-red-200",indigo:"bg-gradient-to-br from-indigo-50 to-indigo-100 text-indigo-600 border-indigo-200"};return e.jsx("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:i}),e.jsx("p",{className:"text-3xl font-bold text-gray-900 mb-2",children:r}),f&&e.jsx("p",{className:"text-sm text-gray-500",children:f})]}),e.jsx("div",{className:`p-4 rounded-xl border ${j[N]}`,children:p})]})})};return a?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Manager Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Company management and partner oversight"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})]}):e.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Manager Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Comprehensive company management and partner oversight"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[e.jsx(d,{title:"Managed Companies",value:t.companies.total_companies,subtitle:`${t.companies.active_companies} active companies`,icon:e.jsx(T,{className:"h-8 w-8"}),color:"blue"}),e.jsx(d,{title:"Total Inventory Value",value:`$${t.inventory.total_value.toLocaleString()}`,subtitle:`${t.inventory.total_items} total items`,icon:e.jsx(H,{className:"h-8 w-8"}),color:"green"}),e.jsx(d,{title:"Low Stock Alerts",value:t.inventory.low_stock_count,subtitle:"Items need attention",icon:e.jsx(B,{className:"h-8 w-8"}),color:"red"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Y,{className:"h-5 w-5 text-green-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Top Companies by Value"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:n.slice(0,5).map((i,r)=>{var p;return e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 ${["bg-blue-500","bg-green-500","bg-purple-500","bg-orange-500","bg-red-500"][r]}`,children:r+1}),e.jsxs("div",{children:[e.jsx("p",{className:"font-semibold text-gray-900",children:i.name}),e.jsx("p",{className:"text-sm text-gray-500",children:i.industry})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-bold text-green-600",children:["$",(p=i.total_value)==null?void 0:p.toLocaleString()]}),e.jsxs("p",{className:"text-sm text-gray-500",children:[i.inventory_count," items"]})]})]},r)})})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ye,{className:"h-5 w-5 text-purple-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Inventory Categories"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:t.top_categories.slice(0,5).map((i,r)=>{var p;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-3 h-3 rounded-full mr-3 ${["bg-blue-500","bg-green-500","bg-purple-500","bg-orange-500","bg-red-500"][r]}`}),e.jsx("span",{className:"font-medium text-gray-900",children:i.category||"Uncategorized"})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-sm font-semibold text-gray-900",children:[i.item_count," items"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["$",(p=i.total_value)==null?void 0:p.toLocaleString()]})]})]},r)})})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(je,{className:"h-5 w-5 text-blue-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Company Activity"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:t.recent_activity.slice(0,6).map((i,r)=>e.jsxs("div",{className:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:`w-2 h-2 rounded-full mt-2 ${i.action==="create"?"bg-green-500":i.action==="update"?"bg-blue-500":i.action==="delete"?"bg-red-500":"bg-gray-500"}`}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:[i.action==="create"&&"Created ",i.action==="update"&&"Updated ",i.action==="delete"&&"Deleted ",i.entity_type]}),e.jsxs("p",{className:"text-xs text-gray-500",children:[i.user_name," • ",new Date(i.created_at).toLocaleDateString()]})]})]},r))})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Quick Actions"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(he,{className:"h-5 w-5 text-blue-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Add Partner"})]}),e.jsx(E,{className:"h-4 w-4 text-blue-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(V,{className:"h-5 w-5 text-green-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-green-900",children:"Manage Companies"})]}),e.jsx(E,{className:"h-4 w-4 text-green-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Q,{className:"h-5 w-5 text-purple-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-purple-900",children:"View Reports"})]}),e.jsx(E,{className:"h-4 w-4 text-purple-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Qe,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-orange-900",children:"Audit Trail"})]}),e.jsx(E,{className:"h-4 w-4 text-orange-600 transform rotate-45"})]})]})})]})]})]})},_s=()=>{const{user:o}=l.useContext(F),[t,s]=l.useState({assigned_companies:[],inventory:{total_items:0,total_value:0,low_stock_count:0},top_categories:[],recent_activity:[]}),[a,m]=l.useState(!0),[n,u]=l.useState([]);l.useEffect(()=>{o&&(async()=>{try{m(!0);const r=await M.getDashboardData();r&&s({assigned_companies:r.assigned_companies||[],inventory:r.inventory||{total_items:0,total_value:0,low_stock_count:0},top_categories:r.top_categories||[],recent_activity:r.recent_activity||[]});try{const p=await W.getAssignedCompanies();p&&u(p)}catch(p){console.error("Failed to load companies:",p)}}catch(r){console.error("Failed to load dashboard data:",r)}finally{m(!1)}})()},[o]);const d=({title:i,value:r,icon:p,color:N="blue",subtitle:f,actionButton:y})=>{const j={blue:"bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 border-blue-200",green:"bg-gradient-to-br from-green-50 to-green-100 text-green-600 border-green-200",purple:"bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600 border-purple-200",red:"bg-gradient-to-br from-red-50 to-red-100 text-red-600 border-red-200",orange:"bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600 border-orange-200"};return e.jsx("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:i}),e.jsx("p",{className:"text-3xl font-bold text-gray-900 mb-2",children:r}),f&&e.jsx("p",{className:"text-sm text-gray-500",children:f}),y&&e.jsx("div",{className:"mt-3",children:y})]}),e.jsx("div",{className:`p-4 rounded-xl border ${j[N]}`,children:p})]})})};return a?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Partner Portfolio"}),e.jsx("p",{className:"text-gray-600",children:"Manage your assigned companies and inventory"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})]}):e.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Partner Portfolio"}),e.jsx("p",{className:"text-gray-600",children:"Comprehensive view of your assigned companies and inventory management"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsx(d,{title:"Assigned Companies",value:t.assigned_companies.length,subtitle:"Active partnerships",icon:e.jsx(T,{className:"h-8 w-8"}),color:"blue"}),e.jsx(d,{title:"Total Items",value:t.inventory.total_items,subtitle:"Across all companies",icon:e.jsx(O,{className:"h-8 w-8"}),color:"green"}),e.jsx(d,{title:"Portfolio Value",value:`$${t.inventory.total_value.toLocaleString()}`,subtitle:"Total inventory worth",icon:e.jsx(H,{className:"h-8 w-8"}),color:"purple"}),e.jsx(d,{title:"Low Stock Alerts",value:t.inventory.low_stock_count,subtitle:"Items need restocking",icon:e.jsx(B,{className:"h-8 w-8"}),color:"red",actionButton:t.inventory.low_stock_count>0&&e.jsx("button",{className:"text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full hover:bg-red-200 transition-colors",children:"View Details"})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[e.jsxs("div",{className:"lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(T,{className:"h-5 w-5 text-blue-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Your Companies"})]}),e.jsx("button",{className:"text-sm text-blue-600 hover:text-blue-800 font-medium",children:"View All"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:n.slice(0,4).map((i,r)=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4",children:e.jsx(T,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-semibold text-gray-900",children:i.name||`Company ${r+1}`}),e.jsx("p",{className:"text-sm text-gray-500",children:i.industry||"Technology"}),e.jsxs("p",{className:"text-xs text-gray-400",children:[i.inventory_count||0," inventory items"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${i.status==="active"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:i.status||"Active"}),e.jsx("button",{className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",children:e.jsx(ee,{className:"h-4 w-4"})})]})]},r))})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Quick Actions"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"p-3 bg-blue-50 border border-blue-200 rounded-lg",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(be,{className:"h-5 w-5 text-blue-600 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-blue-900",children:"Read-Only Access"}),e.jsx("p",{className:"text-xs text-blue-700 mt-1",children:"Contact your manager for inventory updates"})]})]})}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Y,{className:"h-5 w-5 text-purple-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-purple-900",children:"View Reports"})]}),e.jsx(E,{className:"h-4 w-4 text-purple-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(B,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-orange-900",children:"Stock Alerts"})]}),e.jsx(E,{className:"h-4 w-4 text-orange-600 transform rotate-45"})]})]})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ye,{className:"h-5 w-5 text-purple-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Inventory Categories"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:t.top_categories.slice(0,5).map((i,r)=>{var p;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-3 h-3 rounded-full mr-3 ${["bg-blue-500","bg-green-500","bg-purple-500","bg-orange-500","bg-red-500"][r]}`}),e.jsx("span",{className:"font-medium text-gray-900",children:i.category||"Uncategorized"})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-sm font-semibold text-gray-900",children:[i.item_count," items"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["$",(p=i.total_value)==null?void 0:p.toLocaleString()]})]})]},r)})})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(je,{className:"h-5 w-5 text-blue-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Activity"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:t.recent_activity.slice(0,6).map((i,r)=>e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsxs("div",{className:"flex-shrink-0",children:[i.action==="create"&&e.jsx(le,{className:"h-5 w-5 text-green-500"}),i.action==="update"&&e.jsx(Ye,{className:"h-5 w-5 text-blue-500"}),i.action==="delete"&&e.jsx(G,{className:"h-5 w-5 text-red-500"}),!["create","update","delete"].includes(i.action)&&e.jsx(Z,{className:"h-5 w-5 text-gray-500"})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:[i.action==="create"&&"Created ",i.action==="update"&&"Updated ",i.action==="delete"&&"Deleted ",i.entity_type]}),e.jsxs("p",{className:"text-xs text-gray-500",children:[new Date(i.created_at).toLocaleDateString()," • ",new Date(i.created_at).toLocaleTimeString()]})]})]},r))})})]})]})]})},fe=({isOpen:o,onClose:t,onSubmit:s,userRole:a="superadmin",modalType:m="partner"})=>{const[n,u]=l.useState({firstName:"",lastName:"",mobile:"",email:"",address:"",businessModel:"",customBusinessModel:"",role:m==="manager"?"manager":"partner",password:"",confirmPassword:""}),[d,i]=l.useState(!1),[r,p]=l.useState({}),[N,f]=l.useState(null),y=c=>{const{name:x,value:g}=c.target;u(h=>({...h,[x]:g})),r[x]&&p(h=>({...h,[x]:""}))},j=["Amazon","Walmart","Business Equity","Inventory Partner","Other"],w=()=>{const c={};return n.firstName.trim()||(c.firstName="First name is required"),n.lastName.trim()||(c.lastName="Last name is required"),n.mobile.trim()?/^\+?[\d\s-()]+$/.test(n.mobile)||(c.mobile="Invalid mobile number format"):c.mobile="Mobile number is required",n.email.trim()?/\S+@\S+\.\S+/.test(n.email)||(c.email="Email is invalid"):c.email="Email is required",n.address.trim()||(c.address="Address is required"),m==="partner"&&(n.businessModel||(c.businessModel="Business model is required"),n.businessModel==="Other"&&!n.customBusinessModel.trim()&&(c.customBusinessModel="Please specify the business model"),N||(c.cnicDocument="CNIC/Passport document is required")),n.password?n.password.length<6&&(c.password="Password must be at least 6 characters"):c.password="Password is required",n.password!==n.confirmPassword&&(c.confirmPassword="Passwords do not match"),p(c),Object.keys(c).length===0},k=c=>{const x=c.target.files[0];if(x){if(!["image/jpeg","image/jpg","image/png","application/pdf"].includes(x.type)){p(h=>({...h,cnicDocument:"Please upload a valid image (JPG, PNG) or PDF file"}));return}if(x.size>5*1024*1024){p(h=>({...h,cnicDocument:"File size must be less than 5MB"}));return}f(x),p(h=>({...h,cnicDocument:""}))}},b=()=>{f(null)},I=async c=>{if(c.preventDefault(),!!w()){i(!0);try{const x=new FormData;if(x.append("firstName",n.firstName),x.append("lastName",n.lastName),x.append("name",`${n.firstName} ${n.lastName}`),x.append("mobile",n.mobile),x.append("email",n.email),x.append("address",n.address),x.append("role",n.role),x.append("password",n.password),m==="partner"){const g=n.businessModel==="Other"?n.customBusinessModel:n.businessModel;x.append("businessModel",g),N&&x.append("cnicDocument",N)}await s(x),_()}catch(x){console.error("Failed to add user:",x),p({submit:"Failed to add user. Please try again."})}finally{i(!1)}}},_=()=>{u({firstName:"",lastName:"",mobile:"",email:"",address:"",businessModel:"",customBusinessModel:"",role:m==="manager"?"manager":"partner",password:"",confirmPassword:""}),p({}),f(null),i(!1),t()};return o?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:_}),e.jsxs("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[e.jsx("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:m==="manager"?"Add New Manager":"Add New Partner"}),e.jsx("button",{onClick:_,className:"text-gray-400 hover:text-gray-600",children:e.jsx(P,{className:"h-6 w-6"})})]})}),e.jsxs("form",{onSubmit:I,children:[e.jsxs("div",{className:"px-4 pb-4 sm:px-6 sm:pb-6",children:[r.submit&&e.jsx("div",{className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:r.submit}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700",children:"First Name *"}),e.jsx("input",{type:"text",id:"firstName",name:"firstName",value:n.firstName,onChange:y,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.firstName?"border-red-300":"border-gray-300"}`,placeholder:"Enter first name"}),r.firstName&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.firstName})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700",children:"Last Name *"}),e.jsx("input",{type:"text",id:"lastName",name:"lastName",value:n.lastName,onChange:y,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.lastName?"border-red-300":"border-gray-300"}`,placeholder:"Enter last name"}),r.lastName&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.lastName})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"mobile",className:"block text-sm font-medium text-gray-700",children:"Mobile Number *"}),e.jsx("input",{type:"tel",id:"mobile",name:"mobile",value:n.mobile,onChange:y,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.mobile?"border-red-300":"border-gray-300"}`,placeholder:"+****************"}),r.mobile&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.mobile})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address *"}),e.jsx("input",{type:"email",id:"email",name:"email",value:n.email,onChange:y,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.email?"border-red-300":"border-gray-300"}`,placeholder:"Enter email address"}),r.email&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.email})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"address",className:"block text-sm font-medium text-gray-700",children:"Address *"}),e.jsx("textarea",{id:"address",name:"address",rows:3,value:n.address,onChange:y,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.address?"border-red-300":"border-gray-300"}`,placeholder:"Enter full address"}),r.address&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.address})]}),m==="partner"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"businessModel",className:"block text-sm font-medium text-gray-700",children:"Business Model *"}),e.jsxs("select",{id:"businessModel",name:"businessModel",value:n.businessModel,onChange:y,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.businessModel?"border-red-300":"border-gray-300"}`,children:[e.jsx("option",{value:"",children:"Select Business Model"}),j.map(c=>e.jsx("option",{value:c,children:c},c))]}),r.businessModel&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.businessModel})]}),n.businessModel==="Other"&&e.jsxs("div",{children:[e.jsx("label",{htmlFor:"customBusinessModel",className:"block text-sm font-medium text-gray-700",children:"Specify Business Model *"}),e.jsx("input",{type:"text",id:"customBusinessModel",name:"customBusinessModel",value:n.customBusinessModel,onChange:y,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.customBusinessModel?"border-red-300":"border-gray-300"}`,placeholder:"Enter your business model"}),r.customBusinessModel&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.customBusinessModel})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CNIC/Passport Document *"}),N?e.jsx("div",{className:"border border-gray-300 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Q,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:N.name}),e.jsxs("p",{className:"text-xs text-gray-500",children:[(N.size/1024/1024).toFixed(2)," MB"]})]})]}),e.jsx("button",{type:"button",onClick:b,className:"text-red-600 hover:text-red-800",children:e.jsx(G,{className:"h-5 w-5"})})]})}):e.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",children:[e.jsx("input",{type:"file",id:"cnicDocument",accept:".jpg,.jpeg,.png,.pdf",onChange:k,className:"hidden"}),e.jsxs("label",{htmlFor:"cnicDocument",className:"cursor-pointer flex flex-col items-center",children:[e.jsx(se,{className:"h-8 w-8 text-gray-400 mb-2"}),e.jsx("span",{className:"text-sm text-gray-600",children:"Click to upload CNIC or Passport"}),e.jsx("span",{className:"text-xs text-gray-500 mt-1",children:"JPG, PNG, or PDF (max 5MB)"})]})]}),r.cnicDocument&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.cnicDocument})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password *"}),e.jsx("input",{type:"password",id:"password",name:"password",value:n.password,onChange:y,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.password?"border-red-300":"border-gray-300"}`,placeholder:"Enter password"}),r.password&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.password})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password *"}),e.jsx("input",{type:"password",id:"confirmPassword",name:"confirmPassword",value:n.confirmPassword,onChange:y,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.confirmPassword?"border-red-300":"border-gray-300"}`,placeholder:"Confirm password"}),r.confirmPassword&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.confirmPassword})]})]})]}),e.jsxs("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[e.jsx("button",{type:"submit",disabled:d,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:d?"Adding...":m==="manager"?"Add Manager":"Add Partner"}),e.jsx("button",{type:"button",onClick:_,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"Cancel"})]})]})]})]})}):null},Me=({isOpen:o,onClose:t,user:s,onApprove:a,onReject:m})=>{const[n,u]=l.useState(!1),[d,i]=l.useState(""),[r,p]=l.useState(!1),N=async()=>{u(!0);try{await a(s.id,{approved:!0,notes:"User approved by Super Admin"})}catch(j){console.error("Failed to approve user:",j)}finally{u(!1)}},f=async()=>{if(d.trim()){u(!0);try{await m(s.id,d)}catch(j){console.error("Failed to reject user:",j)}finally{u(!1)}}},y=()=>{i(""),p(!1),u(!1),t()};return!o||!s?null:e.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:e.jsxs("div",{className:"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white",children:[e.jsxs("div",{className:"flex items-center justify-between pb-4 border-b border-gray-200",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"User Approval Review"}),e.jsx("button",{onClick:y,className:"text-gray-400 hover:text-gray-600",children:e.jsx(P,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6 mb-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"flex-shrink-0 h-16 w-16",children:e.jsx("div",{className:"h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center",children:e.jsx(ne,{className:"h-8 w-8 text-blue-600"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h4",{className:"text-xl font-semibold text-gray-900",children:s.name}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Pending approval since ",new Date(s.created_at).toLocaleDateString()]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ie,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Email"}),e.jsx("p",{className:"text-sm text-gray-600",children:s.email})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ze,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Role"}),e.jsx("p",{className:"text-sm text-gray-600 capitalize",children:s.role})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(ke,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Created"}),e.jsx("p",{className:"text-sm text-gray-600",children:new Date(s.created_at).toLocaleString()})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(ne,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Created By"}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Manager ID: ",s.created_by]})]})]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(z,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm font-medium text-orange-800",children:"Approval Required"}),e.jsx("p",{className:"text-sm text-orange-700 mt-1",children:"This user was created by a Manager and requires Super Admin approval before gaining system access."})]})]})})}),r&&e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"rejectionReason",className:"block text-sm font-medium text-gray-700 mb-2",children:"Rejection Reason *"}),e.jsx("textarea",{id:"rejectionReason",rows:4,value:d,onChange:j=>i(j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500",placeholder:"Please provide a reason for rejecting this user...",required:!0}),!d.trim()&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:"Rejection reason is required"})]}),e.jsxs("div",{className:"flex justify-end space-x-3",children:[e.jsx("button",{onClick:y,className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",disabled:n,children:"Cancel"}),r?e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>p(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",disabled:n,children:"Back"}),e.jsxs("button",{onClick:f,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",disabled:n||!d.trim(),children:[n?e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):e.jsx(G,{className:"h-4 w-4 mr-2"}),"Confirm Rejection"]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>p(!0),className:"inline-flex items-center px-4 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",disabled:n,children:[e.jsx(G,{className:"h-4 w-4 mr-2"}),"Reject"]}),e.jsxs("button",{onClick:N,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",disabled:n,children:[n?e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):e.jsx(te,{className:"h-4 w-4 mr-2"}),"Approve User"]})]})]})]})]})})};Me.propTypes={isOpen:v.bool.isRequired,onClose:v.func.isRequired,user:v.object,onApprove:v.func.isRequired,onReject:v.func.isRequired};const Is=()=>{const{userRole:o,user:t}=l.useContext(F),[s,a]=l.useState([{id:"1",name:"John Admin",email:"<EMAIL>",role:"superadmin",status:"active",approval_status:"approved"},{id:"2",name:"Jane Manager",email:"<EMAIL>",role:"manager",status:"active",approval_status:"approved"},{id:"3",name:"Bob Partner",email:"<EMAIL>",role:"partner",status:"active",approval_status:"approved"},{id:"4",name:"Alice Smith",email:"<EMAIL>",role:"partner",status:"active",approval_status:"pending",created_by:"2",created_at:"2025-07-15T10:30:00Z"},{id:"5",name:"Mike Johnson",email:"<EMAIL>",role:"manager",status:"active",approval_status:"pending",created_by:"2",created_at:"2025-07-15T09:15:00Z"}]),[m,n]=l.useState(!1),[u,d]=l.useState(!1),[i,r]=l.useState(!1),[p,N]=l.useState(null),[f,y]=l.useState("all"),j=async c=>{try{console.log("Creating user with data:",c);const x=await Te.createUser(c);console.log("User created successfully:",x),a(g=>[...g,x]),n(!1),d(!1)}catch(x){throw console.error("Failed to add user:",x),x}},w=async(c,x)=>{try{a(g=>g.map(h=>h.id===c?{...h,approval_status:"approved",approved_by:h.id,approved_at:new Date().toISOString()}:h)),r(!1),N(null)}catch(g){throw console.error("Failed to approve user:",g),g}},k=async(c,x)=>{try{a(g=>g.map(h=>h.id===c?{...h,approval_status:"rejected",rejection_reason:x,rejected_at:new Date().toISOString()}:h)),r(!1),N(null)}catch(g){throw console.error("Failed to reject user:",g),g}},b=c=>{N(c),r(!0)},I=s.filter(c=>f==="all"?!0:f==="pending"?c.approval_status==="pending":f==="approved"?c.approval_status==="approved":f==="rejected"?c.approval_status==="rejected":!0),_=s.filter(c=>c.approval_status==="pending").length;return e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"User Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage system users and their permissions"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[_>0&&e.jsx("div",{className:"bg-orange-100 border border-orange-200 rounded-lg px-3 py-2",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Z,{className:"h-4 w-4 text-orange-600 mr-2"}),e.jsxs("span",{className:"text-sm font-medium text-orange-800",children:[_," pending approval",_!==1?"s":""]})]})}),e.jsxs("button",{onClick:()=>d(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 mr-3",children:[e.jsx(K,{className:"h-4 w-4 mr-2"}),"Add Manager"]}),e.jsxs("button",{onClick:()=>n(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(K,{className:"h-4 w-4 mr-2"}),"Add Partner"]})]})]})}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"border-b border-gray-200",children:e.jsx("nav",{className:"-mb-px flex space-x-8",children:[{id:"all",name:"All Users",count:s.length},{id:"pending",name:"Pending Approval",count:_},{id:"approved",name:"Approved",count:s.filter(c=>c.approval_status==="approved").length},{id:"rejected",name:"Rejected",count:s.filter(c=>c.approval_status==="rejected").length}].map(c=>e.jsxs("button",{onClick:()=>y(c.id),className:`${f===c.id?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`,children:[c.name,e.jsx("span",{className:`ml-2 py-0.5 px-2 rounded-full text-xs ${f===c.id?"bg-primary text-white":"bg-gray-100 text-gray-600"}`,children:c.count})]},c.id))})})}),e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx(re,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search users...",className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"})]}),e.jsxs("button",{className:"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[e.jsx(_e,{className:"h-4 w-4 mr-2"}),"Filter"]})]})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Approval"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:I.map(c=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:e.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center",children:e.jsx(ne,{className:"h-5 w-5 text-gray-500"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:c.name}),e.jsx("div",{className:"text-sm text-gray-500",children:c.email})]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800",children:c.role})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${c.status==="active"?"bg-green-100 text-green-800":c.status==="inactive"?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:c.status})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${c.approval_status==="approved"?"bg-green-100 text-green-800":c.approval_status==="pending"?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800"}`,children:[c.approval_status==="approved"&&e.jsx(te,{className:"h-3 w-3 mr-1"}),c.approval_status==="pending"&&e.jsx(Z,{className:"h-3 w-3 mr-1"}),c.approval_status==="rejected"&&e.jsx(P,{className:"h-3 w-3 mr-1"}),c.approval_status]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:c.created_at?new Date(c.created_at).toLocaleDateString():"-"}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[c.approval_status==="pending"&&e.jsx("button",{onClick:()=>b(c),className:"text-primary hover:text-primary-dark mr-3",children:"Review"}),e.jsx("button",{className:"text-gray-600 hover:text-gray-900 mr-3",children:"Edit"}),e.jsx("button",{className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},c.id))})]})})]}),e.jsx(fe,{isOpen:u,onClose:()=>d(!1),onSubmit:j,userRole:o,modalType:"manager"}),e.jsx(fe,{isOpen:m,onClose:()=>n(!1),onSubmit:j,userRole:o,modalType:"partner"}),e.jsx(Me,{isOpen:i,onClose:()=>r(!1),user:p,onApprove:w,onReject:k})]})},J=({onFileSelect:o,acceptedTypes:t=[".xlsx",".xls",".pdf"],maxSize:s=10*1024*1024,multiple:a=!1,disabled:m=!1,className:n=""})=>{const[u,d]=l.useState(!1),[i,r]=l.useState([]),[p,N]=l.useState([]),f=l.useRef(null),y=x=>{var S;const g=[],h="."+((S=x.name.split(".").pop())==null?void 0:S.toLowerCase());if(t.includes(h)||g.push(`Invalid file type. Accepted types: ${t.join(", ")}`),x.size>s){const A=Math.round(s/1048576);g.push(`File size exceeds ${A}MB limit`)}return x.size===0&&g.push("File is empty"),g},j=x=>{const g=Array.from(x),h=[],S=[];g.forEach(A=>{const $=y(A);$.length===0?h.push(A):S.push(`${A.name}: ${$.join(", ")}`)}),N(S),a?(r(A=>[...A,...h]),o([...i,...h])):(r(h.slice(0,1)),o(h[0]||null))},w=x=>{x.preventDefault(),x.stopPropagation(),x.type==="dragenter"||x.type==="dragover"?d(!0):x.type==="dragleave"&&d(!1)},k=x=>{x.preventDefault(),x.stopPropagation(),d(!1),!m&&x.dataTransfer.files&&x.dataTransfer.files[0]&&j(x.dataTransfer.files)},b=x=>{x.preventDefault(),!m&&x.target.files&&x.target.files[0]&&j(x.target.files)},I=x=>{const g=i.filter((h,S)=>S!==x);r(g),o(a?g:null)},_=()=>{!m&&f.current&&f.current.click()},c=x=>{if(x===0)return"0 Bytes";const g=1024,h=["Bytes","KB","MB","GB"],S=Math.floor(Math.log(x)/Math.log(g));return parseFloat((x/Math.pow(g,S)).toFixed(2))+" "+h[S]};return e.jsxs("div",{className:`w-full ${n}`,children:[e.jsxs("div",{className:`
          relative border-2 border-dashed rounded-lg p-6 text-center transition-colors
          ${u?"border-primary bg-primary bg-opacity-5":"border-gray-300"}
          ${m?"bg-gray-100 cursor-not-allowed":"hover:border-primary hover:bg-primary hover:bg-opacity-5 cursor-pointer"}
        `,onDragEnter:w,onDragLeave:w,onDragOver:w,onDrop:k,onClick:_,children:[e.jsx("input",{ref:f,type:"file",multiple:a,accept:t.join(","),onChange:b,disabled:m,className:"hidden"}),e.jsx(se,{className:`mx-auto h-12 w-12 ${m?"text-gray-400":"text-gray-500"}`}),e.jsxs("div",{className:"mt-4",children:[e.jsx("p",{className:`text-sm font-medium ${m?"text-gray-400":"text-gray-900"}`,children:u?"Drop files here":"Click to upload or drag and drop"}),e.jsxs("p",{className:`text-xs mt-1 ${m?"text-gray-400":"text-gray-500"}`,children:[t.join(", ")," up to ",Math.round(s/(1024*1024)),"MB"]})]})]}),p.length>0&&e.jsx("div",{className:"mt-3 space-y-1",children:p.map((x,g)=>e.jsxs("div",{className:"flex items-center text-sm text-red-600",children:[e.jsx(z,{className:"h-4 w-4 mr-2 flex-shrink-0"}),x]},g))}),i.length>0&&e.jsxs("div",{className:"mt-4 space-y-2",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Selected Files:"}),i.map((x,g)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center min-w-0 flex-1",children:[e.jsx(Ie,{className:"h-5 w-5 text-gray-400 mr-3 flex-shrink-0"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:x.name}),e.jsx("p",{className:"text-xs text-gray-500",children:c(x.size)})]})]}),e.jsxs("div",{className:"flex items-center ml-4",children:[e.jsx(le,{className:"h-5 w-5 text-green-500 mr-2"}),e.jsx("button",{type:"button",onClick:h=>{h.stopPropagation(),I(g)},className:"p-1 hover:bg-gray-200 rounded",disabled:m,children:e.jsx(P,{className:"h-4 w-4 text-gray-400"})})]})]},g))]})]})};J.propTypes={onFileSelect:v.func.isRequired,acceptedTypes:v.arrayOf(v.string),maxSize:v.number,multiple:v.bool,disabled:v.bool,className:v.string};const $e=({isOpen:o,onClose:t,onSubmit:s,userRole:a="superadmin",currentUserId:m=null})=>{const[n,u]=l.useState({name:"",description:"",industry:"",email:"",phone:"",address:"",website:"",managerId:a==="manager"?m:""}),[d,i]=l.useState(!1),[r,p]=l.useState({}),[N,f]=l.useState([]),[y,j]=l.useState([]),[w,k]=l.useState("basic");l.useEffect(()=>{(async()=>{if(a==="superadmin"&&o)try{f([{id:"2",name:"Jane Manager",email:"<EMAIL>"}])}catch(S){console.error("Failed to load managers:",S),f([])}})()},[a,o]);const b=h=>{const{name:S,value:A}=h.target;u($=>({...$,[S]:A})),r[S]&&p($=>({...$,[S]:""}))},I=()=>{const h={};return n.name.trim()||(h.name="Company name is required"),n.email.trim()?/\S+@\S+\.\S+/.test(n.email)||(h.email="Email is invalid"):h.email="Email is required",n.industry.trim()||(h.industry="Industry is required"),p(h),Object.keys(h).length===0},_=(h,S)=>{if(h){const A={id:Date.now(),file:h,type:S,name:h.name,size:h.size,status:"pending"};j($=>[...$,A])}},c=h=>{j(S=>S.filter(A=>A.id!==h))},x=async h=>{if(h.preventDefault(),!!I()){i(!0);try{const S={...n,documents:y};await s(S),g()}catch(S){console.error("Failed to add company:",S),p({submit:"Failed to add company. Please try again."})}finally{i(!1)}}},g=()=>{u({name:"",description:"",industry:"",email:"",phone:"",address:"",website:"",managerId:a==="manager"?m:""}),p({}),j([]),k("basic"),i(!1),t()};return o?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:g}),e.jsxs("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full",children:[e.jsxs("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Add New Company"}),e.jsx("button",{onClick:g,className:"text-gray-400 hover:text-gray-600",children:e.jsx(P,{className:"h-6 w-6"})})]}),e.jsx("div",{className:"mt-4",children:e.jsx("div",{className:"border-b border-gray-200",children:e.jsxs("nav",{className:"-mb-px flex space-x-8",children:[e.jsx("button",{type:"button",onClick:()=>k("basic"),className:`${w==="basic"?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`,children:"Basic Information"}),e.jsxs("button",{type:"button",onClick:()=>k("documents"),className:`${w==="documents"?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`,children:[e.jsx(Q,{className:"h-4 w-4 mr-1"}),"Documents",y.length>0&&e.jsx("span",{className:"ml-1 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:y.length})]})]})})})]}),e.jsxs("form",{onSubmit:x,children:[e.jsxs("div",{className:"px-4 pb-4 sm:px-6 sm:pb-6",children:[r.submit&&e.jsx("div",{className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:r.submit}),w==="basic"&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Company Name *"}),e.jsx("input",{type:"text",id:"name",name:"name",value:n.name,onChange:b,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.name?"border-red-300":"border-gray-300"}`,placeholder:"Enter company name"}),r.name&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.name})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email *"}),e.jsx("input",{type:"email",id:"email",name:"email",value:n.email,onChange:b,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.email?"border-red-300":"border-gray-300"}`,placeholder:"<EMAIL>"}),r.email&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.email})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"industry",className:"block text-sm font-medium text-gray-700",children:"Industry *"}),e.jsxs("select",{id:"industry",name:"industry",value:n.industry,onChange:b,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.industry?"border-red-300":"border-gray-300"}`,children:[e.jsx("option",{value:"",children:"Select industry"}),e.jsx("option",{value:"Technology",children:"Technology"}),e.jsx("option",{value:"Manufacturing",children:"Manufacturing"}),e.jsx("option",{value:"Retail",children:"Retail"}),e.jsx("option",{value:"Healthcare",children:"Healthcare"}),e.jsx("option",{value:"Finance",children:"Finance"}),e.jsx("option",{value:"Education",children:"Education"}),e.jsx("option",{value:"Other",children:"Other"})]}),r.industry&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.industry})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone"}),e.jsx("input",{type:"tel",id:"phone",name:"phone",value:n.phone,onChange:b,className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"+****************"})]}),a==="superadmin"&&e.jsxs("div",{children:[e.jsx("label",{htmlFor:"managerId",className:"block text-sm font-medium text-gray-700",children:"Assign Manager (Optional)"}),e.jsxs("select",{id:"managerId",name:"managerId",value:n.managerId,onChange:b,className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",children:[e.jsx("option",{value:"",children:"No manager assigned"}),N.map(h=>e.jsxs("option",{value:h.id,children:[h.name," (",h.email,")"]},h.id))]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700",children:"Description"}),e.jsx("textarea",{id:"description",name:"description",rows:3,value:n.description,onChange:b,className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Brief description of the company"})]})]}),w==="documents"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Company Documents"}),e.jsx("p",{className:"text-sm text-gray-600 mb-6",children:"Upload required documents for company verification. These will be reviewed during the approval process."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-3",children:"Company Registration"}),e.jsx(J,{onFileSelect:h=>_(h,"registration"),acceptedTypes:[".pdf",".jpg",".jpeg",".png"],maxSize:5*1024*1024,multiple:!1,className:"mb-3"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Upload company registration certificate"})]}),e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-3",children:"Tax Certificate"}),e.jsx(J,{onFileSelect:h=>_(h,"tax_certificate"),acceptedTypes:[".pdf",".jpg",".jpeg",".png"],maxSize:5*1024*1024,multiple:!1,className:"mb-3"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Upload tax registration certificate"})]}),e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-3",children:"Business License"}),e.jsx(J,{onFileSelect:h=>_(h,"business_license"),acceptedTypes:[".pdf",".jpg",".jpeg",".png"],maxSize:5*1024*1024,multiple:!1,className:"mb-3"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Upload business license (optional)"})]}),e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-3",children:"Other Documents"}),e.jsx(J,{onFileSelect:h=>_(h,"other"),acceptedTypes:[".pdf",".jpg",".jpeg",".png"],maxSize:5*1024*1024,multiple:!1,className:"mb-3"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Upload any additional documents"})]})]}),y.length>0&&e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-3",children:"Uploaded Documents"}),e.jsx("div",{className:"space-y-2",children:y.map(h=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Q,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:h.name}),e.jsx("p",{className:"text-xs text-gray-500 capitalize",children:h.type.replace("_"," ")})]})]}),e.jsx("button",{type:"button",onClick:()=>c(h.id),className:"text-red-600 hover:text-red-800",children:e.jsx(G,{className:"h-5 w-5"})})]},h.id))})]})]})]}),e.jsxs("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[e.jsx("button",{type:"submit",disabled:d,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:d?"Adding...":"Add Company"}),e.jsx("button",{type:"button",onClick:g,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"Cancel"})]})]})]})]})}):null},Le=({isOpen:o,onClose:t,company:s,onApprove:a,onReject:m})=>{const[n,u]=l.useState(!1),[d,i]=l.useState(""),[r,p]=l.useState(!1),[N,f]=l.useState("all"),y=async()=>{u(!0);try{await a(s.id,{approved:!0,notes:"Company approved by Super Admin"})}catch(b){console.error("Failed to approve company:",b)}finally{u(!1)}},j=async()=>{if(d.trim()){u(!0);try{await m(s.id,d)}catch(b){console.error("Failed to reject company:",b)}finally{u(!1)}}},w=()=>{i(""),p(!1),u(!1),t()};if(!o||!s)return null;const k=b=>{switch(b){case"verified":return"bg-green-100 text-green-800";case"pending":return"bg-orange-100 text-orange-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return e.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:e.jsxs("div",{className:"relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white",children:[e.jsxs("div",{className:"flex items-center justify-between pb-4 border-b border-gray-200",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Company Approval Review"}),e.jsx("button",{onClick:w,className:"text-gray-400 hover:text-gray-600",children:e.jsx(P,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6 mb-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"flex-shrink-0 h-16 w-16",children:e.jsx("div",{className:"h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center",children:e.jsx(V,{className:"h-8 w-8 text-blue-600"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h4",{className:"text-xl font-semibold text-gray-900",children:s.name}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Pending approval since ",new Date(s.created_at).toLocaleDateString()]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ie,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Email"}),e.jsx("p",{className:"text-sm text-gray-600",children:s.email||"Not provided"})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ke,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Phone"}),e.jsx("p",{className:"text-sm text-gray-600",children:s.phone||"Not provided"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ke,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Industry"}),e.jsx("p",{className:"text-sm text-gray-600",children:s.industry||"Not specified"})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(V,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Created By"}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Manager ID: ",s.created_by]})]})]})]})]}),s.description&&e.jsxs("div",{className:"mt-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 mb-1",children:"Description"}),e.jsx("p",{className:"text-sm text-gray-600",children:s.description})]})]}),s.documents&&s.documents.length>0&&e.jsxs("div",{className:"mb-6",children:[e.jsx("h5",{className:"text-lg font-medium text-gray-900 mb-4",children:"Document Verification"}),e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsx("div",{className:"flex space-x-4",children:["all","registration","tax_certificate","business_license"].map(b=>e.jsx("button",{onClick:()=>f(b),className:`px-3 py-2 text-sm font-medium rounded-md ${N===b?"bg-primary text-white":"text-gray-500 hover:text-gray-700"}`,children:b==="all"?"All Documents":b.replace("_"," ").replace(/\b\w/g,I=>I.toUpperCase())},b))})}),e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"space-y-3",children:s.documents.filter(b=>N==="all"||b.type===N).map((b,I)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Q,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:b.filename}),e.jsx("p",{className:"text-xs text-gray-500 capitalize",children:b.type.replace("_"," ")})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${k(b.status)}`,children:b.status}),e.jsx("button",{className:"text-primary hover:text-primary-dark",children:e.jsx(ee,{className:"h-4 w-4"})})]})]},I))})})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(z,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm font-medium text-orange-800",children:"Approval Required"}),e.jsx("p",{className:"text-sm text-orange-700 mt-1",children:"This company was created by a Manager and requires Super Admin approval before becoming active in the system."})]})]})})}),r&&e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"rejectionReason",className:"block text-sm font-medium text-gray-700 mb-2",children:"Rejection Reason *"}),e.jsx("textarea",{id:"rejectionReason",rows:4,value:d,onChange:b=>i(b.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500",placeholder:"Please provide a reason for rejecting this company...",required:!0}),!d.trim()&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:"Rejection reason is required"})]}),e.jsxs("div",{className:"flex justify-end space-x-3",children:[e.jsx("button",{onClick:w,className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",disabled:n,children:"Cancel"}),r?e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>p(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",disabled:n,children:"Back"}),e.jsxs("button",{onClick:j,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",disabled:n||!d.trim(),children:[n?e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):e.jsx(G,{className:"h-4 w-4 mr-2"}),"Confirm Rejection"]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>p(!0),className:"inline-flex items-center px-4 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",disabled:n,children:[e.jsx(G,{className:"h-4 w-4 mr-2"}),"Reject"]}),e.jsxs("button",{onClick:y,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",disabled:n,children:[n?e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):e.jsx(te,{className:"h-4 w-4 mr-2"}),"Approve Company"]})]})]})]})]})})};Le.propTypes={isOpen:v.bool.isRequired,onClose:v.func.isRequired,company:v.object,onApprove:v.func.isRequired,onReject:v.func.isRequired};const As=()=>{const{user:o,userRole:t}=l.useContext(F),[s,a]=l.useState([]),[m,n]=l.useState(!0),[u,d]=l.useState(!1),[i,r]=l.useState(!1),[p,N]=l.useState(null),[f,y]=l.useState("all");l.useEffect(()=>{o&&(async()=>{try{n(!0);const g=(await W.getCompanies()).map(h=>({...h,approval_status:h.id<=3?"approved":"pending",created_by:h.id>3?"2":"1",created_at:h.id>3?"2025-07-15T11:00:00Z":"2025-07-10T10:00:00Z",documents:h.id>3?[{type:"registration",filename:"company_registration.pdf",status:"pending"},{type:"tax_certificate",filename:"tax_cert.pdf",status:"pending"}]:[]}));a(g)}catch(x){console.error("Failed to load companies:",x),a([])}finally{n(!1)}})()},[o]);const j=async c=>{try{const g={...await W.createCompany(c),approval_status:t==="superadmin"?"approved":"pending",created_by:o.id,created_at:new Date().toISOString(),documents:[]};a(h=>[...h,g]),d(!1)}catch(x){throw console.error("Failed to add company:",x),x}},w=async(c,x)=>{try{a(g=>g.map(h=>h.id===c?{...h,approval_status:"approved",approved_by:o.id,approved_at:new Date().toISOString()}:h)),r(!1),N(null)}catch(g){throw console.error("Failed to approve company:",g),g}},k=async(c,x)=>{try{a(g=>g.map(h=>h.id===c?{...h,approval_status:"rejected",rejection_reason:x,rejected_at:new Date().toISOString()}:h)),r(!1),N(null)}catch(g){throw console.error("Failed to reject company:",g),g}},b=c=>{N(c),r(!0)},I=s.filter(c=>f==="all"?!0:f==="pending"?c.approval_status==="pending":f==="approved"?c.approval_status==="approved":f==="rejected"?c.approval_status==="rejected":!0),_=s.filter(c=>c.approval_status==="pending").length;return m?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Company Oversight"}),e.jsx("p",{className:"text-gray-600",children:"Monitor and manage all companies in the system"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})})]}):e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Company Oversight"}),e.jsx("p",{className:"text-gray-600",children:"Monitor and manage all companies in the system"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[_>0&&e.jsx("div",{className:"bg-orange-100 border border-orange-200 rounded-lg px-3 py-2",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Z,{className:"h-4 w-4 text-orange-600 mr-2"}),e.jsxs("span",{className:"text-sm font-medium text-orange-800",children:[_," pending approval",_!==1?"s":""]})]})}),e.jsxs("button",{onClick:()=>d(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(K,{className:"h-4 w-4 mr-2"}),"Add Company"]})]})]})}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"border-b border-gray-200",children:e.jsx("nav",{className:"-mb-px flex space-x-8",children:[{id:"all",name:"All Companies",count:s.length},{id:"pending",name:"Pending Approval",count:_},{id:"approved",name:"Approved",count:s.filter(c=>c.approval_status==="approved").length},{id:"rejected",name:"Rejected",count:s.filter(c=>c.approval_status==="rejected").length}].map(c=>e.jsxs("button",{onClick:()=>y(c.id),className:`${f===c.id?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`,children:[c.name,e.jsx("span",{className:`ml-2 py-0.5 px-2 rounded-full text-xs ${f===c.id?"bg-primary text-white":"bg-gray-100 text-gray-600"}`,children:c.count})]},c.id))})})}),e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(re,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search companies...",className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary w-64"})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6",children:I.map(c=>e.jsxs("div",{className:`border rounded-lg p-6 hover:shadow-md transition-shadow ${c.approval_status==="pending"?"border-orange-200 bg-orange-50":c.approval_status==="rejected"?"border-red-200 bg-red-50":"border-gray-200 bg-white"}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(V,{className:"h-5 w-5 text-gray-400 mr-2"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:c.name})]}),e.jsxs("span",{className:`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${c.approval_status==="approved"?"bg-green-100 text-green-800":c.approval_status==="pending"?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800"}`,children:[c.approval_status==="approved"&&e.jsx(te,{className:"h-3 w-3 mr-1"}),c.approval_status==="pending"&&e.jsx(Z,{className:"h-3 w-3 mr-1"}),c.approval_status==="rejected"&&e.jsx(P,{className:"h-3 w-3 mr-1"}),c.approval_status]})]}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600 mb-4",children:[e.jsxs("p",{children:["ID: ",c.id]}),e.jsxs("p",{children:["Industry: ",c.industry||"Not specified"]}),e.jsxs("p",{children:["Created: ",new Date(c.created_at).toLocaleDateString()]}),c.documents&&c.documents.length>0&&e.jsxs("div",{className:"flex items-center",children:[e.jsx(Q,{className:"h-4 w-4 mr-1"}),e.jsxs("span",{children:[c.documents.length," document",c.documents.length!==1?"s":""]})]})]}),c.approval_status==="pending"&&e.jsxs("div",{className:"border-t border-gray-200 pt-4",children:[e.jsx("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-3 mb-3",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(z,{className:"h-4 w-4 text-orange-600 mr-2"}),e.jsx("span",{className:"text-xs font-medium text-orange-800",children:"Requires approval"})]})}),e.jsx("button",{onClick:()=>b(c),className:"w-full bg-primary text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-primary-dark transition-colors",children:"Review & Approve"})]}),c.approval_status==="rejected"&&c.rejection_reason&&e.jsx("div",{className:"border-t border-gray-200 pt-4",children:e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:[e.jsx("p",{className:"text-xs font-medium text-red-800 mb-1",children:"Rejection Reason:"}),e.jsx("p",{className:"text-xs text-red-700",children:c.rejection_reason})]})}),c.approval_status==="approved"&&e.jsxs("div",{className:"mt-4 flex space-x-2",children:[e.jsx("button",{className:"text-primary hover:text-primary-dark text-sm",children:"View Details"}),e.jsx("button",{className:"text-gray-600 hover:text-gray-900 text-sm",children:"Edit"})]})]},c.id))})]}),e.jsx($e,{isOpen:u,onClose:()=>d(!1),onSubmit:j,userRole:t,currentUserId:o==null?void 0:o.id}),e.jsx(Le,{isOpen:i,onClose:()=>r(!1),company:p,onApprove:w,onReject:k})]})},ce=({selectedCompany:o,onCompanyChange:t,companies:s,className:a="",showAllOption:m=!0,disabled:n=!1})=>{const{user:u,userRole:d}=l.useContext(F),[i,r]=l.useState([]),[p,N]=l.useState([]);l.useEffect(()=>{u&&(async()=>{try{if(s)r(s);else{const j=await W.getCompanies();N(j);let w=[];switch(d){case"superadmin":case"manager":w=j;break;case"partner":w=(u==null?void 0:u.assigned_companies)||[];break;default:w=[]}r(w),!o&&w.length>0&&!m&&t(w[0].id.toString())}}catch(j){console.error("Failed to load companies:",j),r([])}})()},[u,d,s,o,m,t]);const f=y=>{t(y.target.value)};return e.jsx("div",{className:`relative ${a}`,children:e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:o,onChange:f,disabled:n,className:`
            appearance-none w-full bg-white border border-gray-300 rounded-lg px-4 py-2 pr-10
            text-sm font-medium text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary 
            focus:border-primary disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed
          `,children:[m&&e.jsx("option",{value:"all",children:"All Companies"}),i.map(y=>e.jsx("option",{value:y.id,children:y.name},y.id))]}),e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:e.jsx(ve,{className:"h-4 w-4 text-gray-400"})}),e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(T,{className:"h-4 w-4 text-gray-400"})})]})})};ce.propTypes={selectedCompany:v.string.isRequired,onCompanyChange:v.func.isRequired,companies:v.arrayOf(v.shape({id:v.string.isRequired,name:v.string.isRequired})),className:v.string,showAllOption:v.bool,disabled:v.bool};v.arrayOf(v.string).isRequired,v.func.isRequired,v.arrayOf(v.shape({id:v.string.isRequired,name:v.string.isRequired})),v.string,v.string,v.bool,v.bool,v.number;const Ne=({isOpen:o,onClose:t,onUpload:s})=>{var w;const[a,m]=l.useState(null),[n,u]=l.useState("select"),[d,i]=l.useState(0),[r,p]=l.useState(null),N=k=>{m(k),u("select")},f=async()=>{if(a){u("uploading"),i(0);try{const k=setInterval(()=>{i(I=>I>=90?(clearInterval(k),90):I+10)},200),b=await s(a);clearInterval(k),i(100),setTimeout(()=>{p(b),u(b.success?"success":"error")},500)}catch(k){u("error"),p({success:!1,error:k.message||"Upload failed"})}}},y=()=>{m(null),u("select"),i(0),p(null),t()},j=()=>{m(null),u("select"),i(0),p(null)};return o?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:y}),e.jsxs("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[e.jsx("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Upload Inventory File"}),e.jsx("button",{onClick:y,className:"text-gray-400 hover:text-gray-600",children:e.jsx(P,{className:"h-6 w-6"})})]})}),e.jsxs("div",{className:"px-4 pb-4 sm:px-6 sm:pb-6",children:[n==="select"&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Upload Excel (.xlsx, .xls) or PDF files containing inventory data. The system will automatically detect and parse the content."}),e.jsx(J,{onFileSelect:N,acceptedTypes:[".xlsx",".xls",".pdf"],maxSize:10*1024*1024,multiple:!1}),a&&e.jsx("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ie,{className:"h-5 w-5 text-blue-500 mr-2"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-blue-900",children:a.name}),e.jsxs("p",{className:"text-xs text-blue-700",children:[(a.size/1024/1024).toFixed(2)," MB"]})]})]})})]}),n==="uploading"&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(se,{className:"mx-auto h-12 w-12 text-blue-500 animate-pulse"}),e.jsx("p",{className:"mt-2 text-sm font-medium text-gray-900",children:"Uploading and processing..."}),e.jsx("p",{className:"text-xs text-gray-500",children:"Please wait while we process your file"})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${d}%`}})}),e.jsxs("p",{className:"text-center text-sm text-gray-600",children:[d,"% complete"]})]}),n==="success"&&r&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(le,{className:"mx-auto h-12 w-12 text-green-500"}),e.jsx("p",{className:"mt-2 text-sm font-medium text-gray-900",children:"Upload Successful!"})]}),e.jsx("div",{className:"bg-green-50 p-4 rounded-lg",children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-900",children:"Processed Items:"}),e.jsx("p",{className:"text-green-700",children:r.processedItems||0})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-900",children:"Created Items:"}),e.jsx("p",{className:"text-green-700",children:r.createdItems||0})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-900",children:"Updated Items:"}),e.jsx("p",{className:"text-green-700",children:r.updatedItems||0})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-900",children:"Errors:"}),e.jsx("p",{className:"text-green-700",children:((w=r.errors)==null?void 0:w.length)||0})]})]})}),r.errors&&r.errors.length>0&&e.jsxs("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[e.jsx("p",{className:"font-medium text-yellow-900 mb-2",children:"Warnings:"}),e.jsxs("ul",{className:"text-sm text-yellow-700 space-y-1",children:[r.errors.slice(0,5).map((k,b)=>e.jsxs("li",{children:["• ",k]},b)),r.errors.length>5&&e.jsxs("li",{children:["• ... and ",r.errors.length-5," more"]})]})]})]}),n==="error"&&r&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(z,{className:"mx-auto h-12 w-12 text-red-500"}),e.jsx("p",{className:"mt-2 text-sm font-medium text-gray-900",children:"Upload Failed"})]}),e.jsx("div",{className:"bg-red-50 p-4 rounded-lg",children:e.jsx("p",{className:"text-sm text-red-700",children:r.error||"An error occurred during upload"})})]})]}),e.jsxs("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[n==="select"&&e.jsxs(e.Fragment,{children:[e.jsx("button",{type:"button",onClick:f,disabled:!a,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm disabled:bg-gray-300 disabled:cursor-not-allowed",children:"Upload File"}),e.jsx("button",{type:"button",onClick:y,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"Cancel"})]}),n==="uploading"&&e.jsx("button",{type:"button",onClick:y,className:"w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:w-auto sm:text-sm",children:"Cancel"}),(n==="success"||n==="error")&&e.jsxs(e.Fragment,{children:[e.jsx("button",{type:"button",onClick:y,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm",children:"Done"}),e.jsx("button",{type:"button",onClick:j,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"Upload Another"})]})]})]})]})}):null};Ne.propTypes={isOpen:v.bool.isRequired,onClose:v.func.isRequired,onUpload:v.func.isRequired};const ae=({items:o=[],onItemSelect:t,selectedItems:s=[],showCompanyColumn:a=!0,showActions:m=!0,className:n=""})=>{const[u,d]=l.useState("name"),[i,r]=l.useState("asc"),[p,N]=l.useState(""),[f,y]=l.useState("all"),j=l.useMemo(()=>{let x=o.filter(g=>{var A;const h=g.name.toLowerCase().includes(p.toLowerCase())||((A=g.sku)==null?void 0:A.toLowerCase().includes(p.toLowerCase()))||g.category.toLowerCase().includes(p.toLowerCase()),S=f==="all"||g.status===f;return h&&S});return x.sort((g,h)=>{let S=g[u],A=h[u];return typeof S=="string"&&(S=S.toLowerCase(),A=A.toLowerCase()),i==="asc"?S<A?-1:S>A?1:0:S>A?-1:S<A?1:0}),x},[o,p,f,u,i]),w=x=>{u===x?r(i==="asc"?"desc":"asc"):(d(x),r("asc"))},k=x=>{t(x?j.map(g=>g.id):[])},b=(x,g)=>{t(g?[...s,x]:s.filter(h=>h!==x))},I=({field:x,children:g})=>e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>w(x),children:e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("span",{children:g}),u===x&&(i==="asc"?e.jsx(Xe,{className:"h-4 w-4"}):e.jsx(ve,{className:"h-4 w-4"}))]})}),_=x=>{const g={active:"bg-green-100 text-green-800",inactive:"bg-yellow-100 text-yellow-800",discontinued:"bg-red-100 text-red-800"};return e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${g[x]||"bg-gray-100 text-gray-800"}`,children:x})},c=x=>x.currentQuantity<=x.minStockLevel;return e.jsxs("div",{className:`bg-white shadow rounded-lg ${n}`,children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx(re,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search items...",value:p,onChange:x=>N(x.target.value),className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"})]}),e.jsxs("select",{value:f,onChange:x=>y(x.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"}),e.jsx("option",{value:"discontinued",children:"Discontinued"})]})]}),e.jsxs("div",{className:"text-sm text-gray-500",children:[j.length," of ",o.length," items"]})]})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[m&&e.jsx("th",{className:"px-6 py-3 text-left",children:e.jsx("input",{type:"checkbox",checked:s.length===j.length&&j.length>0,onChange:x=>k(x.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"})}),e.jsx(I,{field:"name",children:"Name"}),e.jsx(I,{field:"sku",children:"SKU"}),e.jsx(I,{field:"category",children:"Category"}),e.jsx(I,{field:"currentQuantity",children:"Quantity"}),e.jsx(I,{field:"unitPrice",children:"Unit Price"}),e.jsx(I,{field:"totalValue",children:"Total Value"}),a&&e.jsx(I,{field:"companyName",children:"Company"}),e.jsx(I,{field:"status",children:"Status"}),e.jsx(I,{field:"lastUpdated",children:"Last Updated"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:j.map(x=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[m&&e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("input",{type:"checkbox",checked:s.includes(x.id),onChange:g=>b(x.id,g.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:x.name}),x.description&&e.jsx("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:x.description})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:x.sku||"-"}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:x.category}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.jsxs("div",{className:`${c(x)?"text-red-600 font-semibold":""}`,children:[x.currentQuantity,c(x)&&e.jsx("span",{className:"ml-1 text-xs text-red-500",children:"(Low)"})]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Min: ",x.minStockLevel]})]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",x.unitPrice.toFixed(2)]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",x.totalValue.toFixed(2)]}),a&&e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:x.companyName}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:_(x.status)}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(x.lastUpdated).toLocaleDateString()})]},x.id))})]})}),j.length===0&&e.jsx("div",{className:"text-center py-12",children:e.jsx("div",{className:"text-gray-500",children:"No items found"})})]})};ae.propTypes={items:v.arrayOf(v.object),onItemSelect:v.func,selectedItems:v.arrayOf(v.string),showCompanyColumn:v.bool,showActions:v.bool,className:v.string};const qe=({stats:o,className:t=""})=>{const s=n=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(n),a=n=>new Intl.NumberFormat("en-US").format(n),m=({title:n,value:u,icon:d,trend:i,trendValue:r,color:p="blue"})=>{const N={blue:"bg-blue-50 text-blue-600",green:"bg-green-50 text-green-600",yellow:"bg-yellow-50 text-yellow-600",red:"bg-red-50 text-red-600",purple:"bg-purple-50 text-purple-600"};return e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:n}),e.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:u}),i&&e.jsxs("div",{className:"flex items-center mt-2",children:[i==="up"?e.jsx(ge,{className:"h-4 w-4 text-green-500 mr-1"}):e.jsx(es,{className:"h-4 w-4 text-red-500 mr-1"}),e.jsx("span",{className:`text-sm ${i==="up"?"text-green-600":"text-red-600"}`,children:r})]})]}),e.jsx("div",{className:`p-3 rounded-full ${N[p]}`,children:d})]})})};return e.jsxs("div",{className:`space-y-6 ${t}`,children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.jsx(m,{title:"Total Items",value:a(o.totalItems),icon:e.jsx(O,{className:"h-6 w-6"}),color:"blue"}),e.jsx(m,{title:"Total Value",value:s(o.totalValue),icon:e.jsx(H,{className:"h-6 w-6"}),color:"green"}),e.jsx(m,{title:"Low Stock Items",value:a(o.lowStockItems),icon:e.jsx(B,{className:"h-6 w-6"}),color:"red"}),e.jsx(m,{title:"Companies",value:a(o.companiesCount),icon:e.jsx(V,{className:"h-6 w-6"}),color:"purple"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Top Categories"})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:o.topCategories.map((n,u)=>{const d=n.value/o.totalValue*100;return e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:`w-3 h-3 rounded-full ${u===0?"bg-blue-500":u===1?"bg-green-500":u===2?"bg-yellow-500":u===3?"bg-purple-500":"bg-gray-500"}`})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:n.category}),e.jsxs("p",{className:"text-xs text-gray-500",children:[n.count," items"]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:s(n.value)}),e.jsxs("p",{className:"text-xs text-gray-500",children:[d.toFixed(1),"%"]})]})]},n.category)})})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Average Item Value"}),e.jsx("p",{className:"text-xl font-semibold text-gray-900",children:s(o.totalValue/o.totalItems||0)})]}),e.jsx(H,{className:"h-8 w-8 text-gray-400"})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Low Stock Rate"}),e.jsxs("p",{className:"text-xl font-semibold text-gray-900",children:[(o.lowStockItems/o.totalItems*100||0).toFixed(1),"%"]})]}),e.jsx(B,{className:"h-8 w-8 text-gray-400"})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Recent Transactions"}),e.jsx("p",{className:"text-xl font-semibold text-gray-900",children:a(o.recentTransactions)})]}),e.jsx(ge,{className:"h-8 w-8 text-gray-400"})]})})]})]})};qe.propTypes={stats:v.shape({totalItems:v.number.isRequired,totalValue:v.number.isRequired,lowStockItems:v.number.isRequired,companiesCount:v.number.isRequired,recentTransactions:v.number.isRequired,topCategories:v.arrayOf(v.shape({category:v.string.isRequired,count:v.number.isRequired,value:v.number.isRequired})).isRequired}).isRequired,className:v.string};const Rs=()=>{const{user:o}=l.useContext(F),[t,s]=l.useState([]),[a,m]=l.useState("all"),[n,u]=l.useState([]),[d,i]=l.useState(!1),[r,p]=l.useState([]),[N,f]=l.useState(!0),[y,j]=l.useState(null);l.useEffect(()=>{o&&(async()=>{try{f(!0);const b=await M.getInventoryItems();s(b);const I={totalItems:b.length,totalValue:b.reduce((_,c)=>_+(c.total_value||0),0),lowStockItems:b.filter(_=>_.current_quantity<=_.min_stock_level).length,categories:[...new Set(b.map(_=>_.category))].length};j(I)}catch(b){console.error("Failed to load inventory data:",b),s([]),j(null)}finally{f(!1)}})()},[o]),l.useEffect(()=>{p(a==="all"?t:t.filter(k=>k.companyId===a))},[t,a]);const w=async k=>new Promise(b=>{setTimeout(()=>{b({success:!0,processedItems:15,createdItems:8,updatedItems:7,errors:['Row 3: Missing SKU for item "Test Item"']})},2e3)});return N?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Inventory Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage inventory across all companies"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})})]}):e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Inventory Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage inventory across all companies"})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsxs("button",{onClick:()=>i(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(se,{className:"h-4 w-4 mr-2"}),"Upload Inventory"]}),e.jsxs("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[e.jsx(Ae,{className:"h-4 w-4 mr-2"}),"Export"]})]})]})}),y&&e.jsx("div",{className:"mb-8",children:e.jsx(qe,{stats:y})}),e.jsxs("div",{className:"mb-6 flex items-center space-x-4",children:[e.jsx(ce,{selectedCompany:a,onCompanyChange:m,className:"w-64"}),e.jsxs("button",{className:"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[e.jsx(_e,{className:"h-4 w-4 mr-2"}),"More Filters"]})]}),e.jsx(ae,{items:r,selectedItems:n,onItemSelect:u,showCompanyColumn:a==="all"}),e.jsx(Ne,{isOpen:d,onClose:()=>i(!1),onUpload:w})]})},Ds=()=>{const{userRole:o}=l.useContext(F),[t,s]=l.useState([{id:"1",name:"Bob Partner",email:"<EMAIL>",companies:["Acme Corp","TechStart Inc"],status:"active"},{id:"2",name:"Alice Partner",email:"<EMAIL>",companies:["Global Ventures"],status:"active"}]),[a,m]=l.useState(!1),n=async u=>{try{const d={...u,role:"partner"},i=await Te.createUser(d);s(r=>[...r,{id:i.id,name:i.name,email:i.email,companies:[],status:i.status||"active"}]),m(!1)}catch(d){throw console.error("Failed to add partner:",d),d}};return e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Partner Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage partners and their company assignments"})]}),e.jsxs("button",{onClick:()=>m(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(K,{className:"h-4 w-4 mr-2"}),"Add Partner"]})]})}),e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(re,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search partners...",className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary w-64"})]})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Partner"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Assigned Companies"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map(u=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:u.name}),e.jsx("div",{className:"text-sm text-gray-500",children:u.email})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:u.companies.join(", ")})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:u.status})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[e.jsx("button",{className:"text-primary hover:text-primary-dark mr-3",children:"Edit"}),e.jsx("button",{className:"text-red-600 hover:text-red-900",children:"Remove"})]})]},u.id))})]})})]}),e.jsx(fe,{isOpen:a,onClose:()=>m(!1),onSubmit:n,userRole:o})]})},Fs=()=>{const{user:o,userRole:t}=l.useContext(F),[s,a]=l.useState([]),[m,n]=l.useState(!0),[u,d]=l.useState(!1);l.useEffect(()=>{o&&(async()=>{try{n(!0);const p=await W.getCompanies();a(p)}catch(p){console.error("Failed to load companies:",p),a([])}finally{n(!1)}})()},[o]);const i=async r=>{try{const p={...r,managerId:t==="manager"?o.id:r.managerId},N=await W.createCompany(p);a(f=>[...f,N]),d(!1)}catch(p){throw console.error("Failed to add company:",p),p}};return m?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Company Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage companies and their details"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})})]}):e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Company Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage companies and their details"})]}),e.jsxs("button",{onClick:()=>d(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(K,{className:"h-4 w-4 mr-2"}),"Add Company"]})]})}),e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(re,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search companies...",className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary w-64"})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6",children:s.map(r=>e.jsxs("div",{className:"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:r.name}),e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:"Active"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsxs("p",{children:["ID: ",r.id]}),e.jsx("p",{children:"Inventory Items: 3"}),e.jsx("p",{children:"Assigned Partners: 1"})]}),e.jsxs("div",{className:"mt-4 flex space-x-2",children:[e.jsx("button",{className:"text-primary hover:text-primary-dark text-sm",children:"View Details"}),e.jsx("button",{className:"text-gray-600 hover:text-gray-900 text-sm",children:"Edit"})]})]},r.id))})]}),e.jsx($e,{isOpen:u,onClose:()=>d(!1),onSubmit:i,userRole:t,currentUserId:o==null?void 0:o.id})]})},Es=()=>{const{user:o}=l.useContext(F),[t,s]=l.useState([]),[a,m]=l.useState("all"),[n,u]=l.useState([]),[d,i]=l.useState(!1),[r,p]=l.useState([]),[N,f]=l.useState(!0);l.useEffect(()=>{o&&(async()=>{try{f(!0);const w=await M.getInventoryItems();s(w)}catch(w){console.error("Failed to load inventory items:",w),s([])}finally{f(!1)}})()},[o]),l.useEffect(()=>{p(a==="all"?t:t.filter(j=>j.companyId===a))},[t,a]);const y=async j=>new Promise(w=>{setTimeout(()=>{w({success:!0,processedItems:10,createdItems:5,updatedItems:5,errors:[]})},2e3)});return e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Inventory Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage inventory for your companies"})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsxs("button",{onClick:()=>i(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(se,{className:"h-4 w-4 mr-2"}),"Upload Inventory"]}),e.jsxs("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[e.jsx(Ae,{className:"h-4 w-4 mr-2"}),"Export"]})]})]})}),e.jsx("div",{className:"mb-6",children:e.jsx(ce,{selectedCompany:a,onCompanyChange:m,className:"w-64"})}),e.jsx(ae,{items:r,selectedItems:n,onItemSelect:u,showCompanyColumn:a==="all"}),e.jsx(Ne,{isOpen:d,onClose:()=>i(!1),onUpload:y})]})},Ps=()=>{const o=X(),[t]=l.useState([{id:"1",name:"Acme Corp",inventoryItems:3,totalValue:17899.67,lastUpdate:"2024-01-15"},{id:"2",name:"TechStart Inc",inventoryItems:2,totalValue:2949.83,lastUpdate:"2024-01-13"}]);return e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Your Companies"}),e.jsx("p",{className:"text-gray-600",children:"Companies assigned to your portfolio"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:t.map(s=>e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:s.name}),e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:"Active"})]}),e.jsxs("div",{className:"space-y-3 mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Inventory Items"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(O,{className:"h-4 w-4 text-gray-400 mr-1"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:s.inventoryItems})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Total Value"}),e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["$",s.totalValue.toLocaleString()]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Last Updated"}),e.jsx("span",{className:"text-sm text-gray-500",children:new Date(s.lastUpdate).toLocaleDateString()})]})]}),e.jsxs("button",{onClick:()=>o(`/partner/company/${s.id}`),className:"w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(ee,{className:"h-4 w-4 mr-2"}),"View Details"]})]},s.id))})]})},Us=()=>{const{id:o}=Be(),t=X(),{user:s}=l.useContext(F),[a,m]=l.useState(null),[n,u]=l.useState([]),[d,i]=l.useState(!0);if(l.useEffect(()=>{s&&o&&(async()=>{try{i(!0);const f=await W.getCompany(o);m(f);const y=await M.getInventoryItems({company_id:o});u(y)}catch(f){console.error("Failed to load company data:",f),m(null),u([])}finally{i(!1)}})()},[o,s]),d)return e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})})});if(!a)return e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-gray-500",children:"Company not found"})})});const r=n.reduce((N,f)=>N+f.totalValue,0),p=n.filter(N=>N.currentQuantity<=N.minStockLevel).length;return e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("button",{onClick:()=>t("/partner/companies"),className:"inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mb-4",children:[e.jsx(Ce,{className:"h-4 w-4 mr-1"}),"Back to Companies"]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:a.name}),e.jsx("p",{className:"text-gray-600",children:"Company inventory and details"})]}),e.jsx("span",{className:"inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800",children:"Active"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Items"}),e.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:n.length})]}),e.jsx("div",{className:"p-3 rounded-full bg-blue-50 text-blue-600",children:e.jsx(O,{className:"h-6 w-6"})})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Value"}),e.jsxs("p",{className:"text-2xl font-semibold text-gray-900",children:["$",r.toLocaleString()]})]}),e.jsx("div",{className:"p-3 rounded-full bg-green-50 text-green-600",children:e.jsx(H,{className:"h-6 w-6"})})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Low Stock Items"}),e.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:p})]}),e.jsx("div",{className:"p-3 rounded-full bg-red-50 text-red-600",children:e.jsx(ge,{className:"h-6 w-6"})})]})})]}),e.jsx(ae,{items:n,showCompanyColumn:!1,showActions:!1})]})},Ts=()=>{const{user:o}=l.useContext(F),[t,s]=l.useState([]),[a,m]=l.useState("all"),[n,u]=l.useState([]),[d,i]=l.useState([]),[r,p]=l.useState(!0);return l.useEffect(()=>{o&&(async()=>{var f;try{p(!0);const y=await M.getInventoryItems();s(y),((f=o==null?void 0:o.assigned_companies)==null?void 0:f.length)>0&&m(o.assigned_companies[0].id.toString())}catch(y){console.error("Failed to load inventory items:",y),s([])}finally{p(!1)}})()},[o]),l.useEffect(()=>{i(a==="all"?t:t.filter(N=>N.companyId===a))},[t,a]),e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Inventory Management"}),e.jsx("p",{className:"text-gray-600",children:"View inventory for your assigned companies"})]}),e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg px-4 py-2",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(be,{className:"h-4 w-4 text-blue-600 mr-2"}),e.jsx("span",{className:"text-sm font-medium text-blue-800",children:"Read-Only Access"})]})})]})}),e.jsx("div",{className:"mb-6",children:e.jsx(ce,{selectedCompany:a,onCompanyChange:m,className:"w-64",showAllOption:!0})}),e.jsx(ae,{items:d,selectedItems:n,onItemSelect:u,showCompanyColumn:a==="all",showActions:!1})]})};class Ms{async getNotifications(t={}){try{const s=await C.getNotifications(t);if(s.success)return s.data||[];throw new Error(s.message||"Failed to fetch notifications")}catch(s){throw console.error("Error fetching notifications:",s),s}}async markAsRead(t){try{const s=await C.markNotificationAsRead(t);if(s.success)return!0;throw new Error(s.message||"Failed to mark notification as read")}catch(s){throw console.error("Error marking notification as read:",s),s}}async markAllAsRead(){try{const t=await C.markAllNotificationsAsRead();if(t.success)return t.data;throw new Error(t.message||"Failed to mark all notifications as read")}catch(t){throw console.error("Error marking all notifications as read:",t),t}}async deleteNotification(t){try{const s=await C.deleteNotification(t);if(s.success)return!0;throw new Error(s.message||"Failed to delete notification")}catch(s){throw console.error("Error deleting notification:",s),s}}async getUnreadCount(){try{return(await this.getNotifications({unread_only:!0})).unread_count||0}catch(t){return console.error("Error fetching unread count:",t),0}}}const $s=new Ms,D=({children:o})=>{const[t,s]=l.useState(!1),[a,m]=l.useState(0),{user:n,userRole:u,userEmail:d,logout:i}=l.useContext(F),r=X(),p=Ve();l.useEffect(()=>{(async()=>{try{if(n){const w=await $s.getNotifications({is_read:!1});m(w.length)}}catch(w){console.error("Failed to load notification count:",w),m(0)}})()},[n]);const N=()=>{i(),r("/login")},y=(()=>{switch(u){case"superadmin":return[{name:"Dashboard",icon:e.jsx(de,{size:20}),path:"/superadmin"},{name:"User Management",icon:e.jsx(pe,{size:20}),path:"/superadmin/users"},{name:"Company Oversight",icon:e.jsx(T,{size:20}),path:"/superadmin/companies"},{name:"Inventory",icon:e.jsx(O,{size:20}),path:"/superadmin/inventory"},{name:"Notifications",icon:e.jsx(q,{size:20}),path:"/superadmin/notifications",badge:a},{name:"Settings",icon:e.jsx(me,{size:20}),path:"/superadmin/settings"}];case"manager":return[{name:"Dashboard",icon:e.jsx(de,{size:20}),path:"/manager"},{name:"Partners",icon:e.jsx(pe,{size:20}),path:"/manager/partners"},{name:"Companies",icon:e.jsx(T,{size:20}),path:"/manager/companies"},{name:"Inventory",icon:e.jsx(O,{size:20}),path:"/manager/inventory"},{name:"Notifications",icon:e.jsx(q,{size:20}),path:"/manager/notifications",badge:a},{name:"Settings",icon:e.jsx(me,{size:20}),path:"/manager/settings"}];case"partner":return[{name:"Portfolio",icon:e.jsx(de,{size:20}),path:"/partner"},{name:"Companies",icon:e.jsx(T,{size:20}),path:"/partner/companies"},{name:"Inventory",icon:e.jsx(O,{size:20}),path:"/partner/inventory"},{name:"Notifications",icon:e.jsx(q,{size:20}),path:"/partner/notifications",badge:a},{name:"Settings",icon:e.jsx(me,{size:20}),path:"/partner/settings"}];default:return[]}})();return e.jsxs("div",{className:"flex h-screen bg-neutral-light overflow-hidden",children:[t&&e.jsx("div",{className:"fixed inset-0 z-20 bg-black bg-opacity-50 transition-opacity md:hidden",onClick:()=>s(!1)}),e.jsx("div",{className:`fixed inset-y-0 left-0 z-30 w-64 transform bg-secondary transition duration-300 ease-in-out md:relative md:translate-x-0 ${t?"translate-x-0":"-translate-x-full"}`,children:e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between px-4 py-5 border-b border-secondary-dark",children:[e.jsx("div",{className:"flex items-center",children:e.jsx("span",{className:"text-xl font-bold text-white",children:"Enterprise Portal"})}),e.jsx("button",{onClick:()=>s(!1),className:"text-white md:hidden",children:e.jsx(P,{size:24})})]}),e.jsx("div",{className:"px-4 py-4 border-b border-secondary-dark",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"h-10 w-10 rounded-full bg-primary flex items-center justify-center text-white font-bold",children:u==null?void 0:u.charAt(0).toUpperCase()}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm font-medium text-white",children:u==="superadmin"?"Super Admin":u==="manager"?"Manager":"Partner"}),e.jsx("p",{className:"text-xs text-white text-opacity-70",children:d||"No email"})]}),e.jsx(ve,{size:16,className:"ml-auto text-white text-opacity-70"})]})}),e.jsx("nav",{className:"flex-1 overflow-y-auto px-2 py-4",children:e.jsx("ul",{className:"space-y-1",children:y.map(j=>e.jsx("li",{children:e.jsxs("a",{href:j.path,onClick:w=>{w.preventDefault(),r(j.path),s(!1)},className:`flex items-center px-4 py-3 text-sm rounded-lg ${p.pathname===j.path?"bg-primary text-white":"text-white text-opacity-80 hover:bg-secondary-dark"}`,children:[e.jsx("span",{className:"mr-3",children:j.icon}),j.name,j.badge&&e.jsx("span",{className:"ml-auto bg-red-500 text-white text-xs font-bold px-2 py-0.5 rounded-full",children:j.badge})]})},j.name))})}),e.jsx("div",{className:"border-t border-secondary-dark p-4",children:e.jsxs("button",{onClick:N,className:"flex w-full items-center px-4 py-3 text-sm text-white text-opacity-80 rounded-lg hover:bg-secondary-dark",children:[e.jsx(ss,{size:20,className:"mr-3"}),"Sign Out"]})})]})}),e.jsxs("div",{className:"flex flex-1 flex-col overflow-hidden",children:[e.jsx("header",{className:"bg-white shadow-sm z-10",children:e.jsxs("div",{className:"px-4 py-4 flex items-center justify-between",children:[e.jsx("button",{onClick:()=>s(!0),className:"text-neutral-dark md:hidden",children:e.jsx(ts,{size:24})}),e.jsx("div",{className:"md:hidden font-montserrat font-bold text-lg",children:"Enterprise Portal"}),e.jsx("div",{className:"flex items-center space-x-4",children:e.jsxs("button",{className:"relative p-1 rounded-full text-gray-500 hover:bg-gray-100 hover:text-gray-600",onClick:()=>r(`/${u}/notifications`),children:[e.jsx(q,{size:20}),a>0&&e.jsx("span",{className:"absolute top-0 right-0 block h-4 w-4 rounded-full bg-red-500 text-white text-xs font-bold flex items-center justify-center",children:a})]})})]})}),e.jsx("main",{className:"flex-1 overflow-y-auto bg-neutral-light",children:o})]})]})};D.propTypes={children:v.node.isRequired};const xe=()=>{const[o,t]=l.useState([{id:"1",type:"warning",title:"Low Stock Alert",message:"Conference Tables at TechStart Inc is running low (2 remaining, minimum 1)",timestamp:"2024-01-15T10:30:00Z",read:!1},{id:"2",type:"info",title:"Inventory Upload Completed",message:"Successfully processed 15 items from purchases_january_2024.xlsx",timestamp:"2024-01-15T09:15:00Z",read:!1},{id:"3",type:"success",title:"New Partner Added",message:"Alice Partner has been successfully added to the system",timestamp:"2024-01-14T16:45:00Z",read:!0}]),s=d=>{t(i=>i.map(r=>r.id===d?{...r,read:!0}:r))},a=()=>{t(d=>d.map(i=>({...i,read:!0})))},m=d=>{t(i=>i.filter(r=>r.id!==d))},n=d=>{switch(d){case"warning":return e.jsx(B,{className:"h-5 w-5 text-yellow-500"});case"info":return e.jsx(be,{className:"h-5 w-5 text-blue-500"});case"success":return e.jsx(te,{className:"h-5 w-5 text-green-500"});default:return e.jsx(q,{className:"h-5 w-5 text-gray-500"})}},u=o.filter(d=>!d.read).length;return e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"}),e.jsx("p",{className:"text-gray-600",children:u>0?`${u} unread notifications`:"All notifications read"})]}),u>0&&e.jsx("button",{onClick:a,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:"Mark all as read"})]})}),e.jsx("div",{className:"bg-white shadow rounded-lg",children:o.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(q,{className:"mx-auto h-12 w-12 text-gray-400"}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No notifications"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"You're all caught up!"})]}):e.jsx("div",{className:"divide-y divide-gray-200",children:o.map(d=>e.jsx("div",{className:`p-6 hover:bg-gray-50 ${d.read?"":"bg-blue-50"}`,children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:n(d.type)}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("p",{className:`text-sm font-medium ${d.read?"text-gray-700":"text-gray-900"}`,children:d.title}),!d.read&&e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})]}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:d.message}),e.jsx("p",{className:"mt-2 text-xs text-gray-500",children:new Date(d.timestamp).toLocaleString()})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[!d.read&&e.jsx("button",{onClick:()=>s(d.id),className:"text-blue-600 hover:text-blue-800 text-sm",children:"Mark as read"}),e.jsx("button",{onClick:()=>m(d.id),className:"text-gray-400 hover:text-gray-600",children:e.jsx(P,{className:"h-4 w-4"})})]})]})},d.id))})})]})},ue=()=>{const{userEmail:o,userRole:t}=l.useContext(F),[s,a]=l.useState({emailNotifications:!0,pushNotifications:!1,lowStockAlerts:!0,weeklyReports:!0,theme:"light",language:"en"}),m=(d,i)=>{a(r=>({...r,[d]:i}))},n=({title:d,icon:i,children:r})=>e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-2 bg-gray-100 rounded-lg mr-3",children:i}),e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:d})]})}),e.jsx("div",{className:"p-6",children:r})]}),u=({label:d,description:i,checked:r,onChange:p})=>e.jsxs("div",{className:"flex items-center justify-between py-3",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:d}),i&&e.jsx("p",{className:"text-sm text-gray-500",children:i})]}),e.jsx("button",{onClick:()=>p(!r),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${r?"bg-primary":"bg-gray-200"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${r?"translate-x-6":"translate-x-1"}`})})]});return e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Settings"}),e.jsx("p",{className:"text-gray-600",children:"Manage your account preferences and system settings"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(n,{title:"Profile",icon:e.jsx(ne,{className:"h-5 w-5 text-gray-600"}),children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),e.jsx("input",{type:"email",value:o,disabled:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Role"}),e.jsx("input",{type:"text",value:t,disabled:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm"})]})]})}),e.jsx(n,{title:"Notifications",icon:e.jsx(q,{className:"h-5 w-5 text-gray-600"}),children:e.jsxs("div",{className:"space-y-1",children:[e.jsx(u,{label:"Email Notifications",description:"Receive notifications via email",checked:s.emailNotifications,onChange:d=>m("emailNotifications",d)}),e.jsx(u,{label:"Push Notifications",description:"Receive browser push notifications",checked:s.pushNotifications,onChange:d=>m("pushNotifications",d)}),e.jsx(u,{label:"Low Stock Alerts",description:"Get notified when inventory is running low",checked:s.lowStockAlerts,onChange:d=>m("lowStockAlerts",d)}),e.jsx(u,{label:"Weekly Reports",description:"Receive weekly inventory summary reports",checked:s.weeklyReports,onChange:d=>m("weeklyReports",d)})]})}),e.jsx(n,{title:"Security",icon:e.jsx(rs,{className:"h-5 w-5 text-gray-600"}),children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:"Change Password"}),e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-600",children:["Last login: ",new Date().toLocaleDateString()," at ",new Date().toLocaleTimeString()]})})]})}),(t==="superadmin"||t==="manager")&&e.jsx(n,{title:"System",icon:e.jsx(as,{className:"h-5 w-5 text-gray-600"}),children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Default File Upload Size Limit"}),e.jsxs("select",{className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",children:[e.jsx("option",{children:"10 MB"}),e.jsx("option",{children:"25 MB"}),e.jsx("option",{children:"50 MB"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Data Retention Period"}),e.jsxs("select",{className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",children:[e.jsx("option",{children:"1 Year"}),e.jsx("option",{children:"2 Years"}),e.jsx("option",{children:"5 Years"})]})]})]})})]}),e.jsx("div",{className:"mt-8 flex justify-end",children:e.jsx("button",{className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:"Save Changes"})})]})},F=l.createContext({isAuthenticated:!1,user:null,userRole:null,userEmail:"",login:()=>{},logout:()=>{}});function Ls(){const[o,t]=l.useState(!1),[s,a]=l.useState(null),[m,n]=l.useState(null),[u,d]=l.useState(""),[i,r]=l.useState(!0);l.useEffect(()=>{(async()=>{try{if(L.isAuthenticated()){await L.initializeFromToken();const j=L.getCurrentUser();j&&(t(!0),a(j),n(j.role),d(j.email))}}catch(j){console.error("Auth initialization failed:",j),L.logout()}finally{r(!1)}})()},[]);const p=y=>(t(!0),a(y),n(y.role),d(y.email),!0),N=async()=>{try{await L.logout()}catch(y){console.error("Logout error:",y)}finally{t(!1),a(null),n(null),d("")}};if(i)return e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"}),e.jsx("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})});const f=({children:y,requiredRole:j})=>o?j&&m!==j?m==="superadmin"?e.jsx(U,{to:"/superadmin",replace:!0}):m==="manager"?e.jsx(U,{to:"/manager",replace:!0}):e.jsx(U,{to:"/partner",replace:!0}):y:e.jsx(U,{to:"/login",replace:!0});return f.propTypes={children:v.node.isRequired,requiredRole:v.oneOf(["superadmin","manager","partner"])},e.jsx(F.Provider,{value:{isAuthenticated:o,user:s,userRole:m,userEmail:u,login:p,logout:N},children:e.jsx(Ge,{children:e.jsxs(We,{children:[e.jsx(R,{path:"/",element:o?m==="superadmin"?e.jsx(U,{to:"/superadmin",replace:!0}):m==="manager"?e.jsx(U,{to:"/manager",replace:!0}):e.jsx(U,{to:"/partner",replace:!0}):e.jsx(U,{to:"/login",replace:!0})}),e.jsx(R,{path:"/login",element:e.jsx(bs,{})}),e.jsx(R,{path:"/reset-password",element:e.jsx(vs,{})}),e.jsx(R,{path:"/superadmin",element:e.jsx(f,{requiredRole:"superadmin",children:e.jsx(D,{children:e.jsx(Cs,{})})})}),e.jsx(R,{path:"/superadmin/users",element:e.jsx(f,{requiredRole:"superadmin",children:e.jsx(D,{children:e.jsx(Is,{})})})}),e.jsx(R,{path:"/superadmin/companies",element:e.jsx(f,{requiredRole:"superadmin",children:e.jsx(D,{children:e.jsx(As,{})})})}),e.jsx(R,{path:"/superadmin/inventory",element:e.jsx(f,{requiredRole:"superadmin",children:e.jsx(D,{children:e.jsx(Rs,{})})})}),e.jsx(R,{path:"/superadmin/notifications",element:e.jsx(f,{requiredRole:"superadmin",children:e.jsx(D,{children:e.jsx(xe,{})})})}),e.jsx(R,{path:"/superadmin/settings",element:e.jsx(f,{requiredRole:"superadmin",children:e.jsx(D,{children:e.jsx(ue,{})})})}),e.jsx(R,{path:"/manager",element:e.jsx(f,{requiredRole:"manager",children:e.jsx(D,{children:e.jsx(ks,{})})})}),e.jsx(R,{path:"/manager/partners",element:e.jsx(f,{requiredRole:"manager",children:e.jsx(D,{children:e.jsx(Ds,{})})})}),e.jsx(R,{path:"/manager/companies",element:e.jsx(f,{requiredRole:"manager",children:e.jsx(D,{children:e.jsx(Fs,{})})})}),e.jsx(R,{path:"/manager/inventory",element:e.jsx(f,{requiredRole:"manager",children:e.jsx(D,{children:e.jsx(Es,{})})})}),e.jsx(R,{path:"/manager/notifications",element:e.jsx(f,{requiredRole:"manager",children:e.jsx(D,{children:e.jsx(xe,{})})})}),e.jsx(R,{path:"/manager/settings",element:e.jsx(f,{requiredRole:"manager",children:e.jsx(D,{children:e.jsx(ue,{})})})}),e.jsx(R,{path:"/partner",element:e.jsx(f,{requiredRole:"partner",children:e.jsx(D,{children:e.jsx(_s,{})})})}),e.jsx(R,{path:"/partner/companies",element:e.jsx(f,{requiredRole:"partner",children:e.jsx(D,{children:e.jsx(Ps,{})})})}),e.jsx(R,{path:"/partner/company/:id",element:e.jsx(f,{requiredRole:"partner",children:e.jsx(D,{children:e.jsx(Us,{})})})}),e.jsx(R,{path:"/partner/inventory",element:e.jsx(f,{requiredRole:"partner",children:e.jsx(D,{children:e.jsx(Ts,{})})})}),e.jsx(R,{path:"/partner/notifications",element:e.jsx(f,{requiredRole:"partner",children:e.jsx(D,{children:e.jsx(xe,{})})})}),e.jsx(R,{path:"/partner/settings",element:e.jsx(f,{requiredRole:"partner",children:e.jsx(D,{children:e.jsx(ue,{})})})}),e.jsx(R,{path:"*",element:e.jsx(U,{to:"/login",replace:!0})})]})})})}const qs=Fe(document.getElementById("root"));qs.render(e.jsx(Ls,{}));
