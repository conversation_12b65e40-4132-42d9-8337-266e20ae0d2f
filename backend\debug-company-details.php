<?php
/**
 * Debug Company Details
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:5173');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

try {
    $db = Database::getInstance();
    
    $companyId = $_GET['id'] ?? null;
    
    if (!$companyId) {
        echo json_encode(['success' => false, 'message' => 'Company ID is required']);
        exit();
    }
    
    // First, let's see if the company exists at all
    $basicCompany = $db->fetchAll("SELECT * FROM companies WHERE id = ?", [$companyId]);

    if (!$basicCompany || empty($basicCompany)) {
        echo json_encode([
            'success' => false,
            'message' => 'Company not found',
            'debug' => [
                'company_id' => $companyId,
                'query_result' => $basicCompany
            ]
        ]);
        exit();
    }

    $company = $basicCompany[0];
    
    // Get manager info if exists
    $manager = null;
    if ($company['manager_id']) {
        $managerResult = $db->fetchAll("SELECT * FROM users WHERE id = ?", [$company['manager_id']]);
        if ($managerResult && !empty($managerResult)) {
            $manager = $managerResult[0];
        }
    }

    // Get creator info if exists
    $creator = null;
    if ($company['created_by']) {
        $creatorResult = $db->fetchAll("SELECT * FROM users WHERE id = ?", [$company['created_by']]);
        if ($creatorResult && !empty($creatorResult)) {
            $creator = $creatorResult[0];
        }
    }
    
    // Get documents
    $documents = $db->fetchAll(
        "SELECT cd.*, u.first_name, u.last_name 
         FROM company_documents cd
         LEFT JOIN users u ON cd.uploaded_by = u.id
         WHERE cd.company_id = ? AND cd.status = 'active'
         ORDER BY cd.uploaded_at DESC",
        [$companyId]
    );
    
    // Prepare response
    $response = [
        'success' => true,
        'data' => [
            'company' => $company,
            'manager' => $manager,
            'creator' => $creator,
            'documents' => $documents,
            'partners' => [] // We'll add this later
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage(),
        'debug' => [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
}
?>
