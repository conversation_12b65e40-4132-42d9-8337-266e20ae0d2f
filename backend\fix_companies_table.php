<?php
/**
 * Fix companies table - Add all missing fields
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

try {
    $db = Database::getInstance();
    
    echo "🔧 Fixing companies table structure...\n";
    
    // Get current table structure
    $columns = $db->fetchAll("SHOW COLUMNS FROM companies");
    $existingColumns = array_column($columns, 'Field');
    
    echo "📋 Current columns: " . implode(', ', $existingColumns) . "\n";
    
    // Define all required columns
    $requiredColumns = [
        'email' => 'VARCHAR(255) NULL',
        'phone' => 'VARCHAR(50) NULL',
        'website' => 'VARCHAR(255) NULL',
        'address' => 'TEXT NULL',
        'city' => 'VARCHAR(100) NULL',
        'state' => 'VARCHAR(100) NULL',
        'country' => 'VARCHAR(100) NULL',
        'postal_code' => 'VARCHAR(20) NULL',
        'approval_status' => "ENUM('pending', 'approved', 'rejected') DEFAULT 'pending'",
        'approved_by' => 'INT NULL',
        'rejected_by' => 'INT NULL',
        'approved_at' => 'TIMESTAMP NULL',
        'rejected_at' => 'TIMESTAMP NULL',
        'rejection_reason' => 'TEXT NULL'
    ];
    
    // Add missing columns
    $addedColumns = [];
    foreach ($requiredColumns as $column => $definition) {
        if (!in_array($column, $existingColumns)) {
            try {
                $sql = "ALTER TABLE companies ADD COLUMN $column $definition";
                $db->execute($sql);
                $addedColumns[] = $column;
                echo "✅ Added column: $column\n";
            } catch (Exception $e) {
                echo "❌ Failed to add column $column: " . $e->getMessage() . "\n";
            }
        } else {
            echo "ℹ️ Column $column already exists\n";
        }
    }
    
    // Add foreign key constraints if they don't exist
    try {
        $sql = "ALTER TABLE companies 
                ADD CONSTRAINT fk_companies_approved_by FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
                ADD CONSTRAINT fk_companies_rejected_by FOREIGN KEY (rejected_by) REFERENCES users(id) ON DELETE SET NULL";
        $db->execute($sql);
        echo "✅ Added foreign key constraints\n";
    } catch (Exception $e) {
        echo "ℹ️ Foreign key constraints may already exist: " . $e->getMessage() . "\n";
    }
    
    // Add indexes for better performance
    $indexes = [
        'idx_companies_email' => 'email',
        'idx_companies_approval_status' => 'approval_status',
        'idx_companies_approved_by' => 'approved_by',
        'idx_companies_rejected_by' => 'rejected_by'
    ];
    
    foreach ($indexes as $indexName => $column) {
        try {
            $sql = "ALTER TABLE companies ADD INDEX $indexName ($column)";
            $db->execute($sql);
            echo "✅ Added index: $indexName\n";
        } catch (Exception $e) {
            echo "ℹ️ Index $indexName may already exist\n";
        }
    }
    
    // Verify final structure
    $finalColumns = $db->fetchAll("SHOW COLUMNS FROM companies");
    $finalColumnNames = array_column($finalColumns, 'Field');
    
    echo "\n🎉 Companies table structure fixed!\n";
    echo "📋 Final columns: " . implode(', ', $finalColumnNames) . "\n";
    echo "✅ Added " . count($addedColumns) . " new columns: " . implode(', ', $addedColumns) . "\n";
    
} catch (Exception $e) {
    echo "❌ Error fixing companies table: " . $e->getMessage() . "\n";
    exit(1);
}
?>
