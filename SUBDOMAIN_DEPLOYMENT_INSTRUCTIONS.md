# 🚀 EskillVisor Subdomain Deployment Instructions
## Deploy to: https://eskillvisor.wallistry.pk

## 🔐 cPanel Access Information

```
Domain: wallistry.pk
cPanel URL: https://wallistry.pk:2083
Username: wallistry
Password: +GlESn;lJteQ%VXf
Target Subdomain: eskillvisor.wallistry.pk
Upload Directory: /home9/wallistry/eskillvisor.wallistry.pk/
```

## 📁 Updated File Upload Structure

### **Subdomain Directory Mapping**

```
LOCAL DIRECTORY                          → CPANEL DESTINATION
cpanel-deployment-2025-07-19/public_html/ → /home9/wallistry/eskillvisor.wallistry.pk/

Specific Files:
├── index.html                           → /home9/wallistry/eskillvisor.wallistry.pk/index.html
├── .htaccess                           → /home9/wallistry/eskillvisor.wallistry.pk/.htaccess
├── assets/index-DMBFIW6C.css           → /home9/wallistry/eskillvisor.wallistry.pk/assets/index-DMBFIW6C.css
├── assets/index-DN-MEaKQ.js            → /home9/wallistry/eskillvisor.wallistry.pk/assets/index-DN-MEaKQ.js
├── assets/ui-D4-VV43f.js               → /home9/wallistry/eskillvisor.wallistry.pk/assets/ui-D4-VV43f.js
├── assets/vendor-CMsH-4Bd.js           → /home9/wallistry/eskillvisor.wallistry.pk/assets/vendor-CMsH-4Bd.js
└── api/                                → /home9/wallistry/eskillvisor.wallistry.pk/api/
    ├── index.php                       → /home9/wallistry/eskillvisor.wallistry.pk/api/index.php
    ├── .htaccess                       → /home9/wallistry/eskillvisor.wallistry.pk/api/.htaccess
    ├── config/                         → /home9/wallistry/eskillvisor.wallistry.pk/api/config/
    ├── controllers/                    → /home9/wallistry/eskillvisor.wallistry.pk/api/controllers/
    ├── models/                         → /home9/wallistry/eskillvisor.wallistry.pk/api/models/
    ├── services/                       → /home9/wallistry/eskillvisor.wallistry.pk/api/services/
    ├── migrations/                     → /home9/wallistry/eskillvisor.wallistry.pk/api/migrations/
    └── uploads/                        → /home9/wallistry/eskillvisor.wallistry.pk/api/uploads/
```

## 🔧 Step-by-Step Deployment Process

### **Step 1: Create Subdomain (if not exists)**
1. **Login to cPanel**:
   - Go to: https://wallistry.pk:2083
   - Username: `wallistry`
   - Password: `+GlESn;lJteQ%VXf`

2. **Create Subdomain**:
   - Click "Subdomains" in Domains section
   - Subdomain: `eskillvisor`
   - Domain: `wallistry.pk`
   - Document Root: `/home9/wallistry/eskillvisor.wallistry.pk/` (auto-filled)
   - Click "Create"

3. **Verify Directory**:
   - Check that `/home9/wallistry/eskillvisor.wallistry.pk/` exists
   - If not, create manually in File Manager

### **Step 2: Upload Files**
1. **Access File Manager**:
   - In cPanel, click "File Manager"
   - Navigate to `/home9/wallistry/eskillvisor.wallistry.pk/`

2. **Upload Method A - ZIP Upload (Recommended)**:
   - Compress `cpanel-deployment-2025-07-19/public_html/` contents
   - Name: `eskillvisor-deployment.zip`
   - Upload to `/home9/wallistry/eskillvisor.wallistry.pk/`
   - Extract in place
   - Delete ZIP file

3. **Upload Method B - Direct Upload**:
   - Upload all files from `cpanel-deployment-2025-07-19/public_html/`
   - Maintain exact directory structure

4. **Set File Permissions**:
   ```
   Directories: 755
   PHP files: 644
   uploads/ directory: 777
   .htaccess files: 644
   ```

### **Step 3: Database Setup**
1. **Create Database** (same as before):
   - Database Name: `eskillvisor_db` → `wallistry_eskillvisor_db`
   - Username: `eskill` → `wallistry_eskill`
   - Password: `EskillVisor2024!`
   - Grant ALL PRIVILEGES

2. **Run Installation**:
   - Visit: **https://eskillvisor.wallistry.pk/api/install.php**
   - Creates tables and default users

### **Step 4: Testing**
1. **Test API**: https://eskillvisor.wallistry.pk/api/test
2. **Test Database**: https://eskillvisor.wallistry.pk/api/db-test.php
3. **Test Frontend**: https://eskillvisor.wallistry.pk
4. **Login**: <EMAIL> / password

## 🌐 **Updated System URLs**

```
Frontend Application: https://eskillvisor.wallistry.pk
API Base URL:        https://eskillvisor.wallistry.pk/api
Database Installation: https://eskillvisor.wallistry.pk/api/install.php
API Test:            https://eskillvisor.wallistry.pk/api/test
Database Test:       https://eskillvisor.wallistry.pk/api/db-test.php
```

## 🧪 **Updated Testing Checklist**

### **Critical Tests for Subdomain**
- [ ] **Subdomain resolves**: https://eskillvisor.wallistry.pk
- [ ] **API responds**: https://eskillvisor.wallistry.pk/api/test
- [ ] **Database connects**: https://eskillvisor.wallistry.pk/api/db-test.php
- [ ] **Login works**: <EMAIL> / password
- [ ] **Dashboard loads**: After successful login
- [ ] **CORS working**: No CORS errors in browser console
- [ ] **File uploads**: Upload functionality works
- [ ] **Mobile responsive**: Works on mobile devices

## 🔒 **Security Configuration**

### **CORS Origins Updated**
The system is configured to accept requests from:
- `https://eskillvisor.wallistry.pk` (primary)
- `https://wallistry.pk`
- `https://inventory-system-e-skill-visor.vercel.app`
- `http://localhost:5173` (development)
- `http://localhost:3000` (development)

### **Default User Accounts**
| Role | Email | Password | Access Level |
|------|-------|----------|--------------|
| Super Admin | <EMAIL> | password | Full system access |
| Manager | <EMAIL> | password | Manage partners and companies |
| Partner | <EMAIL> | password | View assigned companies only |

⚠️ **CRITICAL**: Change these passwords immediately after deployment!

## 🚨 **Troubleshooting for Subdomain**

### **Subdomain Not Resolving**
- **Check**: Subdomain created in cPanel
- **Wait**: DNS propagation (up to 24 hours)
- **Test**: Try accessing from different network/device

### **"Subdomain Not Found" Error**
- **Verify**: Directory `/home9/wallistry/eskillvisor.wallistry.pk/` exists
- **Check**: Files uploaded to correct subdomain directory
- **Ensure**: index.html is in subdomain root

### **API CORS Errors**
- **Verify**: .htaccess includes eskillvisor.wallistry.pk in CORS origins
- **Check**: Frontend making requests to correct subdomain API URL
- **Test**: API endpoints directly via browser

## ✅ **Deployment Summary for Subdomain**

### **Upload Destination**: `/home9/wallistry/eskillvisor.wallistry.pk/`
### **Final URLs**:
- **Frontend**: https://eskillvisor.wallistry.pk
- **API**: https://eskillvisor.wallistry.pk/api
- **Installation**: https://eskillvisor.wallistry.pk/api/install.php

### **Total Deployment Time**: ~25 minutes
- Subdomain creation: 5 minutes
- File upload: 10 minutes
- Database setup: 5 minutes
- Testing: 5 minutes

## 🎉 **Ready for Subdomain Deployment!**

Your EskillVisor Investment System is configured and ready for deployment to the subdomain `https://eskillvisor.wallistry.pk`!

**Next Action**: Follow these instructions to deploy to the subdomain directory.

---

**Upload Directory**: `/home9/wallistry/eskillvisor.wallistry.pk/`
**Target URL**: https://eskillvisor.wallistry.pk
**Status**: Ready for Subdomain Deployment 🚀
