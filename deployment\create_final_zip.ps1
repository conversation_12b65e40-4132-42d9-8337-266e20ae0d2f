# PowerShell script to create deployment ZIP
Write-Host "Creating EskillVisor User Management Updates ZIP package..." -ForegroundColor Green

$sourceDir = "files"
$zipFile = "user-management-updates-2025-07-18.zip"

# Remove existing ZIP if it exists
if (Test-Path $zipFile) {
    Remove-Item $zipFile -Force
    Write-Host "Removed existing ZIP file" -ForegroundColor Yellow
}

# Create ZIP file
try {
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::CreateFromDirectory($sourceDir, $zipFile)
    Write-Host "✅ ZIP file created successfully!" -ForegroundColor Green
    
    # Get file size
    $fileSize = (Get-Item $zipFile).Length
    $fileSizeMB = [math]::Round($fileSize / 1MB, 2)
    
    Write-Host ""
    Write-Host "📦 Package Details:" -ForegroundColor Cyan
    Write-Host "   File: $zipFile" -ForegroundColor White
    Write-Host "   Size: $fileSizeMB MB" -ForegroundColor White
    Write-Host "   Location: $(Get-Location)\$zipFile" -ForegroundColor White
    Write-Host ""
    Write-Host "🎯 Ready for upload to cPanel at:" -ForegroundColor Yellow
    Write-Host "   /home9/wallistry/eskillvisor.wallistry.pk/" -ForegroundColor White
    Write-Host ""
    
} catch {
    Write-Host "❌ Error creating ZIP file: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
