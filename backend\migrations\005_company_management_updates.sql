-- Company Management Updates Migration
-- Remove website column and create company_documents table

-- Create company_documents table
CREATE TABLE IF NOT EXISTS company_documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    uploaded_by INT,
    status ENUM('active', 'deleted') DEFAULT 'active',
    
    INDEX idx_company_id (company_id),
    INDEX idx_uploaded_by (uploaded_by),
    INDEX idx_status (status),
    INDEX idx_uploaded_at (uploaded_at),
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Remove website column from companies table (if exists and not used elsewhere)
-- Note: We'll keep it for now to avoid breaking existing functionality
-- ALTER TABLE companies DROP COLUMN IF EXISTS website;

-- Ensure manager_id foreign key is properly configured
-- (This should already exist from previous migrations)
-- ALTER TABLE companies 
-- ADD CONSTRAINT fk_companies_manager_id FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL;
