#!/usr/bin/env node

/**
 * EskillVisor Non-Destructive Package Builder
 * Creates deployment packages without affecting local development environment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class PackageBuilder {
    constructor() {
        this.sourceDir = process.cwd();
        this.deploymentDir = path.join(this.sourceDir, 'cpanel-deployment');
        this.packageDir = path.join(this.deploymentDir, 'public_html');
        this.timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    }

    /**
     * Build complete deployment package without affecting source files
     */
    async buildPackage(changes) {
        console.log('📦 Non-Destructive Package Building Started');
        console.log('=' .repeat(50));
        console.log(`Source Directory: ${this.sourceDir}`);
        console.log(`Package Directory: ${this.packageDir}`);
        console.log('');

        try {
            // Step 1: Prepare deployment directory
            console.log('🗂️ Step 1: Preparing deployment directory...');
            await this.prepareDeploymentDirectory();

            // Step 2: Build frontend if needed
            if (this.shouldBuildFrontend(changes)) {
                console.log('🔨 Step 2: Building frontend (non-destructive)...');
                await this.buildFrontendNonDestructive();
            } else {
                console.log('⏭️ Step 2: Frontend build not required');
            }

            // Step 3: Copy frontend files
            console.log('📁 Step 3: Copying frontend files...');
            await this.copyFrontendFiles();

            // Step 4: Copy backend files
            console.log('🔧 Step 4: Copying backend files...');
            await this.copyBackendFiles();

            // Step 5: Create ZIP package
            console.log('📦 Step 5: Creating ZIP package...');
            const zipPath = await this.createZipPackage();

            // Step 6: Generate package manifest
            console.log('📋 Step 6: Generating package manifest...');
            const manifest = await this.generatePackageManifest();

            console.log('');
            console.log('✅ Package Building Completed Successfully!');
            console.log('=' .repeat(50));

            return {
                packagePath: this.packageDir,
                zipPath: zipPath,
                manifest: manifest,
                timestamp: this.timestamp
            };

        } catch (error) {
            throw new Error(`Package building failed: ${error.message}`);
        }
    }

    /**
     * Prepare deployment directory structure
     */
    async prepareDeploymentDirectory() {
        // Create deployment directories
        const directories = [
            this.deploymentDir,
            this.packageDir,
            path.join(this.packageDir, 'api'),
            path.join(this.packageDir, 'assets')
        ];

        for (const dir of directories) {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                console.log(`   📁 Created: ${path.relative(this.sourceDir, dir)}`);
            }
        }

        console.log('   ✅ Deployment directory structure ready');
    }

    /**
     * Check if frontend build is needed
     */
    shouldBuildFrontend(changes) {
        if (!changes) return true;
        
        const frontendChanges = changes.categories.frontend.length > 0 || 
                               changes.categories.assets.length > 0;
        const distExists = fs.existsSync(path.join(this.sourceDir, 'dist'));
        
        return frontendChanges || !distExists;
    }

    /**
     * Build frontend without affecting source files
     */
    async buildFrontendNonDestructive() {
        try {
            // Check if dist directory exists
            const distPath = path.join(this.sourceDir, 'dist');
            const distBackupPath = path.join(this.sourceDir, 'dist-backup-' + this.timestamp);
            
            // Backup existing dist if it exists
            if (fs.existsSync(distPath)) {
                console.log('   💾 Backing up existing dist directory...');
                execSync(`cp -r "${distPath}" "${distBackupPath}"`, { stdio: 'pipe' });
            }

            try {
                // Build frontend
                console.log('   🔨 Running frontend build...');
                execSync('npm run build', { 
                    stdio: 'pipe',
                    cwd: this.sourceDir 
                });
                console.log('   ✅ Frontend build completed');

                // Copy built files to package
                if (fs.existsSync(distPath)) {
                    console.log('   📋 Copying build output to package...');
                    execSync(`cp -r "${distPath}"/* "${this.packageDir}"/`, { stdio: 'pipe' });
                }

                // Restore original dist if backup exists
                if (fs.existsSync(distBackupPath)) {
                    console.log('   🔄 Restoring original dist directory...');
                    execSync(`rm -rf "${distPath}"`, { stdio: 'pipe' });
                    execSync(`mv "${distBackupPath}" "${distPath}"`, { stdio: 'pipe' });
                }

            } catch (buildError) {
                // Restore backup if build failed
                if (fs.existsSync(distBackupPath)) {
                    console.log('   🔄 Restoring dist backup due to build failure...');
                    if (fs.existsSync(distPath)) {
                        execSync(`rm -rf "${distPath}"`, { stdio: 'pipe' });
                    }
                    execSync(`mv "${distBackupPath}" "${distPath}"`, { stdio: 'pipe' });
                }
                throw buildError;
            }

        } catch (error) {
            throw new Error(`Frontend build failed: ${error.message}`);
        }
    }

    /**
     * Copy frontend files to package
     */
    async copyFrontendFiles() {
        const distPath = path.join(this.sourceDir, 'dist');
        
        if (fs.existsSync(distPath)) {
            // Copy all files from dist to package root
            const files = fs.readdirSync(distPath);
            let copiedFiles = 0;

            for (const file of files) {
                const sourcePath = path.join(distPath, file);
                const targetPath = path.join(this.packageDir, file);
                
                if (fs.statSync(sourcePath).isDirectory()) {
                    execSync(`cp -r "${sourcePath}" "${targetPath}"`, { stdio: 'pipe' });
                } else {
                    fs.copyFileSync(sourcePath, targetPath);
                }
                copiedFiles++;
            }

            console.log(`   ✅ Copied ${copiedFiles} frontend items`);
        } else {
            console.log('   ⚠️ No dist directory found, skipping frontend copy');
        }

        // Copy .htaccess file for React Router support
        const htaccessPath = path.join(this.sourceDir, '.htaccess');
        if (fs.existsSync(htaccessPath)) {
            const targetPath = path.join(this.packageDir, '.htaccess');
            fs.copyFileSync(htaccessPath, targetPath);
            console.log('   ✅ Copied .htaccess for React Router support');
        } else {
            console.log('   ⚠️ .htaccess file not found - React Router may not work on reload');
        }
    }

    /**
     * Copy backend files to package
     */
    async copyBackendFiles() {
        const backendPath = path.join(this.sourceDir, 'backend');
        const apiPath = path.join(this.packageDir, 'api');

        if (fs.existsSync(backendPath)) {
            // Copy all backend files to api directory
            execSync(`cp -r "${backendPath}"/* "${apiPath}"/`, { stdio: 'pipe' });
            
            // Count copied files
            const copiedFiles = this.countFiles(apiPath);
            console.log(`   ✅ Copied ${copiedFiles} backend files`);
        } else {
            console.log('   ⚠️ No backend directory found, skipping backend copy');
        }
    }

    /**
     * Create ZIP package for upload
     */
    async createZipPackage() {
        const zipFileName = `enhanced-dashboards-deployment-${this.timestamp}.zip`;
        const zipPath = path.join(this.sourceDir, zipFileName);

        try {
            // Create ZIP using PowerShell on Windows
            const powershellCommand = `Compress-Archive -Path '${this.packageDir}/*' -DestinationPath '${zipPath}' -Force`;
            execSync(`powershell "${powershellCommand}"`, { stdio: 'pipe' });

            if (fs.existsSync(zipPath)) {
                const stats = fs.statSync(zipPath);
                console.log(`   ✅ Created: ${zipFileName} (${this.formatFileSize(stats.size)})`);
                return zipPath;
            } else {
                throw new Error('ZIP file was not created');
            }

        } catch (error) {
            console.log('   ⚠️ PowerShell ZIP creation failed, trying alternative...');
            
            // Fallback: Try using tar if available
            try {
                const tarFileName = `enhanced-dashboards-deployment-${this.timestamp}.tar.gz`;
                const tarPath = path.join(this.sourceDir, tarFileName);
                
                execSync(`cd "${this.packageDir}" && tar -czf "${tarPath}" *`, { stdio: 'pipe' });
                
                if (fs.existsSync(tarPath)) {
                    const stats = fs.statSync(tarPath);
                    console.log(`   ✅ Created: ${tarFileName} (${this.formatFileSize(stats.size)})`);
                    return tarPath;
                }
            } catch (tarError) {
                console.log('   ⚠️ Archive creation failed, package available as directory');
                return null;
            }
        }
    }

    /**
     * Generate package manifest
     */
    async generatePackageManifest() {
        const manifest = {
            timestamp: this.timestamp,
            packageInfo: {
                sourceDirectory: this.sourceDir,
                packageDirectory: this.packageDir,
                totalFiles: this.countFiles(this.packageDir),
                totalSize: this.getDirectorySize(this.packageDir)
            },
            contents: {
                frontend: this.scanDirectory(this.packageDir, ['api']),
                backend: this.scanDirectory(path.join(this.packageDir, 'api'))
            },
            deployment: {
                target: 'https://eskillvisor.wallistry.pk',
                uploadPath: '/home9/wallistry/eskillvisor.wallistry.pk/',
                method: 'cPanel File Manager'
            }
        };

        const manifestPath = path.join(this.deploymentDir, 'package-manifest.json');
        fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
        console.log(`   ✅ Generated: package-manifest.json`);

        return manifest;
    }

    /**
     * Count files in directory
     */
    countFiles(dir) {
        if (!fs.existsSync(dir)) return 0;
        
        let count = 0;
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const itemPath = path.join(dir, item);
            if (fs.statSync(itemPath).isDirectory()) {
                count += this.countFiles(itemPath);
            } else {
                count++;
            }
        }
        
        return count;
    }

    /**
     * Get directory size
     */
    getDirectorySize(dir) {
        if (!fs.existsSync(dir)) return 0;
        
        let size = 0;
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const itemPath = path.join(dir, item);
            const stats = fs.statSync(itemPath);
            
            if (stats.isDirectory()) {
                size += this.getDirectorySize(itemPath);
            } else {
                size += stats.size;
            }
        }
        
        return size;
    }

    /**
     * Scan directory for manifest
     */
    scanDirectory(dir, excludeDirs = []) {
        if (!fs.existsSync(dir)) return [];
        
        const files = [];
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            if (excludeDirs.includes(item)) continue;
            
            const itemPath = path.join(dir, item);
            const stats = fs.statSync(itemPath);
            const relativePath = path.relative(this.packageDir, itemPath);
            
            if (stats.isDirectory()) {
                files.push(...this.scanDirectory(itemPath, excludeDirs));
            } else {
                files.push({
                    path: relativePath,
                    size: stats.size,
                    modified: stats.mtime.toISOString()
                });
            }
        }
        
        return files;
    }

    /**
     * Format file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
}

module.exports = PackageBuilder;
