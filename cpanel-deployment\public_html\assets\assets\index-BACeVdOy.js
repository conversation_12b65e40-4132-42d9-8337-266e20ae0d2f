import{r as l,a as qe,g as Oe,u as K,b as ze,c as Be,B as Ve,R as We,d as R,N as T}from"./vendor-CMsH-4Bd.js";import{L as Ne,M as ae,C as z,E as He,a as X,b as ne,A as Se,U as ue,B as P,D as Q,T as B,c as he,d as V,e as fe,f as ye,g as J,h as F,i as Qe,F as Y,S as Ge,P as O,I as je,R as Je,j as G,k as Z,X as U,l as re,m as Ye,n as Ce,o as ee,p as ie,q as se,r as ke,s as le,t as _e,u as Ze,v as be,w as Ke,x as ge,y as Xe,z as Ie,G as es,H as ss,J as q,K as de,N as me,O as ts,Q as rs}from"./ui-eN6PbTDd.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))a(o);new MutationObserver(o=>{for(const c of o)if(c.type==="childList")for(const m of c.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&a(m)}).observe(document,{childList:!0,subtree:!0});function t(o){const c={};return o.integrity&&(c.integrity=o.integrity),o.referrerPolicy&&(c.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?c.credentials="include":o.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function a(o){if(o.ep)return;o.ep=!0;const c=t(o);fetch(o.href,c)}})();var Ae={exports:{}},oe={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var as=l,ns=Symbol.for("react.element"),is=Symbol.for("react.fragment"),ls=Object.prototype.hasOwnProperty,os=as.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,cs={key:!0,ref:!0,__self:!0,__source:!0};function Re(i,s,t){var a,o={},c=null,m=null;t!==void 0&&(c=""+t),s.key!==void 0&&(c=""+s.key),s.ref!==void 0&&(m=s.ref);for(a in s)ls.call(s,a)&&!cs.hasOwnProperty(a)&&(o[a]=s[a]);if(i&&i.defaultProps)for(a in s=i.defaultProps,s)o[a]===void 0&&(o[a]=s[a]);return{$$typeof:ns,type:i,key:c,ref:m,props:o,_owner:os.current}}oe.Fragment=is;oe.jsx=Re;oe.jsxs=Re;Ae.exports=oe;var e=Ae.exports,Ee,we=qe;Ee=we.createRoot,we.hydrateRoot;var De={exports:{}},ds="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",ms=ds,xs=ms;function Fe(){}function Ue(){}Ue.resetWarningCache=Fe;var ps=function(){function i(a,o,c,m,d,r){if(r!==xs){var n=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw n.name="Invariant Violation",n}}i.isRequired=i;function s(){return i}var t={array:i,bigint:i,bool:i,func:i,number:i,object:i,string:i,symbol:i,any:i,arrayOf:s,element:i,elementType:i,instanceOf:s,node:i,objectOf:s,oneOf:s,oneOfType:s,shape:s,exact:s,checkPropTypes:Ue,resetWarningCache:Fe};return t.PropTypes=t,t};De.exports=ps();var us=De.exports;const N=Oe(us),hs=()=>window.location.hostname==="eskillvisor.wallistry.pk"||window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1"?"https://eskillvisor.wallistry.pk/api":"https://wallistry.pk/api",gs=hs();class fs{constructor(){this.baseURL=gs,this.token=localStorage.getItem("auth_token")}setToken(s){this.token=s,s?localStorage.setItem("auth_token",s):localStorage.removeItem("auth_token")}getHeaders(){const s={"Content-Type":"application/json"};return this.token&&(s.Authorization=`Bearer ${this.token}`),s}async request(s,t={}){const a=`${this.baseURL}${s}`,o={headers:this.getHeaders(),...t};try{const c=await fetch(a,o),m=await c.json();if(!c.ok)throw new Error(m.message||"API request failed");return m}catch(c){throw console.error("API request failed:",c),c}}async get(s){return this.request(s,{method:"GET"})}async post(s,t){return this.request(s,{method:"POST",body:JSON.stringify(t)})}async put(s,t){return this.request(s,{method:"PUT",body:JSON.stringify(t)})}async delete(s){return this.request(s,{method:"DELETE"})}async login(s,t){const a=await this.post("/login.php",{email:s,password:t});return a.success&&a.data.access_token&&this.setToken(a.data.access_token),a}async logout(){try{await this.post("/api/auth/logout")}finally{this.setToken(null)}}async getCurrentUser(){return this.get("/api/auth/me")}async refreshToken(s){const t=await this.post("/api/auth/refresh",{refresh_token:s});return t.success&&t.data.access_token&&this.setToken(t.data.access_token),t}async getUsers(s={}){const t=new URLSearchParams(s).toString();return this.get(`/users.php${t?`?${t}`:""}`)}async getUser(s){return this.get(`/users.php?id=${s}`)}async createUser(s){return this.post("/users.php",s)}async updateUser(s,t){return this.put(`/users.php?id=${s}`,t)}async deleteUser(s){return this.delete(`/users.php?id=${s}`)}async getCompanies(s={}){const t=new URLSearchParams(s).toString();return this.get(`/companies.php${t?`?${t}`:""}`)}async getCompany(s){return this.get(`/companies.php?id=${s}`)}async createCompany(s){return this.post("/companies.php",s)}async updateCompany(s,t){return this.put(`/companies.php?id=${s}`,t)}async deleteCompany(s){return this.delete(`/companies.php?id=${s}`)}async assignPartner(s,t){return this.post(`/api/companies/${s}/partners`,{user_id:t})}async removePartner(s,t){return this.delete(`/api/companies/${s}/partners/${t}`)}async getInventory(s={}){const t=new URLSearchParams(s).toString();return this.get(`/api/inventory${t?`?${t}`:""}`)}async getInventoryItem(s){return this.get(`/api/inventory/${s}`)}async createInventoryItem(s){return this.post("/api/inventory",s)}async updateInventoryItem(s,t){return this.put(`/api/inventory/${s}`,t)}async deleteInventoryItem(s){return this.delete(`/api/inventory/${s}`)}async getLowStockItems(){return this.get("/api/inventory/low-stock")}async getTransactions(s={}){const t=new URLSearchParams(s).toString();return this.get(`/api/transactions${t?`?${t}`:""}`)}async createTransaction(s){return this.post("/api/transactions",s)}async uploadFile(s){const t=new FormData;t.append("file",s);const a=await fetch(`${this.baseURL}/api/files/upload`,{method:"POST",headers:{Authorization:`Bearer ${this.token}`},body:t}),o=await a.json();if(!a.ok)throw new Error(o.message||"File upload failed");return o}async getUploads(s={}){const t=new URLSearchParams(s).toString();return this.get(`/api/files/uploads${t?`?${t}`:""}`)}async processFile(s){return this.post(`/api/files/process/${s}`)}async getDashboardData(){return this.get("/api/analytics/dashboard")}async getInventoryStats(){return this.get("/api/analytics/inventory-stats")}async getTrends(){return this.get("/api/analytics/trends")}async getCompanyStats(){return this.get("/api/analytics/company-stats")}async getAssignedCompanies(){return this.get("/api/companies/assigned")}async getCompanyStats(){return this.get("/api/analytics/company-stats")}async getTrends(){return this.get("/api/analytics/trends")}async exportData(s,t="csv"){return this.get(`/api/analytics/export?type=${s}&format=${t}`)}async getNotifications(s={}){const t=new URLSearchParams(s).toString();return this.get(`/api/notifications${t?`?${t}`:""}`)}async markNotificationAsRead(s){return this.put(`/api/notifications/${s}/read`)}async markAllNotificationsAsRead(){return this.post("/api/notifications/mark-all-read")}async deleteNotification(s){return this.delete(`/api/notifications/${s}`)}}const k=new fs;class ys{constructor(){this.currentUser=null,this.token=localStorage.getItem("auth_token"),this.refreshToken=localStorage.getItem("refresh_token"),this.token&&this.initializeFromToken()}async initializeFromToken(){try{if(this.token){k.setToken(this.token);const s=await k.getCurrentUser();s.success?this.currentUser=s.data:this.logout()}}catch(s){console.error("Failed to initialize from token:",s),this.logout()}}async login(s,t){try{const a=await k.login(s,t);if(a.success)return this.token=a.data.access_token,this.refreshToken=a.data.refresh_token,this.currentUser=a.data.user,localStorage.setItem("auth_token",this.token),localStorage.setItem("refresh_token",this.refreshToken),{success:!0,user:this.currentUser,token:this.token,refreshToken:this.refreshToken};throw new Error(a.message||"Login failed")}catch(a){throw new Error(a.message||"Login failed")}}async logout(){try{this.token&&await k.logout()}catch(s){console.error("Logout error:",s)}finally{localStorage.removeItem("auth_token"),localStorage.removeItem("refresh_token"),this.token=null,this.refreshToken=null,this.currentUser=null,k.setToken(null)}return{success:!0}}async refreshAuthToken(){try{if(!this.refreshToken)throw new Error("No refresh token available");const s=await k.refreshToken(this.refreshToken);if(s.success)return this.token=s.data.access_token,localStorage.setItem("auth_token",this.token),k.setToken(this.token),{success:!0,token:this.token};throw new Error(s.message||"Token refresh failed")}catch{throw this.logout(),new Error("Token refresh failed")}}async resetPassword(s){try{return await k.post("/api/auth/forgot-password",{email:s})}catch(t){throw new Error(t.message||"Password reset failed")}}getCurrentUser(){return this.currentUser}getToken(){return this.token}isAuthenticated(){return!!this.token&&!!this.currentUser}hasRole(s){var t;return((t=this.currentUser)==null?void 0:t.role)===s}hasAnyRole(s){var t;return s.includes((t=this.currentUser)==null?void 0:t.role)}isSuperAdmin(){return this.hasRole("superadmin")}isManager(){return this.hasAnyRole(["superadmin","manager"])}isPartner(){return this.hasRole("partner")}canAccessCompany(s){var t;return this.currentUser?this.hasAnyRole(["superadmin","manager"])?!0:this.hasRole("partner")&&((t=this.currentUser.assigned_companies)==null?void 0:t.some(a=>a.id===s))||!1:!1}getAccessibleCompanies(){return this.currentUser?this.hasAnyRole(["superadmin","manager"])?"all":this.hasRole("partner")?this.currentUser.assigned_companies||[]:[]:[]}}const M=new ys,js=()=>{const[i,s]=l.useState(""),[t,a]=l.useState(""),[o,c]=l.useState(!1),[m,d]=l.useState(!1),[r,n]=l.useState(""),[x,w]=l.useState(""),[g,j]=l.useState(!1),{login:h,isAuthenticated:S,userRole:_}=l.useContext(D),b=K();l.useEffect(()=>{S&&b(_==="superadmin"?"/superadmin":_==="manager"?"/manager":"/partner")},[S,_,b]);const v=y=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(y),I=async y=>{if(y.preventDefault(),!v(i)){n("Please enter a valid email address");return}n(""),w(""),d(!0);try{const u=await M.login(i,t);u.success&&(h(u.user),u.user.role==="superadmin"?b("/superadmin"):u.user.role==="manager"?b("/manager"):b("/partner"))}catch(u){w(u.message||"Login failed. Please try again.")}finally{d(!1)}},f=async(y,u)=>{s(y),a(u),n(""),w(""),d(!0);try{const C=await M.login(y,u);C.success&&(h(C.user),C.user.role==="superadmin"?b("/superadmin"):C.user.role==="manager"?b("/manager"):b("/partner"))}catch(C){w(C.message||"Demo login failed. Please try again.")}finally{d(!1)}},p=[{email:"<EMAIL>",password:"password",role:"Super Admin",description:"Full system access"},{email:"<EMAIL>",password:"password",role:"Manager",description:"Manage partners and companies"},{email:"<EMAIL>",password:"password",role:"Partner",description:"View assigned companies"}];return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-primary to-secondary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-20"}),e.jsxs("div",{className:"max-w-md w-full space-y-8 relative z-10",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mx-auto h-16 w-16 bg-white rounded-full flex items-center justify-center shadow-lg",children:e.jsx(Ne,{className:"h-8 w-8 text-primary"})}),e.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Enterprise Portal"}),e.jsx("p",{className:"mt-2 text-center text-sm text-white text-opacity-80",children:"Sign in to your account"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-xl p-8",children:[e.jsxs("form",{className:"space-y-6",onSubmit:I,children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(ae,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:i,onChange:y=>s(y.target.value),className:`appearance-none relative block w-full pl-10 pr-3 py-2 border ${r?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm`,placeholder:"Enter your email"})]}),r&&e.jsxs("div",{className:"mt-1 flex items-center text-sm text-red-600",children:[e.jsx(z,{className:"h-4 w-4 mr-1"}),r]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(Ne,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{id:"password",name:"password",type:o?"text":"password",autoComplete:"current-password",required:!0,value:t,onChange:y=>a(y.target.value),className:"appearance-none relative block w-full pl-10 pr-10 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm",placeholder:"Enter your password"}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>c(!o),children:o?e.jsx(He,{className:"h-5 w-5 text-gray-400"}):e.jsx(X,{className:"h-5 w-5 text-gray-400"})})]})]}),x&&e.jsxs("div",{className:"flex items-center text-sm text-red-600 bg-red-50 p-3 rounded-md",children:[e.jsx(z,{className:"h-4 w-4 mr-2"}),x]}),e.jsx("div",{children:e.jsx("button",{type:"submit",disabled:m,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:m?"Signing in...":"Sign in"})}),e.jsx("div",{className:"text-center",children:e.jsxs("button",{type:"button",onClick:()=>j(!g),className:"text-sm text-primary hover:text-primary-dark",children:[g?"Hide":"Show"," Demo Accounts"]})})]}),g&&e.jsxs("div",{className:"mt-6 border-t border-gray-200 pt-6",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Demo Accounts:"}),e.jsx("div",{className:"space-y-2",children:p.map((y,u)=>e.jsx("button",{onClick:()=>f(y.email,y.password),className:"w-full text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:y.role}),e.jsx("p",{className:"text-xs text-gray-500",children:y.email})]}),e.jsx("p",{className:"text-xs text-gray-400",children:y.description})]})},u))})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsx("a",{href:"/reset-password",onClick:y=>{y.preventDefault(),b("/reset-password")},className:"text-sm text-primary hover:text-primary-dark",children:"Forgot your password?"})})]})]})]})},bs=()=>{const[i,s]=l.useState(""),[t,a]=l.useState(!1),[o,c]=l.useState(!1),m=K(),d=r=>{r.preventDefault(),a(!0),setTimeout(()=>{a(!1),c(!0)},1500)};return o?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary to-secondary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"max-w-md w-full space-y-8",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-xl p-8 text-center",children:[e.jsx(ne,{className:"mx-auto h-16 w-16 text-green-500"}),e.jsx("h2",{className:"mt-6 text-2xl font-bold text-gray-900",children:"Check your email"}),e.jsxs("p",{className:"mt-2 text-sm text-gray-600",children:["We've sent a password reset link to ",i]}),e.jsx("button",{onClick:()=>m("/login"),className:"mt-6 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:"Back to Login"})]})})}):e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary to-secondary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-md w-full space-y-8",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Reset your password"}),e.jsx("p",{className:"mt-2 text-center text-sm text-white text-opacity-80",children:"Enter your email address and we'll send you a link to reset your password."})]}),e.jsx("div",{className:"bg-white rounded-lg shadow-xl p-8",children:e.jsxs("form",{className:"space-y-6",onSubmit:d,children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(ae,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:i,onChange:r=>s(r.target.value),className:"appearance-none relative block w-full pl-10 pr-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm",placeholder:"Enter your email"})]})]}),e.jsx("div",{children:e.jsx("button",{type:"submit",disabled:t,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:t?"Sending...":"Send reset link"})}),e.jsx("div",{className:"text-center",children:e.jsxs("button",{type:"button",onClick:()=>m("/login"),className:"inline-flex items-center text-sm text-primary hover:text-primary-dark",children:[e.jsx(Se,{className:"h-4 w-4 mr-1"}),"Back to login"]})})]})})]})})};class vs{async getInventoryItems(s={}){try{const t=await k.getInventory(s);if(t.success)return t.data||[];throw new Error(t.message||"Failed to fetch inventory items")}catch(t){throw console.error("Error fetching inventory items:",t),t}}async getInventoryItem(s){try{const t=await k.getInventoryItem(s);if(t.success)return t.data;throw new Error(t.message||"Failed to fetch inventory item")}catch(t){throw console.error("Error fetching inventory item:",t),t}}async createInventoryItem(s){try{const t=await k.createInventoryItem(s);if(t.success)return t.data;throw new Error(t.message||"Failed to create inventory item")}catch(t){throw console.error("Error creating inventory item:",t),t}}async updateInventoryItem(s,t){try{const a=await k.updateInventoryItem(s,t);if(a.success)return a.data;throw new Error(a.message||"Failed to update inventory item")}catch(a){throw console.error("Error updating inventory item:",a),a}}async deleteInventoryItem(s){try{const t=await k.deleteInventoryItem(s);if(t.success)return!0;throw new Error(t.message||"Failed to delete inventory item")}catch(t){throw console.error("Error deleting inventory item:",t),t}}async getLowStockItems(){try{const s=await k.getLowStockItems();if(s.success)return s.data||[];throw new Error(s.message||"Failed to fetch low stock items")}catch(s){throw console.error("Error fetching low stock items:",s),s}}async getTransactions(s={}){try{const t=await k.getTransactions(s);if(t.success)return t.data||[];throw new Error(t.message||"Failed to fetch transactions")}catch(t){throw console.error("Error fetching transactions:",t),t}}async createTransaction(s){try{const t=await k.createTransaction(s);if(t.success)return t.data;throw new Error(t.message||"Failed to create transaction")}catch(t){throw console.error("Error creating transaction:",t),t}}async uploadFile(s){try{const t=await k.uploadFile(s);if(t.success)return t.data;throw new Error(t.message||"Failed to upload file")}catch(t){throw console.error("Error uploading file:",t),t}}async processFile(s){try{const t=await k.processFile(s);if(t.success)return t.data;throw new Error(t.message||"Failed to process file")}catch(t){throw console.error("Error processing file:",t),t}}async getUploads(s={}){try{const t=await k.getUploads(s);if(t.success)return t.data||[];throw new Error(t.message||"Failed to fetch uploads")}catch(t){throw console.error("Error fetching uploads:",t),t}}async getInventoryStats(){try{const s=await k.getInventoryStats();if(s.success)return s.data;throw new Error(s.message||"Failed to fetch inventory stats")}catch(s){throw console.error("Error fetching inventory stats:",s),s}}async getDashboardData(){try{const s=await k.getDashboardData();if(s.success)return s.data;throw new Error(s.message||"Failed to fetch dashboard data")}catch(s){throw console.error("Error fetching dashboard data:",s),s}}async getTrends(){try{const s=await k.getTrends();if(s.success)return s.data;throw new Error(s.message||"Failed to fetch trends data")}catch(s){throw console.error("Get trends data failed:",s),s}}async getCompanyStats(){try{const s=await k.getCompanyStats();if(s.success)return s.data;throw new Error(s.message||"Failed to fetch company stats")}catch(s){throw console.error("Get company stats failed:",s),s}}async exportData(s="inventory",t="csv"){try{return await k.exportData(s,t)}catch(a){throw console.error("Error exporting data:",a),a}}}const $=new vs;class Ns{async getUsers(s={}){try{const t=await k.getUsers(s);if(t.success)return t.data||[];throw new Error(t.message||"Failed to fetch users")}catch(t){throw console.error("Error fetching users:",t),t}}async getUser(s){try{const t=await k.getUser(s);if(t.success)return t.data;throw new Error(t.message||"Failed to fetch user")}catch(t){throw console.error("Error fetching user:",t),t}}async createUser(s){try{const t=await k.createUser(s);if(t.success)return t.data;throw new Error(t.message||"Failed to create user")}catch(t){throw console.error("Error creating user:",t),t}}async updateUser(s,t){try{const a=await k.updateUser(s,t);if(a.success)return a.data;throw new Error(a.message||"Failed to update user")}catch(a){throw console.error("Error updating user:",a),a}}async deleteUser(s){try{const t=await k.deleteUser(s);if(t.success)return!0;throw new Error(t.message||"Failed to delete user")}catch(t){throw console.error("Error deleting user:",t),t}}}const ws=new Ns;class Ss{async getCompanies(s={}){try{const t=await k.getCompanies(s);if(t.success)return t.data||[];throw new Error(t.message||"Failed to fetch companies")}catch(t){throw console.error("Error fetching companies:",t),t}}async getCompany(s){try{const t=await k.getCompany(s);if(t.success)return t.data;throw new Error(t.message||"Failed to fetch company")}catch(t){throw console.error("Error fetching company:",t),t}}async createCompany(s){try{const t=await k.createCompany(s);if(t.success)return t.data;throw new Error(t.message||"Failed to create company")}catch(t){throw console.error("Error creating company:",t),t}}async updateCompany(s,t){try{const a=await k.updateCompany(s,t);if(a.success)return a.data;throw new Error(a.message||"Failed to update company")}catch(a){throw console.error("Error updating company:",a),a}}async deleteCompany(s){try{const t=await k.deleteCompany(s);if(t.success)return!0;throw new Error(t.message||"Failed to delete company")}catch(t){throw console.error("Error deleting company:",t),t}}async assignPartner(s,t){try{const a=await k.assignPartner(s,t);if(a.success)return!0;throw new Error(a.message||"Failed to assign partner")}catch(a){throw console.error("Error assigning partner:",a),a}}async removePartner(s,t){try{const a=await k.removePartner(s,t);if(a.success)return!0;throw new Error(a.message||"Failed to remove partner")}catch(a){throw console.error("Error removing partner:",a),a}}async getCompanyStats(){try{const s=await k.getCompanyStats();if(s.success)return s.data;throw new Error(s.message||"Failed to fetch company stats")}catch(s){throw console.error("Error fetching company stats:",s),s}}async getAssignedCompanies(){try{const s=await k.getAssignedCompanies();if(s.success)return s.data||[];throw new Error(s.message||"Failed to fetch assigned companies")}catch(s){throw console.error("Error fetching assigned companies:",s),s}}}const W=new Ss,Cs=()=>{const{user:i}=l.useContext(D),[s,t]=l.useState({users:{total_users:0,active_users:0,new_users_today:0},companies:{total_companies:0,active_companies:0,new_companies_today:0},inventory:{total_items:0,total_value:0,low_stock_count:0},top_categories:[],recent_activity:[]}),[a,o]=l.useState(!0),[c,m]=l.useState([]);l.useEffect(()=>{i&&(async()=>{try{o(!0);const n=await $.getDashboardData();n&&t({users:n.users||{total_users:0,active_users:0,new_users_today:0},companies:n.companies||{total_companies:0,active_companies:0,new_companies_today:0},inventory:n.inventory||{total_items:0,total_value:0,low_stock_count:0},top_categories:n.top_categories||[],recent_activity:n.recent_activity||[]});const x=await $.getTrends();x&&m(x.trends||[])}catch(n){console.error("Failed to load dashboard data:",n)}finally{o(!1)}})()},[i]);const d=({title:r,value:n,icon:x,color:w="blue",subtitle:g,trend:j})=>{const h={blue:"bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 border-blue-200",green:"bg-gradient-to-br from-green-50 to-green-100 text-green-600 border-green-200",purple:"bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600 border-purple-200",orange:"bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600 border-orange-200",red:"bg-gradient-to-br from-red-50 to-red-100 text-red-600 border-red-200",indigo:"bg-gradient-to-br from-indigo-50 to-indigo-100 text-indigo-600 border-indigo-200"};return e.jsx("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:r}),e.jsx("p",{className:"text-3xl font-bold text-gray-900 mb-2",children:n}),g&&e.jsx("p",{className:"text-sm text-gray-500",children:g}),j&&e.jsxs("div",{className:"flex items-center mt-2",children:[j.direction==="up"?e.jsx(F,{className:"h-4 w-4 text-green-500 mr-1"}):e.jsx(Qe,{className:"h-4 w-4 text-red-500 mr-1"}),e.jsx("span",{className:`text-sm font-medium ${j.direction==="up"?"text-green-600":"text-red-600"}`,children:j.value}),e.jsx("span",{className:"text-sm text-gray-500 ml-1",children:"vs last month"})]})]}),e.jsx("div",{className:`p-4 rounded-xl border ${h[w]}`,children:x})]})})};return a?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Super Admin Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"System-wide overview and analytics"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})]}):e.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Super Admin Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Complete system overview and business intelligence - Enhanced v2.0"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[e.jsx(d,{title:"Total Users",value:s.users.total_users,subtitle:`${s.users.active_users} active users`,icon:e.jsx(ue,{className:"h-8 w-8"}),color:"blue",trend:{direction:"up",value:"+12%"}}),e.jsx(d,{title:"Companies",value:s.companies.total_companies,subtitle:`${s.companies.active_companies} active companies`,icon:e.jsx(P,{className:"h-8 w-8"}),color:"green",trend:{direction:"up",value:"+8%"}}),e.jsx(d,{title:"Total Inventory Value",value:`$${s.inventory.total_value.toLocaleString()}`,subtitle:`${s.inventory.total_items} total items`,icon:e.jsx(Q,{className:"h-8 w-8"}),color:"purple",trend:{direction:"up",value:"+15%"}})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsx(d,{title:"Low Stock Alerts",value:s.inventory.low_stock_count,subtitle:"Items need restocking",icon:e.jsx(B,{className:"h-6 w-6"}),color:"red"}),e.jsx(d,{title:"New Users Today",value:s.users.new_users_today,subtitle:"User registrations",icon:e.jsx(he,{className:"h-6 w-6"}),color:"indigo"}),e.jsx(d,{title:"New Companies",value:s.companies.new_companies_today,subtitle:"Companies added today",icon:e.jsx(V,{className:"h-6 w-6"}),color:"orange"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(fe,{className:"h-5 w-5 text-purple-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Top Inventory Categories"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:s.top_categories.slice(0,5).map((r,n)=>{var x;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-3 h-3 rounded-full mr-3 ${["bg-blue-500","bg-green-500","bg-purple-500","bg-orange-500","bg-red-500"][n]}`}),e.jsx("span",{className:"font-medium text-gray-900",children:r.category||"Uncategorized"})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-sm font-semibold text-gray-900",children:[r.item_count," items"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["$",(x=r.total_value)==null?void 0:x.toLocaleString()]})]})]},n)})})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ye,{className:"h-5 w-5 text-blue-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent System Activity"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:s.recent_activity.slice(0,6).map((r,n)=>e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:`w-2 h-2 rounded-full mt-2 ${r.action==="create"?"bg-green-500":r.action==="update"?"bg-blue-500":r.action==="delete"?"bg-red-500":"bg-gray-500"}`}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:[r.action==="create"&&"Created ",r.action==="update"&&"Updated ",r.action==="delete"&&"Deleted ",r.entity_type]}),e.jsxs("p",{className:"text-xs text-gray-500",children:[r.user_name," • ",new Date(r.created_at).toLocaleDateString()]})]})]},n))})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(J,{className:"h-5 w-5 text-green-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Growth Trends (Last 30 Days)"})]})}),e.jsx("div",{className:"p-6",children:c.length>0?e.jsx("div",{className:"space-y-4",children:c.slice(-7).map((r,n)=>{var x;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:new Date(r.date).toLocaleDateString()}),e.jsxs("div",{className:"text-xs text-gray-500",children:[r.items_added," items added"]})]}),e.jsx("div",{className:"text-right",children:e.jsxs("div",{className:"text-sm font-semibold text-green-600",children:["+$",(x=r.value_added)==null?void 0:x.toLocaleString()]})})]},n)})}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(J,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500",children:"No trend data available"})]})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Quick Actions"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(he,{className:"h-5 w-5 text-blue-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Add New User"})]}),e.jsx(F,{className:"h-4 w-4 text-blue-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(V,{className:"h-5 w-5 text-green-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-green-900",children:"Add Company"})]}),e.jsx(F,{className:"h-4 w-4 text-green-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(J,{className:"h-5 w-5 text-purple-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-purple-900",children:"View Reports"})]}),e.jsx(F,{className:"h-4 w-4 text-purple-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(X,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-orange-900",children:"System Overview"})]}),e.jsx(F,{className:"h-4 w-4 text-orange-600 transform rotate-45"})]})]})})]})]})]})},ks=()=>{const{user:i}=l.useContext(D),[s,t]=l.useState({companies:{total_companies:0,active_companies:0},inventory:{total_items:0,total_value:0,low_stock_count:0},top_categories:[],recent_activity:[],assigned_companies:[]}),[a,o]=l.useState(!0),[c,m]=l.useState([]);l.useEffect(()=>{i&&(async()=>{try{o(!0);const n=await $.getDashboardData();n&&t({companies:n.companies||{total_companies:0,active_companies:0},inventory:n.inventory||{total_items:0,total_value:0,low_stock_count:0},top_categories:n.top_categories||[],recent_activity:n.recent_activity||[],assigned_companies:n.assigned_companies||[]});try{const x=await $.getCompanyStats();x&&m(x.top_companies||[])}catch(x){console.error("Failed to load company stats:",x)}}catch(n){console.error("Failed to load dashboard data:",n)}finally{o(!1)}})()},[i]);const d=({title:r,value:n,icon:x,color:w="blue",subtitle:g,trend:j})=>{const h={blue:"bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 border-blue-200",green:"bg-gradient-to-br from-green-50 to-green-100 text-green-600 border-green-200",purple:"bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600 border-purple-200",orange:"bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600 border-orange-200",red:"bg-gradient-to-br from-red-50 to-red-100 text-red-600 border-red-200",indigo:"bg-gradient-to-br from-indigo-50 to-indigo-100 text-indigo-600 border-indigo-200"};return e.jsx("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:r}),e.jsx("p",{className:"text-3xl font-bold text-gray-900 mb-2",children:n}),g&&e.jsx("p",{className:"text-sm text-gray-500",children:g})]}),e.jsx("div",{className:`p-4 rounded-xl border ${h[w]}`,children:x})]})})};return a?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Manager Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Company management and partner oversight"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})]}):e.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Manager Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Comprehensive company management and partner oversight"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[e.jsx(d,{title:"Managed Companies",value:s.companies.total_companies,subtitle:`${s.companies.active_companies} active companies`,icon:e.jsx(P,{className:"h-8 w-8"}),color:"blue"}),e.jsx(d,{title:"Total Inventory Value",value:`$${s.inventory.total_value.toLocaleString()}`,subtitle:`${s.inventory.total_items} total items`,icon:e.jsx(Q,{className:"h-8 w-8"}),color:"green"}),e.jsx(d,{title:"Low Stock Alerts",value:s.inventory.low_stock_count,subtitle:"Items need attention",icon:e.jsx(B,{className:"h-8 w-8"}),color:"red"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(J,{className:"h-5 w-5 text-green-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Top Companies by Value"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:c.slice(0,5).map((r,n)=>{var x;return e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 ${["bg-blue-500","bg-green-500","bg-purple-500","bg-orange-500","bg-red-500"][n]}`,children:n+1}),e.jsxs("div",{children:[e.jsx("p",{className:"font-semibold text-gray-900",children:r.name}),e.jsx("p",{className:"text-sm text-gray-500",children:r.industry})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-bold text-green-600",children:["$",(x=r.total_value)==null?void 0:x.toLocaleString()]}),e.jsxs("p",{className:"text-sm text-gray-500",children:[r.inventory_count," items"]})]})]},n)})})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(fe,{className:"h-5 w-5 text-purple-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Inventory Categories"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:s.top_categories.slice(0,5).map((r,n)=>{var x;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-3 h-3 rounded-full mr-3 ${["bg-blue-500","bg-green-500","bg-purple-500","bg-orange-500","bg-red-500"][n]}`}),e.jsx("span",{className:"font-medium text-gray-900",children:r.category||"Uncategorized"})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-sm font-semibold text-gray-900",children:[r.item_count," items"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["$",(x=r.total_value)==null?void 0:x.toLocaleString()]})]})]},n)})})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ye,{className:"h-5 w-5 text-blue-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Company Activity"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:s.recent_activity.slice(0,6).map((r,n)=>e.jsxs("div",{className:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:`w-2 h-2 rounded-full mt-2 ${r.action==="create"?"bg-green-500":r.action==="update"?"bg-blue-500":r.action==="delete"?"bg-red-500":"bg-gray-500"}`}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:[r.action==="create"&&"Created ",r.action==="update"&&"Updated ",r.action==="delete"&&"Deleted ",r.entity_type]}),e.jsxs("p",{className:"text-xs text-gray-500",children:[r.user_name," • ",new Date(r.created_at).toLocaleDateString()]})]})]},n))})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Quick Actions"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(he,{className:"h-5 w-5 text-blue-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Add Partner"})]}),e.jsx(F,{className:"h-4 w-4 text-blue-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(V,{className:"h-5 w-5 text-green-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-green-900",children:"Manage Companies"})]}),e.jsx(F,{className:"h-4 w-4 text-green-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Y,{className:"h-5 w-5 text-purple-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-purple-900",children:"View Reports"})]}),e.jsx(F,{className:"h-4 w-4 text-purple-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ge,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-orange-900",children:"Audit Trail"})]}),e.jsx(F,{className:"h-4 w-4 text-orange-600 transform rotate-45"})]})]})})]})]})]})},_s=()=>{const{user:i}=l.useContext(D),[s,t]=l.useState({assigned_companies:[],inventory:{total_items:0,total_value:0,low_stock_count:0},top_categories:[],recent_activity:[]}),[a,o]=l.useState(!0),[c,m]=l.useState([]);l.useEffect(()=>{i&&(async()=>{try{o(!0);const n=await $.getDashboardData();n&&t({assigned_companies:n.assigned_companies||[],inventory:n.inventory||{total_items:0,total_value:0,low_stock_count:0},top_categories:n.top_categories||[],recent_activity:n.recent_activity||[]});try{const x=await W.getAssignedCompanies();x&&m(x)}catch(x){console.error("Failed to load companies:",x)}}catch(n){console.error("Failed to load dashboard data:",n)}finally{o(!1)}})()},[i]);const d=({title:r,value:n,icon:x,color:w="blue",subtitle:g,actionButton:j})=>{const h={blue:"bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 border-blue-200",green:"bg-gradient-to-br from-green-50 to-green-100 text-green-600 border-green-200",purple:"bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600 border-purple-200",red:"bg-gradient-to-br from-red-50 to-red-100 text-red-600 border-red-200",orange:"bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600 border-orange-200"};return e.jsx("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:r}),e.jsx("p",{className:"text-3xl font-bold text-gray-900 mb-2",children:n}),g&&e.jsx("p",{className:"text-sm text-gray-500",children:g}),j&&e.jsx("div",{className:"mt-3",children:j})]}),e.jsx("div",{className:`p-4 rounded-xl border ${h[w]}`,children:x})]})})};return a?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Partner Portfolio"}),e.jsx("p",{className:"text-gray-600",children:"Manage your assigned companies and inventory"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})]}):e.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Partner Portfolio"}),e.jsx("p",{className:"text-gray-600",children:"Comprehensive view of your assigned companies and inventory management"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsx(d,{title:"Assigned Companies",value:s.assigned_companies.length,subtitle:"Active partnerships",icon:e.jsx(P,{className:"h-8 w-8"}),color:"blue"}),e.jsx(d,{title:"Total Items",value:s.inventory.total_items,subtitle:"Across all companies",icon:e.jsx(O,{className:"h-8 w-8"}),color:"green"}),e.jsx(d,{title:"Portfolio Value",value:`$${s.inventory.total_value.toLocaleString()}`,subtitle:"Total inventory worth",icon:e.jsx(Q,{className:"h-8 w-8"}),color:"purple"}),e.jsx(d,{title:"Low Stock Alerts",value:s.inventory.low_stock_count,subtitle:"Items need restocking",icon:e.jsx(B,{className:"h-8 w-8"}),color:"red",actionButton:s.inventory.low_stock_count>0&&e.jsx("button",{className:"text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full hover:bg-red-200 transition-colors",children:"View Details"})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[e.jsxs("div",{className:"lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(P,{className:"h-5 w-5 text-blue-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Your Companies"})]}),e.jsx("button",{className:"text-sm text-blue-600 hover:text-blue-800 font-medium",children:"View All"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:c.slice(0,4).map((r,n)=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4",children:e.jsx(P,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-semibold text-gray-900",children:r.name||`Company ${n+1}`}),e.jsx("p",{className:"text-sm text-gray-500",children:r.industry||"Technology"}),e.jsxs("p",{className:"text-xs text-gray-400",children:[r.inventory_count||0," inventory items"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${r.status==="active"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:r.status||"Active"}),e.jsx("button",{className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",children:e.jsx(X,{className:"h-4 w-4"})})]})]},n))})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Quick Actions"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"p-3 bg-blue-50 border border-blue-200 rounded-lg",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(je,{className:"h-5 w-5 text-blue-600 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-blue-900",children:"Read-Only Access"}),e.jsx("p",{className:"text-xs text-blue-700 mt-1",children:"Contact your manager for inventory updates"})]})]})}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(J,{className:"h-5 w-5 text-purple-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-purple-900",children:"View Reports"})]}),e.jsx(F,{className:"h-4 w-4 text-purple-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(B,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-orange-900",children:"Stock Alerts"})]}),e.jsx(F,{className:"h-4 w-4 text-orange-600 transform rotate-45"})]})]})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(fe,{className:"h-5 w-5 text-purple-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Inventory Categories"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:s.top_categories.slice(0,5).map((r,n)=>{var x;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-3 h-3 rounded-full mr-3 ${["bg-blue-500","bg-green-500","bg-purple-500","bg-orange-500","bg-red-500"][n]}`}),e.jsx("span",{className:"font-medium text-gray-900",children:r.category||"Uncategorized"})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-sm font-semibold text-gray-900",children:[r.item_count," items"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["$",(x=r.total_value)==null?void 0:x.toLocaleString()]})]})]},n)})})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ye,{className:"h-5 w-5 text-blue-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Activity"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:s.recent_activity.slice(0,6).map((r,n)=>e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsxs("div",{className:"flex-shrink-0",children:[r.action==="create"&&e.jsx(ne,{className:"h-5 w-5 text-green-500"}),r.action==="update"&&e.jsx(Je,{className:"h-5 w-5 text-blue-500"}),r.action==="delete"&&e.jsx(G,{className:"h-5 w-5 text-red-500"}),!["create","update","delete"].includes(r.action)&&e.jsx(Z,{className:"h-5 w-5 text-gray-500"})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:[r.action==="create"&&"Created ",r.action==="update"&&"Updated ",r.action==="delete"&&"Deleted ",r.entity_type]}),e.jsxs("p",{className:"text-xs text-gray-500",children:[new Date(r.created_at).toLocaleDateString()," • ",new Date(r.created_at).toLocaleTimeString()]})]})]},n))})})]})]})]})},Te=({isOpen:i,onClose:s,onSubmit:t,userRole:a="superadmin"})=>{const[o,c]=l.useState({name:"",email:"",role:"",password:"",confirmPassword:""}),[m,d]=l.useState(!1),[r,n]=l.useState({}),x=h=>{const{name:S,value:_}=h.target;c(b=>({...b,[S]:_})),r[S]&&n(b=>({...b,[S]:""}))},w=()=>{const h={};return o.name.trim()||(h.name="Name is required"),o.email.trim()?/\S+@\S+\.\S+/.test(o.email)||(h.email="Email is invalid"):h.email="Email is required",o.role||(h.role="Role is required"),o.password?o.password.length<6&&(h.password="Password must be at least 6 characters"):h.password="Password is required",o.password!==o.confirmPassword&&(h.confirmPassword="Passwords do not match"),n(h),Object.keys(h).length===0},g=async h=>{if(h.preventDefault(),!!w()){d(!0);try{const S={name:o.name,email:o.email,role:o.role,password:o.password};await t(S),j()}catch(S){console.error("Failed to add user:",S),n({submit:"Failed to add user. Please try again."})}finally{d(!1)}}},j=()=>{c({name:"",email:"",role:"",password:"",confirmPassword:""}),n({}),d(!1),s()};return i?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:j}),e.jsxs("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[e.jsx("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Add New User"}),e.jsx("button",{onClick:j,className:"text-gray-400 hover:text-gray-600",children:e.jsx(U,{className:"h-6 w-6"})})]})}),e.jsxs("form",{onSubmit:g,children:[e.jsxs("div",{className:"px-4 pb-4 sm:px-6 sm:pb-6",children:[r.submit&&e.jsx("div",{className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:r.submit}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Full Name *"}),e.jsx("input",{type:"text",id:"name",name:"name",value:o.name,onChange:x,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.name?"border-red-300":"border-gray-300"}`,placeholder:"Enter full name"}),r.name&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.name})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email *"}),e.jsx("input",{type:"email",id:"email",name:"email",value:o.email,onChange:x,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.email?"border-red-300":"border-gray-300"}`,placeholder:"<EMAIL>"}),r.email&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.email})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700",children:"Role *"}),e.jsxs("select",{id:"role",name:"role",value:o.role,onChange:x,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.role?"border-red-300":"border-gray-300"}`,children:[e.jsx("option",{value:"",children:"Select role"}),a==="superadmin"&&e.jsxs(e.Fragment,{children:[e.jsx("option",{value:"superadmin",children:"Super Admin"}),e.jsx("option",{value:"manager",children:"Manager"})]}),e.jsx("option",{value:"partner",children:"Partner"})]}),r.role&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.role})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password *"}),e.jsx("input",{type:"password",id:"password",name:"password",value:o.password,onChange:x,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.password?"border-red-300":"border-gray-300"}`,placeholder:"Enter password"}),r.password&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.password})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password *"}),e.jsx("input",{type:"password",id:"confirmPassword",name:"confirmPassword",value:o.confirmPassword,onChange:x,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.confirmPassword?"border-red-300":"border-gray-300"}`,placeholder:"Confirm password"}),r.confirmPassword&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.confirmPassword})]})]})]}),e.jsxs("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[e.jsx("button",{type:"submit",disabled:m,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:m?"Adding...":"Add User"}),e.jsx("button",{type:"button",onClick:j,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"Cancel"})]})]})]})]})}):null},Pe=({isOpen:i,onClose:s,user:t,onApprove:a,onReject:o})=>{const[c,m]=l.useState(!1),[d,r]=l.useState(""),[n,x]=l.useState(!1),w=async()=>{m(!0);try{await a(t.id,{approved:!0,notes:"User approved by Super Admin"})}catch(h){console.error("Failed to approve user:",h)}finally{m(!1)}},g=async()=>{if(d.trim()){m(!0);try{await o(t.id,d)}catch(h){console.error("Failed to reject user:",h)}finally{m(!1)}}},j=()=>{r(""),x(!1),m(!1),s()};return!i||!t?null:e.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:e.jsxs("div",{className:"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white",children:[e.jsxs("div",{className:"flex items-center justify-between pb-4 border-b border-gray-200",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"User Approval Review"}),e.jsx("button",{onClick:j,className:"text-gray-400 hover:text-gray-600",children:e.jsx(U,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6 mb-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"flex-shrink-0 h-16 w-16",children:e.jsx("div",{className:"h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center",children:e.jsx(re,{className:"h-8 w-8 text-blue-600"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h4",{className:"text-xl font-semibold text-gray-900",children:t.name}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Pending approval since ",new Date(t.created_at).toLocaleDateString()]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ae,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Email"}),e.jsx("p",{className:"text-sm text-gray-600",children:t.email})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ye,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Role"}),e.jsx("p",{className:"text-sm text-gray-600 capitalize",children:t.role})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ce,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Created"}),e.jsx("p",{className:"text-sm text-gray-600",children:new Date(t.created_at).toLocaleString()})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(re,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Created By"}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Manager ID: ",t.created_by]})]})]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(z,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm font-medium text-orange-800",children:"Approval Required"}),e.jsx("p",{className:"text-sm text-orange-700 mt-1",children:"This user was created by a Manager and requires Super Admin approval before gaining system access."})]})]})})}),n&&e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"rejectionReason",className:"block text-sm font-medium text-gray-700 mb-2",children:"Rejection Reason *"}),e.jsx("textarea",{id:"rejectionReason",rows:4,value:d,onChange:h=>r(h.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500",placeholder:"Please provide a reason for rejecting this user...",required:!0}),!d.trim()&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:"Rejection reason is required"})]}),e.jsxs("div",{className:"flex justify-end space-x-3",children:[e.jsx("button",{onClick:j,className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",disabled:c,children:"Cancel"}),n?e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>x(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",disabled:c,children:"Back"}),e.jsxs("button",{onClick:g,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",disabled:c||!d.trim(),children:[c?e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):e.jsx(G,{className:"h-4 w-4 mr-2"}),"Confirm Rejection"]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>x(!0),className:"inline-flex items-center px-4 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",disabled:c,children:[e.jsx(G,{className:"h-4 w-4 mr-2"}),"Reject"]}),e.jsxs("button",{onClick:w,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",disabled:c,children:[c?e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):e.jsx(ee,{className:"h-4 w-4 mr-2"}),"Approve User"]})]})]})]})]})})};Pe.propTypes={isOpen:N.bool.isRequired,onClose:N.func.isRequired,user:N.object,onApprove:N.func.isRequired,onReject:N.func.isRequired};const Is=()=>{const{userRole:i,user:s}=l.useContext(D),[t,a]=l.useState([{id:"1",name:"John Admin",email:"<EMAIL>",role:"superadmin",status:"active",approval_status:"approved"},{id:"2",name:"Jane Manager",email:"<EMAIL>",role:"manager",status:"active",approval_status:"approved"},{id:"3",name:"Bob Partner",email:"<EMAIL>",role:"partner",status:"active",approval_status:"approved"},{id:"4",name:"Alice Smith",email:"<EMAIL>",role:"partner",status:"active",approval_status:"pending",created_by:"2",created_at:"2025-07-15T10:30:00Z"},{id:"5",name:"Mike Johnson",email:"<EMAIL>",role:"manager",status:"active",approval_status:"pending",created_by:"2",created_at:"2025-07-15T09:15:00Z"}]),[o,c]=l.useState(!1),[m,d]=l.useState(!1),[r,n]=l.useState(null),[x,w]=l.useState("all"),g=async v=>{try{const I={id:String(t.length+1),...v,status:"active",approval_status:i==="superadmin"?"approved":"pending",created_by:s.id,created_at:new Date().toISOString()};a(f=>[...f,I]),c(!1)}catch(I){throw console.error("Failed to add user:",I),I}},j=async(v,I)=>{try{a(f=>f.map(p=>p.id===v?{...p,approval_status:"approved",approved_by:p.id,approved_at:new Date().toISOString()}:p)),d(!1),n(null)}catch(f){throw console.error("Failed to approve user:",f),f}},h=async(v,I)=>{try{a(f=>f.map(p=>p.id===v?{...p,approval_status:"rejected",rejection_reason:I,rejected_at:new Date().toISOString()}:p)),d(!1),n(null)}catch(f){throw console.error("Failed to reject user:",f),f}},S=v=>{n(v),d(!0)},_=t.filter(v=>x==="all"?!0:x==="pending"?v.approval_status==="pending":x==="approved"?v.approval_status==="approved":x==="rejected"?v.approval_status==="rejected":!0),b=t.filter(v=>v.approval_status==="pending").length;return e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"User Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage system users and their permissions"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[b>0&&e.jsx("div",{className:"bg-orange-100 border border-orange-200 rounded-lg px-3 py-2",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Z,{className:"h-4 w-4 text-orange-600 mr-2"}),e.jsxs("span",{className:"text-sm font-medium text-orange-800",children:[b," pending approval",b!==1?"s":""]})]})}),e.jsxs("button",{onClick:()=>c(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(ie,{className:"h-4 w-4 mr-2"}),"Add User"]})]})]})}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"border-b border-gray-200",children:e.jsx("nav",{className:"-mb-px flex space-x-8",children:[{id:"all",name:"All Users",count:t.length},{id:"pending",name:"Pending Approval",count:b},{id:"approved",name:"Approved",count:t.filter(v=>v.approval_status==="approved").length},{id:"rejected",name:"Rejected",count:t.filter(v=>v.approval_status==="rejected").length}].map(v=>e.jsxs("button",{onClick:()=>w(v.id),className:`${x===v.id?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`,children:[v.name,e.jsx("span",{className:`ml-2 py-0.5 px-2 rounded-full text-xs ${x===v.id?"bg-primary text-white":"bg-gray-100 text-gray-600"}`,children:v.count})]},v.id))})})}),e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx(se,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search users...",className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"})]}),e.jsxs("button",{className:"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[e.jsx(ke,{className:"h-4 w-4 mr-2"}),"Filter"]})]})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Approval"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:_.map(v=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:e.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center",children:e.jsx(re,{className:"h-5 w-5 text-gray-500"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:v.name}),e.jsx("div",{className:"text-sm text-gray-500",children:v.email})]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800",children:v.role})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${v.status==="active"?"bg-green-100 text-green-800":v.status==="inactive"?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:v.status})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${v.approval_status==="approved"?"bg-green-100 text-green-800":v.approval_status==="pending"?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800"}`,children:[v.approval_status==="approved"&&e.jsx(ee,{className:"h-3 w-3 mr-1"}),v.approval_status==="pending"&&e.jsx(Z,{className:"h-3 w-3 mr-1"}),v.approval_status==="rejected"&&e.jsx(U,{className:"h-3 w-3 mr-1"}),v.approval_status]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:v.created_at?new Date(v.created_at).toLocaleDateString():"-"}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[v.approval_status==="pending"&&e.jsx("button",{onClick:()=>S(v),className:"text-primary hover:text-primary-dark mr-3",children:"Review"}),e.jsx("button",{className:"text-gray-600 hover:text-gray-900 mr-3",children:"Edit"}),e.jsx("button",{className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},v.id))})]})})]}),e.jsx(Te,{isOpen:o,onClose:()=>c(!1),onSubmit:g,userRole:i}),e.jsx(Pe,{isOpen:m,onClose:()=>d(!1),user:r,onApprove:j,onReject:h})]})},H=({onFileSelect:i,acceptedTypes:s=[".xlsx",".xls",".pdf"],maxSize:t=10*1024*1024,multiple:a=!1,disabled:o=!1,className:c=""})=>{const[m,d]=l.useState(!1),[r,n]=l.useState([]),[x,w]=l.useState([]),g=l.useRef(null),j=p=>{var C;const y=[],u="."+((C=p.name.split(".").pop())==null?void 0:C.toLowerCase());if(s.includes(u)||y.push(`Invalid file type. Accepted types: ${s.join(", ")}`),p.size>t){const A=Math.round(t/1048576);y.push(`File size exceeds ${A}MB limit`)}return p.size===0&&y.push("File is empty"),y},h=p=>{const y=Array.from(p),u=[],C=[];y.forEach(A=>{const L=j(A);L.length===0?u.push(A):C.push(`${A.name}: ${L.join(", ")}`)}),w(C),a?(n(A=>[...A,...u]),i([...r,...u])):(n(u.slice(0,1)),i(u[0]||null))},S=p=>{p.preventDefault(),p.stopPropagation(),p.type==="dragenter"||p.type==="dragover"?d(!0):p.type==="dragleave"&&d(!1)},_=p=>{p.preventDefault(),p.stopPropagation(),d(!1),!o&&p.dataTransfer.files&&p.dataTransfer.files[0]&&h(p.dataTransfer.files)},b=p=>{p.preventDefault(),!o&&p.target.files&&p.target.files[0]&&h(p.target.files)},v=p=>{const y=r.filter((u,C)=>C!==p);n(y),i(a?y:null)},I=()=>{!o&&g.current&&g.current.click()},f=p=>{if(p===0)return"0 Bytes";const y=1024,u=["Bytes","KB","MB","GB"],C=Math.floor(Math.log(p)/Math.log(y));return parseFloat((p/Math.pow(y,C)).toFixed(2))+" "+u[C]};return e.jsxs("div",{className:`w-full ${c}`,children:[e.jsxs("div",{className:`
          relative border-2 border-dashed rounded-lg p-6 text-center transition-colors
          ${m?"border-primary bg-primary bg-opacity-5":"border-gray-300"}
          ${o?"bg-gray-100 cursor-not-allowed":"hover:border-primary hover:bg-primary hover:bg-opacity-5 cursor-pointer"}
        `,onDragEnter:S,onDragLeave:S,onDragOver:S,onDrop:_,onClick:I,children:[e.jsx("input",{ref:g,type:"file",multiple:a,accept:s.join(","),onChange:b,disabled:o,className:"hidden"}),e.jsx(le,{className:`mx-auto h-12 w-12 ${o?"text-gray-400":"text-gray-500"}`}),e.jsxs("div",{className:"mt-4",children:[e.jsx("p",{className:`text-sm font-medium ${o?"text-gray-400":"text-gray-900"}`,children:m?"Drop files here":"Click to upload or drag and drop"}),e.jsxs("p",{className:`text-xs mt-1 ${o?"text-gray-400":"text-gray-500"}`,children:[s.join(", ")," up to ",Math.round(t/(1024*1024)),"MB"]})]})]}),x.length>0&&e.jsx("div",{className:"mt-3 space-y-1",children:x.map((p,y)=>e.jsxs("div",{className:"flex items-center text-sm text-red-600",children:[e.jsx(z,{className:"h-4 w-4 mr-2 flex-shrink-0"}),p]},y))}),r.length>0&&e.jsxs("div",{className:"mt-4 space-y-2",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Selected Files:"}),r.map((p,y)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center min-w-0 flex-1",children:[e.jsx(_e,{className:"h-5 w-5 text-gray-400 mr-3 flex-shrink-0"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:p.name}),e.jsx("p",{className:"text-xs text-gray-500",children:f(p.size)})]})]}),e.jsxs("div",{className:"flex items-center ml-4",children:[e.jsx(ne,{className:"h-5 w-5 text-green-500 mr-2"}),e.jsx("button",{type:"button",onClick:u=>{u.stopPropagation(),v(y)},className:"p-1 hover:bg-gray-200 rounded",disabled:o,children:e.jsx(U,{className:"h-4 w-4 text-gray-400"})})]})]},y))]})]})};H.propTypes={onFileSelect:N.func.isRequired,acceptedTypes:N.arrayOf(N.string),maxSize:N.number,multiple:N.bool,disabled:N.bool,className:N.string};const $e=({isOpen:i,onClose:s,onSubmit:t,userRole:a="superadmin",currentUserId:o=null})=>{const[c,m]=l.useState({name:"",description:"",industry:"",email:"",phone:"",address:"",website:"",managerId:a==="manager"?o:""}),[d,r]=l.useState(!1),[n,x]=l.useState({}),[w,g]=l.useState([]),[j,h]=l.useState([]),[S,_]=l.useState("basic");l.useEffect(()=>{(async()=>{if(a==="superadmin"&&i)try{g([{id:"2",name:"Jane Manager",email:"<EMAIL>"}])}catch(C){console.error("Failed to load managers:",C),g([])}})()},[a,i]);const b=u=>{const{name:C,value:A}=u.target;m(L=>({...L,[C]:A})),n[C]&&x(L=>({...L,[C]:""}))},v=()=>{const u={};return c.name.trim()||(u.name="Company name is required"),c.email.trim()?/\S+@\S+\.\S+/.test(c.email)||(u.email="Email is invalid"):u.email="Email is required",c.industry.trim()||(u.industry="Industry is required"),x(u),Object.keys(u).length===0},I=(u,C)=>{if(u){const A={id:Date.now(),file:u,type:C,name:u.name,size:u.size,status:"pending"};h(L=>[...L,A])}},f=u=>{h(C=>C.filter(A=>A.id!==u))},p=async u=>{if(u.preventDefault(),!!v()){r(!0);try{const C={...c,documents:j};await t(C),y()}catch(C){console.error("Failed to add company:",C),x({submit:"Failed to add company. Please try again."})}finally{r(!1)}}},y=()=>{m({name:"",description:"",industry:"",email:"",phone:"",address:"",website:"",managerId:a==="manager"?o:""}),x({}),h([]),_("basic"),r(!1),s()};return i?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:y}),e.jsxs("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full",children:[e.jsxs("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Add New Company"}),e.jsx("button",{onClick:y,className:"text-gray-400 hover:text-gray-600",children:e.jsx(U,{className:"h-6 w-6"})})]}),e.jsx("div",{className:"mt-4",children:e.jsx("div",{className:"border-b border-gray-200",children:e.jsxs("nav",{className:"-mb-px flex space-x-8",children:[e.jsx("button",{type:"button",onClick:()=>_("basic"),className:`${S==="basic"?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`,children:"Basic Information"}),e.jsxs("button",{type:"button",onClick:()=>_("documents"),className:`${S==="documents"?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`,children:[e.jsx(Y,{className:"h-4 w-4 mr-1"}),"Documents",j.length>0&&e.jsx("span",{className:"ml-1 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:j.length})]})]})})})]}),e.jsxs("form",{onSubmit:p,children:[e.jsxs("div",{className:"px-4 pb-4 sm:px-6 sm:pb-6",children:[n.submit&&e.jsx("div",{className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:n.submit}),S==="basic"&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Company Name *"}),e.jsx("input",{type:"text",id:"name",name:"name",value:c.name,onChange:b,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${n.name?"border-red-300":"border-gray-300"}`,placeholder:"Enter company name"}),n.name&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:n.name})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email *"}),e.jsx("input",{type:"email",id:"email",name:"email",value:c.email,onChange:b,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${n.email?"border-red-300":"border-gray-300"}`,placeholder:"<EMAIL>"}),n.email&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:n.email})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"industry",className:"block text-sm font-medium text-gray-700",children:"Industry *"}),e.jsxs("select",{id:"industry",name:"industry",value:c.industry,onChange:b,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${n.industry?"border-red-300":"border-gray-300"}`,children:[e.jsx("option",{value:"",children:"Select industry"}),e.jsx("option",{value:"Technology",children:"Technology"}),e.jsx("option",{value:"Manufacturing",children:"Manufacturing"}),e.jsx("option",{value:"Retail",children:"Retail"}),e.jsx("option",{value:"Healthcare",children:"Healthcare"}),e.jsx("option",{value:"Finance",children:"Finance"}),e.jsx("option",{value:"Education",children:"Education"}),e.jsx("option",{value:"Other",children:"Other"})]}),n.industry&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:n.industry})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone"}),e.jsx("input",{type:"tel",id:"phone",name:"phone",value:c.phone,onChange:b,className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"+****************"})]}),a==="superadmin"&&e.jsxs("div",{children:[e.jsx("label",{htmlFor:"managerId",className:"block text-sm font-medium text-gray-700",children:"Assign Manager (Optional)"}),e.jsxs("select",{id:"managerId",name:"managerId",value:c.managerId,onChange:b,className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",children:[e.jsx("option",{value:"",children:"No manager assigned"}),w.map(u=>e.jsxs("option",{value:u.id,children:[u.name," (",u.email,")"]},u.id))]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700",children:"Description"}),e.jsx("textarea",{id:"description",name:"description",rows:3,value:c.description,onChange:b,className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Brief description of the company"})]})]}),S==="documents"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Company Documents"}),e.jsx("p",{className:"text-sm text-gray-600 mb-6",children:"Upload required documents for company verification. These will be reviewed during the approval process."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-3",children:"Company Registration"}),e.jsx(H,{onFileSelect:u=>I(u,"registration"),acceptedTypes:[".pdf",".jpg",".jpeg",".png"],maxSize:5*1024*1024,multiple:!1,className:"mb-3"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Upload company registration certificate"})]}),e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-3",children:"Tax Certificate"}),e.jsx(H,{onFileSelect:u=>I(u,"tax_certificate"),acceptedTypes:[".pdf",".jpg",".jpeg",".png"],maxSize:5*1024*1024,multiple:!1,className:"mb-3"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Upload tax registration certificate"})]}),e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-3",children:"Business License"}),e.jsx(H,{onFileSelect:u=>I(u,"business_license"),acceptedTypes:[".pdf",".jpg",".jpeg",".png"],maxSize:5*1024*1024,multiple:!1,className:"mb-3"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Upload business license (optional)"})]}),e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-3",children:"Other Documents"}),e.jsx(H,{onFileSelect:u=>I(u,"other"),acceptedTypes:[".pdf",".jpg",".jpeg",".png"],maxSize:5*1024*1024,multiple:!1,className:"mb-3"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Upload any additional documents"})]})]}),j.length>0&&e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-3",children:"Uploaded Documents"}),e.jsx("div",{className:"space-y-2",children:j.map(u=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Y,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:u.name}),e.jsx("p",{className:"text-xs text-gray-500 capitalize",children:u.type.replace("_"," ")})]})]}),e.jsx("button",{type:"button",onClick:()=>f(u.id),className:"text-red-600 hover:text-red-800",children:e.jsx(G,{className:"h-5 w-5"})})]},u.id))})]})]})]}),e.jsxs("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[e.jsx("button",{type:"submit",disabled:d,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:d?"Adding...":"Add Company"}),e.jsx("button",{type:"button",onClick:y,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"Cancel"})]})]})]})]})}):null},Le=({isOpen:i,onClose:s,company:t,onApprove:a,onReject:o})=>{const[c,m]=l.useState(!1),[d,r]=l.useState(""),[n,x]=l.useState(!1),[w,g]=l.useState("all"),j=async()=>{m(!0);try{await a(t.id,{approved:!0,notes:"Company approved by Super Admin"})}catch(b){console.error("Failed to approve company:",b)}finally{m(!1)}},h=async()=>{if(d.trim()){m(!0);try{await o(t.id,d)}catch(b){console.error("Failed to reject company:",b)}finally{m(!1)}}},S=()=>{r(""),x(!1),m(!1),s()};if(!i||!t)return null;const _=b=>{switch(b){case"verified":return"bg-green-100 text-green-800";case"pending":return"bg-orange-100 text-orange-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return e.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:e.jsxs("div",{className:"relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white",children:[e.jsxs("div",{className:"flex items-center justify-between pb-4 border-b border-gray-200",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Company Approval Review"}),e.jsx("button",{onClick:S,className:"text-gray-400 hover:text-gray-600",children:e.jsx(U,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6 mb-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"flex-shrink-0 h-16 w-16",children:e.jsx("div",{className:"h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center",children:e.jsx(V,{className:"h-8 w-8 text-blue-600"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h4",{className:"text-xl font-semibold text-gray-900",children:t.name}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Pending approval since ",new Date(t.created_at).toLocaleDateString()]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ae,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Email"}),e.jsx("p",{className:"text-sm text-gray-600",children:t.email||"Not provided"})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ze,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Phone"}),e.jsx("p",{className:"text-sm text-gray-600",children:t.phone||"Not provided"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ce,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Industry"}),e.jsx("p",{className:"text-sm text-gray-600",children:t.industry||"Not specified"})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(V,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Created By"}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Manager ID: ",t.created_by]})]})]})]})]}),t.description&&e.jsxs("div",{className:"mt-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 mb-1",children:"Description"}),e.jsx("p",{className:"text-sm text-gray-600",children:t.description})]})]}),t.documents&&t.documents.length>0&&e.jsxs("div",{className:"mb-6",children:[e.jsx("h5",{className:"text-lg font-medium text-gray-900 mb-4",children:"Document Verification"}),e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsx("div",{className:"flex space-x-4",children:["all","registration","tax_certificate","business_license"].map(b=>e.jsx("button",{onClick:()=>g(b),className:`px-3 py-2 text-sm font-medium rounded-md ${w===b?"bg-primary text-white":"text-gray-500 hover:text-gray-700"}`,children:b==="all"?"All Documents":b.replace("_"," ").replace(/\b\w/g,v=>v.toUpperCase())},b))})}),e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"space-y-3",children:t.documents.filter(b=>w==="all"||b.type===w).map((b,v)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Y,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:b.filename}),e.jsx("p",{className:"text-xs text-gray-500 capitalize",children:b.type.replace("_"," ")})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${_(b.status)}`,children:b.status}),e.jsx("button",{className:"text-primary hover:text-primary-dark",children:e.jsx(X,{className:"h-4 w-4"})})]})]},v))})})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(z,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm font-medium text-orange-800",children:"Approval Required"}),e.jsx("p",{className:"text-sm text-orange-700 mt-1",children:"This company was created by a Manager and requires Super Admin approval before becoming active in the system."})]})]})})}),n&&e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"rejectionReason",className:"block text-sm font-medium text-gray-700 mb-2",children:"Rejection Reason *"}),e.jsx("textarea",{id:"rejectionReason",rows:4,value:d,onChange:b=>r(b.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500",placeholder:"Please provide a reason for rejecting this company...",required:!0}),!d.trim()&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:"Rejection reason is required"})]}),e.jsxs("div",{className:"flex justify-end space-x-3",children:[e.jsx("button",{onClick:S,className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",disabled:c,children:"Cancel"}),n?e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>x(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",disabled:c,children:"Back"}),e.jsxs("button",{onClick:h,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",disabled:c||!d.trim(),children:[c?e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):e.jsx(G,{className:"h-4 w-4 mr-2"}),"Confirm Rejection"]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>x(!0),className:"inline-flex items-center px-4 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",disabled:c,children:[e.jsx(G,{className:"h-4 w-4 mr-2"}),"Reject"]}),e.jsxs("button",{onClick:j,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",disabled:c,children:[c?e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):e.jsx(ee,{className:"h-4 w-4 mr-2"}),"Approve Company"]})]})]})]})]})})};Le.propTypes={isOpen:N.bool.isRequired,onClose:N.func.isRequired,company:N.object,onApprove:N.func.isRequired,onReject:N.func.isRequired};const As=()=>{const{user:i,userRole:s}=l.useContext(D),[t,a]=l.useState([]),[o,c]=l.useState(!0),[m,d]=l.useState(!1),[r,n]=l.useState(!1),[x,w]=l.useState(null),[g,j]=l.useState("all");l.useEffect(()=>{i&&(async()=>{try{c(!0);const y=(await W.getCompanies()).map(u=>({...u,approval_status:u.id<=3?"approved":"pending",created_by:u.id>3?"2":"1",created_at:u.id>3?"2025-07-15T11:00:00Z":"2025-07-10T10:00:00Z",documents:u.id>3?[{type:"registration",filename:"company_registration.pdf",status:"pending"},{type:"tax_certificate",filename:"tax_cert.pdf",status:"pending"}]:[]}));a(y)}catch(p){console.error("Failed to load companies:",p),a([])}finally{c(!1)}})()},[i]);const h=async f=>{try{const y={...await W.createCompany(f),approval_status:s==="superadmin"?"approved":"pending",created_by:i.id,created_at:new Date().toISOString(),documents:[]};a(u=>[...u,y]),d(!1)}catch(p){throw console.error("Failed to add company:",p),p}},S=async(f,p)=>{try{a(y=>y.map(u=>u.id===f?{...u,approval_status:"approved",approved_by:i.id,approved_at:new Date().toISOString()}:u)),n(!1),w(null)}catch(y){throw console.error("Failed to approve company:",y),y}},_=async(f,p)=>{try{a(y=>y.map(u=>u.id===f?{...u,approval_status:"rejected",rejection_reason:p,rejected_at:new Date().toISOString()}:u)),n(!1),w(null)}catch(y){throw console.error("Failed to reject company:",y),y}},b=f=>{w(f),n(!0)},v=t.filter(f=>g==="all"?!0:g==="pending"?f.approval_status==="pending":g==="approved"?f.approval_status==="approved":g==="rejected"?f.approval_status==="rejected":!0),I=t.filter(f=>f.approval_status==="pending").length;return o?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Company Oversight"}),e.jsx("p",{className:"text-gray-600",children:"Monitor and manage all companies in the system"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})})]}):e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Company Oversight"}),e.jsx("p",{className:"text-gray-600",children:"Monitor and manage all companies in the system"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[I>0&&e.jsx("div",{className:"bg-orange-100 border border-orange-200 rounded-lg px-3 py-2",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Z,{className:"h-4 w-4 text-orange-600 mr-2"}),e.jsxs("span",{className:"text-sm font-medium text-orange-800",children:[I," pending approval",I!==1?"s":""]})]})}),e.jsxs("button",{onClick:()=>d(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(ie,{className:"h-4 w-4 mr-2"}),"Add Company"]})]})]})}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"border-b border-gray-200",children:e.jsx("nav",{className:"-mb-px flex space-x-8",children:[{id:"all",name:"All Companies",count:t.length},{id:"pending",name:"Pending Approval",count:I},{id:"approved",name:"Approved",count:t.filter(f=>f.approval_status==="approved").length},{id:"rejected",name:"Rejected",count:t.filter(f=>f.approval_status==="rejected").length}].map(f=>e.jsxs("button",{onClick:()=>j(f.id),className:`${g===f.id?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`,children:[f.name,e.jsx("span",{className:`ml-2 py-0.5 px-2 rounded-full text-xs ${g===f.id?"bg-primary text-white":"bg-gray-100 text-gray-600"}`,children:f.count})]},f.id))})})}),e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(se,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search companies...",className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary w-64"})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6",children:v.map(f=>e.jsxs("div",{className:`border rounded-lg p-6 hover:shadow-md transition-shadow ${f.approval_status==="pending"?"border-orange-200 bg-orange-50":f.approval_status==="rejected"?"border-red-200 bg-red-50":"border-gray-200 bg-white"}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(V,{className:"h-5 w-5 text-gray-400 mr-2"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:f.name})]}),e.jsxs("span",{className:`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${f.approval_status==="approved"?"bg-green-100 text-green-800":f.approval_status==="pending"?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800"}`,children:[f.approval_status==="approved"&&e.jsx(ee,{className:"h-3 w-3 mr-1"}),f.approval_status==="pending"&&e.jsx(Z,{className:"h-3 w-3 mr-1"}),f.approval_status==="rejected"&&e.jsx(U,{className:"h-3 w-3 mr-1"}),f.approval_status]})]}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600 mb-4",children:[e.jsxs("p",{children:["ID: ",f.id]}),e.jsxs("p",{children:["Industry: ",f.industry||"Not specified"]}),e.jsxs("p",{children:["Created: ",new Date(f.created_at).toLocaleDateString()]}),f.documents&&f.documents.length>0&&e.jsxs("div",{className:"flex items-center",children:[e.jsx(Y,{className:"h-4 w-4 mr-1"}),e.jsxs("span",{children:[f.documents.length," document",f.documents.length!==1?"s":""]})]})]}),f.approval_status==="pending"&&e.jsxs("div",{className:"border-t border-gray-200 pt-4",children:[e.jsx("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-3 mb-3",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(z,{className:"h-4 w-4 text-orange-600 mr-2"}),e.jsx("span",{className:"text-xs font-medium text-orange-800",children:"Requires approval"})]})}),e.jsx("button",{onClick:()=>b(f),className:"w-full bg-primary text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-primary-dark transition-colors",children:"Review & Approve"})]}),f.approval_status==="rejected"&&f.rejection_reason&&e.jsx("div",{className:"border-t border-gray-200 pt-4",children:e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:[e.jsx("p",{className:"text-xs font-medium text-red-800 mb-1",children:"Rejection Reason:"}),e.jsx("p",{className:"text-xs text-red-700",children:f.rejection_reason})]})}),f.approval_status==="approved"&&e.jsxs("div",{className:"mt-4 flex space-x-2",children:[e.jsx("button",{className:"text-primary hover:text-primary-dark text-sm",children:"View Details"}),e.jsx("button",{className:"text-gray-600 hover:text-gray-900 text-sm",children:"Edit"})]})]},f.id))})]}),e.jsx($e,{isOpen:m,onClose:()=>d(!1),onSubmit:h,userRole:s,currentUserId:i==null?void 0:i.id}),e.jsx(Le,{isOpen:r,onClose:()=>n(!1),company:x,onApprove:S,onReject:_})]})},ce=({selectedCompany:i,onCompanyChange:s,companies:t,className:a="",showAllOption:o=!0,disabled:c=!1})=>{const{user:m,userRole:d}=l.useContext(D),[r,n]=l.useState([]),[x,w]=l.useState([]);l.useEffect(()=>{m&&(async()=>{try{if(t)n(t);else{const h=await W.getCompanies();w(h);let S=[];switch(d){case"superadmin":case"manager":S=h;break;case"partner":S=(m==null?void 0:m.assigned_companies)||[];break;default:S=[]}n(S),!i&&S.length>0&&!o&&s(S[0].id.toString())}}catch(h){console.error("Failed to load companies:",h),n([])}})()},[m,d,t,i,o,s]);const g=j=>{s(j.target.value)};return e.jsx("div",{className:`relative ${a}`,children:e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:i,onChange:g,disabled:c,className:`
            appearance-none w-full bg-white border border-gray-300 rounded-lg px-4 py-2 pr-10
            text-sm font-medium text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary 
            focus:border-primary disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed
          `,children:[o&&e.jsx("option",{value:"all",children:"All Companies"}),r.map(j=>e.jsx("option",{value:j.id,children:j.name},j.id))]}),e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:e.jsx(be,{className:"h-4 w-4 text-gray-400"})}),e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(P,{className:"h-4 w-4 text-gray-400"})})]})})};ce.propTypes={selectedCompany:N.string.isRequired,onCompanyChange:N.func.isRequired,companies:N.arrayOf(N.shape({id:N.string.isRequired,name:N.string.isRequired})),className:N.string,showAllOption:N.bool,disabled:N.bool};N.arrayOf(N.string).isRequired,N.func.isRequired,N.arrayOf(N.shape({id:N.string.isRequired,name:N.string.isRequired})),N.string,N.string,N.bool,N.bool,N.number;const ve=({isOpen:i,onClose:s,onUpload:t})=>{var S;const[a,o]=l.useState(null),[c,m]=l.useState("select"),[d,r]=l.useState(0),[n,x]=l.useState(null),w=_=>{o(_),m("select")},g=async()=>{if(a){m("uploading"),r(0);try{const _=setInterval(()=>{r(v=>v>=90?(clearInterval(_),90):v+10)},200),b=await t(a);clearInterval(_),r(100),setTimeout(()=>{x(b),m(b.success?"success":"error")},500)}catch(_){m("error"),x({success:!1,error:_.message||"Upload failed"})}}},j=()=>{o(null),m("select"),r(0),x(null),s()},h=()=>{o(null),m("select"),r(0),x(null)};return i?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:j}),e.jsxs("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[e.jsx("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Upload Inventory File"}),e.jsx("button",{onClick:j,className:"text-gray-400 hover:text-gray-600",children:e.jsx(U,{className:"h-6 w-6"})})]})}),e.jsxs("div",{className:"px-4 pb-4 sm:px-6 sm:pb-6",children:[c==="select"&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Upload Excel (.xlsx, .xls) or PDF files containing inventory data. The system will automatically detect and parse the content."}),e.jsx(H,{onFileSelect:w,acceptedTypes:[".xlsx",".xls",".pdf"],maxSize:10*1024*1024,multiple:!1}),a&&e.jsx("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(_e,{className:"h-5 w-5 text-blue-500 mr-2"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-blue-900",children:a.name}),e.jsxs("p",{className:"text-xs text-blue-700",children:[(a.size/1024/1024).toFixed(2)," MB"]})]})]})})]}),c==="uploading"&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(le,{className:"mx-auto h-12 w-12 text-blue-500 animate-pulse"}),e.jsx("p",{className:"mt-2 text-sm font-medium text-gray-900",children:"Uploading and processing..."}),e.jsx("p",{className:"text-xs text-gray-500",children:"Please wait while we process your file"})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${d}%`}})}),e.jsxs("p",{className:"text-center text-sm text-gray-600",children:[d,"% complete"]})]}),c==="success"&&n&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(ne,{className:"mx-auto h-12 w-12 text-green-500"}),e.jsx("p",{className:"mt-2 text-sm font-medium text-gray-900",children:"Upload Successful!"})]}),e.jsx("div",{className:"bg-green-50 p-4 rounded-lg",children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-900",children:"Processed Items:"}),e.jsx("p",{className:"text-green-700",children:n.processedItems||0})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-900",children:"Created Items:"}),e.jsx("p",{className:"text-green-700",children:n.createdItems||0})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-900",children:"Updated Items:"}),e.jsx("p",{className:"text-green-700",children:n.updatedItems||0})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-900",children:"Errors:"}),e.jsx("p",{className:"text-green-700",children:((S=n.errors)==null?void 0:S.length)||0})]})]})}),n.errors&&n.errors.length>0&&e.jsxs("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[e.jsx("p",{className:"font-medium text-yellow-900 mb-2",children:"Warnings:"}),e.jsxs("ul",{className:"text-sm text-yellow-700 space-y-1",children:[n.errors.slice(0,5).map((_,b)=>e.jsxs("li",{children:["• ",_]},b)),n.errors.length>5&&e.jsxs("li",{children:["• ... and ",n.errors.length-5," more"]})]})]})]}),c==="error"&&n&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(z,{className:"mx-auto h-12 w-12 text-red-500"}),e.jsx("p",{className:"mt-2 text-sm font-medium text-gray-900",children:"Upload Failed"})]}),e.jsx("div",{className:"bg-red-50 p-4 rounded-lg",children:e.jsx("p",{className:"text-sm text-red-700",children:n.error||"An error occurred during upload"})})]})]}),e.jsxs("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[c==="select"&&e.jsxs(e.Fragment,{children:[e.jsx("button",{type:"button",onClick:g,disabled:!a,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm disabled:bg-gray-300 disabled:cursor-not-allowed",children:"Upload File"}),e.jsx("button",{type:"button",onClick:j,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"Cancel"})]}),c==="uploading"&&e.jsx("button",{type:"button",onClick:j,className:"w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:w-auto sm:text-sm",children:"Cancel"}),(c==="success"||c==="error")&&e.jsxs(e.Fragment,{children:[e.jsx("button",{type:"button",onClick:j,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm",children:"Done"}),e.jsx("button",{type:"button",onClick:h,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"Upload Another"})]})]})]})]})}):null};ve.propTypes={isOpen:N.bool.isRequired,onClose:N.func.isRequired,onUpload:N.func.isRequired};const te=({items:i=[],onItemSelect:s,selectedItems:t=[],showCompanyColumn:a=!0,showActions:o=!0,className:c=""})=>{const[m,d]=l.useState("name"),[r,n]=l.useState("asc"),[x,w]=l.useState(""),[g,j]=l.useState("all"),h=l.useMemo(()=>{let p=i.filter(y=>{var A;const u=y.name.toLowerCase().includes(x.toLowerCase())||((A=y.sku)==null?void 0:A.toLowerCase().includes(x.toLowerCase()))||y.category.toLowerCase().includes(x.toLowerCase()),C=g==="all"||y.status===g;return u&&C});return p.sort((y,u)=>{let C=y[m],A=u[m];return typeof C=="string"&&(C=C.toLowerCase(),A=A.toLowerCase()),r==="asc"?C<A?-1:C>A?1:0:C>A?-1:C<A?1:0}),p},[i,x,g,m,r]),S=p=>{m===p?n(r==="asc"?"desc":"asc"):(d(p),n("asc"))},_=p=>{s(p?h.map(y=>y.id):[])},b=(p,y)=>{s(y?[...t,p]:t.filter(u=>u!==p))},v=({field:p,children:y})=>e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>S(p),children:e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("span",{children:y}),m===p&&(r==="asc"?e.jsx(Ke,{className:"h-4 w-4"}):e.jsx(be,{className:"h-4 w-4"}))]})}),I=p=>{const y={active:"bg-green-100 text-green-800",inactive:"bg-yellow-100 text-yellow-800",discontinued:"bg-red-100 text-red-800"};return e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${y[p]||"bg-gray-100 text-gray-800"}`,children:p})},f=p=>p.currentQuantity<=p.minStockLevel;return e.jsxs("div",{className:`bg-white shadow rounded-lg ${c}`,children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx(se,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search items...",value:x,onChange:p=>w(p.target.value),className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"})]}),e.jsxs("select",{value:g,onChange:p=>j(p.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"}),e.jsx("option",{value:"discontinued",children:"Discontinued"})]})]}),e.jsxs("div",{className:"text-sm text-gray-500",children:[h.length," of ",i.length," items"]})]})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[o&&e.jsx("th",{className:"px-6 py-3 text-left",children:e.jsx("input",{type:"checkbox",checked:t.length===h.length&&h.length>0,onChange:p=>_(p.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"})}),e.jsx(v,{field:"name",children:"Name"}),e.jsx(v,{field:"sku",children:"SKU"}),e.jsx(v,{field:"category",children:"Category"}),e.jsx(v,{field:"currentQuantity",children:"Quantity"}),e.jsx(v,{field:"unitPrice",children:"Unit Price"}),e.jsx(v,{field:"totalValue",children:"Total Value"}),a&&e.jsx(v,{field:"companyName",children:"Company"}),e.jsx(v,{field:"status",children:"Status"}),e.jsx(v,{field:"lastUpdated",children:"Last Updated"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:h.map(p=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[o&&e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("input",{type:"checkbox",checked:t.includes(p.id),onChange:y=>b(p.id,y.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:p.name}),p.description&&e.jsx("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:p.description})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:p.sku||"-"}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:p.category}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.jsxs("div",{className:`${f(p)?"text-red-600 font-semibold":""}`,children:[p.currentQuantity,f(p)&&e.jsx("span",{className:"ml-1 text-xs text-red-500",children:"(Low)"})]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Min: ",p.minStockLevel]})]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",p.unitPrice.toFixed(2)]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",p.totalValue.toFixed(2)]}),a&&e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:p.companyName}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:I(p.status)}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(p.lastUpdated).toLocaleDateString()})]},p.id))})]})}),h.length===0&&e.jsx("div",{className:"text-center py-12",children:e.jsx("div",{className:"text-gray-500",children:"No items found"})})]})};te.propTypes={items:N.arrayOf(N.object),onItemSelect:N.func,selectedItems:N.arrayOf(N.string),showCompanyColumn:N.bool,showActions:N.bool,className:N.string};const Me=({stats:i,className:s=""})=>{const t=c=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(c),a=c=>new Intl.NumberFormat("en-US").format(c),o=({title:c,value:m,icon:d,trend:r,trendValue:n,color:x="blue"})=>{const w={blue:"bg-blue-50 text-blue-600",green:"bg-green-50 text-green-600",yellow:"bg-yellow-50 text-yellow-600",red:"bg-red-50 text-red-600",purple:"bg-purple-50 text-purple-600"};return e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:c}),e.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:m}),r&&e.jsxs("div",{className:"flex items-center mt-2",children:[r==="up"?e.jsx(ge,{className:"h-4 w-4 text-green-500 mr-1"}):e.jsx(Xe,{className:"h-4 w-4 text-red-500 mr-1"}),e.jsx("span",{className:`text-sm ${r==="up"?"text-green-600":"text-red-600"}`,children:n})]})]}),e.jsx("div",{className:`p-3 rounded-full ${w[x]}`,children:d})]})})};return e.jsxs("div",{className:`space-y-6 ${s}`,children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.jsx(o,{title:"Total Items",value:a(i.totalItems),icon:e.jsx(O,{className:"h-6 w-6"}),color:"blue"}),e.jsx(o,{title:"Total Value",value:t(i.totalValue),icon:e.jsx(Q,{className:"h-6 w-6"}),color:"green"}),e.jsx(o,{title:"Low Stock Items",value:a(i.lowStockItems),icon:e.jsx(B,{className:"h-6 w-6"}),color:"red"}),e.jsx(o,{title:"Companies",value:a(i.companiesCount),icon:e.jsx(V,{className:"h-6 w-6"}),color:"purple"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Top Categories"})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:i.topCategories.map((c,m)=>{const d=c.value/i.totalValue*100;return e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:`w-3 h-3 rounded-full ${m===0?"bg-blue-500":m===1?"bg-green-500":m===2?"bg-yellow-500":m===3?"bg-purple-500":"bg-gray-500"}`})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:c.category}),e.jsxs("p",{className:"text-xs text-gray-500",children:[c.count," items"]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:t(c.value)}),e.jsxs("p",{className:"text-xs text-gray-500",children:[d.toFixed(1),"%"]})]})]},c.category)})})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Average Item Value"}),e.jsx("p",{className:"text-xl font-semibold text-gray-900",children:t(i.totalValue/i.totalItems||0)})]}),e.jsx(Q,{className:"h-8 w-8 text-gray-400"})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Low Stock Rate"}),e.jsxs("p",{className:"text-xl font-semibold text-gray-900",children:[(i.lowStockItems/i.totalItems*100||0).toFixed(1),"%"]})]}),e.jsx(B,{className:"h-8 w-8 text-gray-400"})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Recent Transactions"}),e.jsx("p",{className:"text-xl font-semibold text-gray-900",children:a(i.recentTransactions)})]}),e.jsx(ge,{className:"h-8 w-8 text-gray-400"})]})})]})]})};Me.propTypes={stats:N.shape({totalItems:N.number.isRequired,totalValue:N.number.isRequired,lowStockItems:N.number.isRequired,companiesCount:N.number.isRequired,recentTransactions:N.number.isRequired,topCategories:N.arrayOf(N.shape({category:N.string.isRequired,count:N.number.isRequired,value:N.number.isRequired})).isRequired}).isRequired,className:N.string};const Rs=()=>{const{user:i}=l.useContext(D),[s,t]=l.useState([]),[a,o]=l.useState("all"),[c,m]=l.useState([]),[d,r]=l.useState(!1),[n,x]=l.useState([]),[w,g]=l.useState(!0),[j,h]=l.useState(null);l.useEffect(()=>{i&&(async()=>{try{g(!0);const b=await $.getInventoryItems();t(b);const v={totalItems:b.length,totalValue:b.reduce((I,f)=>I+(f.total_value||0),0),lowStockItems:b.filter(I=>I.current_quantity<=I.min_stock_level).length,categories:[...new Set(b.map(I=>I.category))].length};h(v)}catch(b){console.error("Failed to load inventory data:",b),t([]),h(null)}finally{g(!1)}})()},[i]),l.useEffect(()=>{x(a==="all"?s:s.filter(_=>_.companyId===a))},[s,a]);const S=async _=>new Promise(b=>{setTimeout(()=>{b({success:!0,processedItems:15,createdItems:8,updatedItems:7,errors:['Row 3: Missing SKU for item "Test Item"']})},2e3)});return w?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Inventory Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage inventory across all companies"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})})]}):e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Inventory Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage inventory across all companies"})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsxs("button",{onClick:()=>r(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(le,{className:"h-4 w-4 mr-2"}),"Upload Inventory"]}),e.jsxs("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[e.jsx(Ie,{className:"h-4 w-4 mr-2"}),"Export"]})]})]})}),j&&e.jsx("div",{className:"mb-8",children:e.jsx(Me,{stats:j})}),e.jsxs("div",{className:"mb-6 flex items-center space-x-4",children:[e.jsx(ce,{selectedCompany:a,onCompanyChange:o,className:"w-64"}),e.jsxs("button",{className:"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[e.jsx(ke,{className:"h-4 w-4 mr-2"}),"More Filters"]})]}),e.jsx(te,{items:n,selectedItems:c,onItemSelect:m,showCompanyColumn:a==="all"}),e.jsx(ve,{isOpen:d,onClose:()=>r(!1),onUpload:S})]})},Es=()=>{const{userRole:i}=l.useContext(D),[s,t]=l.useState([{id:"1",name:"Bob Partner",email:"<EMAIL>",companies:["Acme Corp","TechStart Inc"],status:"active"},{id:"2",name:"Alice Partner",email:"<EMAIL>",companies:["Global Ventures"],status:"active"}]),[a,o]=l.useState(!1),c=async m=>{try{const d={...m,role:"partner"},r=await ws.createUser(d);t(n=>[...n,{id:r.id,name:r.name,email:r.email,companies:[],status:r.status||"active"}]),o(!1)}catch(d){throw console.error("Failed to add partner:",d),d}};return e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Partner Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage partners and their company assignments"})]}),e.jsxs("button",{onClick:()=>o(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(ie,{className:"h-4 w-4 mr-2"}),"Add Partner"]})]})}),e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(se,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search partners...",className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary w-64"})]})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Partner"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Assigned Companies"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(m=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:m.name}),e.jsx("div",{className:"text-sm text-gray-500",children:m.email})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:m.companies.join(", ")})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:m.status})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[e.jsx("button",{className:"text-primary hover:text-primary-dark mr-3",children:"Edit"}),e.jsx("button",{className:"text-red-600 hover:text-red-900",children:"Remove"})]})]},m.id))})]})})]}),e.jsx(Te,{isOpen:a,onClose:()=>o(!1),onSubmit:c,userRole:i})]})},Ds=()=>{const{user:i,userRole:s}=l.useContext(D),[t,a]=l.useState([]),[o,c]=l.useState(!0),[m,d]=l.useState(!1);l.useEffect(()=>{i&&(async()=>{try{c(!0);const x=await W.getCompanies();a(x)}catch(x){console.error("Failed to load companies:",x),a([])}finally{c(!1)}})()},[i]);const r=async n=>{try{const x={...n,managerId:s==="manager"?i.id:n.managerId},w=await W.createCompany(x);a(g=>[...g,w]),d(!1)}catch(x){throw console.error("Failed to add company:",x),x}};return o?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Company Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage companies and their details"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})})]}):e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Company Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage companies and their details"})]}),e.jsxs("button",{onClick:()=>d(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(ie,{className:"h-4 w-4 mr-2"}),"Add Company"]})]})}),e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(se,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search companies...",className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary w-64"})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6",children:t.map(n=>e.jsxs("div",{className:"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:n.name}),e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:"Active"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsxs("p",{children:["ID: ",n.id]}),e.jsx("p",{children:"Inventory Items: 3"}),e.jsx("p",{children:"Assigned Partners: 1"})]}),e.jsxs("div",{className:"mt-4 flex space-x-2",children:[e.jsx("button",{className:"text-primary hover:text-primary-dark text-sm",children:"View Details"}),e.jsx("button",{className:"text-gray-600 hover:text-gray-900 text-sm",children:"Edit"})]})]},n.id))})]}),e.jsx($e,{isOpen:m,onClose:()=>d(!1),onSubmit:r,userRole:s,currentUserId:i==null?void 0:i.id})]})},Fs=()=>{const{user:i}=l.useContext(D),[s,t]=l.useState([]),[a,o]=l.useState("all"),[c,m]=l.useState([]),[d,r]=l.useState(!1),[n,x]=l.useState([]),[w,g]=l.useState(!0);l.useEffect(()=>{i&&(async()=>{try{g(!0);const S=await $.getInventoryItems();t(S)}catch(S){console.error("Failed to load inventory items:",S),t([])}finally{g(!1)}})()},[i]),l.useEffect(()=>{x(a==="all"?s:s.filter(h=>h.companyId===a))},[s,a]);const j=async h=>new Promise(S=>{setTimeout(()=>{S({success:!0,processedItems:10,createdItems:5,updatedItems:5,errors:[]})},2e3)});return e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Inventory Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage inventory for your companies"})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsxs("button",{onClick:()=>r(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(le,{className:"h-4 w-4 mr-2"}),"Upload Inventory"]}),e.jsxs("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[e.jsx(Ie,{className:"h-4 w-4 mr-2"}),"Export"]})]})]})}),e.jsx("div",{className:"mb-6",children:e.jsx(ce,{selectedCompany:a,onCompanyChange:o,className:"w-64"})}),e.jsx(te,{items:n,selectedItems:c,onItemSelect:m,showCompanyColumn:a==="all"}),e.jsx(ve,{isOpen:d,onClose:()=>r(!1),onUpload:j})]})},Us=()=>{const i=K(),[s]=l.useState([{id:"1",name:"Acme Corp",inventoryItems:3,totalValue:17899.67,lastUpdate:"2024-01-15"},{id:"2",name:"TechStart Inc",inventoryItems:2,totalValue:2949.83,lastUpdate:"2024-01-13"}]);return e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Your Companies"}),e.jsx("p",{className:"text-gray-600",children:"Companies assigned to your portfolio"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:s.map(t=>e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:t.name}),e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:"Active"})]}),e.jsxs("div",{className:"space-y-3 mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Inventory Items"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(O,{className:"h-4 w-4 text-gray-400 mr-1"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:t.inventoryItems})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Total Value"}),e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["$",t.totalValue.toLocaleString()]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Last Updated"}),e.jsx("span",{className:"text-sm text-gray-500",children:new Date(t.lastUpdate).toLocaleDateString()})]})]}),e.jsxs("button",{onClick:()=>i(`/partner/company/${t.id}`),className:"w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(X,{className:"h-4 w-4 mr-2"}),"View Details"]})]},t.id))})]})},Ts=()=>{const{id:i}=ze(),s=K(),{user:t}=l.useContext(D),[a,o]=l.useState(null),[c,m]=l.useState([]),[d,r]=l.useState(!0);if(l.useEffect(()=>{t&&i&&(async()=>{try{r(!0);const g=await W.getCompany(i);o(g);const j=await $.getInventoryItems({company_id:i});m(j)}catch(g){console.error("Failed to load company data:",g),o(null),m([])}finally{r(!1)}})()},[i,t]),d)return e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})})});if(!a)return e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-gray-500",children:"Company not found"})})});const n=c.reduce((w,g)=>w+g.totalValue,0),x=c.filter(w=>w.currentQuantity<=w.minStockLevel).length;return e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("button",{onClick:()=>s("/partner/companies"),className:"inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mb-4",children:[e.jsx(Se,{className:"h-4 w-4 mr-1"}),"Back to Companies"]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:a.name}),e.jsx("p",{className:"text-gray-600",children:"Company inventory and details"})]}),e.jsx("span",{className:"inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800",children:"Active"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Items"}),e.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:c.length})]}),e.jsx("div",{className:"p-3 rounded-full bg-blue-50 text-blue-600",children:e.jsx(O,{className:"h-6 w-6"})})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Value"}),e.jsxs("p",{className:"text-2xl font-semibold text-gray-900",children:["$",n.toLocaleString()]})]}),e.jsx("div",{className:"p-3 rounded-full bg-green-50 text-green-600",children:e.jsx(Q,{className:"h-6 w-6"})})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Low Stock Items"}),e.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:x})]}),e.jsx("div",{className:"p-3 rounded-full bg-red-50 text-red-600",children:e.jsx(ge,{className:"h-6 w-6"})})]})})]}),e.jsx(te,{items:c,showCompanyColumn:!1,showActions:!1})]})},Ps=()=>{const{user:i}=l.useContext(D),[s,t]=l.useState([]),[a,o]=l.useState("all"),[c,m]=l.useState([]),[d,r]=l.useState([]),[n,x]=l.useState(!0);return l.useEffect(()=>{i&&(async()=>{var g;try{x(!0);const j=await $.getInventoryItems();t(j),((g=i==null?void 0:i.assigned_companies)==null?void 0:g.length)>0&&o(i.assigned_companies[0].id.toString())}catch(j){console.error("Failed to load inventory items:",j),t([])}finally{x(!1)}})()},[i]),l.useEffect(()=>{r(a==="all"?s:s.filter(w=>w.companyId===a))},[s,a]),e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Inventory Management"}),e.jsx("p",{className:"text-gray-600",children:"View inventory for your assigned companies"})]}),e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg px-4 py-2",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(je,{className:"h-4 w-4 text-blue-600 mr-2"}),e.jsx("span",{className:"text-sm font-medium text-blue-800",children:"Read-Only Access"})]})})]})}),e.jsx("div",{className:"mb-6",children:e.jsx(ce,{selectedCompany:a,onCompanyChange:o,className:"w-64",showAllOption:!0})}),e.jsx(te,{items:d,selectedItems:c,onItemSelect:m,showCompanyColumn:a==="all",showActions:!1})]})};class $s{async getNotifications(s={}){try{const t=await k.getNotifications(s);if(t.success)return t.data||[];throw new Error(t.message||"Failed to fetch notifications")}catch(t){throw console.error("Error fetching notifications:",t),t}}async markAsRead(s){try{const t=await k.markNotificationAsRead(s);if(t.success)return!0;throw new Error(t.message||"Failed to mark notification as read")}catch(t){throw console.error("Error marking notification as read:",t),t}}async markAllAsRead(){try{const s=await k.markAllNotificationsAsRead();if(s.success)return s.data;throw new Error(s.message||"Failed to mark all notifications as read")}catch(s){throw console.error("Error marking all notifications as read:",s),s}}async deleteNotification(s){try{const t=await k.deleteNotification(s);if(t.success)return!0;throw new Error(t.message||"Failed to delete notification")}catch(t){throw console.error("Error deleting notification:",t),t}}async getUnreadCount(){try{return(await this.getNotifications({unread_only:!0})).unread_count||0}catch(s){return console.error("Error fetching unread count:",s),0}}}const Ls=new $s,E=({children:i})=>{const[s,t]=l.useState(!1),[a,o]=l.useState(0),{user:c,userRole:m,userEmail:d,logout:r}=l.useContext(D),n=K(),x=Be();l.useEffect(()=>{(async()=>{try{if(c){const S=await Ls.getNotifications({is_read:!1});o(S.length)}}catch(S){console.error("Failed to load notification count:",S),o(0)}})()},[c]);const w=()=>{r(),n("/login")},j=(()=>{switch(m){case"superadmin":return[{name:"Dashboard",icon:e.jsx(de,{size:20}),path:"/superadmin"},{name:"User Management",icon:e.jsx(ue,{size:20}),path:"/superadmin/users"},{name:"Company Oversight",icon:e.jsx(P,{size:20}),path:"/superadmin/companies"},{name:"Inventory",icon:e.jsx(O,{size:20}),path:"/superadmin/inventory"},{name:"Notifications",icon:e.jsx(q,{size:20}),path:"/superadmin/notifications",badge:a},{name:"Settings",icon:e.jsx(me,{size:20}),path:"/superadmin/settings"}];case"manager":return[{name:"Dashboard",icon:e.jsx(de,{size:20}),path:"/manager"},{name:"Partners",icon:e.jsx(ue,{size:20}),path:"/manager/partners"},{name:"Companies",icon:e.jsx(P,{size:20}),path:"/manager/companies"},{name:"Inventory",icon:e.jsx(O,{size:20}),path:"/manager/inventory"},{name:"Notifications",icon:e.jsx(q,{size:20}),path:"/manager/notifications",badge:a},{name:"Settings",icon:e.jsx(me,{size:20}),path:"/manager/settings"}];case"partner":return[{name:"Portfolio",icon:e.jsx(de,{size:20}),path:"/partner"},{name:"Companies",icon:e.jsx(P,{size:20}),path:"/partner/companies"},{name:"Inventory",icon:e.jsx(O,{size:20}),path:"/partner/inventory"},{name:"Notifications",icon:e.jsx(q,{size:20}),path:"/partner/notifications",badge:a},{name:"Settings",icon:e.jsx(me,{size:20}),path:"/partner/settings"}];default:return[]}})();return e.jsxs("div",{className:"flex h-screen bg-neutral-light overflow-hidden",children:[s&&e.jsx("div",{className:"fixed inset-0 z-20 bg-black bg-opacity-50 transition-opacity md:hidden",onClick:()=>t(!1)}),e.jsx("div",{className:`fixed inset-y-0 left-0 z-30 w-64 transform bg-secondary transition duration-300 ease-in-out md:relative md:translate-x-0 ${s?"translate-x-0":"-translate-x-full"}`,children:e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between px-4 py-5 border-b border-secondary-dark",children:[e.jsx("div",{className:"flex items-center",children:e.jsx("span",{className:"text-xl font-bold text-white",children:"Enterprise Portal"})}),e.jsx("button",{onClick:()=>t(!1),className:"text-white md:hidden",children:e.jsx(U,{size:24})})]}),e.jsx("div",{className:"px-4 py-4 border-b border-secondary-dark",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"h-10 w-10 rounded-full bg-primary flex items-center justify-center text-white font-bold",children:m==null?void 0:m.charAt(0).toUpperCase()}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm font-medium text-white",children:m==="superadmin"?"Super Admin":m==="manager"?"Manager":"Partner"}),e.jsx("p",{className:"text-xs text-white text-opacity-70",children:d||"No email"})]}),e.jsx(be,{size:16,className:"ml-auto text-white text-opacity-70"})]})}),e.jsx("nav",{className:"flex-1 overflow-y-auto px-2 py-4",children:e.jsx("ul",{className:"space-y-1",children:j.map(h=>e.jsx("li",{children:e.jsxs("a",{href:h.path,onClick:S=>{S.preventDefault(),n(h.path),t(!1)},className:`flex items-center px-4 py-3 text-sm rounded-lg ${x.pathname===h.path?"bg-primary text-white":"text-white text-opacity-80 hover:bg-secondary-dark"}`,children:[e.jsx("span",{className:"mr-3",children:h.icon}),h.name,h.badge&&e.jsx("span",{className:"ml-auto bg-red-500 text-white text-xs font-bold px-2 py-0.5 rounded-full",children:h.badge})]})},h.name))})}),e.jsx("div",{className:"border-t border-secondary-dark p-4",children:e.jsxs("button",{onClick:w,className:"flex w-full items-center px-4 py-3 text-sm text-white text-opacity-80 rounded-lg hover:bg-secondary-dark",children:[e.jsx(es,{size:20,className:"mr-3"}),"Sign Out"]})})]})}),e.jsxs("div",{className:"flex flex-1 flex-col overflow-hidden",children:[e.jsx("header",{className:"bg-white shadow-sm z-10",children:e.jsxs("div",{className:"px-4 py-4 flex items-center justify-between",children:[e.jsx("button",{onClick:()=>t(!0),className:"text-neutral-dark md:hidden",children:e.jsx(ss,{size:24})}),e.jsx("div",{className:"md:hidden font-montserrat font-bold text-lg",children:"Enterprise Portal"}),e.jsx("div",{className:"flex items-center space-x-4",children:e.jsxs("button",{className:"relative p-1 rounded-full text-gray-500 hover:bg-gray-100 hover:text-gray-600",onClick:()=>n(`/${m}/notifications`),children:[e.jsx(q,{size:20}),a>0&&e.jsx("span",{className:"absolute top-0 right-0 block h-4 w-4 rounded-full bg-red-500 text-white text-xs font-bold flex items-center justify-center",children:a})]})})]})}),e.jsx("main",{className:"flex-1 overflow-y-auto bg-neutral-light",children:i})]})]})};E.propTypes={children:N.node.isRequired};const xe=()=>{const[i,s]=l.useState([{id:"1",type:"warning",title:"Low Stock Alert",message:"Conference Tables at TechStart Inc is running low (2 remaining, minimum 1)",timestamp:"2024-01-15T10:30:00Z",read:!1},{id:"2",type:"info",title:"Inventory Upload Completed",message:"Successfully processed 15 items from purchases_january_2024.xlsx",timestamp:"2024-01-15T09:15:00Z",read:!1},{id:"3",type:"success",title:"New Partner Added",message:"Alice Partner has been successfully added to the system",timestamp:"2024-01-14T16:45:00Z",read:!0}]),t=d=>{s(r=>r.map(n=>n.id===d?{...n,read:!0}:n))},a=()=>{s(d=>d.map(r=>({...r,read:!0})))},o=d=>{s(r=>r.filter(n=>n.id!==d))},c=d=>{switch(d){case"warning":return e.jsx(B,{className:"h-5 w-5 text-yellow-500"});case"info":return e.jsx(je,{className:"h-5 w-5 text-blue-500"});case"success":return e.jsx(ee,{className:"h-5 w-5 text-green-500"});default:return e.jsx(q,{className:"h-5 w-5 text-gray-500"})}},m=i.filter(d=>!d.read).length;return e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"}),e.jsx("p",{className:"text-gray-600",children:m>0?`${m} unread notifications`:"All notifications read"})]}),m>0&&e.jsx("button",{onClick:a,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:"Mark all as read"})]})}),e.jsx("div",{className:"bg-white shadow rounded-lg",children:i.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(q,{className:"mx-auto h-12 w-12 text-gray-400"}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No notifications"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"You're all caught up!"})]}):e.jsx("div",{className:"divide-y divide-gray-200",children:i.map(d=>e.jsx("div",{className:`p-6 hover:bg-gray-50 ${d.read?"":"bg-blue-50"}`,children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:c(d.type)}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("p",{className:`text-sm font-medium ${d.read?"text-gray-700":"text-gray-900"}`,children:d.title}),!d.read&&e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})]}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:d.message}),e.jsx("p",{className:"mt-2 text-xs text-gray-500",children:new Date(d.timestamp).toLocaleString()})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[!d.read&&e.jsx("button",{onClick:()=>t(d.id),className:"text-blue-600 hover:text-blue-800 text-sm",children:"Mark as read"}),e.jsx("button",{onClick:()=>o(d.id),className:"text-gray-400 hover:text-gray-600",children:e.jsx(U,{className:"h-4 w-4"})})]})]})},d.id))})})]})},pe=()=>{const{userEmail:i,userRole:s}=l.useContext(D),[t,a]=l.useState({emailNotifications:!0,pushNotifications:!1,lowStockAlerts:!0,weeklyReports:!0,theme:"light",language:"en"}),o=(d,r)=>{a(n=>({...n,[d]:r}))},c=({title:d,icon:r,children:n})=>e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-2 bg-gray-100 rounded-lg mr-3",children:r}),e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:d})]})}),e.jsx("div",{className:"p-6",children:n})]}),m=({label:d,description:r,checked:n,onChange:x})=>e.jsxs("div",{className:"flex items-center justify-between py-3",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:d}),r&&e.jsx("p",{className:"text-sm text-gray-500",children:r})]}),e.jsx("button",{onClick:()=>x(!n),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${n?"bg-primary":"bg-gray-200"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${n?"translate-x-6":"translate-x-1"}`})})]});return e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Settings"}),e.jsx("p",{className:"text-gray-600",children:"Manage your account preferences and system settings"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(c,{title:"Profile",icon:e.jsx(re,{className:"h-5 w-5 text-gray-600"}),children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),e.jsx("input",{type:"email",value:i,disabled:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Role"}),e.jsx("input",{type:"text",value:s,disabled:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm"})]})]})}),e.jsx(c,{title:"Notifications",icon:e.jsx(q,{className:"h-5 w-5 text-gray-600"}),children:e.jsxs("div",{className:"space-y-1",children:[e.jsx(m,{label:"Email Notifications",description:"Receive notifications via email",checked:t.emailNotifications,onChange:d=>o("emailNotifications",d)}),e.jsx(m,{label:"Push Notifications",description:"Receive browser push notifications",checked:t.pushNotifications,onChange:d=>o("pushNotifications",d)}),e.jsx(m,{label:"Low Stock Alerts",description:"Get notified when inventory is running low",checked:t.lowStockAlerts,onChange:d=>o("lowStockAlerts",d)}),e.jsx(m,{label:"Weekly Reports",description:"Receive weekly inventory summary reports",checked:t.weeklyReports,onChange:d=>o("weeklyReports",d)})]})}),e.jsx(c,{title:"Security",icon:e.jsx(ts,{className:"h-5 w-5 text-gray-600"}),children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:"Change Password"}),e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-600",children:["Last login: ",new Date().toLocaleDateString()," at ",new Date().toLocaleTimeString()]})})]})}),(s==="superadmin"||s==="manager")&&e.jsx(c,{title:"System",icon:e.jsx(rs,{className:"h-5 w-5 text-gray-600"}),children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Default File Upload Size Limit"}),e.jsxs("select",{className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",children:[e.jsx("option",{children:"10 MB"}),e.jsx("option",{children:"25 MB"}),e.jsx("option",{children:"50 MB"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Data Retention Period"}),e.jsxs("select",{className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",children:[e.jsx("option",{children:"1 Year"}),e.jsx("option",{children:"2 Years"}),e.jsx("option",{children:"5 Years"})]})]})]})})]}),e.jsx("div",{className:"mt-8 flex justify-end",children:e.jsx("button",{className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:"Save Changes"})})]})},D=l.createContext({isAuthenticated:!1,user:null,userRole:null,userEmail:"",login:()=>{},logout:()=>{}});function Ms(){const[i,s]=l.useState(!1),[t,a]=l.useState(null),[o,c]=l.useState(null),[m,d]=l.useState(""),[r,n]=l.useState(!0);l.useEffect(()=>{(async()=>{try{if(M.isAuthenticated()){await M.initializeFromToken();const h=M.getCurrentUser();h&&(s(!0),a(h),c(h.role),d(h.email))}}catch(h){console.error("Auth initialization failed:",h),M.logout()}finally{n(!1)}})()},[]);const x=j=>(s(!0),a(j),c(j.role),d(j.email),!0),w=async()=>{try{await M.logout()}catch(j){console.error("Logout error:",j)}finally{s(!1),a(null),c(null),d("")}};if(r)return e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"}),e.jsx("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})});const g=({children:j,requiredRole:h})=>i?h&&o!==h?o==="superadmin"?e.jsx(T,{to:"/superadmin",replace:!0}):o==="manager"?e.jsx(T,{to:"/manager",replace:!0}):e.jsx(T,{to:"/partner",replace:!0}):j:e.jsx(T,{to:"/login",replace:!0});return g.propTypes={children:N.node.isRequired,requiredRole:N.oneOf(["superadmin","manager","partner"])},e.jsx(D.Provider,{value:{isAuthenticated:i,user:t,userRole:o,userEmail:m,login:x,logout:w},children:e.jsx(Ve,{children:e.jsxs(We,{children:[e.jsx(R,{path:"/",element:i?o==="superadmin"?e.jsx(T,{to:"/superadmin",replace:!0}):o==="manager"?e.jsx(T,{to:"/manager",replace:!0}):e.jsx(T,{to:"/partner",replace:!0}):e.jsx(T,{to:"/login",replace:!0})}),e.jsx(R,{path:"/login",element:e.jsx(js,{})}),e.jsx(R,{path:"/reset-password",element:e.jsx(bs,{})}),e.jsx(R,{path:"/superadmin",element:e.jsx(g,{requiredRole:"superadmin",children:e.jsx(E,{children:e.jsx(Cs,{})})})}),e.jsx(R,{path:"/superadmin/users",element:e.jsx(g,{requiredRole:"superadmin",children:e.jsx(E,{children:e.jsx(Is,{})})})}),e.jsx(R,{path:"/superadmin/companies",element:e.jsx(g,{requiredRole:"superadmin",children:e.jsx(E,{children:e.jsx(As,{})})})}),e.jsx(R,{path:"/superadmin/inventory",element:e.jsx(g,{requiredRole:"superadmin",children:e.jsx(E,{children:e.jsx(Rs,{})})})}),e.jsx(R,{path:"/superadmin/notifications",element:e.jsx(g,{requiredRole:"superadmin",children:e.jsx(E,{children:e.jsx(xe,{})})})}),e.jsx(R,{path:"/superadmin/settings",element:e.jsx(g,{requiredRole:"superadmin",children:e.jsx(E,{children:e.jsx(pe,{})})})}),e.jsx(R,{path:"/manager",element:e.jsx(g,{requiredRole:"manager",children:e.jsx(E,{children:e.jsx(ks,{})})})}),e.jsx(R,{path:"/manager/partners",element:e.jsx(g,{requiredRole:"manager",children:e.jsx(E,{children:e.jsx(Es,{})})})}),e.jsx(R,{path:"/manager/companies",element:e.jsx(g,{requiredRole:"manager",children:e.jsx(E,{children:e.jsx(Ds,{})})})}),e.jsx(R,{path:"/manager/inventory",element:e.jsx(g,{requiredRole:"manager",children:e.jsx(E,{children:e.jsx(Fs,{})})})}),e.jsx(R,{path:"/manager/notifications",element:e.jsx(g,{requiredRole:"manager",children:e.jsx(E,{children:e.jsx(xe,{})})})}),e.jsx(R,{path:"/manager/settings",element:e.jsx(g,{requiredRole:"manager",children:e.jsx(E,{children:e.jsx(pe,{})})})}),e.jsx(R,{path:"/partner",element:e.jsx(g,{requiredRole:"partner",children:e.jsx(E,{children:e.jsx(_s,{})})})}),e.jsx(R,{path:"/partner/companies",element:e.jsx(g,{requiredRole:"partner",children:e.jsx(E,{children:e.jsx(Us,{})})})}),e.jsx(R,{path:"/partner/company/:id",element:e.jsx(g,{requiredRole:"partner",children:e.jsx(E,{children:e.jsx(Ts,{})})})}),e.jsx(R,{path:"/partner/inventory",element:e.jsx(g,{requiredRole:"partner",children:e.jsx(E,{children:e.jsx(Ps,{})})})}),e.jsx(R,{path:"/partner/notifications",element:e.jsx(g,{requiredRole:"partner",children:e.jsx(E,{children:e.jsx(xe,{})})})}),e.jsx(R,{path:"/partner/settings",element:e.jsx(g,{requiredRole:"partner",children:e.jsx(E,{children:e.jsx(pe,{})})})}),e.jsx(R,{path:"*",element:e.jsx(T,{to:"/login",replace:!0})})]})})})}const qs=Ee(document.getElementById("root"));qs.render(e.jsx(Ms,{}));
