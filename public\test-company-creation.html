<!DOCTYPE html>
<html>
<head>
    <title>Test Company Creation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-result { margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 4px solid #ccc; }
        .success { background: #d4edda; border-left-color: #28a745; }
        .error { background: #f8d7da; border-left-color: #dc3545; }
        .info { background: #d1ecf1; border-left-color: #17a2b8; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; max-height: 300px; }
        .form-group { margin: 10px 0; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Company Creation</h1>
        <p>Direct test of company creation API from frontend domain</p>
        
        <div class="form-group">
            <button onclick="testLogin()">1. Test Login</button>
            <button onclick="testCompanyCreation()">2. Test Company Creation</button>
            <button onclick="testFullWorkflow()">3. Test Full Workflow</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>
        
        <div id="results"></div>
        
        <div style="margin-top: 30px;">
            <h3>Manual Company Creation Test</h3>
            <form id="companyForm">
                <div class="form-group">
                    <label>Company Name:</label>
                    <input type="text" id="companyName" value="Test Company Manual" required>
                </div>
                <div class="form-group">
                    <label>Company Director:</label>
                    <input type="text" id="companyDirector" value="John Smith" required>
                </div>
                <div class="form-group">
                    <label>Registration Territory:</label>
                    <input type="text" id="registrationTerritory" value="United States" required>
                </div>
                <div class="form-group">
                    <label>EIN Number:</label>
                    <input type="text" id="einNumber" value="12-3456789" required>
                </div>
                <div class="form-group">
                    <label>Marketplace:</label>
                    <select id="marketplace">
                        <option value="Amazon">Amazon</option>
                        <option value="eBay">eBay</option>
                        <option value="Shopify">Shopify</option>
                        <option value="Others">Others</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Industry:</label>
                    <input type="text" id="industry" value="Technology">
                </div>
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" id="email" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label>Phone:</label>
                    <input type="text" id="phone" value="+1234567890">
                </div>
                <div class="form-group">
                    <label>Description:</label>
                    <textarea id="description">Manual test company creation</textarea>
                </div>
                <button type="button" onclick="createCompanyManual()">Create Company</button>
            </form>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost/Investment-System-eSkillVisor/backend';
        let authToken = null;
        
        function addResult(title, status, message, details = null) {
            const resultsDiv = document.getElementById('results');
            const statusClass = status === 'success' ? 'success' : status === 'error' ? 'error' : 'info';
            const icon = status === 'success' ? '✅' : status === 'error' ? '❌' : 'ℹ️';
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${statusClass}`;
            resultDiv.innerHTML = `
                <h4>${icon} ${title}</h4>
                <p>${message}</p>
                ${details ? `<pre>${JSON.stringify(details, null, 2)}</pre>` : ''}
            `;
            
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testLogin() {
            clearResults();
            addResult('Login Test', 'info', 'Testing authentication...');
            
            try {
                const response = await fetch(`${API_BASE}/login.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    authToken = data.data.access_token;
                    addResult('Login Success', 'success', 'Authentication successful', {
                        user: data.data.user,
                        tokenLength: authToken.length
                    });
                } else {
                    addResult('Login Failed', 'error', data.message, data);
                }
                
            } catch (error) {
                addResult('Login Error', 'error', `Error: ${error.message}`);
            }
        }
        
        async function testCompanyCreation() {
            if (!authToken) {
                addResult('Authentication Required', 'error', 'Please run login test first');
                return;
            }
            
            addResult('Company Creation Test', 'info', 'Testing company creation...');
            
            const companyData = {
                name: `Test Company ${Date.now()}`,
                companyDirector: 'Test Director',
                registrationTerritory: 'United States',
                einNumber: '12-3456789',
                marketplace: 'Amazon',
                description: 'Test company creation',
                industry: 'Technology',
                email: `test${Date.now()}@test.com`,
                phone: '+1234567890',
                partner_id: 1,
                created_by: 1
            };
            
            try {
                const response = await fetch(`${API_BASE}/companies.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(companyData)
                });
                
                const responseText = await response.text();
                console.log('Company creation response:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    addResult('Parse Error', 'error', 'Invalid JSON response', {
                        responseText: responseText.substring(0, 500),
                        parseError: parseError.message
                    });
                    return;
                }
                
                if (data.success) {
                    addResult('Company Creation Success', 'success', 'Company created successfully', {
                        company: data.data,
                        message: data.message
                    });
                } else {
                    addResult('Company Creation Failed', 'error', data.message, data);
                }
                
            } catch (error) {
                addResult('Company Creation Error', 'error', `Error: ${error.message}`);
            }
        }
        
        async function createCompanyManual() {
            if (!authToken) {
                await testLogin();
                if (!authToken) return;
            }
            
            const companyData = {
                name: document.getElementById('companyName').value,
                companyDirector: document.getElementById('companyDirector').value,
                registrationTerritory: document.getElementById('registrationTerritory').value,
                einNumber: document.getElementById('einNumber').value,
                marketplace: document.getElementById('marketplace').value,
                description: document.getElementById('description').value,
                industry: document.getElementById('industry').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                partner_id: 1,
                created_by: 1
            };
            
            addResult('Manual Company Creation', 'info', 'Creating company with form data...');
            
            try {
                const response = await fetch(`${API_BASE}/companies.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(companyData)
                });
                
                const responseText = await response.text();
                console.log('Manual creation response:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    addResult('Manual Creation Parse Error', 'error', 'Invalid JSON response', {
                        responseText: responseText.substring(0, 500)
                    });
                    return;
                }
                
                if (data.success) {
                    addResult('Manual Creation Success', 'success', 'Company created successfully!', {
                        company: data.data,
                        submittedData: companyData
                    });
                } else {
                    addResult('Manual Creation Failed', 'error', data.message, data);
                }
                
            } catch (error) {
                addResult('Manual Creation Error', 'error', `Error: ${error.message}`);
            }
        }
        
        async function testFullWorkflow() {
            clearResults();
            addResult('Full Workflow Test', 'info', 'Testing complete workflow...');
            
            await testLogin();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            if (authToken) {
                await testCompanyCreation();
            }
        }
        
        // Auto-run login test on page load
        window.onload = function() {
            setTimeout(testLogin, 1000);
        };
    </script>
</body>
</html>
