<?php
/**
 * Database Configuration for Production
 * This file handles database connections and operations
 */

// Database constants - only define if not already defined
if (!defined('DB_HOST')) {
    define('DB_HOST', 'localhost');
}
if (!defined('DB_NAME')) {
    define('DB_NAME', 'wallistry_eskillvisor_db');
}
if (!defined('DB_USER')) {
    define('DB_USER', 'wallistry_eskill');
}
if (!defined('DB_PASS')) {
    define('DB_PASS', 'EskillVisor2024!');
}
if (!defined('DB_CHARSET')) {
    define('DB_CHARSET', 'utf8mb4');
}

/**
 * Database Connection Class
 */
class Database {
    private static $instance = null;
    private $connection;
    private $host;
    private $dbname;
    private $username;
    private $password;
    private $charset;

    private function __construct() {
        $this->host = DB_HOST;
        $this->dbname = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->charset = DB_CHARSET;
        
        $this->connect();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->dbname};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset}"
            ];
            
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
        } catch (PDOException $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }

    public function getConnection() {
        return $this->connection;
    }

    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            throw new Exception("Query failed: " . $e->getMessage());
        }
    }

    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    public function execute($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    public function lastInsertId() {
        return $this->connection->lastInsertId();
    }

    public function getLastInsertId() {
        return $this->connection->lastInsertId();
    }

    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }

    public function commit() {
        return $this->connection->commit();
    }

    public function rollback() {
        return $this->connection->rollback();
    }

    public function createDatabase() {
        try {
            // Connect without database name first
            $dsn = "mysql:host={$this->host};charset={$this->charset}";
            $tempConnection = new PDO($dsn, $this->username, $this->password);
            
            // Create database if it doesn't exist
            $sql = "CREATE DATABASE IF NOT EXISTS `{$this->dbname}` CHARACTER SET {$this->charset} COLLATE {$this->charset}_unicode_ci";
            $tempConnection->exec($sql);
            
            return true;
        } catch (PDOException $e) {
            error_log("Database creation failed: " . $e->getMessage());
            return false;
        }
    }

    public function tableExists($tableName) {
        try {
            $sql = "SHOW TABLES LIKE ?";
            $stmt = $this->query($sql, [$tableName]);
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    public function runMigrations() {
        try {
            $migrationsDir = __DIR__ . '/../migrations/';
            
            // Create migrations table if it doesn't exist
            $sql = "CREATE TABLE IF NOT EXISTS migrations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                migration VARCHAR(255) NOT NULL,
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $this->execute($sql);
            
            // Get executed migrations
            $executedMigrations = $this->fetchAll("SELECT migration FROM migrations");
            $executed = array_column($executedMigrations, 'migration');
            
            // Get all migration files
            $migrationFiles = glob($migrationsDir . '*.sql');
            sort($migrationFiles);
            
            foreach ($migrationFiles as $file) {
                $migrationName = basename($file);
                
                if (!in_array($migrationName, $executed)) {
                    $sql = file_get_contents($file);
                    
                    // Execute migration
                    $this->connection->exec($sql);
                    
                    // Record migration
                    $this->execute("INSERT INTO migrations (migration) VALUES (?)", [$migrationName]);
                    
                    error_log("Executed migration: " . $migrationName);
                }
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Migration failed: " . $e->getMessage());
            return false;
        }
    }
}
?>
