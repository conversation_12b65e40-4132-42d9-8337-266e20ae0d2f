Set objFSO = CreateObject("Scripting.FileSystemObject")
Set objShell = CreateObject("Shell.Application")

' Get current directory
strCurrentDir = objFSO.GetAbsolutePathName(".")
strZipFile = strCurrentDir & "\deployment\eskillvisor-complete-deployment-2025-07-18.zip"

' Delete existing zip if it exists
If objFSO.FileExists(strZipFile) Then
    objFSO.DeleteFile strZipFile
End If

' Create empty zip file
Set objFile = objFSO.CreateTextFile(strZipFile, True)
objFile.Write Chr(80) & Chr(75) & Chr(5) & Chr(6) & String(18, 0)
objFile.Close

' Wait for zip file to be created
WScript.Sleep 1000

' Get zip folder object
Set objZip = objShell.NameSpace(strZipFile)

' Add files to zip
Set objSource = objShell.NameSpace(strCurrentDir)

' Add specific folders and files
objZip.CopyHere objSource.ParseName("backend")
objZip.CopyHere objSource.ParseName("src")
objZip.CopyHere objSource.ParseName("index.html")
objZip.CopyHere objSource.ParseName("package.json")
objZip.CopyHere objSource.ParseName("vite.config.js")
objZip.CopyHere objSource.ParseName("tailwind.config.js")
objZip.CopyHere objSource.ParseName("postcss.config.js")

' Wait for compression to complete
WScript.Sleep 5000

WScript.Echo "ZIP file created successfully: " & strZipFile
