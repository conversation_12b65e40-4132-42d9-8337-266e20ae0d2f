{"templates": {"react-spa": {"name": "React Single Page Application", "description": "Modern React application with build process", "fileCategories": {"frontend": {"extensions": [".js", ".jsx", ".ts", ".tsx", ".css", ".scss", ".sass"], "directories": ["src", "public", "components", "pages", "hooks", "utils"], "buildOutput": "build"}, "config": {"extensions": [".json", ".env", ".env.local"], "directories": ["config"], "files": ["package.json", "package-lock.json", "yarn.lock"]}}, "buildCommands": ["npm run build", "yarn build"], "deploymentPath": "/public_html", "excludePatterns": ["node_modules/**", "src/**", ".git/**"]}, "vue-spa": {"name": "Vue Single Page Application", "description": "Vue.js application with Vite or Webpack", "fileCategories": {"frontend": {"extensions": [".vue", ".js", ".ts", ".css", ".scss", ".sass"], "directories": ["src", "public", "components", "views", "router", "store"], "buildOutput": "dist"}}, "buildCommands": ["npm run build", "yarn build"], "deploymentPath": "/public_html"}, "php-mvc": {"name": "PHP MVC Application", "description": "PHP application with MVC structure", "fileCategories": {"backend": {"extensions": [".php"], "directories": ["app", "controllers", "models", "views", "api", "includes"], "description": "PHP application logic"}, "database": {"extensions": [".sql"], "directories": ["database", "migrations", "sql"], "description": "Database scripts"}, "assets": {"extensions": [".css", ".js", ".png", ".jpg", ".jpeg", ".gif", ".svg"], "directories": ["assets", "public", "static", "css", "js", "images"]}}, "deploymentPath": "/public_html"}, "laravel": {"name": "Laravel Framework", "description": "Laravel PHP framework application", "fileCategories": {"backend": {"extensions": [".php"], "directories": ["app", "routes", "database", "resources"], "description": "Laravel application files"}, "frontend": {"extensions": [".blade.php", ".css", ".js", ".vue"], "directories": ["resources/views", "resources/js", "resources/css"], "description": "Frontend templates and assets"}, "config": {"extensions": [".php", ".env"], "directories": ["config"], "files": [".env", "composer.json", "composer.lock"]}}, "excludePatterns": ["vendor/**", "storage/logs/**", "bootstrap/cache/**"], "deploymentPath": "/public_html"}, "wordpress": {"name": "WordPress Site", "description": "WordPress content management system", "fileCategories": {"backend": {"extensions": [".php"], "directories": ["wp-content/themes", "wp-content/plugins", "wp-content/mu-plugins"], "description": "WordPress themes and plugins"}, "assets": {"extensions": [".css", ".js", ".png", ".jpg", ".jpeg", ".gif", ".svg"], "directories": ["wp-content/themes/*/assets", "wp-content/uploads"], "description": "Theme assets and media"}, "config": {"files": ["wp-config.php", ".htaccess"], "description": "WordPress configuration"}}, "excludePatterns": ["wp-content/uploads/**", "wp-content/cache/**"], "deploymentPath": "/public_html"}, "node-api": {"name": "Node.js API", "description": "Node.js REST API or GraphQL server", "fileCategories": {"backend": {"extensions": [".js", ".ts"], "directories": ["src", "routes", "controllers", "models", "middleware", "utils"], "description": "Node.js server code"}, "config": {"extensions": [".json", ".env", ".yaml", ".yml"], "directories": ["config"], "files": ["package.json", "package-lock.json"]}}, "deploymentPath": "/api"}, "static-site": {"name": "Static Website", "description": "Static HTML/CSS/JS website", "fileCategories": {"frontend": {"extensions": [".html", ".css", ".js"], "directories": ["css", "js", "assets", "images"], "description": "Static website files"}, "assets": {"extensions": [".png", ".jpg", ".jpeg", ".gif", ".svg", ".ico", ".pdf"], "directories": ["images", "assets", "media", "files"], "description": "Static assets"}}, "deploymentPath": "/public_html"}, "full-stack": {"name": "Full Stack Application", "description": "Combined frontend and backend application", "fileCategories": {"frontend": {"extensions": [".html", ".css", ".js", ".jsx", ".ts", ".tsx", ".vue"], "directories": ["frontend", "client", "src", "public"], "description": "Frontend application"}, "backend": {"extensions": [".php", ".js", ".ts", ".py", ".rb"], "directories": ["backend", "server", "api", "app"], "description": "Backend API"}, "database": {"extensions": [".sql"], "directories": ["database", "migrations", "sql"], "description": "Database scripts"}}, "deploymentPath": "/public_html"}}, "defaultExcludePatterns": ["node_modules/**", ".git/**", ".vscode/**", ".idea/**", "*.log", ".env", ".env.local", ".env.production", "coverage/**", ".nyc_output/**", "tmp/**", "temp/**", "*.tmp", "*.temp", ".DS_Store", "Thumbs.db"], "commonBuildOutputs": ["build", "dist", "public", "www", "htdocs"]}