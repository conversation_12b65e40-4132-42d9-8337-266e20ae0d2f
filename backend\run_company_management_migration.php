<?php
/**
 * Run Company Management Updates Migration
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

try {
    $db = Database::getInstance();
    
    echo "🔧 Running Company Management Updates Migration...\n";
    
    // Create company_documents table
    $sql = "CREATE TABLE IF NOT EXISTS company_documents (
        id INT AUTO_INCREMENT PRIMARY KEY,
        company_id INT NOT NULL,
        file_name VARCHAR(255) NOT NULL,
        original_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INT NOT NULL,
        file_type VARCHAR(100) NOT NULL,
        uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        uploaded_by INT,
        status ENUM('active', 'deleted') DEFAULT 'active',
        
        INDEX idx_company_id (company_id),
        INDEX idx_uploaded_by (uploaded_by),
        INDEX idx_status (status),
        INDEX idx_uploaded_at (uploaded_at),
        
        FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
        FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL
    )";
    
    $db->execute($sql);
    echo "✅ Created company_documents table\n";
    
    // Check if website column exists and is used
    $websiteUsage = $db->fetch("SELECT COUNT(*) as count FROM companies WHERE website IS NOT NULL AND website != ''");
    $websiteCount = $websiteUsage[0]['count'] ?? 0;
    
    if ($websiteCount > 0) {
        echo "ℹ️ Website column has data ($websiteCount records), keeping it for now\n";
    } else {
        echo "ℹ️ Website column is empty, but keeping it for compatibility\n";
    }
    
    // Verify manager_id foreign key exists
    try {
        $fkCheck = $db->fetch("SELECT CONSTRAINT_NAME FROM information_schema.KEY_COLUMN_USAGE 
                              WHERE TABLE_SCHEMA = DATABASE() 
                              AND TABLE_NAME = 'companies' 
                              AND COLUMN_NAME = 'manager_id' 
                              AND REFERENCED_TABLE_NAME = 'users'");
        
        if ($fkCheck) {
            echo "✅ Manager foreign key constraint already exists\n";
        } else {
            echo "ℹ️ Adding manager foreign key constraint...\n";
            $db->execute("ALTER TABLE companies 
                         ADD CONSTRAINT fk_companies_manager_id 
                         FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL");
            echo "✅ Added manager foreign key constraint\n";
        }
    } catch (Exception $e) {
        echo "ℹ️ Manager foreign key may already exist: " . $e->getMessage() . "\n";
    }
    
    echo "\n🎉 Company Management Updates Migration completed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
