#!/usr/bin/env node

/**
 * EskillVisor Master Automated Deployment System
 * Complete deployment workflow with change detection, package building, and upload instructions
 */

const fs = require('fs');
const path = require('path');

// Import deployment modules
const AdvancedChangeDetector = require('./advanced-change-detector.cjs');
const PackageBuilder = require('./package-builder.cjs');
const UploadInstructionsGenerator = require('./upload-instructions-generator.cjs');
const DynamicChangeAnalyzer = require('./dynamic-change-analyzer.cjs');

class MasterAutoDeployer {
    constructor() {
        this.startTime = Date.now();
        this.deploymentId = this.generateDeploymentId();
        
        // Initialize modules
        this.changeDetector = new AdvancedChangeDetector();
        this.packageBuilder = new PackageBuilder();
        this.instructionsGenerator = new UploadInstructionsGenerator();
    }

    /**
     * Execute complete automated deployment workflow
     */
    async deploy() {
        console.log('🚀 ESKILLVISOR MASTER AUTOMATED DEPLOYMENT SYSTEM');
        console.log('=' .repeat(60));
        console.log(`Deployment ID: ${this.deploymentId}`);
        console.log(`Started: ${new Date().toISOString()}`);
        console.log('Target: https://eskillvisor.wallistry.pk');
        console.log('');

        try {
            // PHASE 1: ADVANCED CHANGE DETECTION
            console.log('🔍 PHASE 1: ADVANCED CHANGE DETECTION');
            console.log('-' .repeat(40));
            const changes = await this.changeDetector.detectChanges();
            this.displayChangeReport(changes);

            if (!changes.deploymentRequired) {
                console.log('');
                console.log('✅ NO DEPLOYMENT REQUIRED');
                console.log('=' .repeat(60));
                console.log('All files are up to date with the remote server.');
                console.log('No changes detected since last deployment.');
                console.log('');
                console.log('🌐 Current website: https://eskillvisor.wallistry.pk');
                return { status: 'skipped', reason: 'no_changes', changes };
            }

            console.log('');

            // PHASE 2: NON-DESTRUCTIVE PACKAGE BUILDING
            console.log('📦 PHASE 2: NON-DESTRUCTIVE PACKAGE BUILDING');
            console.log('-' .repeat(40));
            const packageInfo = await this.packageBuilder.buildPackage(changes);
            console.log('');

            // PHASE 3: COMPREHENSIVE UPLOAD INSTRUCTIONS
            console.log('📋 PHASE 3: COMPREHENSIVE UPLOAD INSTRUCTIONS');
            console.log('-' .repeat(40));
            const instructions = await this.instructionsGenerator.generateInstructions(
                packageInfo, 
                changes, 
                packageInfo.manifest
            );
            console.log('');

            // PHASE 4: DEPLOYMENT SUMMARY
            console.log('🎉 PHASE 4: DEPLOYMENT SUMMARY');
            console.log('-' .repeat(40));
            await this.displayDeploymentSummary(packageInfo, changes, instructions);

            const duration = Math.round((Date.now() - this.startTime) / 1000);
            
            console.log('');
            // Mark deployment as completed
            this.changeDetector.markDeploymentCompleted();

            console.log('✅ AUTOMATED DEPLOYMENT SYSTEM COMPLETED SUCCESSFULLY!');
            console.log('=' .repeat(60));
            console.log(`Total Duration: ${duration} seconds`);
            console.log('');
            console.log('📋 NEXT STEPS:');
            console.log('1. Open: deployment-upload-instructions.html (in your browser)');
            console.log('2. Follow the step-by-step upload instructions');
            console.log('3. Upload the deployment package to cPanel');
            console.log('4. Test: https://eskillvisor.wallistry.pk');
            console.log('');
            // Generate dynamic deployment message
        const title = dynamicChanges.frontend.length > 0 && dynamicChanges.backend.length > 0
            ? 'FULL-STACK ENHANCEMENTS READY FOR DEPLOYMENT'
            : dynamicChanges.frontend.length > 0
                ? 'FRONTEND ENHANCEMENTS READY FOR DEPLOYMENT'
                : dynamicChanges.backend.length > 0
                    ? 'BACKEND IMPROVEMENTS READY FOR DEPLOYMENT'
                    : 'SYSTEM UPDATES READY FOR DEPLOYMENT';
        console.log(`🚀 ${title}!`);
            console.log('');
            console.log('🌐 Opening deployment instructions in your browser...');

            // PHASE 5: AUTO-OPEN DEPLOYMENT INSTRUCTIONS
            console.log('🌐 PHASE 5: AUTO-OPENING DEPLOYMENT INSTRUCTIONS');
            console.log('-' .repeat(40));
            await this.openDeploymentInstructions(instructions.htmlPath);

            return {
                status: 'success',
                deploymentId: this.deploymentId,
                duration,
                changes,
                packageInfo,
                instructions
            };

        } catch (error) {
            console.error('');
            console.error('❌ DEPLOYMENT SYSTEM FAILED!');
            console.error('=' .repeat(60));
            console.error(`Error: ${error.message}`);
            console.error('');
            throw error;
        }
    }

    /**
     * Display detailed change report
     */
    displayChangeReport(changes) {
        console.log('');
        console.log('📊 CHANGE DETECTION RESULTS:');
        console.log(`   Total Changes: ${changes.summary.totalChanges}`);
        console.log(`   Deployment Type: ${changes.summary.deploymentType}`);
        console.log(`   Added Files: ${changes.summary.added}`);
        console.log(`   Modified Files: ${changes.summary.modified}`);
        console.log(`   Deleted Files: ${changes.summary.deleted}`);
        console.log(`   Unchanged Files: ${changes.summary.unchanged}`);
        console.log('');

        if (changes.summary.totalChanges > 0) {
            console.log('📋 CHANGE CATEGORIES:');
            
            if (changes.categories.frontend.length > 0) {
                console.log(`   Frontend: ${changes.categories.frontend.length} files`);
                changes.categories.frontend.slice(0, 3).forEach(change => {
                    console.log(`     ${this.getChangeIcon(change.action)} ${change.path}`);
                });
                if (changes.categories.frontend.length > 3) {
                    console.log(`     ... and ${changes.categories.frontend.length - 3} more`);
                }
            }

            if (changes.categories.backend.length > 0) {
                console.log(`   Backend: ${changes.categories.backend.length} files`);
                changes.categories.backend.slice(0, 3).forEach(change => {
                    console.log(`     ${this.getChangeIcon(change.action)} ${change.path}`);
                });
                if (changes.categories.backend.length > 3) {
                    console.log(`     ... and ${changes.categories.backend.length - 3} more`);
                }
            }

            if (changes.categories.assets.length > 0) {
                console.log(`   Assets: ${changes.categories.assets.length} files`);
                changes.categories.assets.slice(0, 3).forEach(change => {
                    console.log(`     ${this.getChangeIcon(change.action)} ${change.path}`);
                });
                if (changes.categories.assets.length > 3) {
                    console.log(`     ... and ${changes.categories.assets.length - 3} more`);
                }
            }

            if (changes.categories.database.length > 0) {
                console.log(`   Database: ${changes.categories.database.length} files`);
            }

            if (changes.categories.config.length > 0) {
                console.log(`   Config: ${changes.categories.config.length} files`);
            }
        }

        console.log('');
        console.log('🌐 REMOTE SERVER COMPARISON:');
        console.log(`   Local Files Scanned: ${changes.localFiles}`);
        console.log(`   Remote Files Checked: ${changes.remoteFiles}`);
        console.log(`   Deployment Required: ${changes.deploymentRequired ? 'YES' : 'NO'}`);
    }

    /**
     * Display deployment summary
     */
    async displayDeploymentSummary(packageInfo, changes, instructions) {
        const zipFileName = packageInfo.zipPath ? path.basename(packageInfo.zipPath) : 'No ZIP created';
        const packagePath = path.resolve(packageInfo.packagePath);
        
        console.log('📦 DEPLOYMENT PACKAGE CREATED:');
        console.log(`   Package Directory: ${packagePath}`);
        console.log(`   ZIP File: ${zipFileName}`);
        console.log(`   Total Files: ${packageInfo.manifest.packageInfo.totalFiles}`);
        console.log(`   Package Size: ${this.formatFileSize(packageInfo.manifest.packageInfo.totalSize)}`);
        console.log('');

        console.log('📋 UPLOAD INSTRUCTIONS GENERATED:');
        console.log(`   HTML Guide: deployment-upload-instructions.html`);
        console.log(`   Text Guide: deployment-upload-instructions.txt`);
        console.log(`   Summary: deployment-summary.json`);
        console.log('');

        console.log('🎯 DEPLOYMENT TARGET:');
        console.log(`   Website: https://eskillvisor.wallistry.pk`);
        console.log(`   Server Path: /home9/wallistry/eskillvisor.wallistry.pk/`);
        console.log(`   Upload Method: cPanel File Manager`);
        console.log('');

        // Generate dynamic feature descriptions
        const analyzer = new DynamicChangeAnalyzer(process.cwd());
        const dynamicChanges = analyzer.analyzeChanges();
        const featureDescriptions = analyzer.generateFeatureDescriptions(dynamicChanges);

        if (featureDescriptions.length > 0) {
            featureDescriptions.forEach(description => console.log(description));
        } else {
            console.log('🔧 SYSTEM UPDATES READY:');
            console.log('   ✅ Various system improvements and enhancements');
        }

        console.log('');
        console.log('✅ AUTOMATED DEPLOYMENT SYSTEM COMPLETED SUCCESSFULLY!');
        console.log('=' .repeat(60));
        console.log(`Total Duration: ${Math.round((Date.now() - this.startTime) / 1000)} seconds`);
        console.log('');
        console.log('📋 NEXT STEPS:');
        console.log('1. Open: deployment-upload-instructions.html (in your browser)');
        console.log('2. Follow the step-by-step upload instructions');
        console.log('3. Upload the deployment package to cPanel');
        console.log('4. Test: https://eskillvisor.wallistry.pk');
        console.log('');

        // Generate dynamic deployment message
        const title = dynamicChanges.frontend.length > 0 && dynamicChanges.backend.length > 0
            ? 'FULL-STACK ENHANCEMENTS READY FOR DEPLOYMENT'
            : dynamicChanges.frontend.length > 0
                ? 'FRONTEND ENHANCEMENTS READY FOR DEPLOYMENT'
                : dynamicChanges.backend.length > 0
                    ? 'BACKEND IMPROVEMENTS READY FOR DEPLOYMENT'
                    : 'SYSTEM UPDATES READY FOR DEPLOYMENT';
        console.log(`🚀 ${title}!`);
    }

    /**
     * Get change icon for display
     */
    getChangeIcon(action) {
        switch (action) {
            case 'add': return '➕';
            case 'update': return '📝';
            case 'delete': return '❌';
            default: return '📄';
        }
    }

    /**
     * Format file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    /**
     * Auto-open deployment instructions in browser
     */
    async openDeploymentInstructions(htmlPath) {
        try {
            const { execSync } = require('child_process');
            const path = require('path');
            const fs = require('fs');

            // Ensure the file exists
            if (!fs.existsSync(htmlPath)) {
                console.log('   ⚠️ HTML instructions file not found, skipping auto-open');
                return;
            }

            const absolutePath = path.resolve(htmlPath);
            console.log(`   📂 Instructions file: ${path.basename(htmlPath)}`);
            console.log(`   📍 Full path: ${absolutePath}`);

            // Try different methods to open the file based on platform
            try {
                // Windows - try multiple methods
                if (process.platform === 'win32') {
                    try {
                        // Method 1: Use start command
                        execSync(`start "" "${absolutePath}"`, { stdio: 'ignore' });
                        console.log('   ✅ Opened deployment instructions in default browser');
                        return;
                    } catch (error) {
                        // Method 2: Use PowerShell
                        execSync(`powershell "Start-Process '${absolutePath}'"`, { stdio: 'ignore' });
                        console.log('   ✅ Opened deployment instructions via PowerShell');
                        return;
                    }
                }

                // macOS
                if (process.platform === 'darwin') {
                    execSync(`open "${absolutePath}"`, { stdio: 'ignore' });
                    console.log('   ✅ Opened deployment instructions in default browser');
                    return;
                }

                // Linux
                if (process.platform === 'linux') {
                    execSync(`xdg-open "${absolutePath}"`, { stdio: 'ignore' });
                    console.log('   ✅ Opened deployment instructions in default browser');
                    return;
                }

            } catch (error) {
                console.log('   ⚠️ Could not auto-open browser, please manually open:');
                console.log(`   📄 ${absolutePath}`);
            }

        } catch (error) {
            console.log('   ⚠️ Auto-open failed, please manually open deployment-upload-instructions.html');
        }
    }

    /**
     * Generate unique deployment ID
     */
    generateDeploymentId() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const random = Math.random().toString(36).substring(2, 8);
        return `deploy_${timestamp}_${random}`;
    }
}

module.exports = MasterAutoDeployer;

// CLI usage
if (require.main === module) {
    const deployer = new MasterAutoDeployer();
    
    deployer.deploy()
        .then(result => {
            if (result.status === 'success') {
                console.log('🎉 Deployment system completed successfully!');
                console.log('📋 Instructions opened in your browser for easy access');
                process.exit(0);
            } else if (result.status === 'skipped') {
                console.log('ℹ️ Deployment skipped - no changes detected');
                process.exit(0);
            }
        })
        .catch(error => {
            console.error('💥 Deployment system failed:', error.message);
            process.exit(1);
        });
}
