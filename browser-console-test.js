/**
 * <PERSON><PERSON>er Console Test for User Management Fixes
 * Copy and paste this into the browser console at http://localhost:5173
 */

async function testUserManagementFixes() {
    console.log('🧪 Testing User Management Fixes...\n');
    
    const API_BASE = 'http://localhost/Investment-System-eSkillVisor/backend';
    
    try {
        // Test 1: Login
        console.log('1️⃣ Testing login...');
        const loginResponse = await fetch(`${API_BASE}/login.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Origin': 'http://localhost:5173'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'password'
            })
        });
        
        const loginData = await loginResponse.json();
        console.log('✅ Login response:', loginData);
        
        if (!loginData.success) {
            throw new Error('Login failed: ' + loginData.message);
        }
        
        const token = loginData.data.access_token;
        console.log('🔑 Token received');
        
        // Test 2: Get users list
        console.log('\n2️⃣ Testing user list...');
        const usersResponse = await fetch(`${API_BASE}/users.php`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Origin': 'http://localhost:5173'
            }
        });
        
        const usersData = await usersResponse.json();
        console.log('✅ Users response:', usersData);
        
        if (usersData.success) {
            console.log(`📊 Found ${usersData.data.length} users`);
            const usersByRole = usersData.data.reduce((acc, user) => {
                acc[user.role] = (acc[user.role] || 0) + 1;
                return acc;
            }, {});
            console.log('👥 Users by role:', usersByRole);
        }
        
        // Test 3: Test user creation
        console.log('\n3️⃣ Testing user creation...');
        const formData = new FormData();
        formData.append('firstName', 'Test');
        formData.append('lastName', 'Manager');
        formData.append('email', `test.manager.${Date.now()}@example.com`);
        formData.append('password', 'testpass123');
        formData.append('role', 'manager');
        formData.append('mobile', '1234567890');
        
        const createResponse = await fetch(`${API_BASE}/users.php`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            },
            body: formData
        });
        
        const createData = await createResponse.json();
        console.log('✅ User creation response:', createData);
        
        if (createData.success) {
            console.log('🎉 User created successfully!');
            
            // Test 4: Test user profile
            console.log('\n4️⃣ Testing user profile...');
            const profileResponse = await fetch(`${API_BASE}/user-profile.php?id=${createData.data.id}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Origin': 'http://localhost:5173'
                }
            });
            
            const profileData = await profileResponse.json();
            console.log('✅ Profile response:', profileData);
        }
        
        // Test 5: Test notifications
        console.log('\n5️⃣ Testing notifications...');
        const notifResponse = await fetch(`${API_BASE}/notifications.php?is_read=false`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Origin': 'http://localhost:5173'
            }
        });
        
        const notifData = await notifResponse.json();
        console.log('✅ Notifications response:', notifData);
        
        // Test 6: Test analytics
        console.log('\n6️⃣ Testing analytics...');
        const analyticsResponse = await fetch(`${API_BASE}/analytics/trends.php`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Origin': 'http://localhost:5173'
            }
        });
        
        const analyticsData = await analyticsResponse.json();
        console.log('✅ Analytics response:', analyticsData);
        
        console.log('\n🎉 ALL TESTS PASSED! 🎉');
        console.log('\n📋 Summary:');
        console.log('✅ Login working');
        console.log('✅ User list loading');
        console.log('✅ User creation working');
        console.log('✅ User profiles working');
        console.log('✅ Notifications working');
        console.log('✅ Analytics working');
        console.log('\n🚀 User Management System is fully operational!');
        
        return {
            success: true,
            message: 'All tests passed successfully!'
        };
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Instructions
console.log('🧪 User Management Fixes Test');
console.log('📋 To run the test, execute: testUserManagementFixes()');
console.log('🌐 Make sure you are on http://localhost:5173');

// Auto-run if on the correct domain
if (window.location.hostname === 'localhost' && window.location.port === '5173') {
    console.log('🚀 Auto-running test...');
    testUserManagementFixes().then(result => {
        if (result.success) {
            console.log('🎉 Test completed successfully!');
        } else {
            console.log('❌ Test failed:', result.error);
        }
    });
} else {
    console.log('⚠️ Please navigate to http://localhost:5173 and run testUserManagementFixes()');
}
