import React, { useState, useEffect, useContext } from 'react';
import {
  Building2Icon,
  PackageIcon,
  TrendingUpIcon,
  AlertTriangleIcon,
  DollarSignIcon,
  BarChart3Icon,
  PieChartIcon,
  ActivityIcon,
  ArrowUpIcon,
  FileUpIcon,
  EyeIcon,
  RefreshCwIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  InfoIcon
} from 'lucide-react';
import { AuthContext } from '../../App.jsx';
import inventoryService from '../../services/inventoryService.js';
import companyService from '../../services/companyService.js';

const PartnerDashboard = () => {
  const { user } = useContext(AuthContext);
  const [dashboardData, setDashboardData] = useState({
    assigned_companies: [],
    inventory: { total_items: 0, total_value: 0, low_stock_count: 0 },
    top_categories: [],
    recent_activity: []
  });
  const [loading, setLoading] = useState(true);
  const [companies, setCompanies] = useState([]);

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);

        // Get dashboard data from API
        const data = await inventoryService.getDashboardData();

        if (data) {
          setDashboardData({
            assigned_companies: data.assigned_companies || [],
            inventory: data.inventory || { total_items: 0, total_value: 0, low_stock_count: 0 },
            top_categories: data.top_categories || [],
            recent_activity: data.recent_activity || []
          });
        }

        // Get companies assigned to this partner
        try {
          const companiesResponse = await companyService.getAssignedCompanies();
          if (companiesResponse) {
            setCompanies(companiesResponse);
          }
        } catch (error) {
          console.error('Failed to load companies:', error);
        }
      } catch (error) {
        console.error('Failed to load dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      loadDashboardData();
    }
  }, [user]);

  const StatCard = ({ title, value, icon, color = 'blue', subtitle, actionButton }) => {
    const colorClasses = {
      blue: 'bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 border-blue-200',
      green: 'bg-gradient-to-br from-green-50 to-green-100 text-green-600 border-green-200',
      purple: 'bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600 border-purple-200',
      red: 'bg-gradient-to-br from-red-50 to-red-100 text-red-600 border-red-200',
      orange: 'bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600 border-orange-200'
    };

    return (
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
            <p className="text-3xl font-bold text-gray-900 mb-2">{value}</p>
            {subtitle && (
              <p className="text-sm text-gray-500">{subtitle}</p>
            )}
            {actionButton && (
              <div className="mt-3">
                {actionButton}
              </div>
            )}
          </div>
          <div className={`p-4 rounded-xl border ${colorClasses[color]}`}>
            {icon}
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Partner Portfolio</h1>
          <p className="text-gray-600">Manage your assigned companies and inventory</p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Partner Portfolio</h1>
        <p className="text-gray-600">Comprehensive view of your assigned companies and inventory management</p>
      </div>

      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Assigned Companies"
          value={dashboardData.assigned_companies.length}
          subtitle="Active partnerships"
          icon={<Building2Icon className="h-8 w-8" />}
          color="blue"
        />
        <StatCard
          title="Total Items"
          value={dashboardData.inventory.total_items}
          subtitle="Across all companies"
          icon={<PackageIcon className="h-8 w-8" />}
          color="green"
        />
        <StatCard
          title="Portfolio Value"
          value={`$${dashboardData.inventory.total_value.toLocaleString()}`}
          subtitle="Total inventory worth"
          icon={<DollarSignIcon className="h-8 w-8" />}
          color="purple"
        />
        <StatCard
          title="Low Stock Alerts"
          value={dashboardData.inventory.low_stock_count}
          subtitle="Items need restocking"
          icon={<AlertTriangleIcon className="h-8 w-8" />}
          color="red"
          actionButton={
            dashboardData.inventory.low_stock_count > 0 && (
              <button className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full hover:bg-red-200 transition-colors">
                View Details
              </button>
            )
          }
        />
      </div>

      {/* Company Management */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Assigned Companies */}
        <div className="lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Building2Icon className="h-5 w-5 text-blue-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">Your Companies</h3>
              </div>
              <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
                View All
              </button>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {companies.slice(0, 4).map((company, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                      <Building2Icon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">{company.name || `Company ${index + 1}`}</p>
                      <p className="text-sm text-gray-500">{company.industry || 'Technology'}</p>
                      <p className="text-xs text-gray-400">{company.inventory_count || 0} inventory items</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      company.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {company.status || 'Active'}
                    </span>
                    <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                      <EyeIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
          </div>
          <div className="p-6">
            <div className="space-y-3">
              {/* Partner Role Restriction Notice */}
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center">
                  <InfoIcon className="h-5 w-5 text-blue-600 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-blue-900">Read-Only Access</p>
                    <p className="text-xs text-blue-700 mt-1">Contact your manager for inventory updates</p>
                  </div>
                </div>
              </div>

              <button className="w-full flex items-center justify-between p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
                <div className="flex items-center">
                  <BarChart3Icon className="h-5 w-5 text-purple-600 mr-3" />
                  <span className="text-sm font-medium text-purple-900">View Reports</span>
                </div>
                <ArrowUpIcon className="h-4 w-4 text-purple-600 transform rotate-45" />
              </button>

              <button className="w-full flex items-center justify-between p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors">
                <div className="flex items-center">
                  <AlertTriangleIcon className="h-5 w-5 text-orange-600 mr-3" />
                  <span className="text-sm font-medium text-orange-900">Stock Alerts</span>
                </div>
                <ArrowUpIcon className="h-4 w-4 text-orange-600 transform rotate-45" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Analytics and Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Inventory Categories */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center">
              <PieChartIcon className="h-5 w-5 text-purple-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Inventory Categories</h3>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {dashboardData.top_categories.slice(0, 5).map((category, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-3 ${
                      ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500', 'bg-red-500'][index]
                    }`}></div>
                    <span className="font-medium text-gray-900">{category.category || 'Uncategorized'}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-semibold text-gray-900">{category.item_count} items</div>
                    <div className="text-xs text-gray-500">${category.total_value?.toLocaleString()}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center">
              <ActivityIcon className="h-5 w-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {dashboardData.recent_activity.slice(0, 6).map((activity, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    {activity.action === 'create' && <CheckCircleIcon className="h-5 w-5 text-green-500" />}
                    {activity.action === 'update' && <RefreshCwIcon className="h-5 w-5 text-blue-500" />}
                    {activity.action === 'delete' && <XCircleIcon className="h-5 w-5 text-red-500" />}
                    {!['create', 'update', 'delete'].includes(activity.action) && <ClockIcon className="h-5 w-5 text-gray-500" />}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">
                      {activity.action === 'create' && 'Created '}
                      {activity.action === 'update' && 'Updated '}
                      {activity.action === 'delete' && 'Deleted '}
                      {activity.entity_type}
                    </p>
                    <p className="text-xs text-gray-500">
                      {new Date(activity.created_at).toLocaleDateString()} • {new Date(activity.created_at).toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PartnerDashboard;
