const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

class HistoryManager {
    constructor() {
        this.historyDir = path.join(__dirname, '../../output/history');
        this.deploymentsFile = path.join(this.historyDir, 'deployments.json');
        this.backupsDir = path.join(this.historyDir, 'backups');
        this.maxHistoryEntries = 100;
        this.maxBackupAge = 30 * 24 * 60 * 60 * 1000; // 30 days
        
        this.ensureDirectories();
    }

    async ensureDirectories() {
        try {
            await fs.ensureDir(this.historyDir);
            await fs.ensureDir(this.backupsDir);
            
            // Initialize deployments file if it doesn't exist
            if (!await fs.pathExists(this.deploymentsFile)) {
                await fs.writeJson(this.deploymentsFile, [], { spaces: 2 });
            }
        } catch (error) {
            console.error('Error creating history directories:', error);
        }
    }

    /**
     * Add deployment to history
     */
    async addDeployment(deployment) {
        try {
            const history = await this.getHistory();
            
            const historyEntry = {
                id: deployment.id,
                timestamp: deployment.timestamp,
                projectName: deployment.projectName,
                projectPath: deployment.projectPath,
                packageName: deployment.packageName,
                packagePath: deployment.packagePath,
                documentationPath: deployment.documentationPath,
                summary: deployment.summary,
                analysis: deployment.analysis,
                config: deployment.config,
                status: 'completed',
                deployedAt: null,
                rollbackAvailable: false
            };

            // Add to beginning of array (most recent first)
            history.unshift(historyEntry);

            // Limit history size
            if (history.length > this.maxHistoryEntries) {
                const removed = history.splice(this.maxHistoryEntries);
                // Clean up old packages
                await this.cleanupOldPackages(removed);
            }

            await fs.writeJson(this.deploymentsFile, history, { spaces: 2 });
            
            console.log(`Added deployment ${deployment.id} to history`);
            return historyEntry;

        } catch (error) {
            console.error('Error adding deployment to history:', error);
            throw error;
        }
    }

    /**
     * Get deployment history
     */
    async getHistory() {
        try {
            if (await fs.pathExists(this.deploymentsFile)) {
                return await fs.readJson(this.deploymentsFile);
            }
            return [];
        } catch (error) {
            console.error('Error loading deployment history:', error);
            return [];
        }
    }

    /**
     * Get deployment by ID
     */
    async getDeployment(deploymentId) {
        try {
            const history = await this.getHistory();
            return history.find(d => d.id === deploymentId);
        } catch (error) {
            console.error('Error getting deployment:', error);
            return null;
        }
    }

    /**
     * Mark deployment as deployed
     */
    async markAsDeployed(deploymentId, deploymentInfo = {}) {
        try {
            const history = await this.getHistory();
            const deployment = history.find(d => d.id === deploymentId);
            
            if (deployment) {
                deployment.status = 'deployed';
                deployment.deployedAt = new Date().toISOString();
                deployment.rollbackAvailable = true;
                deployment.deploymentInfo = deploymentInfo;
                
                await fs.writeJson(this.deploymentsFile, history, { spaces: 2 });
                console.log(`Marked deployment ${deploymentId} as deployed`);
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('Error marking deployment as deployed:', error);
            return false;
        }
    }

    /**
     * Create backup before deployment
     */
    async createBackup(projectPath, deploymentId) {
        try {
            const backupId = uuidv4();
            const timestamp = new Date().toISOString();
            const backupName = `backup-${deploymentId}-${timestamp.replace(/[:.]/g, '-')}.json`;
            const backupPath = path.join(this.backupsDir, backupName);

            // Create backup metadata
            const backup = {
                id: backupId,
                deploymentId,
                timestamp,
                projectPath,
                backupPath,
                files: []
            };

            // In a real implementation, you would:
            // 1. Scan the current state of files
            // 2. Create checksums or copies
            // 3. Store file metadata for restoration

            // For this demo, we'll store file metadata
            const files = await this.scanProjectFiles(projectPath);
            backup.files = files;

            await fs.writeJson(backupPath, backup, { spaces: 2 });
            
            console.log(`Created backup ${backupId} for deployment ${deploymentId}`);
            return backup;

        } catch (error) {
            console.error('Error creating backup:', error);
            throw error;
        }
    }

    /**
     * Scan project files for backup
     */
    async scanProjectFiles(projectPath) {
        try {
            const files = [];
            
            const scanDirectory = async (dirPath, relativePath = '') => {
                const items = await fs.readdir(dirPath);
                
                for (const item of items) {
                    const fullPath = path.join(dirPath, item);
                    const itemRelativePath = path.join(relativePath, item);
                    const stats = await fs.stat(fullPath);
                    
                    if (stats.isDirectory()) {
                        // Skip common directories that shouldn't be backed up
                        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
                            await scanDirectory(fullPath, itemRelativePath);
                        }
                    } else {
                        files.push({
                            path: itemRelativePath,
                            fullPath,
                            size: stats.size,
                            modified: stats.mtime.toISOString(),
                            checksum: null // Would calculate in real implementation
                        });
                    }
                }
            };

            await scanDirectory(projectPath);
            return files;

        } catch (error) {
            console.error('Error scanning project files:', error);
            return [];
        }
    }

    /**
     * Create rollback package
     */
    async createRollback(deploymentId) {
        try {
            const deployment = await this.getDeployment(deploymentId);
            if (!deployment) {
                throw new Error('Deployment not found');
            }

            if (!deployment.rollbackAvailable) {
                throw new Error('Rollback not available for this deployment');
            }

            // Find the previous deployment
            const history = await this.getHistory();
            const currentIndex = history.findIndex(d => d.id === deploymentId);
            
            if (currentIndex === -1) {
                throw new Error('Deployment not found in history');
            }

            // Get the previous deployed state
            const previousDeployment = history.slice(currentIndex + 1)
                .find(d => d.status === 'deployed');

            if (!previousDeployment) {
                throw new Error('No previous deployment found for rollback');
            }

            // Create rollback deployment
            const rollbackId = uuidv4();
            const timestamp = new Date().toISOString();
            
            const rollback = {
                id: rollbackId,
                timestamp,
                type: 'rollback',
                originalDeploymentId: deploymentId,
                targetDeploymentId: previousDeployment.id,
                projectName: deployment.projectName,
                projectPath: deployment.projectPath,
                packageName: `rollback-${deploymentId}-${timestamp.replace(/[:.]/g, '-')}.zip`,
                status: 'created',
                rollbackInfo: {
                    from: {
                        id: deploymentId,
                        timestamp: deployment.timestamp,
                        packageName: deployment.packageName
                    },
                    to: {
                        id: previousDeployment.id,
                        timestamp: previousDeployment.timestamp,
                        packageName: previousDeployment.packageName
                    }
                }
            };

            // In a real implementation, you would:
            // 1. Create a package with the previous state files
            // 2. Generate rollback instructions
            // 3. Create documentation

            console.log(`Created rollback ${rollbackId} from ${deploymentId} to ${previousDeployment.id}`);
            return rollback;

        } catch (error) {
            console.error('Error creating rollback:', error);
            throw error;
        }
    }

    /**
     * Get deployment statistics
     */
    async getStatistics() {
        try {
            const history = await this.getHistory();
            
            const stats = {
                totalDeployments: history.length,
                successfulDeployments: history.filter(d => d.status === 'deployed').length,
                failedDeployments: history.filter(d => d.status === 'failed').length,
                pendingDeployments: history.filter(d => d.status === 'completed').length,
                rollbacksAvailable: history.filter(d => d.rollbackAvailable).length,
                projectStats: {},
                recentActivity: history.slice(0, 10),
                deploymentFrequency: this.calculateDeploymentFrequency(history)
            };

            // Calculate project statistics
            history.forEach(deployment => {
                const projectName = deployment.projectName;
                if (!stats.projectStats[projectName]) {
                    stats.projectStats[projectName] = {
                        totalDeployments: 0,
                        lastDeployment: null,
                        averagePackageSize: 0,
                        totalFiles: 0
                    };
                }
                
                stats.projectStats[projectName].totalDeployments++;
                
                if (!stats.projectStats[projectName].lastDeployment || 
                    deployment.timestamp > stats.projectStats[projectName].lastDeployment) {
                    stats.projectStats[projectName].lastDeployment = deployment.timestamp;
                }
                
                if (deployment.summary) {
                    stats.projectStats[projectName].totalFiles += deployment.summary.changedFiles || 0;
                }
            });

            return stats;

        } catch (error) {
            console.error('Error calculating statistics:', error);
            return {
                totalDeployments: 0,
                successfulDeployments: 0,
                failedDeployments: 0,
                pendingDeployments: 0,
                rollbacksAvailable: 0,
                projectStats: {},
                recentActivity: [],
                deploymentFrequency: {}
            };
        }
    }

    /**
     * Calculate deployment frequency
     */
    calculateDeploymentFrequency(history) {
        const frequency = {
            daily: 0,
            weekly: 0,
            monthly: 0
        };

        const now = new Date();
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

        history.forEach(deployment => {
            const deploymentDate = new Date(deployment.timestamp);
            
            if (deploymentDate > oneDayAgo) {
                frequency.daily++;
            }
            if (deploymentDate > oneWeekAgo) {
                frequency.weekly++;
            }
            if (deploymentDate > oneMonthAgo) {
                frequency.monthly++;
            }
        });

        return frequency;
    }

    /**
     * Export deployment history
     */
    async exportHistory(format = 'json') {
        try {
            const history = await this.getHistory();
            const stats = await this.getStatistics();
            
            const exportData = {
                exportedAt: new Date().toISOString(),
                version: '1.0.0',
                statistics: stats,
                deployments: history
            };

            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `deployment-history-${timestamp}.${format}`;
            const exportPath = path.join(this.historyDir, filename);

            if (format === 'json') {
                await fs.writeJson(exportPath, exportData, { spaces: 2 });
            } else if (format === 'csv') {
                const csv = this.convertToCSV(history);
                await fs.writeFile(exportPath, csv, 'utf8');
            }

            console.log(`Exported deployment history to ${filename}`);
            return {
                success: true,
                path: exportPath,
                filename
            };

        } catch (error) {
            console.error('Error exporting history:', error);
            throw error;
        }
    }

    /**
     * Convert history to CSV format
     */
    convertToCSV(history) {
        const headers = [
            'ID', 'Timestamp', 'Project Name', 'Package Name', 
            'Status', 'Total Files', 'Changed Files', 'Package Size'
        ];

        const rows = history.map(deployment => [
            deployment.id,
            deployment.timestamp,
            deployment.projectName,
            deployment.packageName,
            deployment.status,
            deployment.summary?.totalFiles || 0,
            deployment.summary?.changedFiles || 0,
            deployment.summary?.packageSize || 0
        ]);

        return [headers, ...rows]
            .map(row => row.map(cell => `"${cell}"`).join(','))
            .join('\n');
    }

    /**
     * Clean up old packages
     */
    async cleanupOldPackages(removedEntries) {
        try {
            for (const entry of removedEntries) {
                if (entry.packagePath && await fs.pathExists(entry.packagePath)) {
                    await fs.remove(entry.packagePath);
                    console.log(`Cleaned up old package: ${entry.packageName}`);
                }
                
                if (entry.documentationPath && await fs.pathExists(entry.documentationPath)) {
                    await fs.remove(entry.documentationPath);
                    console.log(`Cleaned up old documentation: ${path.basename(entry.documentationPath)}`);
                }
            }
        } catch (error) {
            console.error('Error cleaning up old packages:', error);
        }
    }

    /**
     * Clean up old backups
     */
    async cleanupOldBackups() {
        try {
            const backupFiles = await fs.readdir(this.backupsDir);
            const now = Date.now();

            for (const file of backupFiles) {
                const filePath = path.join(this.backupsDir, file);
                const stats = await fs.stat(filePath);
                
                if (now - stats.mtime.getTime() > this.maxBackupAge) {
                    await fs.remove(filePath);
                    console.log(`Cleaned up old backup: ${file}`);
                }
            }
        } catch (error) {
            console.error('Error cleaning up old backups:', error);
        }
    }

    /**
     * Search deployment history
     */
    async searchHistory(query) {
        try {
            const history = await this.getHistory();
            const searchTerm = query.toLowerCase();

            return history.filter(deployment => {
                return (
                    deployment.projectName.toLowerCase().includes(searchTerm) ||
                    deployment.packageName.toLowerCase().includes(searchTerm) ||
                    deployment.id.toLowerCase().includes(searchTerm) ||
                    (deployment.analysis?.features || []).some(feature => 
                        feature.toLowerCase().includes(searchTerm)
                    )
                );
            });
        } catch (error) {
            console.error('Error searching history:', error);
            return [];
        }
    }

    /**
     * Get deployment comparison
     */
    async compareDeployments(deploymentId1, deploymentId2) {
        try {
            const deployment1 = await this.getDeployment(deploymentId1);
            const deployment2 = await this.getDeployment(deploymentId2);

            if (!deployment1 || !deployment2) {
                throw new Error('One or both deployments not found');
            }

            const comparison = {
                deployment1: {
                    id: deployment1.id,
                    timestamp: deployment1.timestamp,
                    projectName: deployment1.projectName,
                    summary: deployment1.summary
                },
                deployment2: {
                    id: deployment2.id,
                    timestamp: deployment2.timestamp,
                    projectName: deployment2.projectName,
                    summary: deployment2.summary
                },
                differences: {
                    timeDifference: new Date(deployment2.timestamp) - new Date(deployment1.timestamp),
                    fileDifference: (deployment2.summary?.changedFiles || 0) - (deployment1.summary?.changedFiles || 0),
                    sizeDifference: (deployment2.summary?.packageSize || 0) - (deployment1.summary?.packageSize || 0)
                }
            };

            return comparison;

        } catch (error) {
            console.error('Error comparing deployments:', error);
            throw error;
        }
    }
}

module.exports = HistoryManager;
