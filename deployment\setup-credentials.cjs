#!/usr/bin/env node

/**
 * EskillVisor Deployment Credentials Setup
 * Interactive setup for cPanel deployment credentials
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');
const crypto = require('crypto');

class CredentialsSetup {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        this.envPath = path.join(__dirname, '.env');
    }

    /**
     * Main setup process
     */
    async setup() {
        console.log('🔐 EskillVisor Deployment Credentials Setup');
        console.log('=' .repeat(50));
        console.log('This will configure your cPanel credentials for automated deployment.');
        console.log('');

        try {
            const credentials = await this.collectCredentials();
            await this.saveCredentials(credentials);
            await this.testConnection(credentials);
            
            console.log('');
            console.log('✅ Credentials setup completed successfully!');
            console.log('');
            console.log('🚀 You can now use automated deployment:');
            console.log('   npm run auto-deploy');
            console.log('');
            
        } catch (error) {
            console.error('❌ Setup failed:', error.message);
            process.exit(1);
        } finally {
            this.rl.close();
        }
    }

    /**
     * Collect credentials interactively
     */
    async collectCredentials() {
        console.log('📝 Please provide your cPanel credentials:');
        console.log('');

        const credentials = {};

        // cPanel Host
        credentials.CPANEL_HOST = await this.question('cPanel Host (e.g., ftp.wallistry.pk): ');
        if (!credentials.CPANEL_HOST) {
            throw new Error('cPanel host is required');
        }

        // Username
        credentials.CPANEL_USERNAME = await this.question('cPanel Username: ');
        if (!credentials.CPANEL_USERNAME) {
            throw new Error('cPanel username is required');
        }

        // Password
        credentials.CPANEL_PASSWORD = await this.questionHidden('cPanel Password: ');
        if (!credentials.CPANEL_PASSWORD) {
            throw new Error('cPanel password is required');
        }

        // Port
        const port = await this.question('Port (21 for FTP, 22 for SFTP) [21]: ');
        credentials.CPANEL_PORT = port || '21';

        // Protocol
        const protocol = await this.question('Protocol (ftp/sftp) [ftp]: ');
        credentials.CPANEL_PROTOCOL = protocol || 'ftp';

        // Generate secret key
        credentials.DEPLOYMENT_SECRET_KEY = crypto.randomBytes(32).toString('hex');
        credentials.ENCRYPTION_ENABLED = 'true';

        // Optional settings
        console.log('');
        console.log('📊 Optional settings (press Enter to skip):');
        
        credentials.MAX_CONCURRENT_UPLOADS = await this.question('Max concurrent uploads [5]: ') || '5';
        credentials.UPLOAD_TIMEOUT = await this.question('Upload timeout (ms) [30000]: ') || '30000';
        credentials.RETRY_ATTEMPTS = await this.question('Retry attempts [3]: ') || '3';

        // Debug settings
        const debug = await this.question('Enable debug mode? (y/N): ');
        credentials.DEBUG_MODE = debug.toLowerCase() === 'y' ? 'true' : 'false';
        credentials.VERBOSE_LOGGING = 'true';
        credentials.LOG_LEVEL = 'info';

        return credentials;
    }

    /**
     * Save credentials to .env file
     */
    async saveCredentials(credentials) {
        console.log('');
        console.log('💾 Saving credentials...');

        const envContent = [
            '# EskillVisor Auto-Deployment Configuration',
            '# Generated on ' + new Date().toISOString(),
            '',
            '# cPanel Connection Details',
            `CPANEL_HOST=${credentials.CPANEL_HOST}`,
            `CPANEL_PORT=${credentials.CPANEL_PORT}`,
            `CPANEL_USERNAME=${credentials.CPANEL_USERNAME}`,
            `CPANEL_PASSWORD=${credentials.CPANEL_PASSWORD}`,
            `CPANEL_PROTOCOL=${credentials.CPANEL_PROTOCOL}`,
            '',
            '# Deployment Security',
            `DEPLOYMENT_SECRET_KEY=${credentials.DEPLOYMENT_SECRET_KEY}`,
            `ENCRYPTION_ENABLED=${credentials.ENCRYPTION_ENABLED}`,
            '',
            '# Performance Settings',
            `MAX_CONCURRENT_UPLOADS=${credentials.MAX_CONCURRENT_UPLOADS}`,
            `UPLOAD_TIMEOUT=${credentials.UPLOAD_TIMEOUT}`,
            `RETRY_ATTEMPTS=${credentials.RETRY_ATTEMPTS}`,
            '',
            '# Debug Settings',
            `DEBUG_MODE=${credentials.DEBUG_MODE}`,
            `VERBOSE_LOGGING=${credentials.VERBOSE_LOGGING}`,
            `LOG_LEVEL=${credentials.LOG_LEVEL}`,
            '',
            '# Notification Settings (Optional)',
            'WEBHOOK_URL=',
            'SLACK_WEBHOOK=',
            'EMAIL_NOTIFICATIONS=false'
        ].join('\n');

        fs.writeFileSync(this.envPath, envContent);
        
        // Set secure permissions on .env file
        try {
            fs.chmodSync(this.envPath, 0o600);
        } catch (error) {
            console.warn('⚠️ Could not set secure permissions on .env file');
        }

        console.log(`✅ Credentials saved to: ${this.envPath}`);
    }

    /**
     * Test connection with provided credentials
     */
    async testConnection(credentials) {
        console.log('');
        console.log('🔍 Testing connection...');

        try {
            // For now, we'll just validate the credentials format
            // In a real implementation, this would test the actual connection
            
            if (!credentials.CPANEL_HOST.includes('.')) {
                throw new Error('Invalid host format');
            }

            if (credentials.CPANEL_PORT < 1 || credentials.CPANEL_PORT > 65535) {
                throw new Error('Invalid port number');
            }

            if (!['ftp', 'sftp'].includes(credentials.CPANEL_PROTOCOL)) {
                throw new Error('Invalid protocol (must be ftp or sftp)');
            }

            console.log('✅ Connection test passed');
            
        } catch (error) {
            throw new Error(`Connection test failed: ${error.message}`);
        }
    }

    /**
     * Ask a question and return the answer
     */
    async question(prompt) {
        return new Promise((resolve) => {
            this.rl.question(prompt, (answer) => {
                resolve(answer.trim());
            });
        });
    }

    /**
     * Ask for hidden input (password)
     */
    async questionHidden(prompt) {
        return new Promise((resolve) => {
            process.stdout.write(prompt);
            
            // Hide input
            process.stdin.setRawMode(true);
            process.stdin.resume();
            process.stdin.setEncoding('utf8');
            
            let password = '';
            
            const onData = (char) => {
                switch (char) {
                    case '\n':
                    case '\r':
                    case '\u0004': // Ctrl+D
                        process.stdin.setRawMode(false);
                        process.stdin.pause();
                        process.stdin.removeListener('data', onData);
                        process.stdout.write('\n');
                        resolve(password);
                        break;
                    case '\u0003': // Ctrl+C
                        process.exit(1);
                        break;
                    case '\u007f': // Backspace
                        if (password.length > 0) {
                            password = password.slice(0, -1);
                            process.stdout.write('\b \b');
                        }
                        break;
                    default:
                        password += char;
                        process.stdout.write('*');
                        break;
                }
            };
            
            process.stdin.on('data', onData);
        });
    }
}

// CLI usage
if (require.main === module) {
    const setup = new CredentialsSetup();
    setup.setup().catch(error => {
        console.error('Setup failed:', error.message);
        process.exit(1);
    });
}

module.exports = CredentialsSetup;
