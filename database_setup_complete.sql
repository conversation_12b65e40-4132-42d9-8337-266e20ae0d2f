-- EskillVisor Database Setup Script for wallistry.pk
-- Run this script in cPanel phpMyAdmin after creating the database

-- Create database (run this first in cPanel MySQL Databases)
-- Database Name: eskillvisor_db (becomes wallistry_eskillvisor_db)
-- Username: eskill (becomes wallistry_eskill)
-- Password: EskillVisor2024!

USE wallistry_eskillvisor_db;

-- Drop existing tables if they exist (for clean setup)
DROP TABLE IF EXISTS user_tokens;
DROP TABLE IF EXISTS file_uploads;
DROP TABLE IF EXISTS notifications;
DROP TABLE IF EXISTS audit_trail;
DROP TABLE IF EXISTS sales_records;
DROP TABLE IF EXISTS purchase_records;
DROP TABLE IF EXISTS inventory_transactions;
DROP TABLE IF EXISTS inventory_items;
DROP TABLE IF EXISTS company_partners;
DROP TABLE IF EXISTS companies;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS migrations;

-- Create users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    role ENUM('super_admin', 'manager', 'partner') NOT NULL,
    status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    profile_image VARCHAR(255) NULL,
    phone VARCHAR(20) NULL,
    department VARCHAR(100) NULL,
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create companies table
CREATE TABLE companies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    industry VARCHAR(100) NULL,
    website VARCHAR(255) NULL,
    email VARCHAR(255) NULL,
    phone VARCHAR(50) NULL,
    address TEXT NULL,
    city VARCHAR(100) NULL,
    state VARCHAR(100) NULL,
    country VARCHAR(100) NULL,
    postal_code VARCHAR(20) NULL,
    status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_status (status),
    INDEX idx_industry (industry)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create company_partners table
CREATE TABLE company_partners (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    user_id INT NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by INT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_company_partner (company_id, user_id),
    INDEX idx_company (company_id),
    INDEX idx_user (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create inventory_items table
CREATE TABLE inventory_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    sku VARCHAR(100) NULL,
    category VARCHAR(100) NULL,
    quantity INT NOT NULL DEFAULT 0,
    unit_price DECIMAL(10,2) NULL,
    reorder_level INT DEFAULT 10,
    status ENUM('active', 'inactive', 'discontinued') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NULL,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_company (company_id),
    INDEX idx_sku (sku),
    INDEX idx_category (category),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create inventory_transactions table
CREATE TABLE inventory_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    inventory_item_id INT NOT NULL,
    transaction_type ENUM('purchase', 'sale', 'adjustment', 'transfer') NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2) NULL,
    total_amount DECIMAL(10,2) NULL,
    reference_number VARCHAR(100) NULL,
    notes TEXT NULL,
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT NULL,
    FOREIGN KEY (inventory_item_id) REFERENCES inventory_items(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_item (inventory_item_id),
    INDEX idx_type (transaction_type),
    INDEX idx_date (transaction_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default users
INSERT INTO users (email, password_hash, first_name, last_name, role, status) VALUES
('<EMAIL>', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukj/LBB2.', 'System', 'Administrator', 'super_admin', 'active'),
('<EMAIL>', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukj/LBB2.', 'System', 'Manager', 'manager', 'active'),
('<EMAIL>', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukj/LBB2.', 'System', 'Partner', 'partner', 'active');

-- Insert sample companies
INSERT INTO companies (name, description, industry, email, phone, status) VALUES
('TechCorp Solutions', 'Leading technology solutions provider', 'Technology', '<EMAIL>', '******-0101', 'active'),
('Global Manufacturing Inc', 'International manufacturing company', 'Manufacturing', '<EMAIL>', '******-0102', 'active'),
('Retail Dynamics LLC', 'Modern retail solutions', 'Retail', '<EMAIL>', '******-0103', 'active'),
('Healthcare Partners', 'Healthcare service provider', 'Healthcare', '<EMAIL>', '******-0104', 'active'),
('Financial Services Group', 'Comprehensive financial services', 'Finance', '<EMAIL>', '******-0105', 'active');

-- Insert sample inventory items
INSERT INTO inventory_items (company_id, name, description, sku, category, quantity, unit_price, reorder_level, created_by) VALUES
(1, 'Laptop Computer', 'High-performance business laptop', 'TECH-LAP-001', 'Electronics', 25, 1299.99, 5, 1),
(1, 'Wireless Mouse', 'Ergonomic wireless mouse', 'TECH-MOU-001', 'Electronics', 150, 29.99, 20, 1),
(2, 'Steel Brackets', 'Industrial steel mounting brackets', 'MFG-BRK-001', 'Hardware', 500, 15.50, 50, 1),
(3, 'Shopping Bags', 'Eco-friendly shopping bags', 'RTL-BAG-001', 'Supplies', 1000, 2.99, 100, 1),
(4, 'Medical Gloves', 'Disposable medical examination gloves', 'MED-GLV-001', 'Medical', 2000, 0.25, 200, 1);

-- Create migrations table
CREATE TABLE migrations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    migration VARCHAR(255) NOT NULL,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Record migrations
INSERT INTO migrations (migration) VALUES
('001_create_users_table.sql'),
('002_create_companies_table.sql'),
('003_create_company_partners_table.sql'),
('004_create_inventory_items_table.sql'),
('005_create_transactions_table.sql');

-- Create additional required tables for full functionality
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'warning', 'error', 'success') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_read (is_read)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE audit_trail (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT NULL,
    old_values JSON NULL,
    new_values JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_action (action)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE user_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    token_type ENUM('access', 'refresh', 'reset') NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_token (token_hash),
    INDEX idx_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample notifications
INSERT INTO notifications (user_id, title, message, type) VALUES
(1, 'Welcome to EskillVisor', 'Your inventory management system is ready to use!', 'success'),
(2, 'Low Stock Alert', 'Some items are running low on stock', 'warning'),
(3, 'New Assignment', 'You have been assigned to manage TechCorp Solutions', 'info');

-- Database setup complete
SELECT 'Database setup completed successfully!' as status;
