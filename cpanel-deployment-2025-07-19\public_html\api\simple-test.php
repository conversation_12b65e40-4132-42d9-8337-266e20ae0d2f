<?php
// Simple test script without dependencies
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://eskillvisor.wallistry.pk');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

echo json_encode([
    'success' => true,
    'message' => 'Simple API test working!',
    'timestamp' => date('c'),
    'server' => $_SERVER['SERVER_NAME'] ?? 'unknown',
    'php_version' => phpversion(),
    'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown'
]);
?>
