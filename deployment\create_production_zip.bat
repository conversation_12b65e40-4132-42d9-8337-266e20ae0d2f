@echo off
echo Creating production deployment package...

:: Create production build directory
mkdir production_build 2>nul
mkdir production_build\api 2>nul
mkdir production_build\assets 2>nul

echo Copying backend files to api directory...
robocopy "..\backend" "production_build\api" /E /XD node_modules .git logs uploads

echo Copying built frontend files...
copy "..\dist\index.html" "production_build\"
robocopy "..\dist\assets" "production_build\assets" /E

echo Copying updated backend files...
copy "files\backend\controllers\CompanyController.php" "production_build\api\controllers\"
copy "files\backend\controllers\UserController.php" "production_build\api\controllers\"
copy "files\backend\models\Company.php" "production_build\api\models\"

echo Adding database migration...
mkdir production_build\api\migrations 2>nul
copy "database\014_add_company_extended_fields.sql" "production_build\api\migrations\"

echo .htaccess file already created in production_build directory

echo Creating ZIP package...
powershell "Compress-Archive -Path 'production_build\*' -DestinationPath 'eskillvisor-production-deployment-2025-07-18.zip' -Force"

echo Cleaning up...
rmdir /s /q production_build

echo.
echo ✅ Production deployment package created!
echo 📦 File: eskillvisor-production-deployment-2025-07-18.zip
echo 📁 Location: deployment/eskillvisor-production-deployment-2025-07-18.zip
echo.
echo Package contains:
echo - api/ (backend PHP files)
echo - assets/ (built frontend assets)
echo - index.html (built frontend)
echo - .htaccess (server configuration)
echo.
echo Ready for upload to cPanel at: /home9/wallistry/eskillvisor.wallistry.pk/
echo.
pause
