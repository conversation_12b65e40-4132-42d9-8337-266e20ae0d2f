<?php
/**
 * EskillVisor API Entry Point
 */

// Enable CORS for frontend
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'config/database.php';
require_once 'core/Router.php';
require_once 'core/Controller.php';
require_once 'core/Model.php';
require_once 'core/Response.php';
require_once 'core/Validator.php';
require_once 'core/Auth.php';

// Auto-load controllers and models
spl_autoload_register(function ($class) {
    $paths = [
        'controllers/' . $class . '.php',
        'models/' . $class . '.php',
        'services/' . $class . '.php'
    ];
    
    foreach ($paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            return;
        }
    }
});

// Initialize router
$router = new Router();

// Authentication routes
$router->post('/auth/login', 'AuthController@login');
$router->post('/auth/logout', 'AuthController@logout');
$router->get('/auth/me', 'AuthController@me');

// User routes
$router->get('/users', 'UserController@index');
$router->get('/users/{id}', 'UserController@show');
$router->get('/users/{id}/profile', 'UserController@getProfile');
$router->post('/users', 'UserController@store');
$router->put('/users/{id}', 'UserController@update');
$router->post('/users/{id}/approve', 'UserController@approve');
$router->post('/users/{id}/reject', 'UserController@reject');
$router->delete('/users/{id}', 'UserController@delete');

// Company routes
$router->get('/companies', 'CompanyController@index');
$router->get('/companies/all', 'CompanyController@getAll');
$router->get('/companies/manager', 'CompanyController@getByManager');
$router->get('/companies/{id}', 'CompanyController@show');
$router->post('/companies', 'CompanyController@store');
$router->put('/companies/{id}', 'CompanyController@update');
$router->post('/companies/{id}/approve', 'CompanyController@approve');
$router->post('/companies/{id}/reject', 'CompanyController@reject');
$router->delete('/companies/{id}', 'CompanyController@delete');
$router->post('/companies/{id}/partners/{userId}', 'CompanyController@assignPartner');
$router->delete('/companies/{id}/partners/{userId}', 'CompanyController@removePartner');

// Inventory routes
$router->get('/inventory', 'InventoryController@index');
$router->get('/inventory/{id}', 'InventoryController@show');
$router->post('/inventory', 'InventoryController@store');
$router->put('/inventory/{id}', 'InventoryController@update');
$router->delete('/inventory/{id}', 'InventoryController@delete');

// Transaction routes
$router->get('/transactions', 'TransactionController@index');
$router->get('/transactions/{id}', 'TransactionController@show');
$router->post('/transactions', 'TransactionController@store');

// Dashboard routes
$router->get('/dashboard/stats', 'DashboardController@getStats');
$router->get('/dashboard/recent-activity', 'DashboardController@getRecentActivity');

// Handle the request
try {
    $router->handleRequest();
} catch (Exception $e) {
    error_log('API Error: ' . $e->getMessage());
    Response::error('Internal server error', 500);
}
