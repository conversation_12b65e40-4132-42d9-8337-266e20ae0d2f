# 🚀 EskillVisor User Management Updates - ZIP Creation & Deployment

## **📦 ZIP FILE CREATION**

### **MANUAL ZIP CREATION (Recommended)**

1. **Navigate to:** `C:\Users\<USER>\Desktop\EskillVisor\deployment\files\`

2. **Select all contents** inside the `files` folder:
   ```
   ├── backend/
   │   ├── controllers/
   │   │   ├── CompanyController.php
   │   │   └── UserController.php
   │   └── models/
   │       └── Company.php
   └── src/
       ├── components/
       │   └── modals/
       │       ├── AddCompanyModal.jsx
       │       └── UserProfileModal.jsx
       └── pages/
           ├── manager/
           │   └── CompanyManagement.jsx
           └── superadmin/
               ├── CompanyOversight.jsx
               └── UserManagement.jsx
   ```

3. **Right-click** → **Send to** → **Compressed (zipped) folder**

4. **Rename** the ZIP file to: `user-management-updates-2025-07-18.zip`

### **ZIP FILE LOCATION**
```
📁 Location: C:\Users\<USER>\Desktop\EskillVisor\deployment\user-management-updates-2025-07-18.zip
📦 File Name: user-management-updates-2025-07-18.zip
📊 Estimated Size: ~150-200 KB
```

---

## **🎯 EXACT DEPLOYMENT INSTRUCTIONS**

### **cPanel Upload Path:**
```
/home9/wallistry/eskillvisor.wallistry.pk/
```

### **Upload Steps:**
1. **Login to cPanel** for wallistry.pk
2. **File Manager** → Navigate to `/home9/wallistry/eskillvisor.wallistry.pk/`
3. **Upload** → Select `user-management-updates-2025-07-18.zip`
4. **Extract** → Right-click ZIP → Extract → Extract to current directory
5. **Overwrite** → Choose "Yes" to overwrite existing files
6. **Delete ZIP** → Remove the ZIP file after extraction

---

## **🗄️ DATABASE MIGRATION REQUIRED**

### **Database File Location:**
```
📁 C:\Users\<USER>\Desktop\EskillVisor\deployment\database\014_add_company_extended_fields.sql
```

### **Execute in phpMyAdmin:**

1. **Access:** cPanel → phpMyAdmin
2. **Select:** Your EskillVisor database
3. **SQL Tab:** Click "SQL" tab
4. **Execute this SQL:**

```sql
-- Add extended fields to companies table for enhanced company management
-- Migration: 014_add_company_extended_fields.sql
-- Date: 2025-07-18

-- Add new required fields for company registration
ALTER TABLE companies 
ADD COLUMN company_director_id INT NULL AFTER name,
ADD COLUMN registration_territory VARCHAR(255) NULL AFTER company_director_id,
ADD COLUMN ein_number VARCHAR(50) NULL AFTER registration_territory,
ADD COLUMN marketplace VARCHAR(100) NULL AFTER ein_number,
ADD COLUMN custom_marketplace VARCHAR(255) NULL AFTER marketplace,
ADD COLUMN approval_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' AFTER status,
ADD COLUMN approved_by INT NULL AFTER approval_status,
ADD COLUMN approved_at TIMESTAMP NULL AFTER approved_by,
ADD COLUMN rejected_by INT NULL AFTER approved_at,
ADD COLUMN rejected_at TIMESTAMP NULL AFTER rejected_by,
ADD COLUMN rejection_reason TEXT NULL AFTER rejected_at,
ADD INDEX idx_company_director (company_director_id),
ADD INDEX idx_registration_territory (registration_territory),
ADD INDEX idx_ein_number (ein_number),
ADD INDEX idx_marketplace (marketplace),
ADD INDEX idx_approval_status (approval_status),
ADD FOREIGN KEY (company_director_id) REFERENCES users(id) ON DELETE SET NULL,
ADD FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
ADD FOREIGN KEY (rejected_by) REFERENCES users(id) ON DELETE SET NULL;

-- Update existing companies to have approved status
UPDATE companies SET approval_status = 'approved' WHERE approval_status IS NULL OR approval_status = 'pending';
```

5. **Click "Go"** to execute

---

## **📋 DATABASE CHANGES SUMMARY**

### **New Columns Added to `companies` table:**

| Column Name | Type | Description |
|-------------|------|-------------|
| `company_director_id` | INT | Links to users table (partner role) |
| `registration_territory` | VARCHAR(255) | Country where company is registered |
| `ein_number` | VARCHAR(50) | Employer Identification Number |
| `marketplace` | VARCHAR(100) | Primary marketplace (Amazon, Walmart, etc.) |
| `custom_marketplace` | VARCHAR(255) | Custom marketplace name if "Others" selected |
| `approval_status` | ENUM | pending/approved/rejected |
| `approved_by` | INT | User ID who approved |
| `approved_at` | TIMESTAMP | When approved |
| `rejected_by` | INT | User ID who rejected |
| `rejected_at` | TIMESTAMP | When rejected |
| `rejection_reason` | TEXT | Reason for rejection |

### **Indexes Added:**
- `idx_company_director` on `company_director_id`
- `idx_registration_territory` on `registration_territory`
- `idx_ein_number` on `ein_number`
- `idx_marketplace` on `marketplace`
- `idx_approval_status` on `approval_status`

### **Foreign Keys Added:**
- `company_director_id` → `users(id)`
- `approved_by` → `users(id)`
- `rejected_by` → `users(id)`

---

## **📁 FILES INCLUDED IN PACKAGE**

### **FRONTEND FILES (5 files):**
✅ `src/components/modals/UserProfileModal.jsx` - **NEW** user profile system  
✅ `src/pages/superadmin/UserManagement.jsx` - **UPDATED** separated lists  
✅ `src/components/modals/AddCompanyModal.jsx` - **UPDATED** new fields  
✅ `src/pages/superadmin/CompanyOversight.jsx` - **UPDATED** removed button  
✅ `src/pages/manager/CompanyManagement.jsx` - **UPDATED** cleaned up  

### **BACKEND FILES (3 files):**
✅ `backend/controllers/CompanyController.php` - **UPDATED** new field handling  
✅ `backend/controllers/UserController.php` - **UPDATED** profile endpoint  
✅ `backend/models/Company.php` - **UPDATED** extended methods  

### **DATABASE FILE (1 file):**
✅ `deployment/database/014_add_company_extended_fields.sql` - **NEW** migration  

---

## **✅ POST-DEPLOYMENT VERIFICATION**

### **Test These Features:**
- [ ] User Management page loads with Manager/Partner tabs
- [ ] Clicking users opens profile modal with 3 tabs
- [ ] Add Company button appears in user profiles
- [ ] Company creation modal has new required fields:
  - [ ] Company Name
  - [ ] Company Director (dropdown from partners)
  - [ ] Registration Territory (country dropdown)
  - [ ] EIN Number
  - [ ] Marketplace (Amazon, Walmart, TikTok, Shopify, Others)
  - [ ] Custom marketplace field (when "Others" selected)
- [ ] Form validation works correctly
- [ ] Company creation saves to database with new fields

### **Check Database:**
- [ ] `companies` table has new columns
- [ ] Foreign keys are working
- [ ] Existing companies have `approval_status = 'approved'`

---

## **🚀 READY FOR DEPLOYMENT!**

**ZIP Location:** `C:\Users\<USER>\Desktop\EskillVisor\deployment\user-management-updates-2025-07-18.zip`  
**Upload Target:** `/home9/wallistry/eskillvisor.wallistry.pk/`  
**Database Migration:** Execute SQL in phpMyAdmin  
**Impact:** Medium - UI improvements + Database schema updates + Backend enhancements
