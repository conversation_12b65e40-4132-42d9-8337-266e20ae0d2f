# 🗄️ EskillVisor Database Setup for cPanel

## 📋 Database Configuration Overview

### Target Environment
- **Hosting**: cPanel (wallistry.pk)
- **Database Engine**: MySQL 8.0+
- **Character Set**: utf8mb4
- **Collation**: utf8mb4_unicode_ci

### Database Credentials
```
Host: localhost
Database: wallistry_eskillvisor_db
Username: wallistry_eskill
Password: EskillVisor2024!
```

## 🔧 Step-by-Step Database Creation

### Step 1: Access cPanel MySQL Databases
1. **Login to cPanel**:
   - URL: https://wallistry.pk:2083
   - Username: `wallistry`
   - Password: `+GlESn;lJteQ%VXf`

2. **Navigate to MySQL Databases**:
   - In cPanel dashboard, find "Databases" section
   - Click "MySQL Databases"

### Step 2: Create Database
1. **Create New Database**:
   - Database Name: `eskillvisor_db`
   - Full Database Name: `wallistry_eskillvisor_db` (auto-prefixed)
   - Click "Create Database"

2. **Verify Database Creation**:
   - Database should appear in "Current Databases" list
   - Note the full name: `wallistry_eskillvisor_db`

### Step 3: Create Database User
1. **Add New User**:
   - Username: `eskill`
   - Full Username: `wallistry_eskill` (auto-prefixed)
   - Password: `EskillVisor2024!`
   - Password Strength: Should be 100/100
   - Click "Create User"

2. **Verify User Creation**:
   - User should appear in "Current Users" list
   - Note the full username: `wallistry_eskill`

### Step 4: Assign User to Database
1. **Add User to Database**:
   - User: `wallistry_eskill`
   - Database: `wallistry_eskillvisor_db`
   - Click "Add"

2. **Set Privileges**:
   - Check "ALL PRIVILEGES"
   - Click "Make Changes"

3. **Verify Assignment**:
   - Should see user-database pair in "Current Databases" section

## 🚀 Automated Database Installation

### Method 1: Web-Based Installation (Recommended)
1. **Access Installation Script**:
   ```
   URL: https://wallistry.pk/api/install.php
   ```

2. **Installation Process**:
   - Automatically creates all tables
   - Sets up indexes and relationships
   - Creates default user accounts
   - Configures initial system data

3. **Expected Output**:
   ```json
   {
     "success": true,
     "message": "Database installation completed successfully",
     "tables_created": 13,
     "default_users": 3
   }
   ```

### Method 2: Manual SQL Execution
If web installation fails, use phpMyAdmin:

1. **Access phpMyAdmin**:
   - In cPanel, click "phpMyAdmin"
   - Select database: `wallistry_eskillvisor_db`

2. **Execute Migration Files** (in order):
   ```sql
   -- Run these files in sequence:
   001_create_users_table.sql
   002_create_companies_table.sql
   003_create_company_partners_table.sql
   004_create_inventory_items_table.sql
   005_create_transactions_table.sql
   006_create_purchase_records_table.sql
   007_create_sales_records_table.sql
   008_create_audit_trail_table.sql
   009_create_notifications_table.sql
   010_create_file_uploads_table.sql
   011_create_user_tokens_table.sql
   012_add_approval_workflow.sql
   013_add_user_extended_fields_safe.sql
   ```

## 📊 Database Schema Overview

### Core Tables
1. **users** - User accounts and authentication
2. **companies** - Company information
3. **company_partners** - Partner-company relationships
4. **inventory_items** - Product inventory data
5. **transactions** - Financial transactions
6. **purchase_records** - Purchase history
7. **sales_records** - Sales history
8. **audit_trail** - System activity logs
9. **notifications** - User notifications
10. **file_uploads** - Uploaded file tracking
11. **user_tokens** - JWT token management
12. **approval_workflows** - Approval processes
13. **user_extended_fields** - Additional user data

### Default User Accounts
The installation creates these default accounts:

| Role | Email | Password | Description |
|------|-------|----------|-------------|
| superadmin | <EMAIL> | password | Full system access |
| manager | <EMAIL> | password | Manage partners and companies |
| partner | <EMAIL> | password | View assigned companies |

⚠️ **IMPORTANT**: Change these passwords immediately after deployment!

## 🧪 Database Testing

### Test 1: Connection Test
```bash
URL: https://wallistry.pk/api/db-test.php
Expected Response:
{
  "success": true,
  "message": "Database connection successful",
  "test_result": 1
}
```

### Test 2: User Authentication
```bash
URL: https://wallistry.pk/api/login.php
Method: POST
Body: {
  "email": "<EMAIL>",
  "password": "password"
}
Expected: JWT token response
```

### Test 3: Data Retrieval
```bash
URL: https://wallistry.pk/api/users
Method: GET
Headers: Authorization: Bearer [JWT_TOKEN]
Expected: List of users
```

## 🔒 Security Configuration

### Database Security Settings
1. **User Privileges**: Only necessary permissions granted
2. **Connection Security**: Local connections only
3. **Password Policy**: Strong password enforced
4. **Access Control**: Role-based database access

### Application Security
1. **Prepared Statements**: All queries use prepared statements
2. **Input Validation**: Server-side validation for all inputs
3. **SQL Injection Prevention**: PDO with parameter binding
4. **Error Handling**: Secure error messages without data exposure

## 🔧 Maintenance & Backup

### Regular Maintenance
1. **Database Optimization**:
   - Run weekly: `OPTIMIZE TABLE table_name;`
   - Monitor table sizes and indexes

2. **Log Cleanup**:
   - Audit trail: Keep 90 days
   - Notifications: Keep 30 days
   - Error logs: Keep 30 days

### Backup Strategy
1. **Daily Backups**:
   - Use cPanel backup feature
   - Schedule automatic backups

2. **Manual Backup**:
   ```bash
   # Via phpMyAdmin
   Export → Custom → All tables → SQL format
   ```

## 🚨 Troubleshooting

### Common Issues

#### 1. "Database Connection Failed"
**Causes**:
- Incorrect credentials
- Database doesn't exist
- User not assigned to database

**Solutions**:
- Verify credentials in `/api/config/config.php`
- Check database exists in cPanel
- Ensure user has ALL PRIVILEGES

#### 2. "Table doesn't exist"
**Causes**:
- Installation not completed
- Migration files not executed

**Solutions**:
- Run installation script again
- Manually execute SQL files via phpMyAdmin

#### 3. "Access denied for user"
**Causes**:
- User not created properly
- Insufficient privileges

**Solutions**:
- Recreate database user
- Grant ALL PRIVILEGES to user

#### 4. "Character set issues"
**Causes**:
- Wrong character set/collation

**Solutions**:
- Ensure utf8mb4 character set
- Check collation: utf8mb4_unicode_ci

## 📞 Support Information

### Database Access URLs
```
cPanel: https://wallistry.pk:2083
phpMyAdmin: https://wallistry.pk:2083 → phpMyAdmin
Database Host: localhost (from server)
```

### Configuration Files
```
Main Config: /api/config/config.php
Database Config: /api/config/database.php
Migration Scripts: /api/migrations/
```

## ✅ Database Setup Complete!

Once the database is properly configured:
1. ✅ Database created: `wallistry_eskillvisor_db`
2. ✅ User created: `wallistry_eskill`
3. ✅ Privileges assigned: ALL PRIVILEGES
4. ✅ Tables created: 13 tables
5. ✅ Default users: 3 accounts
6. ✅ Security configured: Prepared statements, validation
7. ✅ Testing completed: Connection, authentication, data retrieval

Your EskillVisor database is ready for production use! 🚀
