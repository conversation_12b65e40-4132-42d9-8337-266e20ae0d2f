<?php
// Simple user creation endpoint for testing
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Get POST data
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        
        if (strpos($contentType, 'multipart/form-data') !== false) {
            // Handle FormData
            $data = $_POST;
            error_log("FormData received: " . print_r($data, true));
        } else {
            // Handle JSON
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            error_log("JSO<PERSON> received: " . print_r($data, true));
        }
        
        // Basic validation
        if (empty($data['firstName']) || empty($data['email'])) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'firstName and email are required'
            ]);
            exit;
        }
        
        // For now, just return success without database
        echo json_encode([
            'success' => true,
            'message' => 'User creation test successful',
            'data' => [
                'id' => rand(1000, 9999),
                'name' => $data['firstName'] . ' ' . ($data['lastName'] ?? ''),
                'email' => $data['email'],
                'role' => $data['role'] ?? 'partner',
                'created_at' => date('Y-m-d H:i:s')
            ]
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Simple user create error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'message' => 'Internal server error'
    ]);
}
?>
