<?php
/**
 * Analytics Trends API endpoint
 */

// Enable CORS for local development
$allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:3000',
    'http://127.0.0.1:5173',
    'http://127.0.0.1:3000'
];

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, $allowedOrigins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    header('Access-Control-Allow-Origin: http://localhost:5173');
}

header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';

try {
    // Get authorization header
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? '';
    
    if (strpos($authHeader, 'Bearer ') !== 0) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Authorization token required']);
        exit();
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Return mock trends data
        $trends = [
            'user_growth' => [
                ['month' => 'Jan', 'users' => 45],
                ['month' => 'Feb', 'users' => 52],
                ['month' => 'Mar', 'users' => 48],
                ['month' => 'Apr', 'users' => 61],
                ['month' => 'May', 'users' => 55],
                ['month' => 'Jun', 'users' => 67]
            ],
            'inventory_value' => [
                ['month' => 'Jan', 'value' => 125000],
                ['month' => 'Feb', 'value' => 132000],
                ['month' => 'Mar', 'value' => 128000],
                ['month' => 'Apr', 'value' => 145000],
                ['month' => 'May', 'value' => 138000],
                ['month' => 'Jun', 'value' => 156000]
            ],
            'company_growth' => [
                ['month' => 'Jan', 'companies' => 12],
                ['month' => 'Feb', 'companies' => 14],
                ['month' => 'Mar', 'companies' => 13],
                ['month' => 'Apr', 'companies' => 16],
                ['month' => 'May', 'companies' => 15],
                ['month' => 'Jun', 'companies' => 18]
            ]
        ];
        
        echo json_encode([
            'success' => true,
            'data' => $trends
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log("Analytics trends error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}
?>
