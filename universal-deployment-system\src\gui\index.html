<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal Deployment System</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-rocket"></i>
                    <h1>Universal Deployment System</h1>
                </div>
                <div class="header-actions">
                    <button id="settingsBtn" class="btn btn-secondary">
                        <i class="fas fa-cog"></i> Settings
                    </button>
                    <button id="helpBtn" class="btn btn-secondary">
                        <i class="fas fa-question-circle"></i> Help
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="project-section">
                    <h3><i class="fas fa-folder-open"></i> Project</h3>
                    <div class="project-selector">
                        <button id="selectProjectBtn" class="btn btn-primary btn-block">
                            <i class="fas fa-folder"></i> Select Project Folder
                        </button>
                        <div id="projectPath" class="project-path"></div>
                    </div>
                </div>

                <div class="config-section">
                    <h3><i class="fas fa-cog"></i> Configuration</h3>
                    <div class="config-form">
                        <div class="form-group">
                            <label for="projectName">Project Name</label>
                            <input type="text" id="projectName" class="form-control" placeholder="Enter project name">
                        </div>
                        
                        <div class="form-group">
                            <label for="projectType">Project Type</label>
                            <select id="projectType" class="form-control">
                                <option value="web-application">Web Application</option>
                                <option value="react-application">React Application</option>
                                <option value="vue-application">Vue Application</option>
                                <option value="php-application">PHP Application</option>
                                <option value="laravel-application">Laravel Application</option>
                                <option value="wordpress-site">WordPress Site</option>
                                <option value="static-website">Static Website</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="deploymentDomain">Deployment Domain</label>
                            <input type="text" id="deploymentDomain" class="form-control" placeholder="example.com">
                        </div>

                        <div class="form-group">
                            <label for="serverPath">Server Path</label>
                            <input type="text" id="serverPath" class="form-control" placeholder="/public_html" value="/public_html">
                        </div>

                        <button id="saveConfigBtn" class="btn btn-success btn-block">
                            <i class="fas fa-save"></i> Save Configuration
                        </button>
                    </div>
                </div>

                <div class="actions-section">
                    <h3><i class="fas fa-play"></i> Actions</h3>
                    <div class="action-buttons">
                        <button id="checkChangesBtn" class="btn btn-info btn-block" disabled>
                            <i class="fas fa-search"></i> Check Changes
                        </button>
                        <button id="deployBtn" class="btn btn-warning btn-block" disabled>
                            <i class="fas fa-upload"></i> Create Deployment
                        </button>
                        <button id="openDocsBtn" class="btn btn-secondary btn-block" disabled>
                            <i class="fas fa-book"></i> Open Deployment Guide
                        </button>
                    </div>
                </div>
            </aside>

            <!-- Content Area -->
            <section class="content-area">
                <!-- Status Bar -->
                <div class="status-bar">
                    <div class="status-info">
                        <span id="statusText">Ready</span>
                        <div id="progressBar" class="progress-bar" style="display: none;">
                            <div class="progress-fill"></div>
                        </div>
                    </div>
                    <div class="status-actions">
                        <span id="lastUpdate" class="last-update"></span>
                    </div>
                </div>

                <!-- Main Display Area -->
                <div class="display-area">
                    <!-- Welcome Screen -->
                    <div id="welcomeScreen" class="welcome-screen">
                        <div class="welcome-content">
                            <i class="fas fa-rocket welcome-icon"></i>
                            <h2>Welcome to Universal Deployment System</h2>
                            <p>Professional deployment automation for web projects</p>
                            <div class="welcome-steps">
                                <div class="step">
                                    <i class="fas fa-folder"></i>
                                    <span>1. Select your project folder</span>
                                </div>
                                <div class="step">
                                    <i class="fas fa-cog"></i>
                                    <span>2. Configure deployment settings</span>
                                </div>
                                <div class="step">
                                    <i class="fas fa-search"></i>
                                    <span>3. Check for changes</span>
                                </div>
                                <div class="step">
                                    <i class="fas fa-upload"></i>
                                    <span>4. Create deployment package</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Changes Display -->
                    <div id="changesDisplay" class="changes-display" style="display: none;">
                        <div class="changes-header">
                            <h2><i class="fas fa-list"></i> Detected Changes</h2>
                            <div class="changes-summary">
                                <div class="summary-item">
                                    <span class="summary-label">Total Files:</span>
                                    <span id="totalFiles" class="summary-value">0</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">Changed:</span>
                                    <span id="changedFiles" class="summary-value">0</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">Added:</span>
                                    <span id="addedFiles" class="summary-value added">0</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">Modified:</span>
                                    <span id="modifiedFiles" class="summary-value modified">0</span>
                                </div>
                            </div>
                        </div>

                        <div class="changes-content">
                            <div class="changes-tabs">
                                <button class="tab-btn active" data-tab="categories">By Category</button>
                                <button class="tab-btn" data-tab="files">All Files</button>
                                <button class="tab-btn" data-tab="features">Features</button>
                            </div>

                            <div class="tab-content">
                                <div id="categoriesTab" class="tab-pane active">
                                    <div id="categoriesList" class="categories-list"></div>
                                </div>
                                
                                <div id="filesTab" class="tab-pane">
                                    <div id="filesList" class="files-list"></div>
                                </div>

                                <div id="featuresTab" class="tab-pane">
                                    <div id="featuresList" class="features-list"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Deployment Results -->
                    <div id="deploymentResults" class="deployment-results" style="display: none;">
                        <div class="results-header">
                            <h2><i class="fas fa-check-circle"></i> Deployment Package Created</h2>
                        </div>
                        <div class="results-content">
                            <div class="package-info">
                                <div class="info-item">
                                    <span class="info-label">Package File:</span>
                                    <span id="packageFile" class="info-value"></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Package Size:</span>
                                    <span id="packageSize" class="info-value"></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Files Included:</span>
                                    <span id="packageFiles" class="info-value"></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Created:</span>
                                    <span id="packageCreated" class="info-value"></span>
                                </div>
                            </div>
                            
                            <div class="deployment-actions">
                                <button id="openPackageBtn" class="btn btn-primary">
                                    <i class="fas fa-folder-open"></i> Open Package Location
                                </button>
                                <button id="openGuideBtn" class="btn btn-success">
                                    <i class="fas fa-book"></i> Open Deployment Guide
                                </button>
                                <button id="newDeploymentBtn" class="btn btn-secondary">
                                    <i class="fas fa-plus"></i> New Deployment
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-content">
                <span>Universal Deployment System v1.0.0</span>
                <span>Professional deployment automation for web projects</span>
            </div>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="spinner"></div>
            <p id="loadingText">Processing...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="app.js"></script>
</body>
</html>
