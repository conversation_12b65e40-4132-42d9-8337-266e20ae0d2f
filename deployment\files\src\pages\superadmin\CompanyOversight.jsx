import React, { useState, useEffect, useContext } from 'react';
import { PlusIcon, SearchIcon, FilterIcon, CheckIcon, XIcon, ClockIcon, BuildingIcon, AlertCircleIcon } from 'lucide-react';
import { AuthContext } from '../../App.jsx';
import companyService from '../../services/companyService.js';
import AddCompanyModal from '../../components/modals/AddCompanyModal.jsx';
import CompanyApprovalModal from '../../components/modals/CompanyApprovalModal.jsx';

const CompanyOversight = () => {
  const { userRole, user } = useContext(AuthContext);
  const [companies, setCompanies] = useState([
    { 
      id: '1', 
      name: 'Acme Corporation', 
      email: '<EMAIL>', 
      industry: 'Technology',
      status: 'active',
      approval_status: 'approved',
      manager: { name: '<PERSON>', email: '<EMAIL>' },
      created_at: '2025-07-10T10:30:00Z'
    },
    { 
      id: '2', 
      name: 'TechStart Inc', 
      email: '<EMAIL>', 
      industry: 'Technology',
      status: 'active',
      approval_status: 'pending',
      created_by: '2',
      created_at: '2025-07-15T14:20:00Z'
    },
    { 
      id: '3', 
      name: 'Global Ventures', 
      email: '<EMAIL>', 
      industry: 'Finance',
      status: 'active',
      approval_status: 'approved',
      manager: { name: 'Mike Johnson', email: '<EMAIL>' },
      created_at: '2025-07-12T09:15:00Z'
    }
  ]);
  const [loading, setLoading] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    loadCompanies();
  }, []);

  const loadCompanies = async () => {
    setLoading(true);
    try {
      // For now, using mock data
      // const companiesData = await companyService.getAllCompanies();
      // setCompanies(companiesData);
    } catch (error) {
      console.error('Failed to load companies:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCompany = async (companyData) => {
    try {
      const newCompany = await companyService.createCompany(companyData);
      setCompanies(prev => [...prev, newCompany]);
      setShowAddModal(false);
    } catch (error) {
      console.error('Failed to add company:', error);
      throw error;
    }
  };

  const handleApproveCompany = async (companyId, approvalData) => {
    try {
      setCompanies(prev => prev.map(company =>
        company.id === companyId 
          ? { 
              ...company, 
              approval_status: 'approved',
              approved_by: user.id,
              approved_at: new Date().toISOString(),
              ...approvalData
            }
          : company
      ));
      setShowApprovalModal(false);
      setSelectedCompany(null);
    } catch (error) {
      console.error('Failed to approve company:', error);
      throw error;
    }
  };

  const handleRejectCompany = async (companyId, rejectionReason) => {
    try {
      setCompanies(prev => prev.map(company =>
        company.id === companyId
          ? {
              ...company,
              approval_status: 'rejected',
              rejection_reason: rejectionReason,
              rejected_by: user.id,
              rejected_at: new Date().toISOString()
            }
          : company
      ));
      setShowApprovalModal(false);
      setSelectedCompany(null);
    } catch (error) {
      console.error('Failed to reject company:', error);
      throw error;
    }
  };

  const openApprovalModal = (company) => {
    setSelectedCompany(company);
    setShowApprovalModal(true);
  };

  const filteredCompanies = companies.filter(company => {
    if (activeTab === 'all') return true;
    if (activeTab === 'pending') return company.approval_status === 'pending';
    if (activeTab === 'approved') return company.approval_status === 'approved';
    if (activeTab === 'rejected') return company.approval_status === 'rejected';
    return true;
  });

  const pendingCount = companies.filter(company => company.approval_status === 'pending').length;

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Company Oversight</h1>
            <p className="text-gray-600">Monitor and manage all companies in the system</p>
          </div>
          {pendingCount > 0 && (
            <div className="bg-orange-100 border border-orange-200 rounded-lg px-3 py-2">
              <div className="flex items-center">
                <ClockIcon className="h-4 w-4 text-orange-600 mr-2" />
                <span className="text-sm font-medium text-orange-800">
                  {pendingCount} pending approval{pendingCount !== 1 ? 's' : ''}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Approval Workflow Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'all', name: 'All Companies', count: companies.length },
              { id: 'pending', name: 'Pending Approval', count: pendingCount },
              { id: 'approved', name: 'Approved', count: companies.filter(c => c.approval_status === 'approved').length },
              { id: 'rejected', name: 'Rejected', count: companies.filter(c => c.approval_status === 'rejected').length }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`}
              >
                {tab.name}
                <span className={`ml-2 py-0.5 px-2 rounded-full text-xs ${
                  activeTab === tab.id ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600'
                }`}>
                  {tab.count}
                </span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search companies..."
                className="pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
              />
            </div>
            <button className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              <FilterIcon className="h-4 w-4 mr-2" />
              Filter
            </button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Industry</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manager</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approval</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredCompanies.map((company) => (
                  <tr key={company.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                            <BuildingIcon className="h-5 w-5 text-gray-500" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{company.name}</div>
                          <div className="text-sm text-gray-500">{company.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                        {company.industry}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {company.manager ? (
                        <div>
                          <div className="font-medium">{company.manager.name}</div>
                          <div className="text-gray-500">{company.manager.email}</div>
                        </div>
                      ) : (
                        <span className="text-gray-400">No manager assigned</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        company.status === 'active' ? 'bg-green-100 text-green-800' :
                        company.status === 'inactive' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {company.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        company.approval_status === 'approved' ? 'bg-green-100 text-green-800' :
                        company.approval_status === 'pending' ? 'bg-orange-100 text-orange-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {company.approval_status === 'approved' && <CheckIcon className="h-3 w-3 mr-1" />}
                        {company.approval_status === 'pending' && <ClockIcon className="h-3 w-3 mr-1" />}
                        {company.approval_status === 'rejected' && <XIcon className="h-3 w-3 mr-1" />}
                        {company.approval_status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {company.created_at ? new Date(company.created_at).toLocaleDateString() : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {company.approval_status === 'pending' && (
                        <button
                          onClick={() => openApprovalModal(company)}
                          className="text-primary hover:text-primary-dark mr-3"
                        >
                          Review
                        </button>
                      )}
                      <button className="text-gray-600 hover:text-gray-900 mr-3">Edit</button>
                      <button className="text-red-600 hover:text-red-900">Delete</button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Company Approval Modal */}
      <CompanyApprovalModal
        isOpen={showApprovalModal}
        onClose={() => setShowApprovalModal(false)}
        company={selectedCompany}
        onApprove={handleApproveCompany}
        onReject={handleRejectCompany}
      />
    </div>
  );
};

export default CompanyOversight;
