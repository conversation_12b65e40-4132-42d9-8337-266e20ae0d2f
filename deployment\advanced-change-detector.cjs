#!/usr/bin/env node

/**
 * EskillVisor Advanced Change Detection System
 * Compares local files with remote cPanel server to detect changes
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const https = require('https');
const http = require('http');

class AdvancedChangeDetector {
    constructor() {
        this.localStateFile = path.join(__dirname, 'local-deployment-state.json');
        this.remoteStateFile = path.join(__dirname, 'remote-deployment-state.json');
        this.baseUrl = 'https://eskillvisor.wallistry.pk';
    }

    /**
     * Detect changes by comparing local and remote states
     */
    async detectChanges() {
        console.log('🔍 Advanced Change Detection Started');
        console.log('=' .repeat(50));
        
        try {
            // Step 1: Scan local files
            console.log('📁 Scanning local files...');
            const localState = await this.scanLocalFiles();
            
            // Step 2: Check remote files
            console.log('🌐 Checking remote server files...');
            const remoteState = await this.checkRemoteFiles(localState);
            
            // Step 3: Compare states
            console.log('🔄 Comparing local vs remote...');
            const changes = this.compareStates(localState, remoteState);
            
            // Step 4: Generate detailed report
            const report = this.generateChangeReport(changes, localState, remoteState);
            
            // Step 5: Save states
            this.saveLocalState(localState);
            this.saveRemoteState(remoteState);
            
            return report;
            
        } catch (error) {
            throw new Error(`Change detection failed: ${error.message}`);
        }
    }

    /**
     * Scan local deployment files
     */
    async scanLocalFiles() {
        const localFiles = new Map();
        const deploymentDir = 'cpanel-deployment/public_html';
        
        if (!fs.existsSync(deploymentDir)) {
            console.log('   ⚠️ Deployment directory not found, creating...');
            return localFiles;
        }
        
        const scanDirectory = (dir, relativePath = '') => {
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const relPath = relativePath ? `${relativePath}/${item}` : item;
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    scanDirectory(fullPath, relPath);
                } else {
                    const content = fs.readFileSync(fullPath);
                    const hash = crypto.createHash('md5').update(content).digest('hex');
                    
                    localFiles.set(relPath, {
                        size: stat.size,
                        modified: stat.mtime.toISOString(),
                        hash: hash,
                        type: this.getFileType(relPath)
                    });
                }
            }
        };
        
        scanDirectory(deploymentDir);
        console.log(`   📊 Found ${localFiles.size} local files`);
        return localFiles;
    }

    /**
     * Check remote files by making HTTP requests
     */
    async checkRemoteFiles(localState) {
        const remoteFiles = new Map();

        // List of key files to check on remote server
        const keyFiles = [
            'index.html',
            'api/index.php',
            'api/test.php',
            'api/controllers/AnalyticsController.php',
            'api/controllers/AuthController.php',
            'api/controllers/CompanyController.php',
            'api/controllers/UserController.php',
            'assets/index-C10uvgBp.js',
            'assets/index-ConY77jt.css',
            'assets/ui-DuuvEgzt.js'
        ];

        // Always check remote files - don't assume based on time
        console.log(`   🔍 Checking actual cPanel files for accurate comparison...`);
        
        for (const file of keyFiles) {
            try {
                const url = `${this.baseUrl}/${file}`;
                const response = await this.makeHttpRequest(url);
                
                if (response.status === 200) {
                    const hash = crypto.createHash('md5').update(response.data).digest('hex');
                    
                    remoteFiles.set(file, {
                        size: response.data.length,
                        modified: response.headers['last-modified'] || new Date().toISOString(),
                        hash: hash,
                        type: this.getFileType(file),
                        status: 'exists'
                    });
                    
                    console.log(`   ✅ ${file} (${this.formatFileSize(response.data.length)})`);
                } else {
                    console.log(`   ❌ ${file} (${response.status})`);
                    // Treat 500 errors as "file needs update" since it might be a backend issue
                    if (response.status === 500) {
                        remoteFiles.set(file, {
                            status: 'needs_update',
                            error: `Server error (${response.status})`,
                            size: 0,
                            hash: 'error'
                        });
                    }
                }
            } catch (error) {
                console.log(`   ⚠️ ${file} (${error.message})`);
                remoteFiles.set(file, {
                    status: 'needs_update',
                    error: error.message,
                    size: 0,
                    hash: 'error'
                });
            }
        }
        
        console.log(`   📊 Checked ${remoteFiles.size} remote files`);
        return remoteFiles;
    }

    /**
     * Compare local and remote states
     */
    compareStates(localState, remoteState) {
        const changes = {
            added: [],
            modified: [],
            deleted: [],
            unchanged: []
        };
        
        // Check for new and modified files
        for (const [filePath, localFile] of localState) {
            if (!remoteState.has(filePath)) {
                changes.added.push({
                    path: filePath,
                    type: localFile.type,
                    size: localFile.size,
                    action: 'add'
                });
            } else {
                const remoteFile = remoteState.get(filePath);
                if (remoteFile.status === 'exists' && localFile.hash !== remoteFile.hash) {
                    changes.modified.push({
                        path: filePath,
                        type: localFile.type,
                        size: localFile.size,
                        localHash: localFile.hash,
                        remoteHash: remoteFile.hash,
                        action: 'update'
                    });
                } else if (remoteFile.status === 'needs_update') {
                    // File has errors on remote, treat as needs update
                    changes.modified.push({
                        path: filePath,
                        type: localFile.type,
                        size: localFile.size,
                        localHash: localFile.hash,
                        remoteHash: 'error',
                        action: 'update',
                        reason: 'Remote file has errors'
                    });
                } else if (remoteFile.status === 'exists') {
                    changes.unchanged.push({
                        path: filePath,
                        type: localFile.type,
                        action: 'unchanged'
                    });
                }
            }
        }
        
        // Check for deleted files (only for files that exist remotely)
        for (const [filePath, remoteFile] of remoteState) {
            if (remoteFile.status === 'exists' && !localState.has(filePath)) {
                changes.deleted.push({
                    path: filePath,
                    type: this.getFileType(filePath),
                    action: 'delete'
                });
            }
        }
        
        return changes;
    }

    /**
     * Generate comprehensive change report
     */
    generateChangeReport(changes, localState, remoteState) {
        const totalChanges = changes.added.length + changes.modified.length + changes.deleted.length;
        
        // Categorize changes
        const categories = {
            frontend: [],
            backend: [],
            database: [],
            config: [],
            assets: []
        };
        
        [...changes.added, ...changes.modified, ...changes.deleted].forEach(change => {
            if (change.path.startsWith('api/')) {
                if (change.path.includes('migration') || change.path.includes('.sql')) {
                    categories.database.push(change);
                } else if (change.path.includes('config')) {
                    categories.config.push(change);
                } else {
                    categories.backend.push(change);
                }
            } else if (change.path.startsWith('assets/')) {
                categories.assets.push(change);
            } else {
                categories.frontend.push(change);
            }
        });
        
        // Determine deployment type
        let deploymentType = 'none';
        if (totalChanges > 0) {
            if (categories.database.length > 0) {
                deploymentType = 'full';
            } else if (categories.backend.length > 0 && categories.frontend.length > 0) {
                deploymentType = 'backend-frontend';
            } else if (categories.backend.length > 0) {
                deploymentType = 'backend-only';
            } else if (categories.frontend.length > 0 || categories.assets.length > 0) {
                deploymentType = 'frontend-only';
            } else if (categories.config.length > 0) {
                deploymentType = 'config-only';
            }
        }
        
        return {
            timestamp: new Date().toISOString(),
            summary: {
                totalChanges,
                deploymentType,
                added: changes.added.length,
                modified: changes.modified.length,
                deleted: changes.deleted.length,
                unchanged: changes.unchanged.length
            },
            changes,
            categories,
            localFiles: localState.size,
            remoteFiles: remoteState.size,
            deploymentRequired: totalChanges > 0
        };
    }

    /**
     * Make HTTP request
     */
    async makeHttpRequest(url, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const client = url.startsWith('https:') ? https : http;
            const timeoutId = setTimeout(() => reject(new Error('Request timeout')), timeout);
            
            const req = client.get(url, (res) => {
                clearTimeout(timeoutId);
                let data = '';
                
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    resolve({
                        status: res.statusCode,
                        data: data,
                        headers: res.headers
                    });
                });
            });
            
            req.on('error', (error) => {
                clearTimeout(timeoutId);
                reject(error);
            });
        });
    }

    /**
     * Get file type based on path
     */
    getFileType(filePath) {
        if (filePath.startsWith('api/')) return 'backend';
        if (filePath.startsWith('assets/')) return 'asset';
        if (filePath.endsWith('.html')) return 'frontend';
        if (filePath.endsWith('.js')) return 'javascript';
        if (filePath.endsWith('.css')) return 'stylesheet';
        if (filePath.endsWith('.php')) return 'php';
        return 'other';
    }

    /**
     * Format file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    /**
     * Save local state
     */
    saveLocalState(localState) {
        const stateData = {
            timestamp: new Date().toISOString(),
            files: Object.fromEntries(localState)
        };
        fs.writeFileSync(this.localStateFile, JSON.stringify(stateData, null, 2));
    }

    /**
     * Save remote state
     */
    saveRemoteState(remoteState) {
        const stateData = {
            timestamp: new Date().toISOString(),
            files: Object.fromEntries(remoteState)
        };
        fs.writeFileSync(this.remoteStateFile, JSON.stringify(stateData, null, 2));
    }

    /**
     * Get last deployment time
     */
    getLastDeploymentTime() {
        try {
            const deploymentStateFile = path.join(__dirname, 'deployment-state.json');
            if (fs.existsSync(deploymentStateFile)) {
                const state = JSON.parse(fs.readFileSync(deploymentStateFile, 'utf8'));
                return new Date(state.timestamp).getTime();
            }
        } catch (error) {
            // Ignore errors
        }
        return 0; // Very old timestamp
    }

    /**
     * Get last remote state
     */
    getLastRemoteState() {
        try {
            if (fs.existsSync(this.remoteStateFile)) {
                const state = JSON.parse(fs.readFileSync(this.remoteStateFile, 'utf8'));
                return new Map(Object.entries(state.files));
            }
        } catch (error) {
            // Ignore errors
        }
        return new Map();
    }

    /**
     * Mark deployment as completed
     */
    markDeploymentCompleted() {
        const deploymentStateFile = path.join(__dirname, 'deployment-state.json');
        const state = {
            timestamp: new Date().toISOString(),
            status: 'completed'
        };
        fs.writeFileSync(deploymentStateFile, JSON.stringify(state, null, 2));
    }
}

module.exports = AdvancedChangeDetector;
