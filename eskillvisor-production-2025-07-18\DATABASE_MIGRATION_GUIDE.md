# 🗄️ Database Migration Guide

## **✅ Migration File Location**

The database migration file is available at:
```
eskillvisor-production-2025-07-18/api/migrations/014_add_company_extended_fields.sql
```

## **📋 Migration Contents**

This migration adds the following new columns to the `companies` table:

### **New Company Fields:**
- `company_director_id` - Links to users table (partner role)
- `registration_territory` - Country where company is registered
- `ein_number` - Employer Identification Number
- `marketplace` - Primary marketplace (Amazon, Walmart, TikTok, Shopify, Others)
- `custom_marketplace` - Custom marketplace name when "Others" is selected

### **Approval Workflow Fields:**
- `approval_status` - ENUM('pending', 'approved', 'rejected')
- `approved_by` - User ID who approved the company
- `approved_at` - Timestamp when approved
- `rejected_by` - User ID who rejected the company
- `rejected_at` - Timestamp when rejected
- `rejection_reason` - Text reason for rejection

### **Indexes and Foreign Keys:**
- Indexes on all new fields for performance
- Foreign key relationships to users table

## **🎯 How to Execute Migration**

### **Step 1: Access phpMyAdmin**
1. Login to cPanel for wallistry.pk
2. Click on "phpMyAdmin"
3. Select your EskillVisor database

### **Step 2: Execute SQL**
1. Click the "SQL" tab
2. Copy the entire contents from `api/migrations/014_add_company_extended_fields.sql`
3. Paste into the SQL text area
4. Click "Go" to execute

### **Step 3: Verify Migration**
After execution, verify:
- No error messages appear
- Companies table has new columns
- Existing companies have `approval_status = 'approved'`

## **📄 Complete SQL Migration**

```sql
-- Add extended fields to companies table for enhanced company management
-- Migration: 014_add_company_extended_fields.sql
-- Date: 2025-07-18

-- Add new required fields for company registration
ALTER TABLE companies 
ADD COLUMN company_director_id INT NULL AFTER name,
ADD COLUMN registration_territory VARCHAR(255) NULL AFTER company_director_id,
ADD COLUMN ein_number VARCHAR(50) NULL AFTER registration_territory,
ADD COLUMN marketplace VARCHAR(100) NULL AFTER ein_number,
ADD COLUMN custom_marketplace VARCHAR(255) NULL AFTER marketplace,
ADD COLUMN approval_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' AFTER status,
ADD COLUMN approved_by INT NULL AFTER approval_status,
ADD COLUMN approved_at TIMESTAMP NULL AFTER approved_by,
ADD COLUMN rejected_by INT NULL AFTER approved_at,
ADD COLUMN rejected_at TIMESTAMP NULL AFTER rejected_by,
ADD COLUMN rejection_reason TEXT NULL AFTER rejected_at,
ADD INDEX idx_company_director (company_director_id),
ADD INDEX idx_registration_territory (registration_territory),
ADD INDEX idx_ein_number (ein_number),
ADD INDEX idx_marketplace (marketplace),
ADD INDEX idx_approval_status (approval_status),
ADD FOREIGN KEY (company_director_id) REFERENCES users(id) ON DELETE SET NULL,
ADD FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
ADD FOREIGN KEY (rejected_by) REFERENCES users(id) ON DELETE SET NULL;

-- Update existing companies to have approved status
UPDATE companies SET approval_status = 'approved' WHERE approval_status IS NULL OR approval_status = 'pending';
```

## **✅ Migration is Ready!**

The database migration file is included in your production package and ready to execute in phpMyAdmin.
