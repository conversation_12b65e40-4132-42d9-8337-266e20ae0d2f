<?php
/**
 * Company Documents API - Handle file uploads and document management
 */

// Enable CORS for local development
$allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:3000',
    'http://127.0.0.1:5173',
    'http://127.0.0.1:3000'
];

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, $allowedOrigins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    header('Access-Control-Allow-Origin: http://localhost:5173');
}

header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

// Create uploads directory if it doesn't exist
$uploadsDir = __DIR__ . '/uploads/company-documents';
if (!is_dir($uploadsDir)) {
    mkdir($uploadsDir, 0755, true);
}

try {
    $db = Database::getInstance();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle file upload
        $companyId = $_POST['company_id'] ?? null;
        $uploadedBy = $_POST['uploaded_by'] ?? null;
        
        if (!$companyId) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Company ID is required']);
            exit();
        }
        
        if (!isset($_FILES['documents']) || empty($_FILES['documents']['name'][0])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No files uploaded']);
            exit();
        }
        
        $allowedTypes = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'];
        $maxFileSize = 10 * 1024 * 1024; // 10MB
        $uploadedFiles = [];
        $errors = [];
        
        $files = $_FILES['documents'];
        $fileCount = count($files['name']);
        
        for ($i = 0; $i < $fileCount; $i++) {
            if ($files['error'][$i] !== UPLOAD_ERR_OK) {
                $errors[] = "Error uploading file: " . $files['name'][$i];
                continue;
            }
            
            $originalName = $files['name'][$i];
            $fileSize = $files['size'][$i];
            $tmpName = $files['tmp_name'][$i];
            
            // Validate file size
            if ($fileSize > $maxFileSize) {
                $errors[] = "File too large: " . $originalName . " (max 10MB)";
                continue;
            }
            
            // Validate file type
            $fileExtension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
            if (!in_array($fileExtension, $allowedTypes)) {
                $errors[] = "Invalid file type: " . $originalName . " (allowed: " . implode(', ', $allowedTypes) . ")";
                continue;
            }
            
            // Generate unique filename
            $fileName = uniqid() . '_' . time() . '.' . $fileExtension;
            $filePath = $uploadsDir . '/' . $fileName;
            
            // Move uploaded file
            if (move_uploaded_file($tmpName, $filePath)) {
                // Save to database
                $result = $db->execute(
                    "INSERT INTO company_documents (company_id, file_name, original_name, file_path, file_size, file_type, uploaded_by) 
                     VALUES (?, ?, ?, ?, ?, ?, ?)",
                    [$companyId, $fileName, $originalName, $filePath, $fileSize, $fileExtension, $uploadedBy]
                );
                
                if ($result) {
                    $uploadedFiles[] = [
                        'id' => $db->lastInsertId(),
                        'original_name' => $originalName,
                        'file_size' => $fileSize,
                        'file_type' => $fileExtension
                    ];
                } else {
                    $errors[] = "Database error for file: " . $originalName;
                    unlink($filePath); // Remove file if database insert failed
                }
            } else {
                $errors[] = "Failed to move uploaded file: " . $originalName;
            }
        }
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => count($uploadedFiles) > 0,
            'uploaded_files' => $uploadedFiles,
            'errors' => $errors,
            'message' => count($uploadedFiles) . ' files uploaded successfully'
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get documents for a company
        $companyId = $_GET['company_id'] ?? null;
        
        if (!$companyId) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Company ID is required']);
            exit();
        }
        
        $documents = $db->fetchAll(
            "SELECT cd.*, u.first_name, u.last_name 
             FROM company_documents cd
             LEFT JOIN users u ON cd.uploaded_by = u.id
             WHERE cd.company_id = ? AND cd.status = 'active'
             ORDER BY cd.uploaded_at DESC",
            [$companyId]
        );
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'data' => $documents
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
        // Delete a document
        $documentId = $_GET['id'] ?? null;
        
        if (!$documentId) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Document ID is required']);
            exit();
        }
        
        // Get document info before deletion
        $document = $db->fetch("SELECT * FROM company_documents WHERE id = ?", [$documentId]);
        
        if (!$document) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Document not found']);
            exit();
        }
        
        $document = $document[0];
        
        // Mark as deleted in database
        $result = $db->execute(
            "UPDATE company_documents SET status = 'deleted' WHERE id = ?",
            [$documentId]
        );
        
        if ($result) {
            // Optionally delete physical file
            if (file_exists($document['file_path'])) {
                unlink($document['file_path']);
            }
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'Document deleted successfully'
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to delete document']);
        }
        
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log("Company Documents API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
