<!DOCTYPE html>
<html>
<head>
    <title>Investment System - Server Status Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #333; margin: 0; font-size: 2.5em; }
        .server-status { margin: 20px 0; padding: 20px; border-radius: 8px; border-left: 4px solid #ccc; }
        .running { background: #d4edda; border-left-color: #28a745; }
        .stopped { background: #f8d7da; border-left-color: #dc3545; }
        .checking { background: #d1ecf1; border-left-color: #17a2b8; }
        .server-info { display: flex; justify-content: space-between; align-items: center; }
        .status-badge { padding: 4px 12px; border-radius: 12px; font-size: 0.8em; font-weight: bold; }
        .status-running { background: #28a745; color: white; }
        .status-stopped { background: #dc3545; color: white; }
        .status-checking { background: #17a2b8; color: white; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .instructions { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .quick-links { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 20px 0; }
        .quick-link { display: block; padding: 10px; background: #007bff; color: white; text-decoration: none; text-align: center; border-radius: 4px; }
        .quick-link:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Investment System</h1>
            <p>Server Status Dashboard</p>
        </div>

        <div id="frontend-status" class="server-status checking">
            <div class="server-info">
                <div>
                    <h3>🎨 Frontend Server (Vite)</h3>
                    <p>React application running on Vite development server</p>
                    <p><strong>URL:</strong> http://localhost:5173</p>
                </div>
                <span id="frontend-badge" class="status-badge status-checking">Checking...</span>
            </div>
        </div>

        <div id="backend-status" class="server-status checking">
            <div class="server-info">
                <div>
                    <h3>⚙️ Backend Server (Apache/PHP)</h3>
                    <p>PHP API server running through XAMPP Apache</p>
                    <p><strong>URL:</strong> http://localhost/Investment-System-eSkillVisor/backend</p>
                </div>
                <span id="backend-badge" class="status-badge status-checking">Checking...</span>
            </div>
        </div>

        <div id="database-status" class="server-status checking">
            <div class="server-info">
                <div>
                    <h3>🗄️ Database Server (MySQL)</h3>
                    <p>MySQL database server running through XAMPP</p>
                    <p><strong>Database:</strong> investment_system</p>
                </div>
                <span id="database-badge" class="status-badge status-checking">Checking...</span>
            </div>
        </div>

        <div class="instructions">
            <h4>📋 Manual Server Startup Instructions</h4>
            <ol>
                <li><strong>XAMPP Control Panel:</strong> Open XAMPP Control Panel (should be starting automatically)</li>
                <li><strong>Start Apache:</strong> Click "Start" button next to Apache</li>
                <li><strong>Start MySQL:</strong> Click "Start" button next to MySQL</li>
                <li><strong>Frontend:</strong> Already running on http://localhost:5173</li>
            </ol>
        </div>

        <button onclick="checkAllServers()">🔄 Refresh Server Status</button>
        <button onclick="openXAMPP()">🎛️ Open XAMPP Control</button>

        <div class="quick-links">
            <a href="http://localhost:5173" target="_blank" class="quick-link">🎨 Frontend App</a>
            <a href="http://localhost/Investment-System-eSkillVisor/backend/test-connection.php" target="_blank" class="quick-link">⚙️ Backend Test</a>
            <a href="http://localhost/phpmyadmin" target="_blank" class="quick-link">🗄️ phpMyAdmin</a>
            <a href="http://localhost:5173/superadmin/users" target="_blank" class="quick-link">👥 User Management</a>
        </div>

        <div id="system-ready" style="display: none; text-align: center; margin: 30px 0; padding: 20px; background: #d4edda; border-radius: 8px;">
            <h2>🎉 All Systems Ready!</h2>
            <p>Your Investment System is fully operational</p>
            <a href="http://localhost:5173" class="quick-link" style="display: inline-block; margin: 10px;">Launch Application</a>
        </div>
    </div>

    <script>
        async function checkFrontend() {
            try {
                const response = await fetch('http://localhost:5173', { mode: 'no-cors' });
                updateServerStatus('frontend', true, 'Frontend server is running');
                return true;
            } catch (error) {
                updateServerStatus('frontend', false, 'Frontend server not accessible');
                return false;
            }
        }

        async function checkBackend() {
            try {
                const response = await fetch('http://localhost/Investment-System-eSkillVisor/backend/test-connection.php');
                if (response.ok) {
                    updateServerStatus('backend', true, 'Backend server is running');
                    return true;
                } else {
                    updateServerStatus('backend', false, 'Backend server returned error');
                    return false;
                }
            } catch (error) {
                updateServerStatus('backend', false, 'Backend server not accessible - Start XAMPP Apache');
                return false;
            }
        }

        async function checkDatabase() {
            try {
                const response = await fetch('http://localhost/Investment-System-eSkillVisor/backend/login.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: 'test', password: 'test' })
                });
                
                if (response.ok) {
                    updateServerStatus('database', true, 'Database server is running');
                    return true;
                } else {
                    updateServerStatus('database', false, 'Database connection failed - Start XAMPP MySQL');
                    return false;
                }
            } catch (error) {
                updateServerStatus('database', false, 'Database server not accessible - Start XAMPP MySQL');
                return false;
            }
        }

        function updateServerStatus(server, isRunning, message) {
            const statusDiv = document.getElementById(`${server}-status`);
            const badge = document.getElementById(`${server}-badge`);
            
            if (isRunning) {
                statusDiv.className = 'server-status running';
                badge.className = 'status-badge status-running';
                badge.textContent = 'Running ✅';
            } else {
                statusDiv.className = 'server-status stopped';
                badge.className = 'status-badge status-stopped';
                badge.textContent = 'Stopped ❌';
            }
            
            // Update the description
            const description = statusDiv.querySelector('p:last-of-type');
            if (description && message) {
                description.innerHTML = `<strong>Status:</strong> ${message}`;
            }
        }

        async function checkAllServers() {
            // Reset to checking state
            ['frontend', 'backend', 'database'].forEach(server => {
                const statusDiv = document.getElementById(`${server}-status`);
                const badge = document.getElementById(`${server}-badge`);
                statusDiv.className = 'server-status checking';
                badge.className = 'status-badge status-checking';
                badge.textContent = 'Checking...';
            });

            // Check all servers
            const frontendOk = await checkFrontend();
            const backendOk = await checkBackend();
            const databaseOk = await checkDatabase();

            // Show system ready message if all are running
            const systemReady = document.getElementById('system-ready');
            if (frontendOk && backendOk && databaseOk) {
                systemReady.style.display = 'block';
            } else {
                systemReady.style.display = 'none';
            }
        }

        function openXAMPP() {
            // This will attempt to open XAMPP control panel
            window.open('http://localhost/dashboard/', '_blank');
        }

        // Check servers on page load
        window.onload = function() {
            setTimeout(checkAllServers, 1000);
        };
    </script>
</body>
</html>
