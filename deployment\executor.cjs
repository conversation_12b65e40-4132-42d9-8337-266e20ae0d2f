#!/usr/bin/env node

/**
 * EskillVisor Deployment Executor
 * Executes deployment plans with automation and logging
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const readline = require('readline');

class DeploymentExecutor {
    constructor(configPath = './deployment/config.json') {
        this.config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        this.logFile = this.config.logging.file;
        this.mode = this.config.automation.defaultMode;
        this.requireConfirmation = this.config.automation.requireConfirmation;
        
        this.ensureLogDirectory();
    }

    /**
     * Execute deployment plan
     */
    async executePlan(deploymentPlan, options = {}) {
        this.mode = options.mode || this.mode;
        this.requireConfirmation = options.requireConfirmation !== undefined ? 
            options.requireConfirmation : this.requireConfirmation;

        const execution = {
            planId: this.generatePlanId(),
            timestamp: new Date().toISOString(),
            plan: deploymentPlan,
            mode: this.mode,
            status: 'started',
            steps: [],
            errors: [],
            warnings: []
        };

        try {
            this.log('info', `Starting deployment execution (${execution.planId})`);
            this.log('info', `Mode: ${this.mode}, Confirmation required: ${this.requireConfirmation}`);

            // Pre-deployment checks
            await this.preDeploymentChecks(execution);

            // Show deployment summary
            await this.showDeploymentSummary(deploymentPlan);

            // Get user confirmation if required
            if (this.requireConfirmation && this.mode !== 'fully-automatic') {
                const confirmed = await this.getUserConfirmation();
                if (!confirmed) {
                    execution.status = 'cancelled';
                    this.log('info', 'Deployment cancelled by user');
                    return execution;
                }
            }

            // Create backups
            await this.createBackups(execution);

            // Execute deployment steps
            for (const step of deploymentPlan.deploymentSteps) {
                await this.executeStep(step, execution);
            }

            // Post-deployment verification
            await this.postDeploymentVerification(execution);

            execution.status = 'completed';
            this.log('info', `Deployment completed successfully (${execution.planId})`);

        } catch (error) {
            execution.status = 'failed';
            execution.errors.push({
                timestamp: new Date().toISOString(),
                message: error.message,
                stack: error.stack
            });
            
            this.log('error', `Deployment failed: ${error.message}`);
            
            // Attempt rollback if enabled
            if (this.config.automation.enableRollback) {
                await this.executeRollback(deploymentPlan, execution);
            }
        }

        // Save execution log
        await this.saveExecutionLog(execution);
        
        return execution;
    }

    /**
     * Pre-deployment checks
     */
    async preDeploymentChecks(execution) {
        this.log('info', 'Running pre-deployment checks...');
        
        const checks = [
            { name: 'Git status clean', check: () => this.checkGitStatus() },
            { name: 'Build tools available', check: () => this.checkBuildTools() },
            { name: 'Target directories accessible', check: () => this.checkTargetDirectories() },
            { name: 'Database connectivity', check: () => this.checkDatabaseConnectivity() }
        ];

        for (const check of checks) {
            try {
                await check.check();
                this.log('info', `✓ ${check.name}`);
            } catch (error) {
                const warning = `⚠ ${check.name}: ${error.message}`;
                this.log('warn', warning);
                execution.warnings.push({
                    timestamp: new Date().toISOString(),
                    check: check.name,
                    message: error.message
                });
            }
        }
    }

    /**
     * Show deployment summary
     */
    async showDeploymentSummary(plan) {
        console.log('\n' + '='.repeat(60));
        console.log('DEPLOYMENT SUMMARY');
        console.log('='.repeat(60));
        console.log(`Deployment Type: ${plan.recommendations.deploymentType}`);
        console.log(`Estimated Time: ${plan.estimatedTime} minutes`);
        console.log(`Total Steps: ${plan.deploymentSteps.length}`);
        
        if (plan.risks.length > 0) {
            console.log('\nRISKS:');
            plan.risks.forEach(risk => {
                console.log(`  ${risk.level.toUpperCase()}: ${risk.description}`);
            });
        }

        console.log('\nSTEPS TO EXECUTE:');
        plan.deploymentSteps.forEach(step => {
            console.log(`  ${step.step}. ${step.description} (${step.estimatedTime}min)`);
        });
        console.log('='.repeat(60) + '\n');
    }

    /**
     * Get user confirmation
     */
    async getUserConfirmation() {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        return new Promise((resolve) => {
            rl.question('Do you want to proceed with this deployment? (y/N): ', (answer) => {
                rl.close();
                resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
            });
        });
    }

    /**
     * Create backups
     */
    async createBackups(execution) {
        this.log('info', 'Creating backups...');
        
        const backupDir = `deployment/backups/${execution.planId}`;
        this.ensureDirectory(backupDir);

        try {
            // Backup current frontend
            if (fs.existsSync('dist')) {
                execSync(`cp -r dist ${backupDir}/frontend`, { stdio: 'pipe' });
                this.log('info', 'Frontend backup created');
            }

            // Backup current backend (from production)
            // Note: This would need actual cPanel access in real implementation
            this.log('info', 'Backend backup noted (manual backup recommended)');

            // Backup database
            const dbBackupFile = `${backupDir}/database_${Date.now()}.sql`;
            // Note: This would need actual database access in real implementation
            this.log('info', `Database backup location: ${dbBackupFile}`);

        } catch (error) {
            this.log('warn', `Backup creation warning: ${error.message}`);
        }
    }

    /**
     * Execute individual step
     */
    async executeStep(step, execution) {
        const stepExecution = {
            step: step.step,
            type: step.type,
            startTime: new Date().toISOString(),
            status: 'running',
            output: [],
            errors: []
        };

        try {
            this.log('info', `Executing step ${step.step}: ${step.description}`);

            if (this.mode === 'manual') {
                await this.executeManualStep(step, stepExecution);
            } else {
                await this.executeAutomaticStep(step, stepExecution);
            }

            stepExecution.status = 'completed';
            stepExecution.endTime = new Date().toISOString();
            
            this.log('info', `Step ${step.step} completed successfully`);

        } catch (error) {
            stepExecution.status = 'failed';
            stepExecution.endTime = new Date().toISOString();
            stepExecution.errors.push(error.message);
            
            this.log('error', `Step ${step.step} failed: ${error.message}`);
            throw error;
        }

        execution.steps.push(stepExecution);
    }

    /**
     * Execute manual step
     */
    async executeManualStep(step, stepExecution) {
        console.log(`\nStep ${step.step}: ${step.description}`);
        console.log('Commands to execute:');
        step.commands.forEach(cmd => console.log(`  ${cmd}`));
        console.log(`Verification: ${step.verification}`);
        
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        return new Promise((resolve, reject) => {
            rl.question('Mark this step as completed? (y/N): ', (answer) => {
                rl.close();
                if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
                    stepExecution.output.push('Manual step marked as completed');
                    resolve();
                } else {
                    reject(new Error('Manual step not completed'));
                }
            });
        });
    }

    /**
     * Execute automatic step
     */
    async executeAutomaticStep(step, stepExecution) {
        for (const command of step.commands) {
            try {
                this.log('info', `Executing: ${command}`);
                
                // For safety, we'll log commands instead of executing them
                // In a real implementation, you'd execute based on step type
                if (step.type === 'frontend_build') {
                    const output = execSync(command, { encoding: 'utf8', cwd: process.cwd() });
                    stepExecution.output.push(output);
                } else {
                    // Log other commands for manual execution
                    stepExecution.output.push(`Command logged: ${command}`);
                    this.log('info', `Command logged for manual execution: ${command}`);
                }
                
            } catch (error) {
                stepExecution.errors.push(`Command failed: ${command} - ${error.message}`);
                throw error;
            }
        }
    }

    /**
     * Post-deployment verification
     */
    async postDeploymentVerification(execution) {
        this.log('info', 'Running post-deployment verification...');
        
        // Add verification results to execution log
        execution.verification = {
            timestamp: new Date().toISOString(),
            results: []
        };

        // Note: In real implementation, these would be actual verification calls
        const verifications = [
            'Frontend accessibility check',
            'Backend API health check',
            'Database connectivity check',
            'Authentication flow test'
        ];

        verifications.forEach(verification => {
            execution.verification.results.push({
                check: verification,
                status: 'manual_verification_required',
                timestamp: new Date().toISOString()
            });
        });
    }

    /**
     * Execute rollback
     */
    async executeRollback(plan, execution) {
        this.log('info', 'Executing rollback...');
        
        execution.rollback = {
            timestamp: new Date().toISOString(),
            steps: []
        };

        for (const rollbackStep of plan.rollbackPlan) {
            try {
                this.log('info', `Rollback step ${rollbackStep.step}: ${rollbackStep.description}`);
                // Log rollback commands for manual execution
                execution.rollback.steps.push({
                    step: rollbackStep.step,
                    description: rollbackStep.description,
                    command: rollbackStep.command,
                    status: 'logged_for_manual_execution'
                });
            } catch (error) {
                this.log('error', `Rollback step failed: ${error.message}`);
            }
        }
    }

    /**
     * Utility methods
     */
    generatePlanId() {
        return `deploy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    ensureLogDirectory() {
        const logDir = path.dirname(this.logFile);
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }
    }

    ensureDirectory(dir) {
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
    }

    log(level, message) {
        const timestamp = new Date().toISOString();
        const logEntry = `[${timestamp}] ${level.toUpperCase()}: ${message}`;
        
        console.log(logEntry);
        
        try {
            fs.appendFileSync(this.logFile, logEntry + '\n');
        } catch (error) {
            console.error('Failed to write to log file:', error.message);
        }
    }

    async saveExecutionLog(execution) {
        const logFile = `deployment/logs/execution_${execution.planId}.json`;
        try {
            fs.writeFileSync(logFile, JSON.stringify(execution, null, 2));
            this.log('info', `Execution log saved: ${logFile}`);
        } catch (error) {
            this.log('error', `Failed to save execution log: ${error.message}`);
        }
    }

    // Check methods (simplified for demo)
    checkGitStatus() {
        try {
            const status = execSync('git status --porcelain', { encoding: 'utf8' });
            if (status.trim()) {
                throw new Error('Git working directory not clean');
            }
        } catch (error) {
            throw new Error('Git status check failed');
        }
    }

    checkBuildTools() {
        try {
            execSync('npm --version', { stdio: 'pipe' });
        } catch (error) {
            throw new Error('npm not available');
        }
    }

    checkTargetDirectories() {
        // In real implementation, this would check cPanel directory access
        this.log('info', 'Target directory check logged (manual verification needed)');
    }

    checkDatabaseConnectivity() {
        // In real implementation, this would test database connection
        this.log('info', 'Database connectivity check logged (manual verification needed)');
    }
}

module.exports = DeploymentExecutor;

// CLI usage
if (require.main === module) {
    const ChangeDetector = require('./change-detector.cjs');
    const DecisionEngine = require('./decision-engine.cjs');

    const detector = new ChangeDetector();
    const engine = new DecisionEngine();
    const executor = new DeploymentExecutor();
    
    // Parse command line arguments
    const args = process.argv.slice(2);
    const mode = args.includes('--auto') ? 'fully-automatic' : 
                 args.includes('--manual') ? 'manual' : 'semi-automatic';
    const skipConfirmation = args.includes('--yes');
    
    detector.detectChanges()
        .then(changes => engine.analyzeChanges(changes))
        .then(plan => executor.executePlan(plan, { 
            mode, 
            requireConfirmation: !skipConfirmation 
        }))
        .then(execution => {
            console.log(`\nDeployment ${execution.status}: ${execution.planId}`);
            if (execution.status === 'failed') {
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('Deployment system error:', error);
            process.exit(1);
        });
}
