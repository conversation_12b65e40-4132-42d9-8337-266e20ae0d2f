<?php
/**
 * Run database migration for company approval fields
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

try {
    $db = Database::getInstance();
    
    echo "Running migration: Add company approval fields...\n";
    
    // Check if columns already exist
    $result = $db->fetch("SHOW COLUMNS FROM companies LIKE 'approval_status'");
    
    if (!$result) {
        // Add approval fields
        $sql = "ALTER TABLE companies 
                ADD COLUMN approval_status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending' AFTER status,
                ADD COLUMN approved_by INT NULL AFTER approval_status,
                ADD COLUMN approved_at TIMESTAMP NULL AFTER approved_by,
                ADD COLUMN rejected_by INT NULL AFTER approved_at,
                ADD COLUMN rejected_at TIMESTAMP NULL AFTER rejected_by,
                ADD COLUMN rejection_reason TEXT NULL AFTER rejected_at";
        
        $db->execute($sql);
        echo "✅ Added approval fields to companies table\n";
        
        // Add foreign key constraints
        $sql2 = "ALTER TABLE companies 
                 ADD CONSTRAINT fk_companies_approved_by FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
                 ADD CONSTRAINT fk_companies_rejected_by FOREIGN KEY (rejected_by) REFERENCES users(id) ON DELETE SET NULL";
        
        $db->execute($sql2);
        echo "✅ Added foreign key constraints\n";
        
        // Add indexes
        $sql3 = "ALTER TABLE companies 
                 ADD INDEX idx_approval_status (approval_status),
                 ADD INDEX idx_approved_by (approved_by),
                 ADD INDEX idx_rejected_by (rejected_by)";
        
        $db->execute($sql3);
        echo "✅ Added indexes\n";
        
        echo "🎉 Migration completed successfully!\n";
    } else {
        echo "ℹ️ Migration already applied - approval fields exist\n";
    }
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
