# 🚀 EskillVisor Investment System - Local Development Setup

## 📋 Prerequisites

### Required Software
- **Node.js** (v16 or higher)
- **PHP** (v7.4 or higher)
- **MySQL** (v8.0 or higher)
- **Web Server** (XAMPP, WAMP, or similar)

## 🔧 Local Environment Setup

### 1. Database Setup

#### Create Database
```sql
CREATE DATABASE eskillvisor_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### Database Configuration
- **Host**: localhost
- **Database**: eskillvisor_db
- **Username**: root
- **Password**: (empty for XAMPP/WAMP)

### 2. Backend Setup

#### Place Backend Files
1. Copy the `backend/` directory to your web server root
2. Typical paths:
   - **XAMPP**: `C:\xampp\htdocs\Investment-System-eSkillVisor\backend\`
   - **WAMP**: `C:\wamp64\www\Investment-System-eSkillVisor\backend\`

#### Configure Database Connection
The configuration is already set in `backend/config/config.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'eskillvisor_db');
define('DB_USER', 'root');
define('DB_PASS', '');
```

#### Install Database Tables
1. Navigate to: `http://localhost/Investment-System-eSkillVisor/backend/install.php`
2. This will create all necessary tables and default users

#### Test Backend API
- Test URL: `http://localhost/Investment-System-eSkillVisor/backend/`
- Should return API information or endpoints list

### 3. Frontend Setup

#### Install Dependencies
```bash
cd Investment-System-eSkillVisor
npm install
```

#### Start Development Server
```bash
npm run dev
```

#### Access Application
- Frontend: `http://localhost:5173`
- The frontend is configured to connect to the local backend automatically

## 🔐 Default User Accounts

| Role | Email | Password |
|------|-------|----------|
| Super Admin | <EMAIL> | password |
| Manager | <EMAIL> | password |
| Partner | <EMAIL> | password |

⚠️ **Change these passwords after first login!**

## 🧪 Testing the Setup

### 1. Backend API Test
- URL: `http://localhost/Investment-System-eSkillVisor/backend/`
- Should return JSON response

### 2. Database Connection Test
- Check that tables are created in `eskillvisor_db`
- Verify default users exist in `users` table

### 3. Frontend-Backend Integration
1. Open `http://localhost:5173`
2. Try logging in with admin credentials
3. Verify dashboard loads with data

## 📁 Project Structure

```
Investment-System-eSkillVisor/
├── backend/                 # PHP API backend
│   ├── config/             # Configuration files
│   ├── controllers/        # API controllers
│   ├── models/            # Data models
│   ├── services/          # Business logic
│   ├── migrations/        # Database migrations
│   └── uploads/           # File upload directory
├── src/                   # React frontend source
│   ├── components/        # React components
│   ├── pages/            # Page components
│   ├── services/         # API services
│   └── types/            # Type definitions
├── package.json          # Node.js dependencies
├── vite.config.js        # Vite configuration
└── tailwind.config.js    # Tailwind CSS configuration
```

## 🔧 Development Configuration

### API Configuration
- **Frontend API URL**: `http://localhost/Investment-System-eSkillVisor/backend`
- **CORS**: Configured for localhost:5173 and localhost:3000
- **Error Reporting**: Enabled for development

### Database Configuration
- **Environment**: Development
- **Debug Mode**: Enabled
- **Error Logging**: Enabled with full details

## 🚨 Troubleshooting

### Common Issues

#### 1. "Database Connection Failed"
- Verify MySQL is running
- Check database credentials in `backend/config/config.php`
- Ensure `eskillvisor_db` database exists

#### 2. "CORS Error"
- Verify backend is running on correct URL
- Check CORS configuration in `backend/index.php`

#### 3. "API Not Found"
- Ensure web server is running
- Verify backend path is correct
- Check `.htaccess` or URL rewriting configuration

#### 4. "Frontend Won't Load"
- Run `npm install` to install dependencies
- Check that Node.js is installed
- Verify port 5173 is available

## 📊 Development Features

### Enabled for Local Development
- ✅ **Debug Mode**: Full error reporting
- ✅ **Hot Reload**: Frontend auto-refreshes on changes
- ✅ **CORS**: Configured for local development
- ✅ **Error Logging**: Detailed error information
- ✅ **Database Debugging**: SQL query logging available

### Development Tools
- **Vite**: Fast frontend development server
- **React DevTools**: Browser extension for React debugging
- **PHP Error Logs**: Check web server error logs
- **MySQL Workbench**: Database management tool

## 🎯 Next Steps

1. **Start Development**: Begin coding new features
2. **Database Changes**: Use migration files in `backend/migrations/`
3. **API Development**: Add new endpoints in `backend/controllers/`
4. **Frontend Development**: Create new components in `src/components/`
5. **Testing**: Write tests for new functionality

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Ensure database and web server are running
4. Check browser console for frontend errors
5. Check web server error logs for backend issues

---

**Happy Coding!** 🚀
