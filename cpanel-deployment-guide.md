# EskillVisor cPanel Deployment Guide

## 🎯 **Complete cPanel Deployment with Custom Domain**

This guide provides step-by-step instructions for deploying the complete EskillVisor application (frontend + backend) to cPanel with a custom domain.

## 📁 **Deployment Package Structure**

The `cpanel-deployment` folder contains the complete application ready for upload:

```
cpanel-deployment/
└── public_html/
    ├── index.html              # Frontend entry point
    ├── assets/                 # Frontend assets (CSS, JS)
    │   ├── index-*.css
    │   ├── index-*.js
    │   ├── ui-*.js
    │   └── vendor-*.js
    └── api/                    # Backend API
        ├── config/
        │   ├── config.php
        │   └── database.php
        ├── core/
        │   └── Response.php
        ├── index.php
        ├── login.php
        ├── companies.php
        └── users.php
```

## 🌐 **Custom Domain Setup Options**

### **Option 1: Subdomain (Recommended)**
- **Domain**: `eskillvisor.wallistry.pk`
- **Easier to set up**
- **Uses existing wallistry.pk domain**

### **Option 2: Separate Domain**
- **Domain**: `eskillvisor.com` (or any custom domain)
- **Requires domain registration**
- **Complete independence**

## 🔧 **Step-by-Step Deployment**

### **Step 1: Custom Domain Configuration in cPanel**

#### **For Subdomain (eskillvisor.wallistry.pk):**

1. **Login to cPanel** (wallistry.pk/cpanel)
2. **Find "Subdomains"** in the Domains section
3. **Create Subdomain:**
   - Subdomain: `eskillvisor`
   - Domain: `wallistry.pk`
   - Document Root: `public_html/eskillvisor`
4. **Click "Create"**

#### **For Custom Domain:**

1. **Login to cPanel**
2. **Find "Addon Domains"** in the Domains section
3. **Add Domain:**
   - New Domain Name: `eskillvisor.com`
   - Subdomain: `eskillvisor`
   - Document Root: `public_html/eskillvisor`
4. **Update DNS** at your domain registrar to point to your hosting server

### **Step 2: Upload Application Files**

1. **Access cPanel File Manager**
2. **Navigate to the domain directory:**
   - For subdomain: `/public_html/eskillvisor/`
   - For addon domain: `/public_html/eskillvisor/`

3. **Upload Files:**
   - Upload all contents from `cpanel-deployment/public_html/` to your domain directory
   - Maintain the directory structure exactly as shown

4. **Set File Permissions:**
   - **HTML/CSS/JS files**: 644
   - **PHP files**: 644
   - **Directories**: 755
   - **config directory**: 755

### **Step 3: Database Configuration**

The database is already configured, but verify the connection:

1. **Check database settings** in `/api/config/config.php`
2. **Ensure database exists**: `wallistry_eskillvisor_db`
3. **Verify user permissions**: `wallistry_eskill`

### **Step 4: Test the Deployment**

1. **Visit your custom domain:**
   - `https://eskillvisor.wallistry.pk` (subdomain)
   - `https://eskillvisor.com` (custom domain)

2. **Test API endpoints:**
   - `https://eskillvisor.wallistry.pk/api/test`
   - `https://eskillvisor.wallistry.pk/api/login.php`

3. **Test application features:**
   - Login functionality
   - Dashboard loading
   - User management
   - Company management

## 🔧 **Backend CORS Configuration**

The backend is already configured to work with the custom domain. The CORS settings in `/api/config/config.php` include:

```php
define('CORS_ALLOWED_ORIGINS', [
    'https://eskillvisor.wallistry.pk',
    'https://wallistry.pk',
    'https://www.wallistry.pk',
    // ... other origins
]);
```

## 📋 **File Permissions Checklist**

After uploading, ensure these permissions:

```
eskillvisor/                    755
├── index.html                  644
├── assets/                     755
│   ├── *.css                   644
│   └── *.js                    644
└── api/                        755
    ├── config/                 755
    │   ├── config.php          644
    │   └── database.php        644
    ├── core/                   755
    │   └── Response.php        644
    ├── index.php               644
    ├── login.php               644
    ├── companies.php           644
    └── users.php               644
```

## 🧪 **Testing Checklist**

- [ ] Custom domain loads the frontend
- [ ] Login page displays correctly
- [ ] API endpoints respond (test with /api/test)
- [ ] Login functionality works
- [ ] Dashboard loads after login
- [ ] User management features work
- [ ] Company management features work
- [ ] No CORS errors in browser console
- [ ] All assets load correctly (CSS, JS)

## 🚨 **Troubleshooting**

### **Common Issues:**

1. **404 Error on Custom Domain**
   - Check domain configuration in cPanel
   - Verify DNS settings (for custom domains)
   - Ensure files are in correct directory

2. **API Not Working**
   - Check file permissions
   - Verify database connection
   - Check PHP error logs in cPanel

3. **CORS Errors**
   - Verify CORS settings in config.php
   - Check allowed origins include your domain

4. **Assets Not Loading**
   - Check file permissions (644 for files, 755 for directories)
   - Verify asset paths in index.html

### **Debug Steps:**

1. **Check PHP Error Logs** in cPanel
2. **Use Browser Developer Tools** to check network requests
3. **Test API endpoints directly** in browser
4. **Verify file permissions** in File Manager

## 📞 **Support Information**

- **Frontend URL**: Your custom domain
- **API Base URL**: Your custom domain + `/api`
- **Database**: Already configured and working
- **File Structure**: Maintained from deployment package
