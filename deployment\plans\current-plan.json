{"timestamp": "2025-07-15T17:17:48.990Z", "changeReport": {"timestamp": "2025-07-15T17:17:48.989Z", "lastDeployment": "2025-07-15T16:17:29.611Z", "changes": {"frontend": {"added": [], "modified": [{"path": "src\\pages\\manager\\Dashboard.jsx", "size": 13054, "modified": "2025-07-15T17:06:06.997Z", "previousModified": "2025-07-14T14:13:07.432Z"}, {"path": "src\\pages\\partner\\Dashboard.jsx", "size": 14187, "modified": "2025-07-15T17:08:56.289Z", "previousModified": "2025-07-14T14:13:07.435Z"}, {"path": "src\\pages\\superadmin\\Dashboard.jsx", "size": 14610, "modified": "2025-07-15T17:03:38.027Z", "previousModified": "2025-07-14T14:13:07.437Z"}, {"path": "src\\services\\api.js", "size": 7516, "modified": "2025-07-15T17:09:40.860Z", "previousModified": "2025-07-15T16:00:19.570Z"}, {"path": "src\\services\\companyService.js", "size": 4042, "modified": "2025-07-15T17:09:27.795Z", "previousModified": "2025-07-14T14:13:07.440Z"}, {"path": "src\\services\\inventoryService.js", "size": 6893, "modified": "2025-07-15T17:06:22.688Z", "previousModified": "2025-07-14T14:13:07.441Z"}, {"path": "package.json", "size": 1009, "modified": "2025-07-15T16:17:29.614Z", "previousModified": "2025-07-15T16:13:56.002Z"}], "deleted": [], "unchanged": [{"path": "src\\App.jsx", "size": 9728, "modified": "2025-07-14T19:12:37.096Z"}, {"path": "src\\components\\dashboard\\superadmin\\ActivityStream.tsx", "size": 3485, "modified": "2025-07-14T14:13:07.425Z"}, {"path": "src\\components\\dashboard\\superadmin\\ApprovalHeatmap.tsx", "size": 4166, "modified": "2025-07-14T14:13:07.425Z"}, {"path": "src\\components\\dashboard\\superadmin\\LiveStats.tsx", "size": 4913, "modified": "2025-07-14T14:13:07.425Z"}, {"path": "src\\components\\inventory\\AdvancedCompanyFilter.jsx", "size": 9591, "modified": "2025-07-14T14:13:07.425Z"}, {"path": "src\\components\\inventory\\CompanyFilter.jsx", "size": 5939, "modified": "2025-07-14T14:13:07.425Z"}, {"path": "src\\components\\inventory\\FileUpload.jsx", "size": 6895, "modified": "2025-07-14T14:13:07.427Z"}, {"path": "src\\components\\inventory\\index.js", "size": 1306, "modified": "2025-07-14T14:13:07.428Z"}, {"path": "src\\components\\inventory\\InventoryAnalytics.jsx", "size": 6797, "modified": "2025-07-14T14:13:07.427Z"}, {"path": "src\\components\\inventory\\InventoryTable.jsx", "size": 9711, "modified": "2025-07-14T14:13:07.427Z"}, {"path": "src\\components\\inventory\\InventoryUploadModal.jsx", "size": 11257, "modified": "2025-07-14T14:13:07.428Z"}, {"path": "src\\components\\layout\\MainLayout.jsx", "size": 9525, "modified": "2025-07-14T20:54:04.298Z"}, {"path": "src\\components\\modals\\AddCompanyModal.jsx", "size": 10865, "modified": "2025-07-14T21:14:03.006Z"}, {"path": "src\\components\\modals\\AddUserModal.jsx", "size": 9630, "modified": "2025-07-14T21:12:04.827Z"}, {"path": "src\\components\\ui\\FloatingParticles.tsx", "size": 1294, "modified": "2025-07-14T14:13:07.429Z"}, {"path": "src\\index.css", "size": 2705, "modified": "2025-07-14T14:13:07.430Z"}, {"path": "src\\index.jsx", "size": 217, "modified": "2025-07-14T14:13:07.430Z"}, {"path": "src\\pages\\auth\\Login.jsx", "size": 10328, "modified": "2025-07-14T19:11:32.022Z"}, {"path": "src\\pages\\auth\\PasswordReset.jsx", "size": 4430, "modified": "2025-07-14T14:13:07.431Z"}, {"path": "src\\pages\\manager\\CompanyManagement.jsx", "size": 4821, "modified": "2025-07-14T21:16:39.670Z"}, {"path": "src\\pages\\manager\\Inventory.jsx", "size": 3474, "modified": "2025-07-14T14:13:07.432Z"}, {"path": "src\\pages\\manager\\PartnerManagement.jsx", "size": 5046, "modified": "2025-07-14T21:15:16.633Z"}, {"path": "src\\pages\\notifications\\NotificationsPage.jsx", "size": 5350, "modified": "2025-07-14T14:13:07.434Z"}, {"path": "src\\pages\\partner\\CompanyList.jsx", "size": 2909, "modified": "2025-07-14T14:13:07.434Z"}, {"path": "src\\pages\\partner\\CompanyView.jsx", "size": 4775, "modified": "2025-07-14T14:13:07.435Z"}, {"path": "src\\pages\\partner\\Inventory.jsx", "size": 3428, "modified": "2025-07-14T14:13:07.435Z"}, {"path": "src\\pages\\settings\\SettingsPage.jsx", "size": 6996, "modified": "2025-07-14T14:13:07.436Z"}, {"path": "src\\pages\\superadmin\\CompanyOversight.jsx", "size": 4627, "modified": "2025-07-14T21:17:52.950Z"}, {"path": "src\\pages\\superadmin\\Inventory.jsx", "size": 5194, "modified": "2025-07-14T14:13:07.437Z"}, {"path": "src\\pages\\superadmin\\UserManagement.jsx", "size": 5272, "modified": "2025-07-14T21:17:21.125Z"}, {"path": "src\\services\\auditTrail.js", "size": 7355, "modified": "2025-07-14T14:13:07.439Z"}, {"path": "src\\services\\authService.js", "size": 4686, "modified": "2025-07-14T14:13:07.439Z"}, {"path": "src\\services\\companyMatcher.js", "size": 13067, "modified": "2025-07-14T14:13:07.439Z"}, {"path": "src\\services\\excelParser.js", "size": 13156, "modified": "2025-07-14T14:13:07.440Z"}, {"path": "src\\services\\exportService.js", "size": 9394, "modified": "2025-07-14T14:13:07.441Z"}, {"path": "src\\services\\inventoryProcessor.js", "size": 6333, "modified": "2025-07-14T14:13:07.441Z"}, {"path": "src\\services\\notificationService.js", "size": 2353, "modified": "2025-07-14T14:13:07.442Z"}, {"path": "src\\services\\pdfParser.js", "size": 12154, "modified": "2025-07-14T14:13:07.442Z"}, {"path": "src\\services\\realTimeUpdates.js", "size": 8597, "modified": "2025-07-14T14:13:07.442Z"}, {"path": "src\\services\\userService.js", "size": 2218, "modified": "2025-07-14T14:13:07.443Z"}, {"path": "src\\services\\validation.js", "size": 10980, "modified": "2025-07-14T14:13:07.443Z"}, {"path": "src\\types\\inventory.js", "size": 9030, "modified": "2025-07-14T14:13:07.443Z"}, {"path": "vite.config.js", "size": 522, "modified": "2025-07-14T14:31:49.734Z"}, {"path": "index.html", "size": 380, "modified": "2025-07-14T14:13:07.421Z"}]}, "backend": {"added": [], "modified": [{"path": "backend\\controllers\\CompanyController.php", "size": 10869, "modified": "2025-07-15T17:11:09.461Z", "previousModified": "2025-07-14T14:13:07.405Z"}, {"path": "backend\\index.php", "size": 4378, "modified": "2025-07-15T17:10:36.580Z", "previousModified": "2025-07-15T12:04:31.215Z"}], "deleted": [], "unchanged": [{"path": "backend\\.htaccess", "size": 1837, "modified": "2025-07-15T15:45:38.450Z"}, {"path": "backend\\companies.php", "size": 3020, "modified": "2025-07-15T12:03:07.785Z"}, {"path": "backend\\config\\config.example.php", "size": 2811, "modified": "2025-07-14T14:26:39.340Z"}, {"path": "backend\\config\\config.local.php", "size": 2738, "modified": "2025-07-15T15:46:45.408Z"}, {"path": "backend\\config\\config.php", "size": 2851, "modified": "2025-07-15T12:02:34.336Z"}, {"path": "backend\\config\\config.production.php", "size": 2811, "modified": "2025-07-14T14:34:13.122Z"}, {"path": "backend\\config\\database.php", "size": 5710, "modified": "2025-07-15T11:32:21.322Z"}, {"path": "backend\\config\\database.production.php", "size": 5613, "modified": "2025-07-14T15:04:03.604Z"}, {"path": "backend\\controllers\\AnalyticsController.php", "size": 8636, "modified": "2025-07-14T14:13:07.404Z"}, {"path": "backend\\controllers\\AuthController.php", "size": 8154, "modified": "2025-07-14T14:13:07.404Z"}, {"path": "backend\\controllers\\FileController.php", "size": 12436, "modified": "2025-07-14T14:13:07.405Z"}, {"path": "backend\\controllers\\InventoryController.php", "size": 10280, "modified": "2025-07-14T14:13:07.405Z"}, {"path": "backend\\controllers\\NotificationController.php", "size": 8394, "modified": "2025-07-14T14:13:07.406Z"}, {"path": "backend\\controllers\\UserController.php", "size": 6204, "modified": "2025-07-14T14:13:07.406Z"}, {"path": "backend\\core\\Auth.php", "size": 9239, "modified": "2025-07-14T14:13:07.407Z"}, {"path": "backend\\core\\Controller.php", "size": 8355, "modified": "2025-07-14T14:13:07.407Z"}, {"path": "backend\\core\\Model.php", "size": 5836, "modified": "2025-07-14T14:13:07.408Z"}, {"path": "backend\\core\\Response.php", "size": 6694, "modified": "2025-07-14T14:13:07.408Z"}, {"path": "backend\\core\\Router.php", "size": 5939, "modified": "2025-07-14T14:13:07.408Z"}, {"path": "backend\\install.php", "size": 3054, "modified": "2025-07-14T14:13:07.409Z"}, {"path": "backend\\login.php", "size": 2581, "modified": "2025-07-15T12:02:52.212Z"}, {"path": "backend\\models\\AuditTrail.php", "size": 2992, "modified": "2025-07-14T14:13:07.413Z"}, {"path": "backend\\models\\Company.php", "size": 6614, "modified": "2025-07-14T14:13:07.414Z"}, {"path": "backend\\models\\InventoryItem.php", "size": 6461, "modified": "2025-07-14T14:13:07.415Z"}, {"path": "backend\\models\\Notification.php", "size": 270, "modified": "2025-07-14T14:13:07.415Z"}, {"path": "backend\\models\\PurchaseRecord.php", "size": 2461, "modified": "2025-07-14T14:13:07.415Z"}, {"path": "backend\\models\\SalesRecord.php", "size": 2422, "modified": "2025-07-14T14:13:07.416Z"}, {"path": "backend\\models\\Transaction.php", "size": 3058, "modified": "2025-07-14T14:13:07.418Z"}, {"path": "backend\\models\\User.php", "size": 6914, "modified": "2025-07-14T14:13:07.418Z"}, {"path": "backend\\services\\CompanyMatcher.php", "size": 3108, "modified": "2025-07-14T14:13:07.419Z"}, {"path": "backend\\services\\EmailService.php", "size": 3040, "modified": "2025-07-14T14:13:07.419Z"}, {"path": "backend\\services\\ExcelParser.php", "size": 1887, "modified": "2025-07-14T14:13:07.419Z"}, {"path": "backend\\services\\PDFParser.php", "size": 1836, "modified": "2025-07-14T14:13:07.420Z"}, {"path": "backend\\setup_database.php", "size": 2259, "modified": "2025-07-14T14:13:07.420Z"}, {"path": "backend\\test.php", "size": 565, "modified": "2025-07-15T15:44:53.936Z"}, {"path": "backend\\uploads\\.gitkeep", "size": 30, "modified": "2025-07-14T14:13:07.420Z"}, {"path": "backend\\uploads\\.htaccess", "size": 184, "modified": "2025-07-14T14:30:22.171Z"}, {"path": "backend\\users.php", "size": 3348, "modified": "2025-07-15T12:03:23.245Z"}]}, "database": {"added": [], "modified": [], "deleted": [], "unchanged": [{"path": "backend\\migrations\\001_create_users_table.sql", "size": 1346, "modified": "2025-07-14T14:13:07.409Z"}, {"path": "backend\\migrations\\002_create_companies_table.sql", "size": 1458, "modified": "2025-07-14T14:13:07.410Z"}, {"path": "backend\\migrations\\003_create_company_partners_table.sql", "size": 1117, "modified": "2025-07-14T14:13:07.410Z"}, {"path": "backend\\migrations\\004_create_inventory_items_table.sql", "size": 2380, "modified": "2025-07-14T14:13:07.411Z"}, {"path": "backend\\migrations\\005_create_transactions_table.sql", "size": 1153, "modified": "2025-07-14T14:13:07.411Z"}, {"path": "backend\\migrations\\006_create_purchase_records_table.sql", "size": 1731, "modified": "2025-07-14T14:13:07.411Z"}, {"path": "backend\\migrations\\007_create_sales_records_table.sql", "size": 1546, "modified": "2025-07-14T14:13:07.411Z"}, {"path": "backend\\migrations\\008_create_audit_trail_table.sql", "size": 678, "modified": "2025-07-14T14:13:07.411Z"}, {"path": "backend\\migrations\\009_create_notifications_table.sql", "size": 1408, "modified": "2025-07-14T14:13:07.412Z"}, {"path": "backend\\migrations\\010_create_file_uploads_table.sql", "size": 1829, "modified": "2025-07-14T14:13:07.412Z"}, {"path": "backend\\migrations\\011_create_user_tokens_table.sql", "size": 640, "modified": "2025-07-14T14:13:07.413Z"}, {"path": "backend\\migrations\\migrate.php", "size": 10196, "modified": "2025-07-15T16:11:55.593Z"}]}, "config": {"added": [], "modified": [], "deleted": [], "unchanged": [{"path": "deployment/config.json", "size": 2123, "modified": "2025-07-15T16:07:38.992Z"}]}}, "summary": {"totalChanges": 9, "categories": {"frontend": {"added": 0, "modified": 7, "deleted": 0, "total": 7}, "backend": {"added": 0, "modified": 2, "deleted": 0, "total": 2}, "database": {"added": 0, "modified": 0, "deleted": 0, "total": 0}, "config": {"added": 0, "modified": 0, "deleted": 0, "total": 0}}, "requiresDeployment": true, "deploymentType": "backend-frontend"}}, "recommendations": {"deploymentType": "backend-frontend", "requiredActions": [{"type": "backend_deployment", "priority": "high", "description": "Deploy backend API changes", "target": "backend", "estimatedTime": 3, "files": [{"path": "backend\\controllers\\CompanyController.php", "size": 10869, "modified": "2025-07-15T17:11:09.461Z", "previousModified": "2025-07-14T14:13:07.405Z"}, {"path": "backend\\index.php", "size": 4378, "modified": "2025-07-15T17:10:36.580Z", "previousModified": "2025-07-15T12:04:31.215Z"}]}, {"type": "frontend_build", "priority": "high", "description": "Build frontend for production", "target": "frontend", "estimatedTime": 2}, {"type": "frontend_deployment", "priority": "high", "description": "Deploy frontend assets", "target": "frontend", "estimatedTime": 1, "files": [{"path": "src\\pages\\manager\\Dashboard.jsx", "size": 13054, "modified": "2025-07-15T17:06:06.997Z", "previousModified": "2025-07-14T14:13:07.432Z"}, {"path": "src\\pages\\partner\\Dashboard.jsx", "size": 14187, "modified": "2025-07-15T17:08:56.289Z", "previousModified": "2025-07-14T14:13:07.435Z"}, {"path": "src\\pages\\superadmin\\Dashboard.jsx", "size": 14610, "modified": "2025-07-15T17:03:38.027Z", "previousModified": "2025-07-14T14:13:07.437Z"}, {"path": "src\\services\\api.js", "size": 7516, "modified": "2025-07-15T17:09:40.860Z", "previousModified": "2025-07-15T16:00:19.570Z"}, {"path": "src\\services\\companyService.js", "size": 4042, "modified": "2025-07-15T17:09:27.795Z", "previousModified": "2025-07-14T14:13:07.440Z"}, {"path": "src\\services\\inventoryService.js", "size": 6893, "modified": "2025-07-15T17:06:22.688Z", "previousModified": "2025-07-14T14:13:07.441Z"}, {"path": "package.json", "size": 1009, "modified": "2025-07-15T16:17:29.614Z", "previousModified": "2025-07-15T16:13:56.002Z"}]}], "optionalActions": [], "dependencies": [{"type": "frontend_update", "reason": "Backend API changes may require frontend updates", "targets": ["frontend_integration"]}], "warnings": []}, "deploymentSteps": [{"step": 1, "type": "backend_deployment", "description": "Deploy backend API changes", "target": "backend", "commands": ["rsync -av --exclude=\"config.local.php\" backend/ /home9/wallistry/eskillvisor.wallistry.pk/api/", "chmod 644 /home9/wallistry/eskillvisor.wallistry.pk/api/*.php", "chmod 755 /home9/wallistry/eskillvisor.wallistry.pk/api/*/", "echo \"Backend deployment completed\""], "verification": "Test API endpoints: /api/test, /api/login.php", "rollback": "Restore previous backend files from backup", "estimatedTime": 3}, {"step": 2, "type": "frontend_build", "description": "Build frontend for production", "target": "frontend", "commands": ["npm run build", "echo \"Frontend build completed\""], "verification": "Manual verification required", "rollback": "Manual rollback required", "estimatedTime": 2}, {"step": 3, "type": "frontend_deployment", "description": "Deploy frontend assets", "target": "frontend", "commands": ["rsync -av --delete dist/ /home9/wallistry/eskillvisor.wallistry.pk/", "chmod 644 /home9/wallistry/eskillvisor.wallistry.pk/*.html", "chmod 644 /home9/wallistry/eskillvisor.wallistry.pk/assets/*", "echo \"Frontend deployment completed\""], "verification": "Verify website loads and assets are accessible", "rollback": "Restore previous frontend build from backup", "estimatedTime": 1}, {"step": 4, "type": "verification", "description": "Verify deployment success", "target": "all", "commands": ["curl -s https://eskillvisor.wallistry.pk/api/test | jq .success", "curl -s https://eskillvisor.wallistry.pk/ | grep -q \"EskillVisor\"", "echo \"Verification completed\""], "verification": "Manual testing of application functionality", "rollback": "Execute rollback plan if verification fails", "estimatedTime": 5}], "risks": [], "estimatedTime": 11, "rollbackPlan": [{"step": 1, "description": "Stop application if necessary", "command": "echo \"Application stopped for rollback\""}, {"step": 2, "description": "Restore database from backup", "command": "mysql -u wallistry_eskill -p wallistry_eskillvisor_db < deployment/backups/latest_backup.sql"}, {"step": 3, "description": "Restore backend files", "command": "rsync -av deployment/backups/backend/ /home9/wallistry/eskillvisor.wallistry.pk/api/"}, {"step": 4, "description": "Restore frontend files", "command": "rsync -av deployment/backups/frontend/ /home9/wallistry/eskillvisor.wallistry.pk/"}, {"step": 5, "description": "Verify rollback success", "command": "curl -s https://eskillvisor.wallistry.pk/api/test"}]}