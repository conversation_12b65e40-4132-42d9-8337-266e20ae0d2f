<!DOCTYPE html>
<html>
<head>
    <title>API Debug Tool - Investment System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-result { margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 4px solid #ccc; }
        .success { background: #d4edda; border-left-color: #28a745; }
        .error { background: #f8d7da; border-left-color: #dc3545; }
        .info { background: #d1ecf1; border-left-color: #17a2b8; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; max-height: 300px; }
        .endpoint-test { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 8px; border: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API Debug Tool</h1>
        <p>Testing the exact API calls that are failing in the frontend</p>
        
        <div class="endpoint-test">
            <h3>🔐 Authentication Test</h3>
            <button onclick="testLogin()">Test Login</button>
            <div id="login-result"></div>
        </div>
        
        <div class="endpoint-test">
            <h3>👥 User Profile Test</h3>
            <button onclick="testUserProfile()">Test User Profile API</button>
            <div id="profile-result"></div>
        </div>
        
        <div class="endpoint-test">
            <h3>🏢 Company Creation Test</h3>
            <button onclick="testCompanyCreation()">Test Company Creation</button>
            <div id="company-result"></div>
        </div>
        
        <div class="endpoint-test">
            <h3>📋 All Endpoints Test</h3>
            <button onclick="testAllEndpoints()">Test All Critical Endpoints</button>
            <div id="all-result"></div>
        </div>
        
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <script>
        const API_BASE = 'http://localhost/Investment-System-eSkillVisor/backend';
        let authToken = null;
        
        function addResult(containerId, title, status, message, details = null) {
            const container = document.getElementById(containerId);
            const statusClass = status === 'success' ? 'success' : status === 'error' ? 'error' : 'info';
            const icon = status === 'success' ? '✅' : status === 'error' ? '❌' : 'ℹ️';
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${statusClass}`;
            resultDiv.innerHTML = `
                <h4>${icon} ${title}</h4>
                <p>${message}</p>
                ${details ? `<pre>${JSON.stringify(details, null, 2)}</pre>` : ''}
            `;
            
            container.appendChild(resultDiv);
        }
        
        function clearResults() {
            ['login-result', 'profile-result', 'company-result', 'all-result'].forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
        }
        
        async function testLogin() {
            const container = 'login-result';
            document.getElementById(container).innerHTML = '';
            
            try {
                addResult(container, 'Login Test', 'info', 'Testing login endpoint...');
                
                const response = await fetch(`${API_BASE}/login.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password'
                    })
                });
                
                const responseText = await response.text();
                console.log('Login response text:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    addResult(container, 'Login Response Parse Error', 'error', 
                        'Server returned invalid JSON', {
                            responseText: responseText.substring(0, 500),
                            parseError: parseError.message
                        });
                    return;
                }
                
                if (data.success) {
                    authToken = data.data.access_token;
                    addResult(container, 'Login Success', 'success', 
                        'Authentication successful', {
                            user: data.data.user,
                            tokenLength: authToken.length
                        });
                } else {
                    addResult(container, 'Login Failed', 'error', data.message, data);
                }
                
            } catch (error) {
                addResult(container, 'Login Error', 'error', 
                    `Network or server error: ${error.message}`, {
                        error: error.toString(),
                        stack: error.stack
                    });
            }
        }
        
        async function testUserProfile() {
            const container = 'profile-result';
            document.getElementById(container).innerHTML = '';
            
            if (!authToken) {
                addResult(container, 'Authentication Required', 'error', 
                    'Please run login test first to get auth token');
                return;
            }
            
            try {
                addResult(container, 'User Profile Test', 'info', 'Testing user profile endpoint...');
                
                // Test user-profile.php endpoint
                const response = await fetch(`${API_BASE}/user-profile.php?id=1`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const responseText = await response.text();
                console.log('Profile response text:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    addResult(container, 'Profile Response Parse Error', 'error', 
                        'Server returned invalid JSON', {
                            responseText: responseText.substring(0, 500),
                            parseError: parseError.message
                        });
                    return;
                }
                
                if (data.success) {
                    addResult(container, 'Profile Success', 'success', 
                        'User profile loaded successfully', {
                            user: data.data.user,
                            hasCompanies: !!data.data.assigned_companies,
                            companiesCount: data.data.assigned_companies?.length || 0
                        });
                } else {
                    addResult(container, 'Profile Failed', 'error', data.message, data);
                }
                
            } catch (error) {
                addResult(container, 'Profile Error', 'error', 
                    `Network or server error: ${error.message}`, {
                        error: error.toString()
                    });
            }
        }
        
        async function testCompanyCreation() {
            const container = 'company-result';
            document.getElementById(container).innerHTML = '';
            
            if (!authToken) {
                addResult(container, 'Authentication Required', 'error', 
                    'Please run login test first to get auth token');
                return;
            }
            
            try {
                addResult(container, 'Company Creation Test', 'info', 'Testing company creation endpoint...');
                
                const companyData = {
                    name: `Debug Test Company ${Date.now()}`,
                    companyDirector: 'Debug Test Director',
                    registrationTerritory: 'United States',
                    einNumber: '12-3456789',
                    marketplace: 'Amazon',
                    description: 'Debug test company',
                    industry: 'Technology',
                    email: `debug${Date.now()}@test.com`,
                    phone: '+1234567890',
                    partner_id: 1,
                    created_by: 1
                };
                
                const response = await fetch(`${API_BASE}/companies.php`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(companyData)
                });
                
                const responseText = await response.text();
                console.log('Company response text:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    addResult(container, 'Company Response Parse Error', 'error', 
                        'Server returned invalid JSON', {
                            responseText: responseText.substring(0, 500),
                            parseError: parseError.message
                        });
                    return;
                }
                
                if (data.success) {
                    addResult(container, 'Company Creation Success', 'success', 
                        'Company created successfully', {
                            company: data.data,
                            message: data.message
                        });
                } else {
                    addResult(container, 'Company Creation Failed', 'error', data.message, data);
                }
                
            } catch (error) {
                addResult(container, 'Company Creation Error', 'error', 
                    `Network or server error: ${error.message}`, {
                        error: error.toString()
                    });
            }
        }
        
        async function testAllEndpoints() {
            const container = 'all-result';
            document.getElementById(container).innerHTML = '';
            
            addResult(container, 'Complete API Test', 'info', 'Testing all critical endpoints...');
            
            // Test login first
            await testLogin();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            if (authToken) {
                // Test user profile
                await testUserProfile();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // Test company creation
                await testCompanyCreation();
                
                addResult(container, 'All Tests Complete', 'success', 
                    'All API endpoints tested. Check individual results above.');
            } else {
                addResult(container, 'Test Sequence Failed', 'error', 
                    'Could not complete all tests due to authentication failure');
            }
        }
        
        // Auto-run basic test on page load
        window.onload = function() {
            setTimeout(() => {
                testLogin();
            }, 1000);
        };
    </script>
</body>
</html>
