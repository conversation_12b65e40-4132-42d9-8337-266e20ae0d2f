#!/usr/bin/env node

/**
 * EskillVisor Force Upload to Production
 * Directly uploads enhanced dashboards to cPanel production server
 */

const fs = require('fs');
const path = require('path');
const { Client } = require('basic-ftp');

class ForceUploader {
    constructor() {
        this.credentials = this.loadCredentials();
        this.startTime = Date.now();
    }

    /**
     * Load credentials
     */
    loadCredentials() {
        try {
            const envPath = path.join(__dirname, '.env');
            const envContent = fs.readFileSync(envPath, 'utf8');
            const credentials = {};
            
            envContent.split('\n').forEach(line => {
                const [key, value] = line.split('=');
                if (key && value && !key.startsWith('#')) {
                    credentials[key.trim()] = value.trim();
                }
            });

            return credentials;
        } catch (error) {
            throw new Error(`Failed to load credentials: ${error.message}`);
        }
    }

    /**
     * Force upload enhanced dashboards to production
     */
    async forceUpload() {
        console.log('🚀 FORCE UPLOADING ENHANCED DASHBOARDS TO PRODUCTION');
        console.log('=' .repeat(60));
        console.log('Target: https://eskillvisor.wallistry.pk');
        console.log('Server: /home9/wallistry/eskillvisor.wallistry.pk/');
        console.log('');

        try {
            // Verify deployment package exists
            if (!fs.existsSync('cpanel-deployment/public_html')) {
                throw new Error('Deployment package not found. Run npm run auto-deploy first.');
            }

            console.log('📦 Deployment package found');
            console.log('🔗 Connecting to cPanel server...');

            await this.uploadToCPanel();

            const duration = Math.round((Date.now() - this.startTime) / 1000);
            console.log('');
            console.log('🎉 ENHANCED DASHBOARDS DEPLOYED SUCCESSFULLY!');
            console.log('=' .repeat(60));
            console.log(`Duration: ${duration} seconds`);
            console.log('');
            console.log('🌐 LIVE WEBSITE: https://eskillvisor.wallistry.pk');
            console.log('🔧 API TEST: https://eskillvisor.wallistry.pk/api/test');
            console.log('');
            console.log('🎨 ENHANCED FEATURES NOW LIVE:');
            console.log('   ✅ Beautiful Super Admin Dashboard with business intelligence');
            console.log('   ✅ Enhanced Manager Dashboard with company performance');
            console.log('   ✅ Improved Partner Dashboard with portfolio management');
            console.log('   ✅ New Analytics APIs for real-time dashboard data');
            console.log('   ✅ Modern gradient designs with interactive elements');
            console.log('');
            console.log('🎯 Test the enhanced dashboards now!');

        } catch (error) {
            console.error('❌ UPLOAD FAILED!');
            console.error('=' .repeat(60));
            console.error(`Error: ${error.message}`);
            console.error('');
            console.error('📋 MANUAL UPLOAD INSTRUCTIONS:');
            console.error('1. Open cPanel File Manager');
            console.error('2. Navigate to: /home9/wallistry/eskillvisor.wallistry.pk/');
            console.error('3. Upload ALL contents from: cpanel-deployment/public_html/');
            console.error('4. Overwrite existing files when prompted');
            console.error('5. Test: https://eskillvisor.wallistry.pk');
            throw error;
        }
    }

    /**
     * Upload to cPanel with enhanced reliability
     */
    async uploadToCPanel() {
        const client = new Client();
        client.ftp.timeout = 60000;
        client.ftp.verbose = true;

        try {
            // Connect with multiple strategies
            console.log('   🔄 Attempting connection...');
            
            await client.access({
                host: this.credentials.CPANEL_HOST,
                port: parseInt(this.credentials.CPANEL_PORT) || 21,
                user: this.credentials.CPANEL_USERNAME,
                password: this.credentials.CPANEL_PASSWORD,
                secure: false
            });

            console.log('   ✅ Connected successfully');

            // Navigate to target directory
            const targetPath = '/home9/wallistry/eskillvisor.wallistry.pk';
            await client.cd(targetPath);
            console.log(`   📁 Changed to: ${targetPath}`);

            // Upload all files
            console.log('   📤 Uploading enhanced dashboards...');
            await this.uploadDirectory(client, 'cpanel-deployment/public_html', '.');

            console.log('   ✅ Upload completed successfully');

        } finally {
            try {
                client.close();
                console.log('   📡 Connection closed');
            } catch (closeError) {
                // Ignore close errors
            }
        }
    }

    /**
     * Upload directory recursively
     */
    async uploadDirectory(client, localPath, remotePath) {
        const items = fs.readdirSync(localPath);
        let uploadCount = 0;

        for (const item of items) {
            const localItemPath = path.join(localPath, item);
            const remoteItemPath = remotePath === '.' ? item : `${remotePath}/${item}`;
            
            const stat = fs.statSync(localItemPath);
            
            if (stat.isDirectory()) {
                // Create directory and upload contents
                try {
                    await client.ensureDir(remoteItemPath);
                    console.log(`     📁 Directory: ${remoteItemPath}`);
                } catch (error) {
                    // Directory might exist
                }
                
                await this.uploadDirectory(client, localItemPath, remoteItemPath);
            } else {
                // Upload file
                try {
                    await client.uploadFrom(localItemPath, remoteItemPath);
                    console.log(`     📄 Uploaded: ${remoteItemPath} (${this.formatFileSize(stat.size)})`);
                    uploadCount++;
                } catch (uploadError) {
                    console.log(`     ❌ Failed: ${remoteItemPath} - ${uploadError.message}`);
                }
            }
        }

        if (remotePath === '.') {
            console.log(`   📊 Total files uploaded: ${uploadCount}`);
        }
    }

    /**
     * Format file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
}

// CLI usage
if (require.main === module) {
    const uploader = new ForceUploader();
    
    uploader.forceUpload()
        .then(() => {
            console.log('🎉 Enhanced dashboards are now live!');
            process.exit(0);
        })
        .catch(error => {
            console.error('💥 Upload failed:', error.message);
            process.exit(1);
        });
}

module.exports = ForceUploader;
