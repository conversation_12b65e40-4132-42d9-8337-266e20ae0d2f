#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');
const { execSync } = require('child_process');

class UniversalDeploymentInstaller {
    constructor() {
        this.basePath = __dirname;
        this.errors = [];
        this.warnings = [];
    }

    async install() {
        console.log('🚀 Installing Universal Deployment System...\n');

        try {
            await this.checkPrerequisites();
            await this.setupDirectories();
            await this.installDependencies();
            await this.createConfigFiles();
            await this.validateInstallation();
            await this.showCompletionMessage();
        } catch (error) {
            console.error('❌ Installation failed:', error.message);
            process.exit(1);
        }
    }

    async checkPrerequisites() {
        console.log('🔍 Checking prerequisites...');

        const nodeVersion = process.version;
        const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
        
        if (majorVersion < 14) {
            throw new Error(`Node.js 14+ required, found ${nodeVersion}`);
        }
        console.log(`✅ Node.js ${nodeVersion} detected`);

        try {
            const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
            console.log(`✅ npm ${npmVersion} detected`);
        } catch (error) {
            throw new Error('npm not found. Please install Node.js with npm.');
        }

        try {
            execSync('electron --version', { encoding: 'utf8', stdio: 'ignore' });
            console.log('✅ Electron found globally');
        } catch (error) {
            this.warnings.push('Electron not found globally - will install locally');
        }

        console.log('');
    }

    async setupDirectories() {
        console.log('📁 Setting up directory structure...');

        const directories = [
            'src/core',
            'src/gui', 
            'src/analyzers',
            'src/generators',
            'src/utils',
            'config',
            'templates',
            'output',
            'output/packages',
            'output/documentation',
            'output/history',
            'output/logs',
            'assets'
        ];

        for (const dir of directories) {
            const dirPath = path.join(this.basePath, dir);
            await fs.ensureDir(dirPath);
            console.log(`  📂 Created: ${dir}`);
        }

        console.log('');
    }

    async installDependencies() {
        console.log('📦 Installing dependencies...');

        try {
            console.log('  Installing npm packages...');
            execSync('npm install', { 
                cwd: this.basePath, 
                stdio: 'inherit' 
            });
            console.log('✅ Dependencies installed successfully');
        } catch (error) {
            throw new Error('Failed to install dependencies: ' + error.message);
        }

        console.log('');
    }

    async createConfigFiles() {
        console.log('⚙️ Creating configuration files...');

        await this.createGitignore();
        await this.createExampleConfig();

        if (process.platform === 'linux') {
            await this.createDesktopEntry();
        }

        console.log('');
    }

    async createGitignore() {
        const gitignorePath = path.join(this.basePath, '.gitignore');
        
        if (!await fs.pathExists(gitignorePath)) {
            const gitignoreContent = `node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

output/packages/
output/logs/
dist/
build/

.env
.env.local
.env.development.local
.env.test.local
.env.production.local

.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

.vscode/
.idea/
*.swp
*.swo

*.tmp
*.temp
tmp/
temp/

*.log
logs/

coverage/
.nyc_output/

out/`;

            await fs.writeFile(gitignorePath, gitignoreContent);
            console.log('  📝 Created .gitignore');
        }
    }

    async createExampleConfig() {
        const configPath = path.join(this.basePath, 'config/example-project.json');
        
        const exampleConfig = {
            projectName: 'Example Project',
            projectType: 'web-application',
            deployment: {
                target: 'cpanel',
                domain: 'example.com',
                username: 'your-username',
                serverPath: '/public_html',
                ftpHost: 'ftp.example.com',
                ftpPort: 21
            },
            fileCategories: {
                frontend: {
                    extensions: ['.html', '.css', '.js', '.jsx', '.ts', '.tsx', '.vue'],
                    directories: ['src', 'public', 'dist', 'build'],
                    description: 'Frontend application files'
                },
                backend: {
                    extensions: ['.php', '.py', '.rb', '.java'],
                    directories: ['api', 'server', 'backend'],
                    description: 'Backend application logic'
                },
                assets: {
                    extensions: ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico'],
                    directories: ['images', 'assets', 'media'],
                    description: 'Static assets and media files'
                }
            },
            excludePatterns: [
                'node_modules/**',
                '.git/**',
                '*.log',
                '.env'
            ]
        };

        await fs.writeJson(configPath, exampleConfig, { spaces: 2 });
        console.log('  📝 Created example project configuration');
    }

    async createDesktopEntry() {
        try {
            const desktopEntry = `[Desktop Entry]
Version=1.0
Type=Application
Name=Universal Deployment System
Comment=Professional deployment automation for web projects
Exec=electron ${this.basePath}/src/main.js
Icon=${this.basePath}/assets/icon.png
Terminal=false
Categories=Development;`;

            const desktopPath = path.join(process.env.HOME, '.local/share/applications/universal-deployment-system.desktop');
            await fs.ensureDir(path.dirname(desktopPath));
            await fs.writeFile(desktopPath, desktopEntry);
            await fs.chmod(desktopPath, '755');
            
            console.log('  🖥️ Created desktop entry');
        } catch (error) {
            this.warnings.push('Could not create desktop entry: ' + error.message);
        }
    }

    async validateInstallation() {
        console.log('🔍 Validating installation...');

        try {
            const SystemValidator = require('./src/utils/SystemValidator');
            const validator = new SystemValidator();
            
            console.log('  Running system validation...');
            const results = await validator.validateSystem();
            
            if (results.failed > 0) {
                this.warnings.push(`${results.failed} validation tests failed`);
            } else {
                console.log('✅ All validation tests passed');
            }
        } catch (error) {
            this.warnings.push('Could not run full validation: ' + error.message);
        }

        console.log('');
    }

    async showCompletionMessage() {
        console.log('🎉 Installation completed successfully!\n');

        console.log('📋 Quick Start:');
        console.log('  1. Start the application:');
        console.log('     npm start');
        console.log('');
        console.log('  2. Or run in development mode:');
        console.log('     npm run dev');
        console.log('');
        console.log('  3. Run validation tests:');
        console.log('     npm run validate');
        console.log('');

        if (this.warnings.length > 0) {
            console.log('⚠️ Warnings:');
            this.warnings.forEach(warning => {
                console.log(`  - ${warning}`);
            });
            console.log('');
        }

        console.log('🚀 Universal Deployment System is ready to use!');
    }
}

if (require.main === module) {
    const installer = new UniversalDeploymentInstaller();
    installer.install().catch(error => {
        console.error('Installation failed:', error);
        process.exit(1);
    });
}

module.exports = UniversalDeploymentInstaller;

