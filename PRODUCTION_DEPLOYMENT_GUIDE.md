# Investment System - Production Deployment Guide 🚀

## 📋 **Overview**

This guide covers the complete deployment of the Investment System to production on **https://eskillvisor.wallistry.pk** using cPanel hosting.

## 🛠 **Prerequisites**

### **Local Development Environment:**
- Node.js (v16 or higher)
- npm or yarn
- Git
- Code editor

### **Production Environment:**
- cPanel hosting account
- MySQL database access
- Domain: eskillvisor.wallistry.pk
- SSL certificate (recommended)

## 🔧 **Environment Configuration**

### **Local Development (.env.local):**
```env
VITE_APP_ENV=local
VITE_API_BASE_URL=http://localhost/Investment-System-eSkillVisor/backend
VITE_APP_BASE_URL=http://localhost:5173
VITE_DEBUG_MODE=true
```

### **Production (.env.production):**
```env
VITE_APP_ENV=production
VITE_API_BASE_URL=https://eskillvisor.wallistry.pk/backend
VITE_APP_BASE_URL=https://eskillvisor.wallistry.pk
VITE_DEBUG_MODE=false
```

## 🏗 **Build Process**

### **1. Production Build Command:**
```bash
# Build for production
npm run build:production

# Or for local testing
npm run build:local
```

### **2. Build Output:**
```
dist/
├── frontend/          # Upload to public_html/
│   ├── index.html
│   ├── .htaccess
│   └── assets/
├── backend/           # Upload to public_html/backend/
│   ├── .htaccess
│   ├── config/
│   ├── *.php
│   ├── uploads/
│   └── logs/
├── database/          # Import to MySQL
│   └── investment_system_schema.sql
└── DEPLOYMENT_INSTRUCTIONS.md
```

## 📁 **cPanel Deployment Steps**

### **Step 1: Database Setup**

1. **Create MySQL Database:**
   - Login to cPanel
   - Go to "MySQL Databases"
   - Create database: `wallistry_investment_system`
   - Create user: `wallistry_investment_user`
   - Assign user to database with ALL PRIVILEGES

2. **Import Database Schema:**
   - Go to phpMyAdmin
   - Select your database
   - Import `dist/database/investment_system_schema.sql`

### **Step 2: Backend Deployment**

1. **Create Backend Directory:**
   - In File Manager, create folder: `public_html/backend/`

2. **Upload Backend Files:**
   - Upload all contents of `dist/backend/` to `public_html/backend/`

3. **Set File Permissions:**
   ```
   backend/uploads/     → 755
   backend/logs/        → 755
   backend/config/      → 644
   backend/*.php        → 644
   ```

4. **Update Configuration:**
   - Edit `backend/config/config.php`
   - Update database credentials:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'wallistry_investment_system');
   define('DB_USER', 'wallistry_investment_user');
   define('DB_PASS', 'your_actual_password');
   ```

### **Step 3: Frontend Deployment**

1. **Upload Frontend Files:**
   - Upload all contents of `dist/frontend/` to `public_html/`
   - Ensure `.htaccess` is uploaded for SPA routing

2. **Verify File Structure:**
   ```
   public_html/
   ├── index.html
   ├── .htaccess
   ├── assets/
   └── backend/
   ```

## 🧪 **Testing Deployment**

### **1. Backend API Test:**
```bash
# Test companies API
curl https://eskillvisor.wallistry.pk/backend/companies.php

# Should return JSON response
```

### **2. Frontend Test:**
- Visit: https://eskillvisor.wallistry.pk
- Should load login page
- Test login with admin credentials

### **3. Complete Workflow Test:**
1. Login as admin
2. Navigate to Company Management
3. Create a new company
4. Upload documents
5. Test all enhanced features

## 🔄 **Development Workflow**

### **Local Development:**
```bash
# Start local development
npm run dev

# Backend runs on: http://localhost/Investment-System-eSkillVisor/backend
# Frontend runs on: http://localhost:5173
```

### **Production Updates:**
```bash
# Make changes locally
# Test thoroughly

# Build for production
npm run build:production

# Upload changed files to cPanel
# Test production deployment
```

### **Quick Update Script:**
```bash
# Use the update script for minor changes
npm run update:production

# Then upload only the changed files
```

## 🔐 **Security Configuration**

### **1. Update Default Credentials:**
```sql
-- Change default admin password
UPDATE users SET password = '$2y$10$newhashedpassword' WHERE email = '<EMAIL>';
```

### **2. Production Security Settings:**
- Update JWT secret in `config.php`
- Update encryption keys
- Enable HTTPS redirect
- Configure proper CORS headers

### **3. File Permissions:**
```bash
# Set secure permissions
chmod 644 backend/*.php
chmod 755 backend/uploads/
chmod 755 backend/logs/
chmod 600 backend/config/config.php
```

## 📊 **Monitoring & Maintenance**

### **1. Log Files:**
- PHP errors: `backend/logs/php_errors.log`
- Application logs: `backend/logs/app.log`

### **2. Database Backup:**
```bash
# Regular database backups via cPanel
# Or automated backup scripts
```

### **3. Performance Monitoring:**
- Monitor API response times
- Check database query performance
- Monitor file upload functionality

## 🚨 **Troubleshooting**

### **Common Issues:**

1. **API Returns 500 Error:**
   - Check PHP error logs
   - Verify database connection
   - Check file permissions

2. **Frontend Shows Blank Page:**
   - Verify .htaccess file
   - Check browser console for errors
   - Ensure all assets uploaded correctly

3. **File Upload Fails:**
   - Check uploads directory permissions (755)
   - Verify PHP upload settings
   - Check file size limits

4. **Database Connection Failed:**
   - Verify database credentials
   - Check database user privileges
   - Ensure database exists

### **Debug Mode:**
```php
// Temporarily enable debug mode in config.php
define('DEBUG_MODE', true);
define('DISPLAY_ERRORS', true);
```

## 📞 **Support & Maintenance**

### **Regular Maintenance Tasks:**
1. **Weekly:**
   - Check error logs
   - Monitor disk space
   - Verify backup integrity

2. **Monthly:**
   - Update dependencies
   - Review security logs
   - Performance optimization

3. **Quarterly:**
   - Security audit
   - Database optimization
   - Feature updates

### **Emergency Procedures:**
1. **Site Down:**
   - Check server status
   - Verify DNS settings
   - Check error logs

2. **Data Loss:**
   - Restore from backup
   - Check database integrity
   - Verify file uploads

## 🎯 **Production Checklist**

### **Pre-Deployment:**
- [ ] Local testing completed
- [ ] Production build successful
- [ ] Database schema updated
- [ ] Configuration files updated
- [ ] Security settings configured

### **Deployment:**
- [ ] Database created and imported
- [ ] Backend files uploaded
- [ ] Frontend files uploaded
- [ ] File permissions set
- [ ] Configuration updated

### **Post-Deployment:**
- [ ] API endpoints tested
- [ ] Frontend functionality verified
- [ ] Login system working
- [ ] File upload tested
- [ ] All features functional

### **Go-Live:**
- [ ] DNS configured
- [ ] SSL certificate active
- [ ] Monitoring enabled
- [ ] Backup procedures active
- [ ] Team notified

## 🌐 **Production URLs**

- **Frontend:** https://eskillvisor.wallistry.pk
- **Backend API:** https://eskillvisor.wallistry.pk/backend/
- **Admin Login:** https://eskillvisor.wallistry.pk/login

**Default Admin Credentials:**
- Email: <EMAIL>
- Password: password (CHANGE IMMEDIATELY)

---

**🎉 Your Investment System is now ready for production deployment on eskillvisor.wallistry.pk!**
