# EskillVisor Master Deployment Guide

## 🎯 **Complete cPanel Deployment with Custom Domain**

This is the master guide for deploying the complete EskillVisor application to cPanel with a custom domain.

## 📋 **Quick Start**

1. **Run deployment script**: `./deploy.sh`
2. **Select option 4**: Create cPanel Deployment Package
3. **Upload to cPanel**: Follow the upload instructions
4. **Configure domain**: Set up eskillvisor.wallistry.pk
5. **Test application**: Verify everything works

## 📚 **Documentation Structure**

### **Core Deployment Files**
- `DEPLOYMENT-MASTER-GUIDE.md` - This master guide
- `cpanel-deployment-guide.md` - Detailed cPanel upload instructions
- `dns-configuration-guide.md` - Domain and DNS setup
- `testing-verification-guide.md` - Complete testing checklist
- `vercel-cleanup-guide.md` - Removing Vercel deployment

### **Deployment Tools**
- `deploy.sh` - Interactive deployment script
- `cpanel-deployment/` - Generated deployment package

## 🚀 **Step-by-Step Deployment Process**

### **Phase 1: Prepare Deployment Package**

1. **Run the deployment script:**
   ```bash
   ./deploy.sh
   ```

2. **Select option 4: "Create cPanel Deployment Package"**
   - Builds frontend for production
   - Copies backend files
   - Creates unified deployment package

3. **Verify package (optional):**
   - Select option 5: "Verify Deployment Package"
   - Confirms all files are present

### **Phase 2: Domain Configuration**

1. **Set up subdomain in cPanel:**
   - Login to cPanel (wallistry.pk/cpanel)
   - Go to "Subdomains"
   - Create: `eskillvisor.wallistry.pk`
   - Document root: `public_html/eskillvisor`

2. **Alternative: Custom domain setup**
   - See `dns-configuration-guide.md` for details
   - Requires domain registration and DNS configuration

### **Phase 3: File Upload**

1. **Access cPanel File Manager**
2. **Navigate to domain directory:**
   - `/public_html/eskillvisor/` (for subdomain)
3. **Upload deployment package:**
   - Upload all contents from `cpanel-deployment/public_html/`
   - Maintain directory structure
4. **Set file permissions:**
   - Files: 644
   - Directories: 755

### **Phase 4: Testing & Verification**

1. **Basic connectivity:**
   - Visit: https://eskillvisor.wallistry.pk
   - Check SSL certificate
   - Verify frontend loads

2. **API testing:**
   - Test: https://eskillvisor.wallistry.pk/api/test
   - Verify API responses

3. **Application testing:**
   - Login with default accounts
   - Test all functionality
   - See `testing-verification-guide.md`

### **Phase 5: Cleanup (Optional)**

1. **Remove Vercel deployment:**
   - See `vercel-cleanup-guide.md`
   - Pause or delete Vercel project
   - Update documentation

## 🏗️ **Application Architecture**

### **Frontend (React)**
- **Location**: Domain root (`/`)
- **Built with**: Vite + React
- **Assets**: `/assets/` directory
- **Entry point**: `index.html`

### **Backend (PHP)**
- **Location**: `/api/` subdirectory
- **Database**: MySQL (wallistry_eskillvisor_db)
- **Endpoints**: Direct PHP files (login.php, companies.php, etc.)
- **Configuration**: `/api/config/` directory

### **Domain Structure**
```
https://eskillvisor.wallistry.pk/
├── index.html (Frontend)
├── assets/ (Frontend assets)
└── api/ (Backend API)
    ├── config/
    ├── core/
    ├── login.php
    ├── companies.php
    └── users.php
```

## 🔧 **Configuration Details**

### **Frontend API Configuration**
- **API Base URL**: Automatically detects domain
- **Custom domain**: Uses same domain + `/api`
- **CORS**: Configured for custom domain

### **Backend CORS Settings**
- **Allowed origins**: Includes custom domain
- **Headers**: Properly configured
- **Methods**: GET, POST, PUT, DELETE, OPTIONS

### **Database Configuration**
- **Host**: localhost
- **Database**: wallistry_eskillvisor_db
- **User**: wallistry_eskill
- **Already configured and working**

## 🎯 **Default Login Accounts**

```
Super Admin:
- Email: <EMAIL>
- Password: password

Manager:
- Email: <EMAIL>
- Password: password

Partner:
- Email: <EMAIL>
- Password: password
```

## 🚨 **Troubleshooting Quick Reference**

### **Common Issues**

1. **Domain not loading**
   - Check domain configuration in cPanel
   - Verify DNS propagation
   - Confirm file upload to correct directory

2. **API not responding**
   - Check file permissions (644/755)
   - Verify database connection
   - Check PHP error logs

3. **CORS errors**
   - Verify CORS settings in backend files
   - Clear browser cache
   - Check allowed origins

4. **SSL certificate issues**
   - Wait for domain propagation
   - Request SSL in cPanel
   - Check certificate status

### **Debug Tools**

```bash
# Test domain resolution
nslookup eskillvisor.wallistry.pk

# Test HTTP response
curl -I https://eskillvisor.wallistry.pk

# Test API endpoint
curl https://eskillvisor.wallistry.pk/api/test
```

## 📞 **Support Resources**

### **Documentation**
- `cpanel-deployment-guide.md` - Detailed upload instructions
- `dns-configuration-guide.md` - Domain setup
- `testing-verification-guide.md` - Testing procedures

### **Tools**
- `deploy.sh` - Deployment automation
- Browser Developer Tools - Debugging
- cPanel Error Logs - Server-side issues

### **External Resources**
- SSL Test: https://www.ssllabs.com/ssltest/
- DNS Checker: https://dnschecker.org/
- Performance Test: https://pagespeed.web.dev/

## ✅ **Deployment Checklist**

- [ ] Deployment package created
- [ ] Domain configured in cPanel
- [ ] Files uploaded to correct directory
- [ ] File permissions set (644/755)
- [ ] SSL certificate active
- [ ] Frontend loads correctly
- [ ] API endpoints respond
- [ ] Authentication works
- [ ] All features tested
- [ ] Vercel cleanup completed (optional)

## 🎉 **Success Indicators**

When deployment is successful, you should have:

1. **Working application** at https://eskillvisor.wallistry.pk
2. **Functional API** at https://eskillvisor.wallistry.pk/api
3. **Valid SSL certificate** (green lock icon)
4. **All features working** (login, user management, company management)
5. **No console errors** in browser developer tools
6. **Fast loading times** and good performance

## 📈 **Next Steps After Deployment**

1. **Update bookmarks** and documentation
2. **Inform team members** of new URL
3. **Monitor performance** and error logs
4. **Set up regular backups** in cPanel
5. **Plan for future updates** using the deployment workflow

---

**🎯 Your EskillVisor application is now fully deployed on cPanel with a custom domain!**
