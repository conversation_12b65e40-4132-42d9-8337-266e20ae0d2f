/**
 * Test script to verify the complete login flow
 * Run this in browser console to test the API integration
 */

async function testLoginFlow() {
    console.log('🧪 Testing Investment System Login Flow...\n');
    
    const API_BASE = 'http://localhost/Investment-System-eSkillVisor/backend';
    
    try {
        // Test 1: Login
        console.log('1️⃣ Testing login...');
        const loginResponse = await fetch(`${API_BASE}/login.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Origin': 'http://localhost:5173'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'password'
            })
        });
        
        const loginData = await loginResponse.json();
        console.log('✅ Login response:', loginData);
        
        if (!loginData.success) {
            throw new Error('Login failed: ' + loginData.message);
        }
        
        const token = loginData.data.access_token;
        console.log('🔑 Token received:', token.substring(0, 20) + '...');
        
        // Test 2: Get current user
        console.log('\n2️⃣ Testing getCurrentUser...');
        const userResponse = await fetch(`${API_BASE}/me.php`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Origin': 'http://localhost:5173'
            }
        });
        
        const userData = await userResponse.json();
        console.log('✅ User data response:', userData);
        
        if (!userData.success) {
            throw new Error('Get user failed: ' + userData.message);
        }
        
        // Test 3: Test API endpoint
        console.log('\n3️⃣ Testing API endpoint...');
        const apiResponse = await fetch(`${API_BASE}/`, {
            method: 'GET',
            headers: {
                'Origin': 'http://localhost:5173'
            }
        });
        
        const apiData = await apiResponse.json();
        console.log('✅ API response:', apiData);
        
        console.log('\n🎉 All tests passed! Login flow is working correctly.');
        console.log('\n📋 Summary:');
        console.log('- ✅ Login endpoint working');
        console.log('- ✅ JWT token generation working');
        console.log('- ✅ getCurrentUser endpoint working');
        console.log('- ✅ CORS headers configured correctly');
        console.log('- ✅ Database connection working');
        
        return {
            success: true,
            token: token,
            user: userData.data
        };
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Auto-run the test
testLoginFlow().then(result => {
    if (result.success) {
        console.log('\n🚀 You can now try logging in at http://localhost:5173');
        console.log('📧 Email: <EMAIL>');
        console.log('🔒 Password: password');
    } else {
        console.log('\n🔧 Please check the backend configuration and try again.');
    }
});
