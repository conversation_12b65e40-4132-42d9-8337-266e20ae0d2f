#!/usr/bin/env node

/**
 * EskillVisor Deployment Decision Engine
 * Analyzes changes and provides deployment recommendations
 */

const fs = require('fs');
const path = require('path');

class DeploymentDecisionEngine {
    constructor(configPath = './deployment/config.json') {
        this.config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        this.dependencies = this.config.deployment.dependencies;
    }

    /**
     * Analyze changes and generate deployment plan
     */
    analyzeChanges(changeReport) {
        const plan = {
            timestamp: new Date().toISOString(),
            changeReport,
            recommendations: this.generateRecommendations(changeReport),
            deploymentSteps: [],
            risks: [],
            estimatedTime: 0,
            rollbackPlan: []
        };

        plan.deploymentSteps = this.generateDeploymentSteps(plan.recommendations);
        plan.risks = this.assessRisks(changeReport);
        plan.estimatedTime = this.estimateDeploymentTime(plan.deploymentSteps);
        plan.rollbackPlan = this.generateRollbackPlan(changeReport);

        return plan;
    }

    /**
     * Generate deployment recommendations
     */
    generateRecommendations(changeReport) {
        const recommendations = {
            deploymentType: changeReport.summary.deploymentType,
            requiredActions: [],
            optionalActions: [],
            dependencies: [],
            warnings: []
        };

        // Database changes
        if (changeReport.changes.database.added.length > 0 || 
            changeReport.changes.database.modified.length > 0) {
            recommendations.requiredActions.push({
                type: 'database_backup',
                priority: 'critical',
                description: 'Create database backup before schema changes',
                target: 'database',
                estimatedTime: 2
            });
            
            recommendations.requiredActions.push({
                type: 'database_migration',
                priority: 'critical',
                description: 'Execute database migrations',
                target: 'database',
                estimatedTime: 5,
                files: [...changeReport.changes.database.added, ...changeReport.changes.database.modified]
            });

            // Check dependencies
            if (this.dependencies.database_schema) {
                recommendations.dependencies.push({
                    type: 'backend_update',
                    reason: 'Database schema changes require backend updates',
                    targets: this.dependencies.database_schema
                });
            }
        }

        // Backend changes
        if (changeReport.changes.backend.added.length > 0 || 
            changeReport.changes.backend.modified.length > 0) {
            
            const backendFiles = [...changeReport.changes.backend.added, ...changeReport.changes.backend.modified];
            
            recommendations.requiredActions.push({
                type: 'backend_deployment',
                priority: 'high',
                description: 'Deploy backend API changes',
                target: 'backend',
                estimatedTime: 3,
                files: backendFiles
            });

            // Check for critical files
            const criticalFiles = backendFiles.filter(file => 
                file.path.includes('config/') || 
                file.path.includes('database.php') ||
                file.path.includes('login.php')
            );

            if (criticalFiles.length > 0) {
                recommendations.warnings.push({
                    type: 'critical_files',
                    message: 'Critical backend files modified - extra caution required',
                    files: criticalFiles
                });
            }

            // Check dependencies
            if (this.dependencies.backend_api) {
                recommendations.dependencies.push({
                    type: 'frontend_update',
                    reason: 'Backend API changes may require frontend updates',
                    targets: this.dependencies.backend_api
                });
            }
        }

        // Frontend changes
        if (changeReport.changes.frontend.added.length > 0 || 
            changeReport.changes.frontend.modified.length > 0) {
            
            recommendations.requiredActions.push({
                type: 'frontend_build',
                priority: 'high',
                description: 'Build frontend for production',
                target: 'frontend',
                estimatedTime: 2
            });

            recommendations.requiredActions.push({
                type: 'frontend_deployment',
                priority: 'high',
                description: 'Deploy frontend assets',
                target: 'frontend',
                estimatedTime: 1,
                files: [...changeReport.changes.frontend.added, ...changeReport.changes.frontend.modified]
            });
        }

        // Config changes
        if (changeReport.changes.config.added.length > 0 || 
            changeReport.changes.config.modified.length > 0) {
            
            recommendations.requiredActions.push({
                type: 'config_review',
                priority: 'medium',
                description: 'Review configuration changes',
                target: 'config',
                estimatedTime: 1,
                files: [...changeReport.changes.config.added, ...changeReport.changes.config.modified]
            });

            recommendations.warnings.push({
                type: 'config_changes',
                message: 'Configuration changes detected - verify settings',
                files: [...changeReport.changes.config.added, ...changeReport.changes.config.modified]
            });
        }

        // Deleted files
        const deletedFiles = [
            ...changeReport.changes.frontend.deleted,
            ...changeReport.changes.backend.deleted,
            ...changeReport.changes.database.deleted
        ];

        if (deletedFiles.length > 0) {
            recommendations.optionalActions.push({
                type: 'cleanup_deleted',
                priority: 'low',
                description: 'Clean up deleted files from production',
                target: 'all',
                estimatedTime: 1,
                files: deletedFiles
            });
        }

        return recommendations;
    }

    /**
     * Generate deployment steps
     */
    generateDeploymentSteps(recommendations) {
        const steps = [];
        let stepNumber = 1;

        // Sort actions by priority and dependencies
        const sortedActions = this.sortActionsByPriority(recommendations.requiredActions);

        for (const action of sortedActions) {
            steps.push({
                step: stepNumber++,
                type: action.type,
                description: action.description,
                target: action.target,
                commands: this.generateCommands(action),
                verification: this.generateVerification(action),
                rollback: this.generateRollbackStep(action),
                estimatedTime: action.estimatedTime
            });
        }

        // Add verification step
        steps.push({
            step: stepNumber++,
            type: 'verification',
            description: 'Verify deployment success',
            target: 'all',
            commands: this.generateVerificationCommands(),
            verification: 'Manual testing of application functionality',
            rollback: 'Execute rollback plan if verification fails',
            estimatedTime: 5
        });

        return steps;
    }

    /**
     * Sort actions by priority and dependencies
     */
    sortActionsByPriority(actions) {
        const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
        const typeOrder = {
            database_backup: 0,
            database_migration: 1,
            backend_deployment: 2,
            frontend_build: 3,
            frontend_deployment: 4,
            config_review: 5
        };

        return actions.sort((a, b) => {
            const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
            if (priorityDiff !== 0) return priorityDiff;
            
            return typeOrder[a.type] - typeOrder[b.type];
        });
    }

    /**
     * Generate commands for action
     */
    generateCommands(action) {
        switch (action.type) {
            case 'database_backup':
                return [
                    'mysqldump -u wallistry_eskill -p wallistry_eskillvisor_db > deployment/backups/backup_$(date +%Y%m%d_%H%M%S).sql',
                    'echo "Database backup created successfully"'
                ];

            case 'database_migration':
                return [
                    'cd backend/migrations',
                    'php migrate.php --execute',
                    'echo "Database migrations completed"'
                ];

            case 'backend_deployment':
                return [
                    'rsync -av --exclude="config.local.php" backend/ /home9/wallistry/eskillvisor.wallistry.pk/api/',
                    'chmod 644 /home9/wallistry/eskillvisor.wallistry.pk/api/*.php',
                    'chmod 755 /home9/wallistry/eskillvisor.wallistry.pk/api/*/',
                    'echo "Backend deployment completed"'
                ];

            case 'frontend_build':
                return [
                    'npm run build',
                    'echo "Frontend build completed"'
                ];

            case 'frontend_deployment':
                return [
                    'rsync -av --delete dist/ /home9/wallistry/eskillvisor.wallistry.pk/',
                    'chmod 644 /home9/wallistry/eskillvisor.wallistry.pk/*.html',
                    'chmod 644 /home9/wallistry/eskillvisor.wallistry.pk/assets/*',
                    'echo "Frontend deployment completed"'
                ];

            default:
                return [`echo "No specific commands for ${action.type}"`];
        }
    }

    /**
     * Generate verification steps
     */
    generateVerification(action) {
        switch (action.type) {
            case 'database_backup':
                return 'Verify backup file exists and has reasonable size';
            
            case 'database_migration':
                return 'Check database schema matches expected structure';
            
            case 'backend_deployment':
                return 'Test API endpoints: /api/test, /api/login.php';
            
            case 'frontend_deployment':
                return 'Verify website loads and assets are accessible';
            
            default:
                return 'Manual verification required';
        }
    }

    /**
     * Generate verification commands
     */
    generateVerificationCommands() {
        return [
            'curl -s https://eskillvisor.wallistry.pk/api/test | jq .success',
            'curl -s https://eskillvisor.wallistry.pk/ | grep -q "EskillVisor"',
            'echo "Verification completed"'
        ];
    }

    /**
     * Generate rollback step
     */
    generateRollbackStep(action) {
        switch (action.type) {
            case 'database_migration':
                return 'Restore from backup: mysql -u wallistry_eskill -p wallistry_eskillvisor_db < backup_file.sql';
            
            case 'backend_deployment':
                return 'Restore previous backend files from backup';
            
            case 'frontend_deployment':
                return 'Restore previous frontend build from backup';
            
            default:
                return 'Manual rollback required';
        }
    }

    /**
     * Assess deployment risks
     */
    assessRisks(changeReport) {
        const risks = [];

        // Database risks
        if (changeReport.changes.database.modified.length > 0) {
            risks.push({
                level: 'high',
                category: 'database',
                description: 'Database schema modifications can cause data loss',
                mitigation: 'Ensure backup is created and tested before deployment'
            });
        }

        // Backend API risks
        const criticalBackendFiles = changeReport.changes.backend.modified.filter(file =>
            file.path.includes('login.php') || 
            file.path.includes('config/') ||
            file.path.includes('database.php')
        );

        if (criticalBackendFiles.length > 0) {
            risks.push({
                level: 'medium',
                category: 'backend',
                description: 'Critical backend files modified',
                mitigation: 'Test authentication and database connectivity after deployment'
            });
        }

        // Large number of changes
        if (changeReport.summary.totalChanges > 20) {
            risks.push({
                level: 'medium',
                category: 'scope',
                description: 'Large number of changes increases deployment complexity',
                mitigation: 'Consider breaking deployment into smaller batches'
            });
        }

        return risks;
    }

    /**
     * Estimate deployment time
     */
    estimateDeploymentTime(steps) {
        return steps.reduce((total, step) => total + step.estimatedTime, 0);
    }

    /**
     * Generate rollback plan
     */
    generateRollbackPlan(changeReport) {
        return [
            {
                step: 1,
                description: 'Stop application if necessary',
                command: 'echo "Application stopped for rollback"'
            },
            {
                step: 2,
                description: 'Restore database from backup',
                command: 'mysql -u wallistry_eskill -p wallistry_eskillvisor_db < deployment/backups/latest_backup.sql'
            },
            {
                step: 3,
                description: 'Restore backend files',
                command: 'rsync -av deployment/backups/backend/ /home9/wallistry/eskillvisor.wallistry.pk/api/'
            },
            {
                step: 4,
                description: 'Restore frontend files',
                command: 'rsync -av deployment/backups/frontend/ /home9/wallistry/eskillvisor.wallistry.pk/'
            },
            {
                step: 5,
                description: 'Verify rollback success',
                command: 'curl -s https://eskillvisor.wallistry.pk/api/test'
            }
        ];
    }
}

module.exports = DeploymentDecisionEngine;

// CLI usage
if (require.main === module) {
    const ChangeDetector = require('./change-detector.cjs');
    const engine = new DeploymentDecisionEngine();
    const detector = new ChangeDetector();
    
    detector.detectChanges().then(changes => {
        const plan = engine.analyzeChanges(changes);
        console.log(JSON.stringify(plan, null, 2));
    }).catch(error => {
        console.error('Error generating deployment plan:', error);
        process.exit(1);
    });
}
