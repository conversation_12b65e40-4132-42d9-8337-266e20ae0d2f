# EskillVisor Deployment Guide - cPanel Only

## 🚀 **Unified cPanel Deployment Workflow**

This guide provides step-by-step instructions for deploying the complete EskillVisor application (frontend + backend) to cPanel with a custom domain.

## 📋 **Prerequisites**

1. **Local Development Environment**
   - Node.js and npm installed
   - Git repository set up
   - Backend files synchronized (completed)

2. **Production Environment**
   - cPanel access to wallistry.pk
   - Custom domain: https://eskillvisor.wallistry.pk
   - Database: wallistry_eskillvisor_db
   - Complete application hosted on single domain

## 🔄 **Unified Deployment Process**

### **Step 1: Complete Application Deployment (cPanel)**

Both frontend and backend are deployed together to cPanel:

```bash
# 1. Make your changes locally
# 2. Test locally
npm run dev

# 3. Build frontend for production
npm run build

# 4. Create deployment package
./deploy.sh
# Select option 4: "Create Deployment Package"

# 5. Upload to cPanel
# Upload contents of cpanel-deployment/public_html/ to your domain directory

# 6. Test complete application
# Visit: https://eskillvisor.wallistry.pk
```

### **Step 2: Manual File Upload Process**

#### **Option A: cPanel File Manager (Recommended)**

1. **Access cPanel File Manager**
   - Login to cPanel at wallistry.pk/cpanel
   - Open File Manager
   - Navigate to `/public_html/api/`

2. **Upload Modified Files**
   - Upload only the files you've changed
   - Key files to sync:
     - `index.php` - Main API entry point
     - `login.php` - Authentication endpoint
     - `companies.php` - Company management
     - `users.php` - User management
     - `config/` folder - Configuration files
     - `core/` folder - Core classes

3. **File Permissions**
   - Ensure PHP files have 644 permissions
   - Ensure directories have 755 permissions

#### **Option B: FTP Deployment**

1. **FTP Client Setup**
   - Host: wallistry.pk
   - Username: wallistry
   - Password: [Your cPanel password]
   - Port: 21

2. **Upload Files**
   - Navigate to `/public_html/api/`
   - Upload modified backend files
   - Maintain directory structure

## 📁 **File Structure Synchronization**

### **Production Structure (cPanel)**
```
/public_html/api/
├── config/
│   ├── config.php
│   └── database.php
├── core/
│   └── Response.php
├── index.php
├── login.php
├── companies.php
├── users.php
└── install.php
```

### **Local Structure**
```
backend/
├── config/
│   ├── config.php
│   └── database.php
├── core/
│   └── Response.php
├── index.php
├── login.php
├── companies.php
├── users.php
└── install.php
```

## 🧪 **Testing Workflow**

### **Local Testing**
1. Test backend endpoints locally
2. Test frontend integration
3. Verify all functionality works

### **Production Testing**
1. Upload backend changes to cPanel
2. Test API endpoints:
   - https://wallistry.pk/api/test
   - https://wallistry.pk/api/login.php
   - https://wallistry.pk/api/companies.php
3. Test frontend integration with production API

## 🔧 **Quick Deployment Commands**

### **Frontend Only Changes**
```bash
# Test locally
npm run dev

# Build and deploy
npm run build
git add .
git commit -m "Frontend: [description]"
git push origin main
```

### **Backend Only Changes**
```bash
# 1. Test locally (if you have local PHP server)
# 2. Upload changed files to cPanel File Manager
# 3. Test production endpoints
```

### **Full Stack Changes**
```bash
# 1. Frontend deployment
npm run build
git add .
git commit -m "Full stack: [description]"
git push origin main

# 2. Backend deployment
# Upload backend files to cPanel manually
# Test both frontend and backend integration
```

## 🚨 **Important Notes**

1. **Database Changes**
   - Database is shared between environments
   - Be careful with schema changes
   - Test thoroughly before deploying

2. **Configuration**
   - Production config is already set in config.php
   - Don't change database credentials
   - CORS settings are configured for production

3. **File Permissions**
   - PHP files: 644
   - Directories: 755
   - Uploads directory: 755 (if created)

4. **Backup**
   - Always backup production files before major changes
   - Use cPanel backup features

## 🔍 **Troubleshooting**

### **Common Issues**

1. **API Not Working**
   - Check file permissions
   - Verify file upload completed
   - Check PHP error logs in cPanel

2. **CORS Errors**
   - Verify CORS settings in config.php
   - Check allowed origins

3. **Database Connection**
   - Verify database credentials
   - Check database server status

### **Debugging**

1. **Enable Error Reporting** (temporarily)
   ```php
   // Add to top of PHP file for debugging
   error_reporting(E_ALL);
   ini_set('display_errors', 1);
   ```

2. **Check Logs**
   - cPanel Error Logs
   - Browser Developer Console
   - Network tab for API calls

## ✅ **Deployment Checklist**

- [ ] Local testing completed
- [ ] Frontend builds successfully
- [ ] Backend files uploaded to cPanel
- [ ] File permissions set correctly
- [ ] API endpoints tested
- [ ] Frontend-backend integration tested
- [ ] Production functionality verified
- [ ] Error logs checked

## 📞 **Support**

If you encounter issues:
1. Check this deployment guide
2. Verify file synchronization
3. Test individual components
4. Check error logs and console
