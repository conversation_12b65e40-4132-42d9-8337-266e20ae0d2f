<?php
/**
 * Company Model
 */

class Company extends Model {
    protected $table = 'companies';
    protected $fillable = [
        'name',
        'company_director_id',
        'registration_territory',
        'ein_number',
        'marketplace',
        'custom_marketplace',
        'description',
        'industry',
        'website',
        'email',
        'phone',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'status',
        'created_by',
        'manager_id',
        'approval_status',
        'approved_by',
        'approved_at',
        'rejected_by',
        'rejected_at',
        'rejection_reason'
    ];
    
    public function getWithStats($id) {
        $sql = "SELECT c.*,
                       COUNT(DISTINCT i.id) as inventory_count,
                       COALESCE(SUM(i.current_quantity * i.unit_price), 0) as total_inventory_value,
                       COUNT(DISTINCT cp.user_id) as partner_count,
                       u.name as created_by_name,
                       director.name as director_name,
                       director.email as director_email,
                       manager.name as manager_name,
                       manager.email as manager_email,
                       approver.name as approved_by_name
                FROM companies c
                LEFT JOIN inventory_items i ON c.id = i.company_id AND i.status = 'active'
                LEFT JOIN company_partners cp ON c.id = cp.company_id AND cp.status = 'active'
                LEFT JOIN users u ON c.created_by = u.id
                LEFT JOIN users director ON c.company_director_id = director.id
                LEFT JOIN users manager ON c.manager_id = manager.id
                LEFT JOIN users approver ON c.approved_by = approver.id
                WHERE c.id = :id
                GROUP BY c.id";
        
        return $this->fetch($sql, ['id' => $id]);
    }
    
    public function getPartners($companyId) {
        $sql = "SELECT u.id, u.name, u.email, u.mobile, u.business_model,
                       cp.assigned_at, cp.status,
                       ua.name as assigned_by_name
                FROM users u
                JOIN company_partners cp ON u.id = cp.user_id
                LEFT JOIN users ua ON cp.assigned_by = ua.id
                WHERE cp.company_id = :company_id AND cp.status = 'active'
                ORDER BY cp.assigned_at DESC";
        
        return $this->fetchAll($sql, ['company_id' => $companyId]);
    }
    
    public function getPaginated($page = 1, $limit = 10, $search = '', $status = '', $approval_status = '') {
        $offset = ($page - 1) * $limit;
        $conditions = [];
        $params = [];
        
        if (!empty($search)) {
            $conditions[] = "(c.name LIKE :search OR c.email LIKE :search OR c.industry LIKE :search)";
            $params['search'] = "%{$search}%";
        }
        
        if (!empty($status)) {
            $conditions[] = "c.status = :status";
            $params['status'] = $status;
        }
        
        if (!empty($approval_status)) {
            $conditions[] = "c.approval_status = :approval_status";
            $params['approval_status'] = $approval_status;
        }
        
        $whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM companies c {$whereClause}";
        $totalResult = $this->fetch($countSql, $params);
        $total = $totalResult['total'];
        
        // Get paginated data
        $sql = "SELECT c.*,
                       u.name as created_by_name,
                       director.name as director_name,
                       manager.name as manager_name,
                       COUNT(DISTINCT i.id) as inventory_count,
                       COUNT(DISTINCT cp.user_id) as partner_count
                FROM companies c
                LEFT JOIN users u ON c.created_by = u.id
                LEFT JOIN users director ON c.company_director_id = director.id
                LEFT JOIN users manager ON c.manager_id = manager.id
                LEFT JOIN inventory_items i ON c.id = i.company_id AND i.status = 'active'
                LEFT JOIN company_partners cp ON c.id = cp.company_id AND cp.status = 'active'
                {$whereClause}
                GROUP BY c.id
                ORDER BY c.created_at DESC
                LIMIT :limit OFFSET :offset";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        $data = $this->fetchAll($sql, $params);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    
    public function getByManager($managerId, $page = 1, $limit = 10, $search = '', $status = '') {
        $offset = ($page - 1) * $limit;
        $conditions = ["(c.created_by = :manager_id OR c.manager_id = :manager_id)"];
        $params = ['manager_id' => $managerId];
        
        if (!empty($search)) {
            $conditions[] = "(c.name LIKE :search OR c.email LIKE :search OR c.industry LIKE :search)";
            $params['search'] = "%{$search}%";
        }
        
        if (!empty($status)) {
            $conditions[] = "c.status = :status";
            $params['status'] = $status;
        }
        
        $whereClause = 'WHERE ' . implode(' AND ', $conditions);
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM companies c {$whereClause}";
        $totalResult = $this->fetch($countSql, $params);
        $total = $totalResult['total'];
        
        // Get paginated data
        $sql = "SELECT c.*,
                       u.name as created_by_name,
                       director.name as director_name,
                       manager.name as manager_name,
                       COUNT(DISTINCT i.id) as inventory_count,
                       COUNT(DISTINCT cp.user_id) as partner_count
                FROM companies c
                LEFT JOIN users u ON c.created_by = u.id
                LEFT JOIN users director ON c.company_director_id = director.id
                LEFT JOIN users manager ON c.manager_id = manager.id
                LEFT JOIN inventory_items i ON c.id = i.company_id AND i.status = 'active'
                LEFT JOIN company_partners cp ON c.id = cp.company_id AND cp.status = 'active'
                {$whereClause}
                GROUP BY c.id
                ORDER BY c.created_at DESC
                LIMIT :limit OFFSET :offset";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        $data = $this->fetchAll($sql, $params);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    
    public function getAll() {
        $sql = "SELECT c.*,
                       u.name as created_by_name,
                       director.name as director_name,
                       manager.name as manager_name,
                       COUNT(DISTINCT i.id) as inventory_count,
                       COUNT(DISTINCT cp.user_id) as partner_count
                FROM companies c
                LEFT JOIN users u ON c.created_by = u.id
                LEFT JOIN users director ON c.company_director_id = director.id
                LEFT JOIN users manager ON c.manager_id = manager.id
                LEFT JOIN inventory_items i ON c.id = i.company_id AND i.status = 'active'
                LEFT JOIN company_partners cp ON c.id = cp.company_id AND cp.status = 'active'
                WHERE c.status = 'active'
                GROUP BY c.id
                ORDER BY c.name";
        
        return $this->fetchAll($sql);
    }
    
    public function assignPartner($companyId, $userId, $assignedBy) {
        $sql = "INSERT INTO company_partners (company_id, user_id, assigned_by, assigned_at, status)
                VALUES (:company_id, :user_id, :assigned_by, NOW(), 'active')";
        
        $this->execute($sql, [
            'company_id' => $companyId,
            'user_id' => $userId,
            'assigned_by' => $assignedBy
        ]);
        
        return [
            'company_id' => $companyId,
            'user_id' => $userId,
            'assigned_by' => $assignedBy,
            'assigned_at' => date('Y-m-d H:i:s'),
            'status' => 'active'
        ];
    }
    
    public function removePartner($companyId, $userId) {
        $sql = "UPDATE company_partners 
                SET status = 'inactive', removed_at = NOW()
                WHERE company_id = :company_id AND user_id = :user_id";
        
        return $this->execute($sql, [
            'company_id' => $companyId,
            'user_id' => $userId
        ]);
    }
    
    public function isPartnerAssigned($companyId, $userId) {
        $sql = "SELECT COUNT(*) as count 
                FROM company_partners 
                WHERE company_id = :company_id AND user_id = :user_id AND status = 'active'";
        
        $result = $this->fetch($sql, [
            'company_id' => $companyId,
            'user_id' => $userId
        ]);
        
        return $result['count'] > 0;
    }
    
    public function getInventoryCount($companyId) {
        $sql = "SELECT COUNT(*) as count 
                FROM inventory_items 
                WHERE company_id = :company_id AND status = 'active'";
        
        $result = $this->fetch($sql, ['company_id' => $companyId]);
        return $result['count'];
    }
    
    public function getCompanyStats($companyId) {
        $sql = "SELECT 
                    COUNT(DISTINCT i.id) as total_items,
                    COALESCE(SUM(i.current_quantity), 0) as total_quantity,
                    COALESCE(SUM(i.current_quantity * i.unit_price), 0) as total_value,
                    COUNT(DISTINCT cp.user_id) as total_partners
                FROM companies c
                LEFT JOIN inventory_items i ON c.id = i.company_id AND i.status = 'active'
                LEFT JOIN company_partners cp ON c.id = cp.company_id AND cp.status = 'active'
                WHERE c.id = :company_id";
        
        return $this->fetch($sql, ['company_id' => $companyId]);
    }
    
    public function getUserCompanies($userId) {
        $sql = "SELECT c.id, c.name, c.status, c.approval_status,
                       COUNT(DISTINCT i.id) as inventory_count,
                       COALESCE(SUM(i.current_quantity * i.unit_price), 0) as total_value
                FROM companies c
                LEFT JOIN company_partners cp ON c.id = cp.company_id AND cp.user_id = :user_id AND cp.status = 'active'
                LEFT JOIN inventory_items i ON c.id = i.company_id AND i.status = 'active'
                WHERE (c.company_director_id = :user_id OR cp.company_id IS NOT NULL)
                GROUP BY c.id
                ORDER BY c.name";
        
        return $this->fetchAll($sql, ['user_id' => $userId]);
    }
    
    public function getPendingApprovals() {
        $sql = "SELECT c.*,
                       u.name as created_by_name,
                       director.name as director_name
                FROM companies c
                LEFT JOIN users u ON c.created_by = u.id
                LEFT JOIN users director ON c.company_director_id = director.id
                WHERE c.approval_status = 'pending'
                ORDER BY c.created_at ASC";
        
        return $this->fetchAll($sql);
    }
}
