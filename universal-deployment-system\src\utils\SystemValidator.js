const fs = require('fs-extra');
const path = require('path');

class SystemValidator {
    constructor() {
        this.validationResults = {
            passed: 0,
            failed: 0,
            warnings: 0,
            tests: []
        };
    }

    /**
     * Run comprehensive system validation
     */
    async validateSystem() {
        console.log('🔍 Starting Universal Deployment System validation...\n');
        
        this.validationResults = {
            passed: 0,
            failed: 0,
            warnings: 0,
            tests: []
        };

        // Core system tests
        await this.testDirectoryStructure();
        await this.testCoreModules();
        await this.testConfigurationSystem();
        await this.testChangeDetection();
        await this.testCodeAnalysis();
        await this.testDeploymentGeneration();
        await this.testDocumentationGeneration();
        await this.testErrorHandling();
        await this.testHistoryManagement();

        // Integration tests
        await this.testEndToEndWorkflow();

        // Performance tests
        await this.testPerformance();

        this.printValidationResults();
        return this.validationResults;
    }

    /**
     * Test directory structure
     */
    async testDirectoryStructure() {
        const requiredDirs = [
            'src/core',
            'src/gui',
            'src/analyzers',
            'src/generators',
            'src/utils',
            'config',
            'templates',
            'output',
            'output/packages',
            'output/documentation',
            'output/history'
        ];

        const requiredFiles = [
            'package.json',
            'README.md',
            'src/main.js',
            'src/core/ConfigManager.js',
            'src/core/ChangeDetector.js',
            'src/core/DeploymentManager.js',
            'src/core/HistoryManager.js',
            'src/analyzers/CodeAnalyzer.js',
            'src/generators/DocumentationGenerator.js',
            'src/gui/index.html',
            'src/gui/styles.css',
            'src/gui/app.js',
            'src/utils/ErrorHandler.js',
            'src/utils/NotificationManager.js'
        ];

        this.addTest('Directory Structure', async () => {
            const basePath = path.join(__dirname, '../..');
            
            // Check directories
            for (const dir of requiredDirs) {
                const dirPath = path.join(basePath, dir);
                if (!await fs.pathExists(dirPath)) {
                    throw new Error(`Required directory missing: ${dir}`);
                }
            }

            // Check files
            for (const file of requiredFiles) {
                const filePath = path.join(basePath, file);
                if (!await fs.pathExists(filePath)) {
                    throw new Error(`Required file missing: ${file}`);
                }
            }

            return 'All required directories and files exist';
        });
    }

    /**
     * Test core modules
     */
    async testCoreModules() {
        this.addTest('Core Module Loading', async () => {
            try {
                const ConfigManager = require('../core/ConfigManager');
                const ChangeDetector = require('../core/ChangeDetector');
                const DeploymentManager = require('../core/DeploymentManager');
                const HistoryManager = require('../core/HistoryManager');
                const CodeAnalyzer = require('../analyzers/CodeAnalyzer');
                const DocumentationGenerator = require('../generators/DocumentationGenerator');
                const ErrorHandler = require('../utils/ErrorHandler');

                // Test instantiation
                new ConfigManager();
                new ChangeDetector();
                new DeploymentManager();
                new HistoryManager();
                new CodeAnalyzer();
                new DocumentationGenerator();
                new ErrorHandler();

                return 'All core modules loaded successfully';
            } catch (error) {
                throw new Error(`Module loading failed: ${error.message}`);
            }
        });
    }

    /**
     * Test configuration system
     */
    async testConfigurationSystem() {
        this.addTest('Configuration System', async () => {
            const ConfigManager = require('../core/ConfigManager');
            const configManager = new ConfigManager();

            // Test default config
            const defaultConfig = configManager.defaultConfig;
            if (!defaultConfig.projectName !== undefined) {
                throw new Error('Default config missing projectName field');
            }

            // Test project type detection
            const testProjectPath = path.join(__dirname, '../..');
            const detectedConfig = await configManager.detectProjectType(testProjectPath);
            
            if (!detectedConfig.projectType) {
                throw new Error('Project type detection failed');
            }

            // Test config validation
            const validation = configManager.validateConfig({
                projectName: 'Test Project',
                deployment: {
                    domain: 'example.com',
                    serverPath: '/public_html'
                }
            });

            if (!validation.isValid) {
                throw new Error('Valid config failed validation');
            }

            return 'Configuration system working correctly';
        });
    }

    /**
     * Test change detection
     */
    async testChangeDetection() {
        this.addTest('Change Detection', async () => {
            const ChangeDetector = require('../core/ChangeDetector');
            const detector = new ChangeDetector();

            // Test file categorization
            const category = detector.categorizeFile('src/components/App.jsx', {
                fileCategories: {
                    frontend: {
                        extensions: ['.jsx', '.js'],
                        directories: ['src', 'components']
                    }
                }
            });

            if (category !== 'frontend') {
                throw new Error('File categorization failed');
            }

            // Test hash calculation
            const testFile = path.join(__dirname, '../main.js');
            if (await fs.pathExists(testFile)) {
                const hash = await detector.calculateFileHash(testFile);
                if (!hash || hash.length !== 32) {
                    throw new Error('Hash calculation failed');
                }
            }

            return 'Change detection system working correctly';
        });
    }

    /**
     * Test code analysis
     */
    async testCodeAnalysis() {
        this.addTest('Code Analysis', async () => {
            const CodeAnalyzer = require('../analyzers/CodeAnalyzer');
            const analyzer = new CodeAnalyzer();

            // Test component extraction
            const reactCode = `
                import React from 'react';
                function MyComponent() {
                    return <div>Hello World</div>;
                }
                export default MyComponent;
            `;

            const components = analyzer.extractComponents(reactCode, '.jsx');
            if (!Array.isArray(components)) {
                throw new Error('Component extraction failed');
            }

            // Test function extraction
            const jsCode = `
                function testFunction() {
                    return true;
                }
                const arrowFunction = () => {
                    return false;
                };
            `;

            const functions = analyzer.extractFunctions(jsCode);
            if (!Array.isArray(functions)) {
                throw new Error('Function extraction failed');
            }

            return 'Code analysis system working correctly';
        });
    }

    /**
     * Test deployment generation
     */
    async testDeploymentGeneration() {
        this.addTest('Deployment Generation', async () => {
            const DeploymentManager = require('../core/DeploymentManager');
            const manager = new DeploymentManager();

            // Test package name generation
            const packageName = manager.generatePackageName('test-project', new Date().toISOString());
            if (!packageName.includes('test-project') || !packageName.endsWith('.zip')) {
                throw new Error('Package name generation failed');
            }

            // Test target path generation
            const targetPath = manager.getTargetPath('src/App.js', 'frontend', {
                projectType: 'react-application',
                deployment: { serverPath: '/public_html' }
            });

            if (!targetPath.includes('public_html')) {
                throw new Error('Target path generation failed');
            }

            return 'Deployment generation system working correctly';
        });
    }

    /**
     * Test documentation generation
     */
    async testDocumentationGeneration() {
        this.addTest('Documentation Generation', async () => {
            const DocumentationGenerator = require('../generators/DocumentationGenerator');
            const generator = new DocumentationGenerator();

            // Test template data preparation
            const mockDeployment = {
                id: 'test-123',
                timestamp: new Date().toISOString(),
                projectName: 'Test Project',
                packageName: 'test-package.zip',
                packageSize: 1024000,
                fileCount: 50,
                config: {
                    projectType: 'react-application',
                    deployment: {
                        domain: 'example.com',
                        serverPath: '/public_html'
                    }
                },
                summary: {
                    totalFiles: 100,
                    changedFiles: 50,
                    addedFiles: 10,
                    modifiedFiles: 40,
                    categories: {}
                },
                analysis: {
                    features: ['Updated UI components'],
                    technicalDetails: ['Modified 5 React components'],
                    impactAssessment: 'medium'
                },
                changes: []
            };

            const templateData = generator.prepareTemplateData(mockDeployment);
            
            if (!templateData.deployment || !templateData.project || !templateData.summary) {
                throw new Error('Template data preparation failed');
            }

            return 'Documentation generation system working correctly';
        });
    }

    /**
     * Test error handling
     */
    async testErrorHandling() {
        this.addTest('Error Handling', async () => {
            const ErrorHandler = require('../utils/ErrorHandler');
            const errorHandler = new ErrorHandler();

            // Test error categorization
            const fileError = new Error('ENOENT: no such file or directory');
            fileError.code = 'ENOENT';
            
            const categorized = errorHandler.categorizeError(fileError, { operation: 'test' });
            
            if (categorized.category !== 'file_not_found') {
                throw new Error('Error categorization failed');
            }

            // Test config validation
            const validation = errorHandler.validateProjectConfig({
                projectName: '',
                deployment: {}
            });

            if (validation.isValid) {
                throw new Error('Config validation should have failed');
            }

            return 'Error handling system working correctly';
        });
    }

    /**
     * Test history management
     */
    async testHistoryManagement() {
        this.addTest('History Management', async () => {
            const HistoryManager = require('../core/HistoryManager');
            const historyManager = new HistoryManager();

            // Test statistics calculation
            const stats = await historyManager.getStatistics();
            
            if (typeof stats.totalDeployments !== 'number') {
                throw new Error('Statistics calculation failed');
            }

            // Test deployment frequency calculation
            const frequency = historyManager.calculateDeploymentFrequency([]);
            
            if (!frequency.daily !== undefined || !frequency.weekly !== undefined) {
                throw new Error('Frequency calculation failed');
            }

            return 'History management system working correctly';
        });
    }

    /**
     * Test end-to-end workflow
     */
    async testEndToEndWorkflow() {
        this.addTest('End-to-End Workflow', async () => {
            // This would test the complete workflow from project selection
            // to deployment package creation in a real implementation
            
            // For now, we'll test the integration points
            const ConfigManager = require('../core/ConfigManager');
            const ChangeDetector = require('../core/ChangeDetector');
            
            const configManager = new ConfigManager();
            const changeDetector = new ChangeDetector();

            // Test workflow integration
            const testConfig = configManager.defaultConfig;
            testConfig.projectName = 'Test Project';
            testConfig.deployment.domain = 'example.com';

            const validation = configManager.validateConfig(testConfig);
            if (!validation.isValid) {
                throw new Error('Workflow integration failed at config validation');
            }

            return 'End-to-end workflow integration working correctly';
        });
    }

    /**
     * Test performance
     */
    async testPerformance() {
        this.addTest('Performance', async () => {
            const startTime = Date.now();
            
            // Test module loading performance
            const ConfigManager = require('../core/ConfigManager');
            const configManager = new ConfigManager();
            
            const loadTime = Date.now() - startTime;
            
            if (loadTime > 1000) { // Should load in under 1 second
                this.addWarning('Module loading took longer than expected');
            }

            // Test config detection performance
            const detectionStart = Date.now();
            await configManager.detectProjectType(__dirname);
            const detectionTime = Date.now() - detectionStart;
            
            if (detectionTime > 500) { // Should detect in under 500ms
                this.addWarning('Project type detection took longer than expected');
            }

            return `Performance tests completed (load: ${loadTime}ms, detection: ${detectionTime}ms)`;
        });
    }

    /**
     * Add a test to the validation suite
     */
    addTest(name, testFunction) {
        const test = {
            name,
            status: 'pending',
            message: '',
            duration: 0
        };

        this.validationResults.tests.push(test);

        // Execute the test
        const startTime = Date.now();
        
        testFunction()
            .then(result => {
                test.status = 'passed';
                test.message = result;
                test.duration = Date.now() - startTime;
                this.validationResults.passed++;
                console.log(`✅ ${name}: ${result} (${test.duration}ms)`);
            })
            .catch(error => {
                test.status = 'failed';
                test.message = error.message;
                test.duration = Date.now() - startTime;
                this.validationResults.failed++;
                console.log(`❌ ${name}: ${error.message} (${test.duration}ms)`);
            });
    }

    /**
     * Add a warning
     */
    addWarning(message) {
        this.validationResults.warnings++;
        console.log(`⚠️  Warning: ${message}`);
    }

    /**
     * Print validation results
     */
    printValidationResults() {
        console.log('\n' + '='.repeat(60));
        console.log('🔍 UNIVERSAL DEPLOYMENT SYSTEM VALIDATION RESULTS');
        console.log('='.repeat(60));
        
        console.log(`\n📊 Summary:`);
        console.log(`   ✅ Passed: ${this.validationResults.passed}`);
        console.log(`   ❌ Failed: ${this.validationResults.failed}`);
        console.log(`   ⚠️  Warnings: ${this.validationResults.warnings}`);
        console.log(`   📝 Total Tests: ${this.validationResults.tests.length}`);

        const successRate = (this.validationResults.passed / this.validationResults.tests.length * 100).toFixed(1);
        console.log(`   📈 Success Rate: ${successRate}%`);

        if (this.validationResults.failed === 0) {
            console.log('\n🎉 All tests passed! The Universal Deployment System is ready for use.');
        } else {
            console.log('\n⚠️  Some tests failed. Please review the issues above.');
        }

        console.log('\n' + '='.repeat(60));
    }

    /**
     * Fix common issues
     */
    async fixCommonIssues() {
        console.log('🔧 Attempting to fix common issues...\n');

        // Fix: Ensure all required directories exist
        await this.ensureDirectories();

        // Fix: Create missing configuration files
        await this.createMissingConfigs();

        // Fix: Update package.json if needed
        await this.updatePackageJson();

        console.log('✅ Common issues fixed.\n');
    }

    async ensureDirectories() {
        const dirs = [
            'output',
            'output/packages',
            'output/documentation', 
            'output/history',
            'output/logs',
            'config',
            'templates'
        ];

        const basePath = path.join(__dirname, '../..');
        
        for (const dir of dirs) {
            const dirPath = path.join(basePath, dir);
            await fs.ensureDir(dirPath);
            console.log(`📁 Ensured directory: ${dir}`);
        }
    }

    async createMissingConfigs() {
        const basePath = path.join(__dirname, '../..');
        
        // Create .gitignore if missing
        const gitignorePath = path.join(basePath, '.gitignore');
        if (!await fs.pathExists(gitignorePath)) {
            const gitignoreContent = `
node_modules/
output/packages/
output/logs/
.env
.env.local
dist/
build/
*.log
.DS_Store
Thumbs.db
            `.trim();
            
            await fs.writeFile(gitignorePath, gitignoreContent);
            console.log('📝 Created .gitignore');
        }
    }

    async updatePackageJson() {
        const basePath = path.join(__dirname, '../..');
        const packagePath = path.join(basePath, 'package.json');
        
        if (await fs.pathExists(packagePath)) {
            const packageJson = await fs.readJson(packagePath);
            
            // Ensure all required scripts exist
            if (!packageJson.scripts) {
                packageJson.scripts = {};
            }
            
            const requiredScripts = {
                'start': 'electron src/main.js',
                'dev': 'electron src/main.js --dev',
                'test': 'node src/utils/SystemValidator.js',
                'validate': 'node -e "const SystemValidator = require(\'./src/utils/SystemValidator\'); new SystemValidator().validateSystem();"'
            };
            
            let updated = false;
            for (const [script, command] of Object.entries(requiredScripts)) {
                if (!packageJson.scripts[script]) {
                    packageJson.scripts[script] = command;
                    updated = true;
                }
            }
            
            if (updated) {
                await fs.writeJson(packagePath, packageJson, { spaces: 2 });
                console.log('📝 Updated package.json scripts');
            }
        }
    }
}

// Allow running validation directly
if (require.main === module) {
    const validator = new SystemValidator();
    validator.fixCommonIssues()
        .then(() => validator.validateSystem())
        .then(results => {
            process.exit(results.failed === 0 ? 0 : 1);
        })
        .catch(error => {
            console.error('Validation failed:', error);
            process.exit(1);
        });
}

module.exports = SystemValidator;
