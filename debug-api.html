<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Investment System API Debug Test</h1>
    <button onclick="runTests()">Run API Tests</button>
    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost/Investment-System-eSkillVisor/backend';
        
        async function testEndpoint(name, url, options = {}) {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-result';
            
            try {
                console.log(`Testing ${name}: ${url}`);
                
                const response = await fetch(url, {
                    headers: {
                        'Origin': 'http://localhost:5173',
                        ...options.headers
                    },
                    ...options
                });
                
                const responseText = await response.text();
                
                let result = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    body: responseText
                };
                
                // Try to parse as JSON
                try {
                    result.json = JSON.parse(responseText);
                    resultDiv.className += ' success';
                } catch (e) {
                    result.parseError = e.message;
                    resultDiv.className += ' error';
                }
                
                resultDiv.innerHTML = `
                    <h3>${name} - ${response.ok ? 'SUCCESS' : 'FAILED'}</h3>
                    <p><strong>URL:</strong> ${url}</p>
                    <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                    <p><strong>Response:</strong></p>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
                
                return result;
                
            } catch (error) {
                resultDiv.className += ' error';
                resultDiv.innerHTML = `
                    <h3>${name} - ERROR</h3>
                    <p><strong>URL:</strong> ${url}</p>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
                return { error: error.message };
            } finally {
                document.getElementById('results').appendChild(resultDiv);
            }
        }
        
        async function runTests() {
            document.getElementById('results').innerHTML = '<h2>Running Tests...</h2>';
            
            // Test 1: Basic API endpoint
            await testEndpoint('Basic API', `${API_BASE}/`);
            
            // Test 2: Local test endpoint
            await testEndpoint('Local Test', `${API_BASE}/local-test.php`);
            
            // Test 3: Login endpoint
            const loginResult = await testEndpoint('Login', `${API_BASE}/login.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: '<EMAIL>',
                    password: 'password'
                })
            });
            
            // Test 4: Me endpoint (if login was successful)
            if (loginResult.json && loginResult.json.success) {
                const token = loginResult.json.data.access_token;
                await testEndpoint('Get Current User', `${API_BASE}/me.php`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
            }
            
            // Test 5: Check if files exist
            await testEndpoint('Check me.php exists', `${API_BASE}/me.php`);
            
            console.log('All tests completed!');
        }
    </script>
</body>
</html>
