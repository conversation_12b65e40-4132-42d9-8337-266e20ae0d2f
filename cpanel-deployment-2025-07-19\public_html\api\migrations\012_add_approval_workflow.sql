-- Add approval workflow fields to users table
ALTER TABLE users 
ADD COLUMN approval_status ENUM('pending', 'approved', 'rejected') DEFAULT 'approved',
ADD COLUMN created_by INT NULL,
ADD COLUMN approved_by INT NULL,
ADD COLUMN approved_at TIMESTAMP NULL,
ADD COLUMN rejection_reason TEXT NULL,
ADD COLUMN rejected_at TIMESTAMP NULL,
ADD INDEX idx_approval_status (approval_status),
ADD INDEX idx_created_by (created_by),
ADD INDEX idx_approved_by (approved_by),
ADD FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
ADD FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL;

-- Add approval workflow fields to companies table
ALTER TABLE companies 
ADD COLUMN approval_status ENUM('pending', 'approved', 'rejected') DEFAULT 'approved',
ADD COLUMN approved_by INT NULL,
ADD COLUMN approved_at TIMESTAMP NULL,
ADD COLUMN rejection_reason TEXT NULL,
ADD COLUMN rejected_at TIMESTAMP NULL,
ADD INDEX idx_approval_status (approval_status),
ADD INDEX idx_approved_by (approved_by),
ADD FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL;

-- Create company_documents table for document verification
CREATE TABLE IF NOT EXISTS company_documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    document_type ENUM('registration', 'tax_certificate', 'business_license', 'other') NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    uploaded_by INT NOT NULL,
    verification_status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
    verified_by INT NULL,
    verified_at TIMESTAMP NULL,
    verification_notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_company_id (company_id),
    INDEX idx_document_type (document_type),
    INDEX idx_verification_status (verification_status),
    INDEX idx_uploaded_by (uploaded_by),
    INDEX idx_verified_by (verified_by),
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Create approval_history table for tracking approval workflow
CREATE TABLE IF NOT EXISTS approval_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    entity_type ENUM('user', 'company') NOT NULL,
    entity_id INT NOT NULL,
    action ENUM('created', 'approved', 'rejected', 'resubmitted') NOT NULL,
    performed_by INT NOT NULL,
    reason TEXT NULL,
    metadata JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_performed_by (performed_by),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (performed_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Update existing users to have approved status (for existing data)
UPDATE users SET approval_status = 'approved', approved_at = created_at WHERE approval_status IS NULL;

-- Update existing companies to have approved status (for existing data)
UPDATE companies SET approval_status = 'approved', approved_at = created_at WHERE approval_status IS NULL;
