class NotificationManager {
    constructor() {
        this.notifications = [];
        this.maxNotifications = 5;
        this.defaultDuration = 5000; // 5 seconds
        this.container = null;
        this.setupNotificationContainer();
    }

    setupNotificationContainer() {
        // Create notification container if it doesn't exist
        this.container = document.getElementById('notification-container');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'notification-container';
            this.container.className = 'notification-container';
            document.body.appendChild(this.container);
            
            // Add CSS styles
            this.addNotificationStyles();
        }
    }

    addNotificationStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .notification-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
            }

            .notification {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                margin-bottom: 10px;
                padding: 16px;
                min-width: 300px;
                max-width: 400px;
                pointer-events: auto;
                transform: translateX(100%);
                transition: all 0.3s ease;
                border-left: 4px solid #007bff;
                position: relative;
                overflow: hidden;
            }

            .notification.show {
                transform: translateX(0);
            }

            .notification.success {
                border-left-color: #28a745;
            }

            .notification.warning {
                border-left-color: #ffc107;
            }

            .notification.error {
                border-left-color: #dc3545;
            }

            .notification.info {
                border-left-color: #17a2b8;
            }

            .notification-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8px;
            }

            .notification-title {
                font-weight: 600;
                color: #333;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .notification-close {
                background: none;
                border: none;
                font-size: 18px;
                cursor: pointer;
                color: #999;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .notification-close:hover {
                color: #666;
            }

            .notification-message {
                color: #666;
                font-size: 14px;
                line-height: 1.4;
                margin-bottom: 8px;
            }

            .notification-actions {
                display: flex;
                gap: 8px;
                margin-top: 12px;
            }

            .notification-btn {
                padding: 4px 12px;
                border: 1px solid #ddd;
                background: white;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .notification-btn:hover {
                background: #f8f9fa;
            }

            .notification-btn.primary {
                background: #007bff;
                color: white;
                border-color: #007bff;
            }

            .notification-btn.primary:hover {
                background: #0056b3;
            }

            .notification-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                height: 3px;
                background: rgba(0, 123, 255, 0.3);
                transition: width linear;
            }

            .notification-icon {
                font-size: 16px;
            }

            .notification-suggestions {
                margin-top: 8px;
                padding-top: 8px;
                border-top: 1px solid #eee;
            }

            .notification-suggestions ul {
                margin: 4px 0 0 0;
                padding-left: 16px;
                font-size: 13px;
                color: #666;
            }

            .notification-suggestions li {
                margin-bottom: 2px;
            }

            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Show a notification
     */
    show(options) {
        const notification = this.createNotification(options);
        this.addNotification(notification);
        return notification.id;
    }

    /**
     * Show success notification
     */
    success(message, options = {}) {
        return this.show({
            type: 'success',
            title: options.title || 'Success',
            message,
            icon: '✅',
            ...options
        });
    }

    /**
     * Show error notification
     */
    error(message, options = {}) {
        return this.show({
            type: 'error',
            title: options.title || 'Error',
            message,
            icon: '❌',
            duration: options.duration || 8000, // Longer for errors
            ...options
        });
    }

    /**
     * Show warning notification
     */
    warning(message, options = {}) {
        return this.show({
            type: 'warning',
            title: options.title || 'Warning',
            message,
            icon: '⚠️',
            ...options
        });
    }

    /**
     * Show info notification
     */
    info(message, options = {}) {
        return this.show({
            type: 'info',
            title: options.title || 'Information',
            message,
            icon: 'ℹ️',
            ...options
        });
    }

    /**
     * Show progress notification
     */
    progress(message, options = {}) {
        return this.show({
            type: 'info',
            title: options.title || 'Processing',
            message,
            icon: '⏳',
            duration: 0, // Don't auto-dismiss
            showProgress: true,
            ...options
        });
    }

    /**
     * Update progress notification
     */
    updateProgress(id, progress, message) {
        const notification = this.notifications.find(n => n.id === id);
        if (notification) {
            const element = notification.element;
            const progressBar = element.querySelector('.notification-progress');
            const messageElement = element.querySelector('.notification-message');
            
            if (progressBar) {
                progressBar.style.width = `${progress}%`;
            }
            
            if (messageElement && message) {
                messageElement.textContent = message;
            }
        }
    }

    /**
     * Create notification element
     */
    createNotification(options) {
        const id = 'notification-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        const {
            type = 'info',
            title = 'Notification',
            message = '',
            icon = '',
            duration = this.defaultDuration,
            actions = [],
            suggestions = [],
            showProgress = false,
            dismissible = true
        } = options;

        const element = document.createElement('div');
        element.className = `notification ${type}`;
        element.setAttribute('data-id', id);

        let actionsHtml = '';
        if (actions.length > 0) {
            actionsHtml = `
                <div class="notification-actions">
                    ${actions.map(action => `
                        <button class="notification-btn ${action.primary ? 'primary' : ''}" 
                                onclick="window.notificationManager.handleAction('${id}', '${action.id}')">
                            ${action.label}
                        </button>
                    `).join('')}
                </div>
            `;
        }

        let suggestionsHtml = '';
        if (suggestions.length > 0) {
            suggestionsHtml = `
                <div class="notification-suggestions">
                    <strong>Suggestions:</strong>
                    <ul>
                        ${suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        element.innerHTML = `
            <div class="notification-header">
                <div class="notification-title">
                    ${icon ? `<span class="notification-icon">${icon}</span>` : ''}
                    ${title}
                </div>
                ${dismissible ? '<button class="notification-close" onclick="window.notificationManager.dismiss(\'' + id + '\')">&times;</button>' : ''}
            </div>
            <div class="notification-message">${message}</div>
            ${suggestionsHtml}
            ${actionsHtml}
            ${showProgress ? '<div class="notification-progress" style="width: 0%"></div>' : ''}
        `;

        const notification = {
            id,
            element,
            type,
            duration,
            actions: actions || [],
            createdAt: Date.now()
        };

        return notification;
    }

    /**
     * Add notification to container
     */
    addNotification(notification) {
        // Remove oldest notification if we have too many
        if (this.notifications.length >= this.maxNotifications) {
            this.dismiss(this.notifications[0].id);
        }

        this.notifications.push(notification);
        this.container.appendChild(notification.element);

        // Trigger animation
        setTimeout(() => {
            notification.element.classList.add('show');
        }, 10);

        // Auto-dismiss if duration is set
        if (notification.duration > 0) {
            setTimeout(() => {
                this.dismiss(notification.id);
            }, notification.duration);
        }
    }

    /**
     * Dismiss notification
     */
    dismiss(id) {
        const index = this.notifications.findIndex(n => n.id === id);
        if (index === -1) return;

        const notification = this.notifications[index];
        notification.element.classList.remove('show');

        setTimeout(() => {
            if (notification.element.parentNode) {
                notification.element.parentNode.removeChild(notification.element);
            }
            this.notifications.splice(index, 1);
        }, 300);
    }

    /**
     * Handle notification actions
     */
    handleAction(notificationId, actionId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (!notification) return;

        const action = notification.actions.find(a => a.id === actionId);
        if (action && action.handler) {
            action.handler();
        }

        // Auto-dismiss after action unless specified otherwise
        if (!action || action.dismissAfter !== false) {
            this.dismiss(notificationId);
        }
    }

    /**
     * Clear all notifications
     */
    clearAll() {
        this.notifications.forEach(notification => {
            this.dismiss(notification.id);
        });
    }

    /**
     * Show deployment error with suggestions
     */
    showDeploymentError(error) {
        return this.error(error.message, {
            title: error.title || 'Deployment Error',
            suggestions: error.suggestions || [],
            actions: error.recoverable ? [
                {
                    id: 'retry',
                    label: 'Retry',
                    primary: true,
                    handler: () => {
                        // Trigger retry logic
                        window.dispatchEvent(new CustomEvent('deployment-retry'));
                    }
                },
                {
                    id: 'details',
                    label: 'Show Details',
                    handler: () => {
                        console.error('Error details:', error);
                        this.info('Error details have been logged to the console', {
                            title: 'Debug Information'
                        });
                    }
                }
            ] : [
                {
                    id: 'details',
                    label: 'Show Details',
                    handler: () => {
                        console.error('Error details:', error);
                    }
                }
            ]
        });
    }

    /**
     * Show configuration validation errors
     */
    showValidationErrors(validation) {
        if (validation.errors.length > 0) {
            const errorMessages = validation.errors.map(e => e.message).join(', ');
            this.error(`Configuration errors: ${errorMessages}`, {
                title: 'Configuration Invalid',
                suggestions: validation.errors.map(e => e.suggestion)
            });
        }

        if (validation.warnings.length > 0) {
            const warningMessages = validation.warnings.map(w => w.message).join(', ');
            this.warning(`Configuration warnings: ${warningMessages}`, {
                title: 'Configuration Warnings',
                suggestions: validation.warnings.map(w => w.suggestion)
            });
        }
    }

    /**
     * Show deployment success
     */
    showDeploymentSuccess(deployment) {
        return this.success(`Deployment package created successfully!`, {
            title: 'Deployment Ready',
            actions: [
                {
                    id: 'open-guide',
                    label: 'Open Guide',
                    primary: true,
                    handler: () => {
                        window.dispatchEvent(new CustomEvent('open-deployment-guide'));
                    }
                },
                {
                    id: 'open-folder',
                    label: 'Open Folder',
                    handler: () => {
                        window.dispatchEvent(new CustomEvent('open-package-folder'));
                    }
                }
            ]
        });
    }
}

// Make it globally available
if (typeof window !== 'undefined') {
    window.notificationManager = new NotificationManager();
}

module.exports = NotificationManager;
