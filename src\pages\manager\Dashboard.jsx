import React, { useState, useEffect, useContext } from 'react';
import {
  UsersIcon,
  Building2Icon,
  PackageIcon,
  TrendingUpIcon,
  DollarSignIcon,
  AlertTriangleIcon,
  BarChart3Icon,
  PieChartIcon,
  ActivityIcon,
  ArrowUpIcon,
  UserPlusIcon,
  BuildingIcon,
  EyeIcon,
  FileTextIcon,
  ShieldCheckIcon
} from 'lucide-react';
import { AuthContext } from '../../App.jsx';
import inventoryService from '../../services/inventoryService.js';
import userService from '../../services/userService.js';
import companyService from '../../services/companyService.js';

const ManagerDashboard = () => {
  const { user } = useContext(AuthContext);
  const [dashboardData, setDashboardData] = useState({
    companies: { total_companies: 0, active_companies: 0 },
    inventory: { total_items: 0, total_value: 0, low_stock_count: 0 },
    top_categories: [],
    recent_activity: [],
    assigned_companies: []
  });
  const [loading, setLoading] = useState(true);
  const [companyStats, setCompanyStats] = useState([]);

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);

        // Get dashboard data from API
        const data = await inventoryService.getDashboardData();

        if (data) {
          setDashboardData({
            companies: data.companies || { total_companies: 0, active_companies: 0 },
            inventory: data.inventory || { total_items: 0, total_value: 0, low_stock_count: 0 },
            top_categories: data.top_categories || [],
            recent_activity: data.recent_activity || [],
            assigned_companies: data.assigned_companies || []
          });
        }

        // Get company stats
        try {
          const companyStatsResponse = await inventoryService.getCompanyStats();
          if (companyStatsResponse) {
            setCompanyStats(companyStatsResponse.top_companies || []);
          }
        } catch (error) {
          console.error('Failed to load company stats:', error);
        }
      } catch (error) {
        console.error('Failed to load dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      loadDashboardData();
    }
  }, [user]);

  const StatCard = ({ title, value, icon, color = 'blue', subtitle, trend }) => {
    const colorClasses = {
      blue: 'bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 border-blue-200',
      green: 'bg-gradient-to-br from-green-50 to-green-100 text-green-600 border-green-200',
      purple: 'bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600 border-purple-200',
      orange: 'bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600 border-orange-200',
      red: 'bg-gradient-to-br from-red-50 to-red-100 text-red-600 border-red-200',
      indigo: 'bg-gradient-to-br from-indigo-50 to-indigo-100 text-indigo-600 border-indigo-200'
    };

    return (
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
            <p className="text-3xl font-bold text-gray-900 mb-2">{value}</p>
            {subtitle && (
              <p className="text-sm text-gray-500">{subtitle}</p>
            )}
          </div>
          <div className={`p-4 rounded-xl border ${colorClasses[color]}`}>
            {icon}
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Manager Dashboard</h1>
          <p className="text-gray-600">Company management and partner oversight</p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Manager Dashboard</h1>
        <p className="text-gray-600">Comprehensive company management and partner oversight</p>
      </div>

      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <StatCard
          title="Managed Companies"
          value={dashboardData.companies.total_companies}
          subtitle={`${dashboardData.companies.active_companies} active companies`}
          icon={<Building2Icon className="h-8 w-8" />}
          color="blue"
        />
        <StatCard
          title="Total Inventory Value"
          value={`$${dashboardData.inventory.total_value.toLocaleString()}`}
          subtitle={`${dashboardData.inventory.total_items} total items`}
          icon={<DollarSignIcon className="h-8 w-8" />}
          color="green"
        />
        <StatCard
          title="Low Stock Alerts"
          value={dashboardData.inventory.low_stock_count}
          subtitle="Items need attention"
          icon={<AlertTriangleIcon className="h-8 w-8" />}
          color="red"
        />
      </div>

      {/* Company Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Top Companies by Value */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center">
              <BarChart3Icon className="h-5 w-5 text-green-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Top Companies by Value</h3>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {companyStats.slice(0, 5).map((company, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 ${
                      ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500', 'bg-red-500'][index]
                    }`}>
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">{company.name}</p>
                      <p className="text-sm text-gray-500">{company.industry}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-green-600">${company.total_value?.toLocaleString()}</p>
                    <p className="text-sm text-gray-500">{company.inventory_count} items</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Inventory Categories */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center">
              <PieChartIcon className="h-5 w-5 text-purple-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Inventory Categories</h3>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {dashboardData.top_categories.slice(0, 5).map((category, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-3 ${
                      ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500', 'bg-red-500'][index]
                    }`}></div>
                    <span className="font-medium text-gray-900">{category.category || 'Uncategorized'}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-semibold text-gray-900">{category.item_count} items</div>
                    <div className="text-xs text-gray-500">${category.total_value?.toLocaleString()}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Activity and Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activity */}
        <div className="lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center">
              <ActivityIcon className="h-5 w-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Recent Company Activity</h3>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {dashboardData.recent_activity.slice(0, 6).map((activity, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    activity.action === 'create' ? 'bg-green-500' :
                    activity.action === 'update' ? 'bg-blue-500' :
                    activity.action === 'delete' ? 'bg-red-500' : 'bg-gray-500'
                  }`}></div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">
                      {activity.action === 'create' && 'Created '}
                      {activity.action === 'update' && 'Updated '}
                      {activity.action === 'delete' && 'Deleted '}
                      {activity.entity_type}
                    </p>
                    <p className="text-xs text-gray-500">
                      {activity.user_name} • {new Date(activity.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
          </div>
          <div className="p-6">
            <div className="space-y-3">
              <button className="w-full flex items-center justify-between p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                <div className="flex items-center">
                  <UserPlusIcon className="h-5 w-5 text-blue-600 mr-3" />
                  <span className="text-sm font-medium text-blue-900">Add Partner</span>
                </div>
                <ArrowUpIcon className="h-4 w-4 text-blue-600 transform rotate-45" />
              </button>

              <button className="w-full flex items-center justify-between p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors">
                <div className="flex items-center">
                  <BuildingIcon className="h-5 w-5 text-green-600 mr-3" />
                  <span className="text-sm font-medium text-green-900">Manage Companies</span>
                </div>
                <ArrowUpIcon className="h-4 w-4 text-green-600 transform rotate-45" />
              </button>

              <button className="w-full flex items-center justify-between p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
                <div className="flex items-center">
                  <FileTextIcon className="h-5 w-5 text-purple-600 mr-3" />
                  <span className="text-sm font-medium text-purple-900">View Reports</span>
                </div>
                <ArrowUpIcon className="h-4 w-4 text-purple-600 transform rotate-45" />
              </button>

              <button className="w-full flex items-center justify-between p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors">
                <div className="flex items-center">
                  <ShieldCheckIcon className="h-5 w-5 text-orange-600 mr-3" />
                  <span className="text-sm font-medium text-orange-900">Audit Trail</span>
                </div>
                <ArrowUpIcon className="h-4 w-4 text-orange-600 transform rotate-45" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManagerDashboard;
