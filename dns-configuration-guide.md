# DNS Configuration Guide for EskillVisor Custom Domain

## 🌐 **Custom Domain Setup Options**

### **Option 1: Subdomain (eskillvisor.wallistry.pk) - RECOMMENDED**

This is the easiest option since you already own wallistry.pk.

#### **cPanel Configuration:**

1. **Login to cPanel** (wallistry.pk/cpanel)
2. **Navigate to "Subdomains"** in the Domains section
3. **Create New Subdomain:**
   - **Subdomain**: `eskillvisor`
   - **Domain**: `wallistry.pk`
   - **Document Root**: `public_html/eskillvisor`
4. **Click "Create"**

#### **DNS Records (Automatic):**
- c<PERSON>anel automatically creates the necessary DNS records
- No additional DNS configuration needed
- Subdomain will be active within 15-30 minutes

#### **SSL Certificate:**
- cPanel should automatically provision SSL for the subdomain
- If not, go to "SSL/TLS" → "Let's Encrypt" and add the subdomain

---

### **Option 2: Custom Domain (e.g., eskillvisor.com)**

If you want a completely separate domain.

#### **Prerequisites:**
1. **Purchase a domain** (e.g., eskillvisor.com from GoDaddy, Namecheap, etc.)
2. **Access to domain registrar's DNS settings**

#### **cPanel Configuration:**

1. **Login to cPanel**
2. **Navigate to "Addon Domains"**
3. **Add New Domain:**
   - **New Domain Name**: `eskillvisor.com`
   - **Subdomain**: `eskillvisor`
   - **Document Root**: `public_html/eskillvisor`
4. **Click "Add Domain"**

#### **DNS Configuration at Domain Registrar:**

You need to update DNS records at your domain registrar to point to your hosting server.

**Required DNS Records:**

```
Type    Name    Value                           TTL
A       @       [Your Server IP Address]       3600
A       www     [Your Server IP Address]       3600
CNAME   *       eskillvisor.com                3600
```

**To find your server IP address:**
1. In cPanel, go to "Server Information"
2. Look for "Server IP Address"
3. Use this IP in the DNS records

#### **Example DNS Settings (GoDaddy):**

1. **Login to GoDaddy** (or your registrar)
2. **Go to DNS Management** for your domain
3. **Add/Edit these records:**
   - **A Record**: `@` → `[Server IP]`
   - **A Record**: `www` → `[Server IP]`
   - **CNAME**: `*` → `eskillvisor.com`

#### **Example DNS Settings (Namecheap):**

1. **Login to Namecheap**
2. **Domain List** → **Manage** → **Advanced DNS**
3. **Add these records:**
   - **A Record**: `@` → `[Server IP]`
   - **A Record**: `www` → `[Server IP]`
   - **CNAME**: `*` → `eskillvisor.com`

---

## 🔧 **Post-Configuration Steps**

### **1. Upload Application Files**

After domain configuration, upload your files:

1. **Navigate to domain directory** in cPanel File Manager:
   - Subdomain: `/public_html/eskillvisor/`
   - Custom domain: `/public_html/eskillvisor/`

2. **Upload files** from `cpanel-deployment/public_html/`:
   - Upload all files and folders
   - Maintain directory structure

3. **Set permissions:**
   - Files: 644
   - Directories: 755

### **2. SSL Certificate Setup**

#### **For Subdomain:**
- Usually automatic via cPanel
- Check "SSL/TLS" → "Let's Encrypt"

#### **For Custom Domain:**
1. **Go to cPanel** → **SSL/TLS** → **Let's Encrypt**
2. **Select your domain**
3. **Click "Issue"**
4. **Wait for certificate generation**

### **3. Force HTTPS Redirect**

Create `.htaccess` file in your domain root with:

```apache
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

---

## 🧪 **Testing Your Domain**

### **1. DNS Propagation Check**

Use online tools to check DNS propagation:
- https://dnschecker.org/
- https://whatsmydns.net/

Enter your domain and check if it resolves to your server IP.

### **2. Domain Accessibility Test**

1. **Visit your domain** in a browser:
   - `https://eskillvisor.wallistry.pk` (subdomain)
   - `https://eskillvisor.com` (custom domain)

2. **Check for:**
   - Frontend loads correctly
   - No SSL certificate errors
   - No mixed content warnings

### **3. API Endpoint Test**

Test API endpoints:
- `https://your-domain/api/test`
- `https://your-domain/api/login.php`

### **4. Application Functionality Test**

- Login with: <EMAIL> / password
- Verify dashboard loads
- Test user and company management features

---

## 🚨 **Troubleshooting**

### **Common Issues:**

#### **1. Domain Not Resolving**
- **Check DNS propagation** (can take 24-48 hours)
- **Verify DNS records** at registrar
- **Confirm server IP address** in cPanel

#### **2. SSL Certificate Issues**
- **Wait for propagation** before requesting SSL
- **Try manual SSL** in cPanel → SSL/TLS
- **Check domain validation** emails

#### **3. 404 Errors**
- **Verify file upload** to correct directory
- **Check file permissions** (644/755)
- **Confirm domain document root** setting

#### **4. API Not Working**
- **Check CORS settings** in backend files
- **Verify database connection**
- **Check PHP error logs** in cPanel

### **Debug Commands:**

```bash
# Check DNS resolution
nslookup eskillvisor.wallistry.pk

# Check HTTP response
curl -I https://eskillvisor.wallistry.pk

# Check API endpoint
curl https://eskillvisor.wallistry.pk/api/test
```

---

## 📋 **Configuration Checklist**

- [ ] Domain/subdomain created in cPanel
- [ ] DNS records configured (if custom domain)
- [ ] Application files uploaded to correct directory
- [ ] File permissions set (644/755)
- [ ] SSL certificate installed
- [ ] HTTPS redirect configured
- [ ] DNS propagation completed
- [ ] Domain loads frontend correctly
- [ ] API endpoints respond
- [ ] Application functionality tested

---

## ⏱️ **Timeline Expectations**

- **Subdomain**: 15-30 minutes
- **Custom Domain**: 2-48 hours (depending on DNS propagation)
- **SSL Certificate**: 5-15 minutes after domain is accessible

---

## 📞 **Support Resources**

- **cPanel Documentation**: Check your hosting provider's knowledge base
- **DNS Propagation Checker**: https://dnschecker.org/
- **SSL Test**: https://www.ssllabs.com/ssltest/
