const fs = require('fs-extra');
const path = require('path');
const Handlebars = require('handlebars');

class DocumentationGenerator {
    constructor() {
        this.templatesDir = path.join(__dirname, '../../templates');
        this.outputDir = path.join(__dirname, '../../output/documentation');
        this.setupHandlebars();
        this.ensureDirectories();
    }

    async ensureDirectories() {
        await fs.ensureDir(this.outputDir);
        await fs.ensureDir(this.templatesDir);
    }

    setupHandlebars() {
        // Register custom helpers
        Handlebars.registerHelper('formatDate', (date) => {
            return new Date(date).toLocaleString();
        });

        Handlebars.registerHelper('formatFileSize', (bytes) => {
            if (!bytes) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        });

        Handlebars.registerHelper('statusIcon', (status) => {
            const icons = {
                added: '✅',
                modified: '🔄',
                deleted: '❌'
            };
            return icons[status] || '📄';
        });

        Handlebars.registerHelper('statusColor', (status) => {
            const colors = {
                added: '#28a745',
                modified: '#ffc107',
                deleted: '#dc3545'
            };
            return colors[status] || '#6c757d';
        });

        Handlebars.registerHelper('categoryIcon', (category) => {
            const icons = {
                frontend: '🎨',
                backend: '⚙️',
                database: '🗄️',
                config: '⚙️',
                assets: '🖼️',
                other: '📄'
            };
            return icons[category] || '📄';
        });

        Handlebars.registerHelper('impactBadge', (impact) => {
            const badges = {
                low: { color: '#28a745', text: 'Low Impact' },
                medium: { color: '#ffc107', text: 'Medium Impact' },
                high: { color: '#dc3545', text: 'High Impact' }
            };
            const badge = badges[impact] || badges.low;
            return `<span style="background: ${badge.color}; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8em;">${badge.text}</span>`;
        });

        Handlebars.registerHelper('eq', (a, b) => a === b);
        Handlebars.registerHelper('gt', (a, b) => a > b);
        Handlebars.registerHelper('add', (a, b) => a + b);
    }

    /**
     * Generate deployment guide HTML
     */
    async generateDeploymentGuide(deployment) {
        try {
            console.log('Generating deployment documentation...');

            const templateData = this.prepareTemplateData(deployment);
            const html = await this.renderTemplate('deployment-guide.hbs', templateData);
            
            const fileName = `deployment-guide-${deployment.id}.html`;
            const filePath = path.join(this.outputDir, fileName);
            
            await fs.writeFile(filePath, html, 'utf8');
            
            deployment.documentationPath = filePath;
            
            console.log(`Documentation generated: ${fileName}`);
            return {
                success: true,
                path: filePath,
                fileName
            };

        } catch (error) {
            console.error('Error generating documentation:', error);
            throw new Error(`Documentation generation failed: ${error.message}`);
        }
    }

    /**
     * Prepare data for template rendering
     */
    prepareTemplateData(deployment) {
        const now = new Date();
        const serverPath = deployment.config.deployment?.serverPath || '/public_html';
        const domain = deployment.config.deployment?.domain || 'your-domain.com';
        
        return {
            // Deployment info
            deployment: {
                id: deployment.id,
                timestamp: deployment.timestamp,
                projectName: deployment.projectName,
                packageName: deployment.packageName,
                packageSize: deployment.packageSize,
                fileCount: deployment.fileCount
            },

            // Project info
            project: {
                name: deployment.projectName,
                type: deployment.config.projectType,
                domain: domain,
                serverPath: serverPath
            },

            // Summary statistics
            summary: {
                totalFiles: deployment.summary.totalFiles,
                changedFiles: deployment.summary.changedFiles,
                addedFiles: deployment.summary.addedFiles,
                modifiedFiles: deployment.summary.modifiedFiles,
                categories: this.formatCategories(deployment.summary.categories)
            },

            // Features and analysis
            features: deployment.analysis?.features || [],
            technicalDetails: deployment.analysis?.technicalDetails || [],
            impactAssessment: deployment.analysis?.impactAssessment || 'low',

            // File changes by category
            changesByCategory: this.organizeChangesByCategory(deployment.changes, deployment.summary.categories),

            // Upload instructions
            uploadInstructions: this.generateDetailedInstructions(deployment),

            // Verification steps
            verificationSteps: this.generateVerificationSteps(deployment),

            // Troubleshooting
            troubleshooting: this.generateTroubleshootingGuide(deployment),

            // Metadata
            meta: {
                generatedAt: now.toISOString(),
                generatedBy: 'Universal Deployment System v1.0.0',
                documentTitle: `Deployment Guide - ${deployment.projectName}`,
                documentDescription: `Deployment guide for ${deployment.projectName} generated on ${now.toLocaleDateString()}`
            }
        };
    }

    /**
     * Format categories for template
     */
    formatCategories(categories) {
        return Object.entries(categories).map(([name, data]) => ({
            name,
            displayName: this.formatCategoryName(name),
            count: data.count,
            description: data.description,
            files: data.files || []
        })).filter(cat => cat.count > 0);
    }

    /**
     * Organize changes by category
     */
    organizeChangesByCategory(changes, categories) {
        const organized = {};
        
        Object.keys(categories).forEach(categoryName => {
            if (categories[categoryName].count > 0) {
                organized[categoryName] = {
                    name: categoryName,
                    displayName: this.formatCategoryName(categoryName),
                    files: categories[categoryName].files || []
                };
            }
        });

        return organized;
    }

    /**
     * Generate detailed upload instructions
     */
    generateDetailedInstructions(deployment) {
        const serverPath = deployment.config.deployment?.serverPath || '/public_html';
        const domain = deployment.config.deployment?.domain || 'your-domain.com';
        
        return {
            preparation: [
                {
                    title: 'Backup Current Files',
                    description: 'Create a backup of your current website files before deploying',
                    action: 'Download a copy of your current website files from cPanel File Manager',
                    importance: 'critical'
                },
                {
                    title: 'Download Deployment Package',
                    description: 'Ensure you have the deployment package ready',
                    action: `Download ${deployment.packageName} to your computer`,
                    importance: 'required'
                }
            ],
            
            upload: [
                {
                    step: 1,
                    title: 'Access cPanel',
                    description: 'Log into your hosting control panel',
                    action: `Navigate to your cPanel for ${domain}`,
                    details: 'Use your hosting provider\'s cPanel login URL'
                },
                {
                    step: 2,
                    title: 'Open File Manager',
                    description: 'Access the file management interface',
                    action: 'Click on "File Manager" in the Files section',
                    details: 'This will open the web-based file browser'
                },
                {
                    step: 3,
                    title: 'Navigate to Web Root',
                    description: 'Go to your website\'s root directory',
                    action: `Navigate to ${serverPath}`,
                    details: 'This is where your website files are stored'
                },
                {
                    step: 4,
                    title: 'Upload Package',
                    description: 'Upload the deployment ZIP file',
                    action: 'Click "Upload" and select the deployment package',
                    details: 'Wait for the upload to complete before proceeding'
                },
                {
                    step: 5,
                    title: 'Extract Files',
                    description: 'Extract the uploaded ZIP file',
                    action: 'Right-click the ZIP file and select "Extract"',
                    details: 'Choose to extract to the current directory'
                },
                {
                    step: 6,
                    title: 'Clean Up',
                    description: 'Remove the ZIP file after extraction',
                    action: 'Delete the uploaded ZIP file',
                    details: 'This saves disk space and keeps your directory clean'
                }
            ],

            postDeployment: [
                {
                    title: 'Set File Permissions',
                    description: 'Ensure correct file permissions are set',
                    action: 'Set files to 644 and directories to 755',
                    importance: 'recommended'
                },
                {
                    title: 'Clear Caches',
                    description: 'Clear any caching systems',
                    action: 'Clear website cache, CDN cache, and browser cache',
                    importance: 'recommended'
                },
                {
                    title: 'Test Functionality',
                    description: 'Verify all features work correctly',
                    action: 'Test critical functionality and user flows',
                    importance: 'critical'
                }
            ]
        };
    }

    /**
     * Generate verification steps
     */
    generateVerificationSteps(deployment) {
        const domain = deployment.config.deployment?.domain || 'your-domain.com';
        
        return [
            {
                title: 'Website Accessibility',
                description: `Verify your website loads correctly`,
                action: `Visit https://${domain}`,
                expected: 'Website loads without errors'
            },
            {
                title: 'File Structure',
                description: 'Check that files are in correct locations',
                action: 'Review file structure in cPanel File Manager',
                expected: 'All files are in their intended directories'
            },
            {
                title: 'Functionality Testing',
                description: 'Test key features and functionality',
                action: 'Navigate through important pages and features',
                expected: 'All features work as expected'
            },
            {
                title: 'Error Checking',
                description: 'Check for any error messages',
                action: 'Review browser console and server error logs',
                expected: 'No critical errors present'
            }
        ];
    }

    /**
     * Generate troubleshooting guide
     */
    generateTroubleshootingGuide(deployment) {
        return {
            common: [
                {
                    issue: 'Website shows 500 Internal Server Error',
                    causes: ['Incorrect file permissions', 'PHP syntax errors', 'Missing dependencies'],
                    solutions: [
                        'Check file permissions (644 for files, 755 for directories)',
                        'Review error logs in cPanel',
                        'Verify all required files were uploaded'
                    ]
                },
                {
                    issue: 'Website not loading or showing old content',
                    causes: ['Browser cache', 'CDN cache', 'Server cache'],
                    solutions: [
                        'Clear browser cache (Ctrl+F5)',
                        'Clear CDN cache if applicable',
                        'Clear server-side cache'
                    ]
                },
                {
                    issue: 'Missing files or broken links',
                    causes: ['Incomplete upload', 'Incorrect file paths', 'Case sensitivity'],
                    solutions: [
                        'Re-upload missing files',
                        'Check file paths and names',
                        'Verify case sensitivity on Linux servers'
                    ]
                }
            ],
            
            contacts: [
                {
                    type: 'Technical Support',
                    description: 'Contact your hosting provider for server-related issues',
                    when: 'Server errors, permission issues, or hosting-specific problems'
                },
                {
                    type: 'Developer Support',
                    description: 'Contact your development team for application issues',
                    when: 'Code errors, functionality problems, or feature-related issues'
                }
            ]
        };
    }

    /**
     * Render template with data
     */
    async renderTemplate(templateName, data) {
        try {
            const templatePath = path.join(this.templatesDir, templateName);
            
            // Check if template exists, if not create a default one
            if (!await fs.pathExists(templatePath)) {
                await this.createDefaultTemplate(templatePath);
            }
            
            const templateContent = await fs.readFile(templatePath, 'utf8');
            const template = Handlebars.compile(templateContent);
            
            return template(data);
            
        } catch (error) {
            console.error('Error rendering template:', error);
            // Fallback to basic HTML generation
            return this.generateBasicHTML(data);
        }
    }

    /**
     * Create default template if none exists
     */
    async createDefaultTemplate(templatePath) {
        const defaultTemplate = await this.getDefaultTemplate();
        await fs.writeFile(templatePath, defaultTemplate, 'utf8');
    }

    /**
     * Get default template content
     */
    async getDefaultTemplate() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{meta.documentTitle}}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; border-radius: 8px; margin-bottom: 2rem; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 2rem 0; }
        .stat-card { background: #f8f9fa; padding: 1.5rem; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 2rem; font-weight: bold; color: #007bff; }
        .section { margin: 2rem 0; padding: 1.5rem; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .step { margin: 1rem 0; padding: 1rem; border-left: 4px solid #007bff; background: #f8f9fa; }
        .file-list { max-height: 300px; overflow-y: auto; }
        .file-item { display: flex; align-items: center; padding: 0.5rem; border-bottom: 1px solid #eee; }
        .status-badge { padding: 2px 8px; border-radius: 12px; font-size: 0.8em; color: white; margin-right: 0.5rem; }
        .added { background: #28a745; }
        .modified { background: #ffc107; }
        .deleted { background: #dc3545; }
        .btn { display: inline-block; padding: 0.75rem 1.5rem; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 0.5rem 0; }
        .alert { padding: 1rem; border-radius: 4px; margin: 1rem 0; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Deployment Guide</h1>
        <p><strong>Project:</strong> {{deployment.projectName}}</p>
        <p><strong>Package:</strong> {{deployment.packageName}}</p>
        <p><strong>Generated:</strong> {{formatDate deployment.timestamp}}</p>
        <p><strong>Impact:</strong> {{{impactBadge impactAssessment}}}</p>
    </div>

    <div class="summary">
        <div class="stat-card">
            <div class="stat-number">{{summary.totalFiles}}</div>
            <div>Total Files</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{summary.changedFiles}}</div>
            <div>Changed Files</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{formatFileSize deployment.packageSize}}</div>
            <div>Package Size</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{deployment.fileCount}}</div>
            <div>Files in Package</div>
        </div>
    </div>

    {{#if features}}
    <div class="section">
        <h2>✨ Features & Improvements</h2>
        <ul>
            {{#each features}}
            <li>{{this}}</li>
            {{/each}}
        </ul>
    </div>
    {{/if}}

    <div class="section">
        <h2>📋 Upload Instructions</h2>
        
        <div class="alert alert-warning">
            <strong>⚠️ Important:</strong> Always backup your current files before deploying!
        </div>

        <h3>Preparation Steps</h3>
        {{#each uploadInstructions.preparation}}
        <div class="step">
            <h4>{{title}}</h4>
            <p>{{description}}</p>
            <strong>Action:</strong> {{action}}
        </div>
        {{/each}}

        <h3>Upload Process</h3>
        {{#each uploadInstructions.upload}}
        <div class="step">
            <h4>Step {{step}}: {{title}}</h4>
            <p>{{description}}</p>
            <strong>Action:</strong> {{action}}
            {{#if details}}<br><em>{{details}}</em>{{/if}}
        </div>
        {{/each}}

        <h3>Post-Deployment</h3>
        {{#each uploadInstructions.postDeployment}}
        <div class="step">
            <h4>{{title}}</h4>
            <p>{{description}}</p>
            <strong>Action:</strong> {{action}}
        </div>
        {{/each}}
    </div>

    <div class="section">
        <h2>✅ Verification Steps</h2>
        {{#each verificationSteps}}
        <div class="step">
            <h4>{{title}}</h4>
            <p>{{description}}</p>
            <strong>Action:</strong> {{action}}<br>
            <strong>Expected:</strong> {{expected}}
        </div>
        {{/each}}
    </div>

    {{#if summary.categories}}
    <div class="section">
        <h2>📁 Files by Category</h2>
        {{#each summary.categories}}
        {{#if (gt count 0)}}
        <h3>{{categoryIcon name}} {{displayName}} ({{count}} files)</h3>
        <div class="file-list">
            {{#each files}}
            <div class="file-item">
                <span class="status-badge {{status}}">{{statusIcon status}}</span>
                <code>{{path}}</code>
                <span style="margin-left: auto; color: #666;">{{formatFileSize size}}</span>
            </div>
            {{/each}}
        </div>
        {{/if}}
        {{/each}}
    </div>
    {{/if}}

    <div class="section">
        <h2>🔧 Troubleshooting</h2>
        {{#each troubleshooting.common}}
        <h4>{{issue}}</h4>
        <p><strong>Possible Causes:</strong> {{#each causes}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}</p>
        <p><strong>Solutions:</strong></p>
        <ul>
            {{#each solutions}}
            <li>{{this}}</li>
            {{/each}}
        </ul>
        {{/each}}
    </div>

    <div class="section">
        <h2>📞 Support Contacts</h2>
        {{#each troubleshooting.contacts}}
        <h4>{{type}}</h4>
        <p>{{description}}</p>
        <p><strong>When to contact:</strong> {{when}}</p>
        {{/each}}
    </div>

    <footer style="text-align: center; margin-top: 3rem; padding: 2rem; color: #666; border-top: 1px solid #eee;">
        <p>Generated by {{meta.generatedBy}} on {{formatDate meta.generatedAt}}</p>
    </footer>
</body>
</html>`;
    }

    /**
     * Generate basic HTML fallback
     */
    generateBasicHTML(data) {
        return `<!DOCTYPE html>
<html>
<head>
    <title>Deployment Guide - ${data.deployment.projectName}</title>
    <style>body{font-family:Arial,sans-serif;margin:40px;}</style>
</head>
<body>
    <h1>Deployment Guide</h1>
    <p><strong>Project:</strong> ${data.deployment.projectName}</p>
    <p><strong>Package:</strong> ${data.deployment.packageName}</p>
    <p><strong>Generated:</strong> ${new Date(data.deployment.timestamp).toLocaleString()}</p>
    
    <h2>Summary</h2>
    <ul>
        <li>Total Files: ${data.summary.totalFiles}</li>
        <li>Changed Files: ${data.summary.changedFiles}</li>
        <li>Package Size: ${this.formatFileSize(data.deployment.packageSize)}</li>
    </ul>
    
    <h2>Upload Instructions</h2>
    <ol>
        <li>Log into cPanel for ${data.project.domain}</li>
        <li>Open File Manager</li>
        <li>Navigate to ${data.project.serverPath}</li>
        <li>Upload ${data.deployment.packageName}</li>
        <li>Extract the ZIP file</li>
        <li>Verify deployment</li>
    </ol>
    
    <p><em>Generated by Universal Deployment System</em></p>
</body>
</html>`;
    }

    formatCategoryName(name) {
        return name.charAt(0).toUpperCase() + name.slice(1).replace(/([A-Z])/g, ' $1');
    }

    formatFileSize(bytes) {
        if (!bytes) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
}

module.exports = DocumentationGenerator;
