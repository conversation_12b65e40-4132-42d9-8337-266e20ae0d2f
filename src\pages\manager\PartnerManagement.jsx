import React, { useState, useContext } from 'react';
import { PlusIcon, SearchIcon } from 'lucide-react';
import { AuthContext } from '../../App.jsx';
import AddUserModal from '../../components/modals/AddUserModal.jsx';
import userService from '../../services/userService.js';

const PartnerManagement = () => {
  const { userRole } = useContext(AuthContext);
  const [partners, setPartners] = useState([
    { id: '1', name: 'Bob Partner', email: '<EMAIL>', companies: ['Acme Corp', 'TechStart Inc'], status: 'active' },
    { id: '2', name: 'Alice Partner', email: '<EMAIL>', companies: ['Global Ventures'], status: 'active' }
  ]);
  const [showAddModal, setShowAddModal] = useState(false);

  const handleAddPartner = async (userData) => {
    try {
      // Ensure role is set to partner for managers
      const partnerData = {
        ...userData,
        role: 'partner'
      };
      const newPartner = await userService.createUser(partnerData);
      setPartners(prev => [...prev, {
        id: newPartner.id,
        name: newPartner.name,
        email: newPartner.email,
        companies: [],
        status: newPartner.status || 'active'
      }]);
      setShowAddModal(false);
    } catch (error) {
      console.error('Failed to add partner:', error);
      throw error; // Re-throw to let modal handle the error
    }
  };

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Partner Management</h1>
            <p className="text-gray-600">Manage partners and their company assignments</p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Partner
          </button>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg">
        <div className="p-4 border-b border-gray-200">
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search partners..."
              className="pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary w-64"
            />
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Partner</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned Companies</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {partners.map((partner) => (
                <tr key={partner.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{partner.name}</div>
                      <div className="text-sm text-gray-500">{partner.email}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{partner.companies.join(', ')}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                      {partner.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-primary hover:text-primary-dark mr-3">Edit</button>
                    <button className="text-red-600 hover:text-red-900">Remove</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Partner Modal */}
      <AddUserModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSubmit={handleAddPartner}
        userRole={userRole}
      />
    </div>
  );
};

export default PartnerManagement;
