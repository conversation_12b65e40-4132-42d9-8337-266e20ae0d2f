<!DOCTYPE html>
<html>
<head>
    <title>Test Role Routing</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background: #d4edda; }
        .error { background: #f8d7da; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; margin: 5px; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Role Routing Test</h1>
    
    <button onclick="testLogin()">Test Admin Login & Routing</button>
    <button onclick="testManagerLogin()">Test Manager Login</button>
    <button onclick="testPartnerLogin()">Test Partner Login</button>
    
    <div id="results"></div>
    
    <script>
        async function testLogin() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h3>Testing Admin Login...</h3>';
            
            try {
                // Test login
                const loginResponse = await fetch('http://localhost/Investment-System-eSkillVisor/backend/login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': 'http://localhost:5173'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password'
                    })
                });
                
                const loginData = await loginResponse.json();
                
                if (loginData.success) {
                    const user = loginData.data.user;
                    
                    resultsDiv.innerHTML += `
                        <div class="test-result success">
                            <h4>✅ Login Successful</h4>
                            <p><strong>User Role:</strong> ${user.role}</p>
                            <p><strong>Expected Route:</strong> ${getExpectedRoute(user.role)}</p>
                            <pre>${JSON.stringify(user, null, 2)}</pre>
                        </div>
                    `;
                    
                    // Test role routing logic
                    testRoleRouting(user.role);
                    
                } else {
                    resultsDiv.innerHTML += `
                        <div class="test-result error">
                            <h4>❌ Login Failed</h4>
                            <p>${loginData.message}</p>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultsDiv.innerHTML += `
                    <div class="test-result error">
                        <h4>❌ Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testManagerLogin() {
            await testUserLogin('<EMAIL>', 'Manager');
        }
        
        async function testPartnerLogin() {
            await testUserLogin('<EMAIL>', 'Partner');
        }
        
        async function testUserLogin(email, userType) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `<h3>Testing ${userType} Login...</h3>`;
            
            try {
                const loginResponse = await fetch('http://localhost/Investment-System-eSkillVisor/backend/login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': 'http://localhost:5173'
                    },
                    body: JSON.stringify({
                        email: email,
                        password: 'password'
                    })
                });
                
                const loginData = await loginResponse.json();
                
                if (loginData.success) {
                    const user = loginData.data.user;
                    
                    resultsDiv.innerHTML += `
                        <div class="test-result success">
                            <h4>✅ ${userType} Login Successful</h4>
                            <p><strong>User Role:</strong> ${user.role}</p>
                            <p><strong>Expected Route:</strong> ${getExpectedRoute(user.role)}</p>
                            <pre>${JSON.stringify(user, null, 2)}</pre>
                        </div>
                    `;
                    
                } else {
                    resultsDiv.innerHTML += `
                        <div class="test-result error">
                            <h4>❌ ${userType} Login Failed</h4>
                            <p>${loginData.message}</p>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultsDiv.innerHTML += `
                    <div class="test-result error">
                        <h4>❌ Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        function getExpectedRoute(role) {
            if (role === 'super_admin' || role === 'superadmin') {
                return '/superadmin';
            } else if (role === 'manager') {
                return '/manager';
            } else {
                return '/partner';
            }
        }
        
        function testRoleRouting(role) {
            const resultsDiv = document.getElementById('results');
            
            // Test the routing logic
            let expectedRoute;
            if (role === 'super_admin' || role === 'superadmin') {
                expectedRoute = '/superadmin';
            } else if (role === 'manager') {
                expectedRoute = '/manager';
            } else {
                expectedRoute = '/partner';
            }
            
            resultsDiv.innerHTML += `
                <div class="test-result success">
                    <h4>🔀 Role Routing Test</h4>
                    <p><strong>Input Role:</strong> ${role}</p>
                    <p><strong>Expected Route:</strong> ${expectedRoute}</p>
                    <p><strong>Routing Logic:</strong> ${role === 'super_admin' ? 'super_admin → /superadmin ✅' : role + ' → ' + expectedRoute + ' ✅'}</p>
                </div>
            `;
        }
    </script>
</body>
</html>
