import{r as l,a as rs,g as as,u as de,b as ns,c as is,B as ls,R as os,d as F,N as z}from"./vendor-CMsH-4Bd.js";import{L as Le,M as ge,C as Q,E as cs,a as me,b as fe,A as Oe,U as Se,B,D as te,T as Y,c as ke,d as J,e as Ie,f as Ae,g as ie,h as $,i as ds,F as re,S as ms,P as W,I as De,R as xs,j as Z,k as le,X as L,l as xe,m as oe,n as us,o as ze,p as ue,q as Be,r as ce,s as ps,t as pe,u as hs,v as Re,w as gs,x as Ce,y as fs,z as Ve,G as ys,H as js,J as bs,K,N as be,O as Ne,Q as Ns,V as vs}from"./ui-D4-VV43f.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))a(d);new MutationObserver(d=>{for(const n of d)if(n.type==="childList")for(const o of n.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&a(o)}).observe(document,{childList:!0,subtree:!0});function s(d){const n={};return d.integrity&&(n.integrity=d.integrity),d.referrerPolicy&&(n.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?n.credentials="include":d.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function a(d){if(d.ep)return;d.ep=!0;const n=s(d);fetch(d.href,n)}})();var Ge={exports:{}},ye={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ws=l,Ss=Symbol.for("react.element"),ks=Symbol.for("react.fragment"),Cs=Object.prototype.hasOwnProperty,_s=ws.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Is={key:!0,ref:!0,__self:!0,__source:!0};function We(c,t,s){var a,d={},n=null,o=null;s!==void 0&&(n=""+s),t.key!==void 0&&(n=""+t.key),t.ref!==void 0&&(o=t.ref);for(a in t)Cs.call(t,a)&&!Is.hasOwnProperty(a)&&(d[a]=t[a]);if(c&&c.defaultProps)for(a in t=c.defaultProps,t)d[a]===void 0&&(d[a]=t[a]);return{$$typeof:Ss,type:c,key:n,ref:o,props:d,_owner:_s.current}}ye.Fragment=ks;ye.jsx=We;ye.jsxs=We;Ge.exports=ye;var e=Ge.exports,Je,qe=rs;Je=qe.createRoot,qe.hydrateRoot;var He={exports:{}},As="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",Ds=As,Rs=Ds;function Ke(){}function Qe(){}Qe.resetWarningCache=Ke;var Fs=function(){function c(a,d,n,o,m,i){if(i!==Rs){var r=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw r.name="Invariant Violation",r}}c.isRequired=c;function t(){return c}var s={array:c,bigint:c,bool:c,func:c,number:c,object:c,string:c,symbol:c,any:c,arrayOf:t,element:c,elementType:c,instanceOf:t,node:c,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:Qe,resetWarningCache:Ke};return s.PropTypes=s,s};He.exports=Fs();var Es=He.exports;const b=as(Es),Ps=()=>window.location.hostname==="eskillvisor.wallistry.pk"||window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1"?"https://eskillvisor.wallistry.pk/api":"https://wallistry.pk/api",Ts=Ps();class Ms{constructor(){this.baseURL=Ts,this.token=localStorage.getItem("auth_token")}setToken(t){this.token=t,t?localStorage.setItem("auth_token",t):localStorage.removeItem("auth_token")}getHeaders(){const t={"Content-Type":"application/json"};return this.token&&(t.Authorization=`Bearer ${this.token}`),t}async request(t,s={}){const a=`${this.baseURL}${t}`,d={headers:this.getHeaders(),...s};try{const n=await fetch(a,d),o=await n.json();if(!n.ok)throw new Error(o.message||"API request failed");return o}catch(n){throw console.error("API request failed:",n),n}}async get(t){return this.request(t,{method:"GET"})}async post(t,s,a={}){const d={method:"POST",body:s instanceof FormData?s:JSON.stringify(s),...a};return s instanceof FormData||(d.headers={...d.headers,"Content-Type":"application/json"}),this.request(t,d)}async put(t,s){return this.request(t,{method:"PUT",body:JSON.stringify(s)})}async delete(t){return this.request(t,{method:"DELETE"})}async login(t,s){const a=await this.post("/login.php",{email:t,password:s});return a.success&&a.data.access_token&&this.setToken(a.data.access_token),a}async logout(){try{await this.post("/auth/logout")}finally{this.setToken(null)}}async getCurrentUser(){return this.get("/auth/me")}async refreshToken(t){const s=await this.post("/auth/refresh",{refresh_token:t});return s.success&&s.data.access_token&&this.setToken(s.data.access_token),s}async getUsers(t={}){const s=new URLSearchParams(t).toString();return this.get(`/users${s?`?${s}`:""}`)}async getUser(t){return this.get(`/users.php?id=${t}`)}async createUser(t){const s={};return t instanceof FormData&&(s.headers={}),this.post("/users",t,s)}async updateUser(t,s){return this.put(`/users.php?id=${t}`,s)}async deleteUser(t){return this.delete(`/users.php?id=${t}`)}async getCompanies(t={}){const s=new URLSearchParams(t).toString();return this.get(`/companies.php${s?`?${s}`:""}`)}async getCompany(t){return this.get(`/companies.php?id=${t}`)}async createCompany(t){return this.post("/companies.php",t)}async updateCompany(t,s){return this.put(`/companies.php?id=${t}`,s)}async deleteCompany(t){return this.delete(`/companies.php?id=${t}`)}async assignPartner(t,s){return this.post(`/companies/${t}/partners`,{user_id:s})}async removePartner(t,s){return this.delete(`/companies/${t}/partners/${s}`)}async getInventory(t={}){const s=new URLSearchParams(t).toString();return this.get(`/inventory${s?`?${s}`:""}`)}async getInventoryItem(t){return this.get(`/inventory/${t}`)}async createInventoryItem(t){return this.post("/inventory",t)}async updateInventoryItem(t,s){return this.put(`/inventory/${t}`,s)}async deleteInventoryItem(t){return this.delete(`/inventory/${t}`)}async getLowStockItems(){return this.get("/inventory/low-stock")}async getTransactions(t={}){const s=new URLSearchParams(t).toString();return this.get(`/transactions${s?`?${s}`:""}`)}async createTransaction(t){return this.post("/transactions",t)}async uploadFile(t){const s=new FormData;s.append("file",t);const a=await fetch(`${this.baseURL}/files/upload`,{method:"POST",headers:{Authorization:`Bearer ${this.token}`},body:s}),d=await a.json();if(!a.ok)throw new Error(d.message||"File upload failed");return d}async getUploads(t={}){const s=new URLSearchParams(t).toString();return this.get(`/files/uploads${s?`?${s}`:""}`)}async processFile(t){return this.post(`/files/process/${t}`)}async getDashboardData(){return this.get("/analytics/dashboard")}async getInventoryStats(){return this.get("/analytics/inventory-stats")}async getTrends(){return this.get("/analytics/trends")}async getCompanyStats(){return this.get("/analytics/company-stats")}async getAssignedCompanies(){return this.get("/companies/assigned")}async exportData(t,s="csv"){return this.get(`/analytics/export?type=${t}&format=${s}`)}async getNotifications(t={}){const s=new URLSearchParams(t).toString();return this.get(`/notifications${s?`?${s}`:""}`)}async markNotificationAsRead(t){return this.put(`/notifications/${t}/read`)}async markAllNotificationsAsRead(){return this.post("/notifications/mark-all-read")}async deleteNotification(t){return this.delete(`/notifications/${t}`)}}const _=new Ms;class Us{constructor(){this.currentUser=null,this.token=localStorage.getItem("auth_token"),this.refreshToken=localStorage.getItem("refresh_token"),this.token&&this.initializeFromToken()}async initializeFromToken(){try{if(this.token){_.setToken(this.token);const t=await _.getCurrentUser();t.success?this.currentUser=t.data:this.logout()}}catch(t){console.error("Failed to initialize from token:",t),this.logout()}}async login(t,s){try{const a=await _.login(t,s);if(a.success)return this.token=a.data.access_token,this.refreshToken=a.data.refresh_token,this.currentUser=a.data.user,localStorage.setItem("auth_token",this.token),localStorage.setItem("refresh_token",this.refreshToken),{success:!0,user:this.currentUser,token:this.token,refreshToken:this.refreshToken};throw new Error(a.message||"Login failed")}catch(a){throw new Error(a.message||"Login failed")}}async logout(){try{this.token&&await _.logout()}catch(t){console.error("Logout error:",t)}finally{localStorage.removeItem("auth_token"),localStorage.removeItem("refresh_token"),this.token=null,this.refreshToken=null,this.currentUser=null,_.setToken(null)}return{success:!0}}async refreshAuthToken(){try{if(!this.refreshToken)throw new Error("No refresh token available");const t=await _.refreshToken(this.refreshToken);if(t.success)return this.token=t.data.access_token,localStorage.setItem("auth_token",this.token),_.setToken(this.token),{success:!0,token:this.token};throw new Error(t.message||"Token refresh failed")}catch{throw this.logout(),new Error("Token refresh failed")}}async resetPassword(t){try{return await _.post("/api/auth/forgot-password",{email:t})}catch(s){throw new Error(s.message||"Password reset failed")}}getCurrentUser(){return this.currentUser}getToken(){return this.token}isAuthenticated(){return!!this.token&&!!this.currentUser}hasRole(t){var s;return((s=this.currentUser)==null?void 0:s.role)===t}hasAnyRole(t){var s;return t.includes((s=this.currentUser)==null?void 0:s.role)}isSuperAdmin(){return this.hasRole("superadmin")}isManager(){return this.hasAnyRole(["superadmin","manager"])}isPartner(){return this.hasRole("partner")}canAccessCompany(t){var s;return this.currentUser?this.hasAnyRole(["superadmin","manager"])?!0:this.hasRole("partner")&&((s=this.currentUser.assigned_companies)==null?void 0:s.some(a=>a.id===t))||!1:!1}getAccessibleCompanies(){return this.currentUser?this.hasAnyRole(["superadmin","manager"])?"all":this.hasRole("partner")?this.currentUser.assigned_companies||[]:[]:[]}}const H=new Us,$s=()=>{const[c,t]=l.useState(""),[s,a]=l.useState(""),[d,n]=l.useState(!1),[o,m]=l.useState(!1),[i,r]=l.useState(""),[x,j]=l.useState(""),[g,f]=l.useState(!1),{login:y,isAuthenticated:k,userRole:C}=l.useContext(U),N=de();l.useEffect(()=>{k&&N(C==="superadmin"?"/superadmin":C==="manager"?"/manager":"/partner")},[k,C,N]);const I=h=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(h),w=async h=>{if(h.preventDefault(),!I(c)){r("Please enter a valid email address");return}r(""),j(""),m(!0);try{const v=await H.login(c,s);v.success&&(y(v.user),v.user.role==="superadmin"?N("/superadmin"):v.user.role==="manager"?N("/manager"):N("/partner"))}catch(v){j(v.message||"Login failed. Please try again.")}finally{m(!1)}},p=async(h,v)=>{t(h),a(v),r(""),j(""),m(!0);try{const R=await H.login(h,v);R.success&&(y(R.user),R.user.role==="superadmin"?N("/superadmin"):R.user.role==="manager"?N("/manager"):N("/partner"))}catch(R){j(R.message||"Demo login failed. Please try again.")}finally{m(!1)}},u=[{email:"<EMAIL>",password:"password",role:"Super Admin",description:"Full system access"},{email:"<EMAIL>",password:"password",role:"Manager",description:"Manage partners and companies"},{email:"<EMAIL>",password:"password",role:"Partner",description:"View assigned companies"}];return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-primary to-secondary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-20"}),e.jsxs("div",{className:"max-w-md w-full space-y-8 relative z-10",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mx-auto h-16 w-16 bg-white rounded-full flex items-center justify-center shadow-lg",children:e.jsx(Le,{className:"h-8 w-8 text-primary"})}),e.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Enterprise Portal"}),e.jsx("p",{className:"mt-2 text-center text-sm text-white text-opacity-80",children:"Sign in to your account"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-xl p-8",children:[e.jsxs("form",{className:"space-y-6",onSubmit:w,children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(ge,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:c,onChange:h=>t(h.target.value),className:`appearance-none relative block w-full pl-10 pr-3 py-2 border ${i?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm`,placeholder:"Enter your email"})]}),i&&e.jsxs("div",{className:"mt-1 flex items-center text-sm text-red-600",children:[e.jsx(Q,{className:"h-4 w-4 mr-1"}),i]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(Le,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{id:"password",name:"password",type:d?"text":"password",autoComplete:"current-password",required:!0,value:s,onChange:h=>a(h.target.value),className:"appearance-none relative block w-full pl-10 pr-10 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm",placeholder:"Enter your password"}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>n(!d),children:d?e.jsx(cs,{className:"h-5 w-5 text-gray-400"}):e.jsx(me,{className:"h-5 w-5 text-gray-400"})})]})]}),x&&e.jsxs("div",{className:"flex items-center text-sm text-red-600 bg-red-50 p-3 rounded-md",children:[e.jsx(Q,{className:"h-4 w-4 mr-2"}),x]}),e.jsx("div",{children:e.jsx("button",{type:"submit",disabled:o,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:o?"Signing in...":"Sign in"})}),e.jsx("div",{className:"text-center",children:e.jsxs("button",{type:"button",onClick:()=>f(!g),className:"text-sm text-primary hover:text-primary-dark",children:[g?"Hide":"Show"," Demo Accounts"]})})]}),g&&e.jsxs("div",{className:"mt-6 border-t border-gray-200 pt-6",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Demo Accounts:"}),e.jsx("div",{className:"space-y-2",children:u.map((h,v)=>e.jsx("button",{onClick:()=>p(h.email,h.password),className:"w-full text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:h.role}),e.jsx("p",{className:"text-xs text-gray-500",children:h.email})]}),e.jsx("p",{className:"text-xs text-gray-400",children:h.description})]})},v))})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsx("a",{href:"/reset-password",onClick:h=>{h.preventDefault(),N("/reset-password")},className:"text-sm text-primary hover:text-primary-dark",children:"Forgot your password?"})})]})]})]})},Ls=()=>{const[c,t]=l.useState(""),[s,a]=l.useState(!1),[d,n]=l.useState(!1),o=de(),m=i=>{i.preventDefault(),a(!0),setTimeout(()=>{a(!1),n(!0)},1500)};return d?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary to-secondary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"max-w-md w-full space-y-8",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-xl p-8 text-center",children:[e.jsx(fe,{className:"mx-auto h-16 w-16 text-green-500"}),e.jsx("h2",{className:"mt-6 text-2xl font-bold text-gray-900",children:"Check your email"}),e.jsxs("p",{className:"mt-2 text-sm text-gray-600",children:["We've sent a password reset link to ",c]}),e.jsx("button",{onClick:()=>o("/login"),className:"mt-6 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:"Back to Login"})]})})}):e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary to-secondary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-md w-full space-y-8",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Reset your password"}),e.jsx("p",{className:"mt-2 text-center text-sm text-white text-opacity-80",children:"Enter your email address and we'll send you a link to reset your password."})]}),e.jsx("div",{className:"bg-white rounded-lg shadow-xl p-8",children:e.jsxs("form",{className:"space-y-6",onSubmit:m,children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),e.jsxs("div",{className:"mt-1 relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(ge,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:c,onChange:i=>t(i.target.value),className:"appearance-none relative block w-full pl-10 pr-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm",placeholder:"Enter your email"})]})]}),e.jsx("div",{children:e.jsx("button",{type:"submit",disabled:s,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:s?"Sending...":"Send reset link"})}),e.jsx("div",{className:"text-center",children:e.jsxs("button",{type:"button",onClick:()=>o("/login"),className:"inline-flex items-center text-sm text-primary hover:text-primary-dark",children:[e.jsx(Oe,{className:"h-4 w-4 mr-1"}),"Back to login"]})})]})})]})})};class qs{async getInventoryItems(t={}){try{const s=await _.getInventory(t);if(s.success)return s.data||[];throw new Error(s.message||"Failed to fetch inventory items")}catch(s){throw console.error("Error fetching inventory items:",s),s}}async getInventoryItem(t){try{const s=await _.getInventoryItem(t);if(s.success)return s.data;throw new Error(s.message||"Failed to fetch inventory item")}catch(s){throw console.error("Error fetching inventory item:",s),s}}async createInventoryItem(t){try{const s=await _.createInventoryItem(t);if(s.success)return s.data;throw new Error(s.message||"Failed to create inventory item")}catch(s){throw console.error("Error creating inventory item:",s),s}}async updateInventoryItem(t,s){try{const a=await _.updateInventoryItem(t,s);if(a.success)return a.data;throw new Error(a.message||"Failed to update inventory item")}catch(a){throw console.error("Error updating inventory item:",a),a}}async deleteInventoryItem(t){try{const s=await _.deleteInventoryItem(t);if(s.success)return!0;throw new Error(s.message||"Failed to delete inventory item")}catch(s){throw console.error("Error deleting inventory item:",s),s}}async getLowStockItems(){try{const t=await _.getLowStockItems();if(t.success)return t.data||[];throw new Error(t.message||"Failed to fetch low stock items")}catch(t){throw console.error("Error fetching low stock items:",t),t}}async getTransactions(t={}){try{const s=await _.getTransactions(t);if(s.success)return s.data||[];throw new Error(s.message||"Failed to fetch transactions")}catch(s){throw console.error("Error fetching transactions:",s),s}}async createTransaction(t){try{const s=await _.createTransaction(t);if(s.success)return s.data;throw new Error(s.message||"Failed to create transaction")}catch(s){throw console.error("Error creating transaction:",s),s}}async uploadFile(t){try{const s=await _.uploadFile(t);if(s.success)return s.data;throw new Error(s.message||"Failed to upload file")}catch(s){throw console.error("Error uploading file:",s),s}}async processFile(t){try{const s=await _.processFile(t);if(s.success)return s.data;throw new Error(s.message||"Failed to process file")}catch(s){throw console.error("Error processing file:",s),s}}async getUploads(t={}){try{const s=await _.getUploads(t);if(s.success)return s.data||[];throw new Error(s.message||"Failed to fetch uploads")}catch(s){throw console.error("Error fetching uploads:",s),s}}async getInventoryStats(){try{const t=await _.getInventoryStats();if(t.success)return t.data;throw new Error(t.message||"Failed to fetch inventory stats")}catch(t){throw console.error("Error fetching inventory stats:",t),t}}async getDashboardData(){try{const t=await _.getDashboardData();if(t.success)return t.data;throw new Error(t.message||"Failed to fetch dashboard data")}catch(t){throw console.error("Error fetching dashboard data:",t),t}}async getTrends(){try{const t=await _.getTrends();if(t.success)return t.data;throw new Error(t.message||"Failed to fetch trends data")}catch(t){throw console.error("Get trends data failed:",t),t}}async getCompanyStats(){try{const t=await _.getCompanyStats();if(t.success)return t.data;throw new Error(t.message||"Failed to fetch company stats")}catch(t){throw console.error("Get company stats failed:",t),t}}async exportData(t="inventory",s="csv"){try{return await _.exportData(t,s)}catch(a){throw console.error("Error exporting data:",a),a}}}const V=new qs;class Os{async getUsers(t={}){try{const s=await _.getUsers(t);if(s.success)return s.data||[];throw new Error(s.message||"Failed to fetch users")}catch(s){throw console.error("Error fetching users:",s),s}}async getUser(t){try{const s=await _.getUser(t);if(s.success)return s.data;throw new Error(s.message||"Failed to fetch user")}catch(s){throw console.error("Error fetching user:",s),s}}async createUser(t){try{const s=await _.createUser(t);if(s.success)return s.data;throw new Error(s.message||"Failed to create user")}catch(s){throw console.error("Error creating user:",s),s}}async updateUser(t,s){try{const a=await _.updateUser(t,s);if(a.success)return a.data;throw new Error(a.message||"Failed to update user")}catch(a){throw console.error("Error updating user:",a),a}}async deleteUser(t){try{const s=await _.deleteUser(t);if(s.success)return!0;throw new Error(s.message||"Failed to delete user")}catch(s){throw console.error("Error deleting user:",s),s}}}const Ye=new Os;class zs{async getCompanies(t={}){try{const s=await _.getCompanies(t);if(s.success)return s.data||[];throw new Error(s.message||"Failed to fetch companies")}catch(s){throw console.error("Error fetching companies:",s),s}}async getCompany(t){try{const s=await _.getCompany(t);if(s.success)return s.data;throw new Error(s.message||"Failed to fetch company")}catch(s){throw console.error("Error fetching company:",s),s}}async createCompany(t){try{const s=await _.createCompany(t);if(s.success)return s.data;throw new Error(s.message||"Failed to create company")}catch(s){throw console.error("Error creating company:",s),s}}async updateCompany(t,s){try{const a=await _.updateCompany(t,s);if(a.success)return a.data;throw new Error(a.message||"Failed to update company")}catch(a){throw console.error("Error updating company:",a),a}}async deleteCompany(t){try{const s=await _.deleteCompany(t);if(s.success)return!0;throw new Error(s.message||"Failed to delete company")}catch(s){throw console.error("Error deleting company:",s),s}}async assignPartner(t,s){try{const a=await _.assignPartner(t,s);if(a.success)return!0;throw new Error(a.message||"Failed to assign partner")}catch(a){throw console.error("Error assigning partner:",a),a}}async removePartner(t,s){try{const a=await _.removePartner(t,s);if(a.success)return!0;throw new Error(a.message||"Failed to remove partner")}catch(a){throw console.error("Error removing partner:",a),a}}async getCompanyStats(){try{const t=await _.getCompanyStats();if(t.success)return t.data;throw new Error(t.message||"Failed to fetch company stats")}catch(t){throw console.error("Error fetching company stats:",t),t}}async getAssignedCompanies(){try{const t=await _.getAssignedCompanies();if(t.success)return t.data||[];throw new Error(t.message||"Failed to fetch assigned companies")}catch(t){throw console.error("Error fetching assigned companies:",t),t}}}const ae=new zs,Bs=()=>{const{user:c}=l.useContext(U),[t,s]=l.useState({users:{total_users:0,active_users:0,new_users_today:0},companies:{total_companies:0,active_companies:0,new_companies_today:0},inventory:{total_items:0,total_value:0,low_stock_count:0},top_categories:[],recent_activity:[]}),[a,d]=l.useState(!0),[n,o]=l.useState([]);l.useEffect(()=>{c&&(async()=>{try{d(!0);const r=await V.getDashboardData();r&&s({users:r.users||{total_users:0,active_users:0,new_users_today:0},companies:r.companies||{total_companies:0,active_companies:0,new_companies_today:0},inventory:r.inventory||{total_items:0,total_value:0,low_stock_count:0},top_categories:r.top_categories||[],recent_activity:r.recent_activity||[]});const x=await V.getTrends();x&&o(x.trends||[])}catch(r){console.error("Failed to load dashboard data:",r)}finally{d(!1)}})()},[c]);const m=({title:i,value:r,icon:x,color:j="blue",subtitle:g,trend:f})=>{const y={blue:"bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 border-blue-200",green:"bg-gradient-to-br from-green-50 to-green-100 text-green-600 border-green-200",purple:"bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600 border-purple-200",orange:"bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600 border-orange-200",red:"bg-gradient-to-br from-red-50 to-red-100 text-red-600 border-red-200",indigo:"bg-gradient-to-br from-indigo-50 to-indigo-100 text-indigo-600 border-indigo-200"};return e.jsx("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:i}),e.jsx("p",{className:"text-3xl font-bold text-gray-900 mb-2",children:r}),g&&e.jsx("p",{className:"text-sm text-gray-500",children:g}),f&&e.jsxs("div",{className:"flex items-center mt-2",children:[f.direction==="up"?e.jsx($,{className:"h-4 w-4 text-green-500 mr-1"}):e.jsx(ds,{className:"h-4 w-4 text-red-500 mr-1"}),e.jsx("span",{className:`text-sm font-medium ${f.direction==="up"?"text-green-600":"text-red-600"}`,children:f.value}),e.jsx("span",{className:"text-sm text-gray-500 ml-1",children:"vs last month"})]})]}),e.jsx("div",{className:`p-4 rounded-xl border ${y[j]}`,children:x})]})})};return a?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Super Admin Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"System-wide overview and analytics"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})]}):e.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Super Admin Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Complete system overview and business intelligence - Enhanced v2.0"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[e.jsx(m,{title:"Total Users",value:t.users.total_users,subtitle:`${t.users.active_users} active users`,icon:e.jsx(Se,{className:"h-8 w-8"}),color:"blue",trend:{direction:"up",value:"+12%"}}),e.jsx(m,{title:"Companies",value:t.companies.total_companies,subtitle:`${t.companies.active_companies} active companies`,icon:e.jsx(B,{className:"h-8 w-8"}),color:"green",trend:{direction:"up",value:"+8%"}}),e.jsx(m,{title:"Total Inventory Value",value:`$${t.inventory.total_value.toLocaleString()}`,subtitle:`${t.inventory.total_items} total items`,icon:e.jsx(te,{className:"h-8 w-8"}),color:"purple",trend:{direction:"up",value:"+15%"}})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsx(m,{title:"Low Stock Alerts",value:t.inventory.low_stock_count,subtitle:"Items need restocking",icon:e.jsx(Y,{className:"h-6 w-6"}),color:"red"}),e.jsx(m,{title:"New Users Today",value:t.users.new_users_today,subtitle:"User registrations",icon:e.jsx(ke,{className:"h-6 w-6"}),color:"indigo"}),e.jsx(m,{title:"New Companies",value:t.companies.new_companies_today,subtitle:"Companies added today",icon:e.jsx(J,{className:"h-6 w-6"}),color:"orange"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ie,{className:"h-5 w-5 text-purple-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Top Inventory Categories"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:t.top_categories.slice(0,5).map((i,r)=>{var x;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-3 h-3 rounded-full mr-3 ${["bg-blue-500","bg-green-500","bg-purple-500","bg-orange-500","bg-red-500"][r]}`}),e.jsx("span",{className:"font-medium text-gray-900",children:i.category||"Uncategorized"})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-sm font-semibold text-gray-900",children:[i.item_count," items"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["$",(x=i.total_value)==null?void 0:x.toLocaleString()]})]})]},r)})})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ae,{className:"h-5 w-5 text-blue-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent System Activity"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:t.recent_activity.slice(0,6).map((i,r)=>e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:`w-2 h-2 rounded-full mt-2 ${i.action==="create"?"bg-green-500":i.action==="update"?"bg-blue-500":i.action==="delete"?"bg-red-500":"bg-gray-500"}`}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:[i.action==="create"&&"Created ",i.action==="update"&&"Updated ",i.action==="delete"&&"Deleted ",i.entity_type]}),e.jsxs("p",{className:"text-xs text-gray-500",children:[i.user_name," • ",new Date(i.created_at).toLocaleDateString()]})]})]},r))})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ie,{className:"h-5 w-5 text-green-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Growth Trends (Last 30 Days)"})]})}),e.jsx("div",{className:"p-6",children:n.length>0?e.jsx("div",{className:"space-y-4",children:n.slice(-7).map((i,r)=>{var x;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:new Date(i.date).toLocaleDateString()}),e.jsxs("div",{className:"text-xs text-gray-500",children:[i.items_added," items added"]})]}),e.jsx("div",{className:"text-right",children:e.jsxs("div",{className:"text-sm font-semibold text-green-600",children:["+$",(x=i.value_added)==null?void 0:x.toLocaleString()]})})]},r)})}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(ie,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500",children:"No trend data available"})]})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Quick Actions"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ke,{className:"h-5 w-5 text-blue-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Create New Partner"})]}),e.jsx($,{className:"h-4 w-4 text-blue-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(J,{className:"h-5 w-5 text-green-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-green-900",children:"Add Company"})]}),e.jsx($,{className:"h-4 w-4 text-green-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ie,{className:"h-5 w-5 text-purple-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-purple-900",children:"View Reports"})]}),e.jsx($,{className:"h-4 w-4 text-purple-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(me,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-orange-900",children:"System Overview"})]}),e.jsx($,{className:"h-4 w-4 text-orange-600 transform rotate-45"})]})]})})]})]})]})},Vs=()=>{const{user:c}=l.useContext(U),[t,s]=l.useState({companies:{total_companies:0,active_companies:0},inventory:{total_items:0,total_value:0,low_stock_count:0},top_categories:[],recent_activity:[],assigned_companies:[]}),[a,d]=l.useState(!0),[n,o]=l.useState([]);l.useEffect(()=>{c&&(async()=>{try{d(!0);const r=await V.getDashboardData();r&&s({companies:r.companies||{total_companies:0,active_companies:0},inventory:r.inventory||{total_items:0,total_value:0,low_stock_count:0},top_categories:r.top_categories||[],recent_activity:r.recent_activity||[],assigned_companies:r.assigned_companies||[]});try{const x=await V.getCompanyStats();x&&o(x.top_companies||[])}catch(x){console.error("Failed to load company stats:",x)}}catch(r){console.error("Failed to load dashboard data:",r)}finally{d(!1)}})()},[c]);const m=({title:i,value:r,icon:x,color:j="blue",subtitle:g,trend:f})=>{const y={blue:"bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 border-blue-200",green:"bg-gradient-to-br from-green-50 to-green-100 text-green-600 border-green-200",purple:"bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600 border-purple-200",orange:"bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600 border-orange-200",red:"bg-gradient-to-br from-red-50 to-red-100 text-red-600 border-red-200",indigo:"bg-gradient-to-br from-indigo-50 to-indigo-100 text-indigo-600 border-indigo-200"};return e.jsx("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:i}),e.jsx("p",{className:"text-3xl font-bold text-gray-900 mb-2",children:r}),g&&e.jsx("p",{className:"text-sm text-gray-500",children:g})]}),e.jsx("div",{className:`p-4 rounded-xl border ${y[j]}`,children:x})]})})};return a?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Manager Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Company management and partner oversight"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})]}):e.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Manager Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Comprehensive company management and partner oversight"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[e.jsx(m,{title:"Managed Companies",value:t.companies.total_companies,subtitle:`${t.companies.active_companies} active companies`,icon:e.jsx(B,{className:"h-8 w-8"}),color:"blue"}),e.jsx(m,{title:"Total Inventory Value",value:`$${t.inventory.total_value.toLocaleString()}`,subtitle:`${t.inventory.total_items} total items`,icon:e.jsx(te,{className:"h-8 w-8"}),color:"green"}),e.jsx(m,{title:"Low Stock Alerts",value:t.inventory.low_stock_count,subtitle:"Items need attention",icon:e.jsx(Y,{className:"h-8 w-8"}),color:"red"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ie,{className:"h-5 w-5 text-green-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Top Companies by Value"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:n.slice(0,5).map((i,r)=>{var x;return e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 ${["bg-blue-500","bg-green-500","bg-purple-500","bg-orange-500","bg-red-500"][r]}`,children:r+1}),e.jsxs("div",{children:[e.jsx("p",{className:"font-semibold text-gray-900",children:i.name}),e.jsx("p",{className:"text-sm text-gray-500",children:i.industry})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-bold text-green-600",children:["$",(x=i.total_value)==null?void 0:x.toLocaleString()]}),e.jsxs("p",{className:"text-sm text-gray-500",children:[i.inventory_count," items"]})]})]},r)})})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ie,{className:"h-5 w-5 text-purple-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Inventory Categories"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:t.top_categories.slice(0,5).map((i,r)=>{var x;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-3 h-3 rounded-full mr-3 ${["bg-blue-500","bg-green-500","bg-purple-500","bg-orange-500","bg-red-500"][r]}`}),e.jsx("span",{className:"font-medium text-gray-900",children:i.category||"Uncategorized"})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-sm font-semibold text-gray-900",children:[i.item_count," items"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["$",(x=i.total_value)==null?void 0:x.toLocaleString()]})]})]},r)})})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ae,{className:"h-5 w-5 text-blue-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Company Activity"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:t.recent_activity.slice(0,6).map((i,r)=>e.jsxs("div",{className:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:`w-2 h-2 rounded-full mt-2 ${i.action==="create"?"bg-green-500":i.action==="update"?"bg-blue-500":i.action==="delete"?"bg-red-500":"bg-gray-500"}`}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:[i.action==="create"&&"Created ",i.action==="update"&&"Updated ",i.action==="delete"&&"Deleted ",i.entity_type]}),e.jsxs("p",{className:"text-xs text-gray-500",children:[i.user_name," • ",new Date(i.created_at).toLocaleDateString()]})]})]},r))})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Quick Actions"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ke,{className:"h-5 w-5 text-blue-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Add Partner"})]}),e.jsx($,{className:"h-4 w-4 text-blue-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(J,{className:"h-5 w-5 text-green-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-green-900",children:"Manage Companies"})]}),e.jsx($,{className:"h-4 w-4 text-green-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(re,{className:"h-5 w-5 text-purple-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-purple-900",children:"View Reports"})]}),e.jsx($,{className:"h-4 w-4 text-purple-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ms,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-orange-900",children:"Audit Trail"})]}),e.jsx($,{className:"h-4 w-4 text-orange-600 transform rotate-45"})]})]})})]})]})]})},Gs=()=>{const{user:c}=l.useContext(U),[t,s]=l.useState({assigned_companies:[],inventory:{total_items:0,total_value:0,low_stock_count:0},top_categories:[],recent_activity:[]}),[a,d]=l.useState(!0),[n,o]=l.useState([]);l.useEffect(()=>{c&&(async()=>{try{d(!0);const r=await V.getDashboardData();r&&s({assigned_companies:r.assigned_companies||[],inventory:r.inventory||{total_items:0,total_value:0,low_stock_count:0},top_categories:r.top_categories||[],recent_activity:r.recent_activity||[]});try{const x=await ae.getAssignedCompanies();x&&o(x)}catch(x){console.error("Failed to load companies:",x)}}catch(r){console.error("Failed to load dashboard data:",r)}finally{d(!1)}})()},[c]);const m=({title:i,value:r,icon:x,color:j="blue",subtitle:g,actionButton:f})=>{const y={blue:"bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 border-blue-200",green:"bg-gradient-to-br from-green-50 to-green-100 text-green-600 border-green-200",purple:"bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600 border-purple-200",red:"bg-gradient-to-br from-red-50 to-red-100 text-red-600 border-red-200",orange:"bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600 border-orange-200"};return e.jsx("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:i}),e.jsx("p",{className:"text-3xl font-bold text-gray-900 mb-2",children:r}),g&&e.jsx("p",{className:"text-sm text-gray-500",children:g}),f&&e.jsx("div",{className:"mt-3",children:f})]}),e.jsx("div",{className:`p-4 rounded-xl border ${y[j]}`,children:x})]})})};return a?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Partner Portfolio"}),e.jsx("p",{className:"text-gray-600",children:"Manage your assigned companies and inventory"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})]}):e.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Partner Portfolio"}),e.jsx("p",{className:"text-gray-600",children:"Comprehensive view of your assigned companies and inventory management"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsx(m,{title:"Assigned Companies",value:t.assigned_companies.length,subtitle:"Active partnerships",icon:e.jsx(B,{className:"h-8 w-8"}),color:"blue"}),e.jsx(m,{title:"Total Items",value:t.inventory.total_items,subtitle:"Across all companies",icon:e.jsx(W,{className:"h-8 w-8"}),color:"green"}),e.jsx(m,{title:"Portfolio Value",value:`$${t.inventory.total_value.toLocaleString()}`,subtitle:"Total inventory worth",icon:e.jsx(te,{className:"h-8 w-8"}),color:"purple"}),e.jsx(m,{title:"Low Stock Alerts",value:t.inventory.low_stock_count,subtitle:"Items need restocking",icon:e.jsx(Y,{className:"h-8 w-8"}),color:"red",actionButton:t.inventory.low_stock_count>0&&e.jsx("button",{className:"text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full hover:bg-red-200 transition-colors",children:"View Details"})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[e.jsxs("div",{className:"lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(B,{className:"h-5 w-5 text-blue-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Your Companies"})]}),e.jsx("button",{className:"text-sm text-blue-600 hover:text-blue-800 font-medium",children:"View All"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:n.slice(0,4).map((i,r)=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4",children:e.jsx(B,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-semibold text-gray-900",children:i.name||`Company ${r+1}`}),e.jsx("p",{className:"text-sm text-gray-500",children:i.industry||"Technology"}),e.jsxs("p",{className:"text-xs text-gray-400",children:[i.inventory_count||0," inventory items"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${i.status==="active"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:i.status||"Active"}),e.jsx("button",{className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",children:e.jsx(me,{className:"h-4 w-4"})})]})]},r))})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Quick Actions"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"p-3 bg-blue-50 border border-blue-200 rounded-lg",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(De,{className:"h-5 w-5 text-blue-600 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-blue-900",children:"Read-Only Access"}),e.jsx("p",{className:"text-xs text-blue-700 mt-1",children:"Contact your manager for inventory updates"})]})]})}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ie,{className:"h-5 w-5 text-purple-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-purple-900",children:"View Reports"})]}),e.jsx($,{className:"h-4 w-4 text-purple-600 transform rotate-45"})]}),e.jsxs("button",{className:"w-full flex items-center justify-between p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Y,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsx("span",{className:"text-sm font-medium text-orange-900",children:"Stock Alerts"})]}),e.jsx($,{className:"h-4 w-4 text-orange-600 transform rotate-45"})]})]})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ie,{className:"h-5 w-5 text-purple-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Inventory Categories"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:t.top_categories.slice(0,5).map((i,r)=>{var x;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-3 h-3 rounded-full mr-3 ${["bg-blue-500","bg-green-500","bg-purple-500","bg-orange-500","bg-red-500"][r]}`}),e.jsx("span",{className:"font-medium text-gray-900",children:i.category||"Uncategorized"})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-sm font-semibold text-gray-900",children:[i.item_count," items"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["$",(x=i.total_value)==null?void 0:x.toLocaleString()]})]})]},r)})})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ae,{className:"h-5 w-5 text-blue-600 mr-2"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Activity"})]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:t.recent_activity.slice(0,6).map((i,r)=>e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsxs("div",{className:"flex-shrink-0",children:[i.action==="create"&&e.jsx(fe,{className:"h-5 w-5 text-green-500"}),i.action==="update"&&e.jsx(xs,{className:"h-5 w-5 text-blue-500"}),i.action==="delete"&&e.jsx(Z,{className:"h-5 w-5 text-red-500"}),!["create","update","delete"].includes(i.action)&&e.jsx(le,{className:"h-5 w-5 text-gray-500"})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:[i.action==="create"&&"Created ",i.action==="update"&&"Updated ",i.action==="delete"&&"Deleted ",i.entity_type]}),e.jsxs("p",{className:"text-xs text-gray-500",children:[new Date(i.created_at).toLocaleDateString()," • ",new Date(i.created_at).toLocaleTimeString()]})]})]},r))})})]})]})]})},_e=({isOpen:c,onClose:t,onSubmit:s,userRole:a="superadmin",modalType:d="partner"})=>{const[n,o]=l.useState({firstName:"",lastName:"",mobile:"",email:"",address:"",businessModel:"",customBusinessModel:"",role:d==="manager"?"manager":"partner",password:"",confirmPassword:""}),[m,i]=l.useState(!1),[r,x]=l.useState({}),[j,g]=l.useState(null),f=p=>{const{name:u,value:h}=p.target;o(v=>({...v,[u]:h})),r[u]&&x(v=>({...v,[u]:""}))},y=["Amazon","Walmart","Business Equity","Inventory Partner","Other"],k=()=>{const p={};return n.firstName.trim()||(p.firstName="First name is required"),n.lastName.trim()||(p.lastName="Last name is required"),n.mobile.trim()?/^\+?[\d\s-()]+$/.test(n.mobile)||(p.mobile="Invalid mobile number format"):p.mobile="Mobile number is required",n.email.trim()?/\S+@\S+\.\S+/.test(n.email)||(p.email="Email is invalid"):p.email="Email is required",n.address.trim()||(p.address="Address is required"),d==="partner"&&(n.businessModel||(p.businessModel="Business model is required"),n.businessModel==="Other"&&!n.customBusinessModel.trim()&&(p.customBusinessModel="Please specify the business model"),j||(p.cnicDocument="CNIC/Passport document is required")),n.password?n.password.length<6&&(p.password="Password must be at least 6 characters"):p.password="Password is required",n.password!==n.confirmPassword&&(p.confirmPassword="Passwords do not match"),x(p),Object.keys(p).length===0},C=p=>{const u=p.target.files[0];if(u){if(!["image/jpeg","image/jpg","image/png","application/pdf"].includes(u.type)){x(v=>({...v,cnicDocument:"Please upload a valid image (JPG, PNG) or PDF file"}));return}if(u.size>5*1024*1024){x(v=>({...v,cnicDocument:"File size must be less than 5MB"}));return}g(u),x(v=>({...v,cnicDocument:""}))}},N=()=>{g(null)},I=async p=>{if(p.preventDefault(),!!k()){i(!0);try{const u=new FormData;if(u.append("firstName",n.firstName),u.append("lastName",n.lastName),u.append("name",`${n.firstName} ${n.lastName}`),u.append("mobile",n.mobile),u.append("email",n.email),u.append("address",n.address),u.append("role",n.role),u.append("password",n.password),d==="partner"){const h=n.businessModel==="Other"?n.customBusinessModel:n.businessModel;u.append("businessModel",h),j&&u.append("cnicDocument",j)}await s(u),w()}catch(u){console.error("Failed to add user:",u),x({submit:"Failed to add user. Please try again."})}finally{i(!1)}}},w=()=>{o({firstName:"",lastName:"",mobile:"",email:"",address:"",businessModel:"",customBusinessModel:"",role:d==="manager"?"manager":"partner",password:"",confirmPassword:""}),x({}),g(null),i(!1),t()};return c?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:w}),e.jsxs("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[e.jsx("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:d==="manager"?"Add New Manager":"Add New Partner"}),e.jsx("button",{onClick:w,className:"text-gray-400 hover:text-gray-600",children:e.jsx(L,{className:"h-6 w-6"})})]})}),e.jsxs("form",{onSubmit:I,children:[e.jsxs("div",{className:"px-4 pb-4 sm:px-6 sm:pb-6",children:[r.submit&&e.jsx("div",{className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:r.submit}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700",children:"First Name *"}),e.jsx("input",{type:"text",id:"firstName",name:"firstName",value:n.firstName,onChange:f,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.firstName?"border-red-300":"border-gray-300"}`,placeholder:"Enter first name"}),r.firstName&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.firstName})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700",children:"Last Name *"}),e.jsx("input",{type:"text",id:"lastName",name:"lastName",value:n.lastName,onChange:f,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.lastName?"border-red-300":"border-gray-300"}`,placeholder:"Enter last name"}),r.lastName&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.lastName})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"mobile",className:"block text-sm font-medium text-gray-700",children:"Mobile Number *"}),e.jsx("input",{type:"tel",id:"mobile",name:"mobile",value:n.mobile,onChange:f,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.mobile?"border-red-300":"border-gray-300"}`,placeholder:"+****************"}),r.mobile&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.mobile})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address *"}),e.jsx("input",{type:"email",id:"email",name:"email",value:n.email,onChange:f,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.email?"border-red-300":"border-gray-300"}`,placeholder:"Enter email address"}),r.email&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.email})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"address",className:"block text-sm font-medium text-gray-700",children:"Address *"}),e.jsx("textarea",{id:"address",name:"address",rows:3,value:n.address,onChange:f,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.address?"border-red-300":"border-gray-300"}`,placeholder:"Enter full address"}),r.address&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.address})]}),d==="partner"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"businessModel",className:"block text-sm font-medium text-gray-700",children:"Business Model *"}),e.jsxs("select",{id:"businessModel",name:"businessModel",value:n.businessModel,onChange:f,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.businessModel?"border-red-300":"border-gray-300"}`,children:[e.jsx("option",{value:"",children:"Select Business Model"}),y.map(p=>e.jsx("option",{value:p,children:p},p))]}),r.businessModel&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.businessModel})]}),n.businessModel==="Other"&&e.jsxs("div",{children:[e.jsx("label",{htmlFor:"customBusinessModel",className:"block text-sm font-medium text-gray-700",children:"Specify Business Model *"}),e.jsx("input",{type:"text",id:"customBusinessModel",name:"customBusinessModel",value:n.customBusinessModel,onChange:f,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.customBusinessModel?"border-red-300":"border-gray-300"}`,placeholder:"Enter your business model"}),r.customBusinessModel&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.customBusinessModel})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CNIC/Passport Document *"}),j?e.jsx("div",{className:"border border-gray-300 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(re,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:j.name}),e.jsxs("p",{className:"text-xs text-gray-500",children:[(j.size/1024/1024).toFixed(2)," MB"]})]})]}),e.jsx("button",{type:"button",onClick:N,className:"text-red-600 hover:text-red-800",children:e.jsx(Z,{className:"h-5 w-5"})})]})}):e.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",children:[e.jsx("input",{type:"file",id:"cnicDocument",accept:".jpg,.jpeg,.png,.pdf",onChange:C,className:"hidden"}),e.jsxs("label",{htmlFor:"cnicDocument",className:"cursor-pointer flex flex-col items-center",children:[e.jsx(xe,{className:"h-8 w-8 text-gray-400 mb-2"}),e.jsx("span",{className:"text-sm text-gray-600",children:"Click to upload CNIC or Passport"}),e.jsx("span",{className:"text-xs text-gray-500 mt-1",children:"JPG, PNG, or PDF (max 5MB)"})]})]}),r.cnicDocument&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.cnicDocument})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password *"}),e.jsx("input",{type:"password",id:"password",name:"password",value:n.password,onChange:f,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.password?"border-red-300":"border-gray-300"}`,placeholder:"Enter password"}),r.password&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.password})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password *"}),e.jsx("input",{type:"password",id:"confirmPassword",name:"confirmPassword",value:n.confirmPassword,onChange:f,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${r.confirmPassword?"border-red-300":"border-gray-300"}`,placeholder:"Confirm password"}),r.confirmPassword&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:r.confirmPassword})]})]})]}),e.jsxs("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[e.jsx("button",{type:"submit",disabled:m,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:m?"Adding...":d==="manager"?"Add Manager":"Add Partner"}),e.jsx("button",{type:"button",onClick:w,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"Cancel"})]})]})]})]})}):null},Ze=({isOpen:c,onClose:t,user:s,onApprove:a,onReject:d})=>{const[n,o]=l.useState(!1),[m,i]=l.useState(""),[r,x]=l.useState(!1),j=async()=>{o(!0);try{await a(s.id,{approved:!0,notes:"User approved by Super Admin"})}catch(y){console.error("Failed to approve user:",y)}finally{o(!1)}},g=async()=>{if(m.trim()){o(!0);try{await d(s.id,m)}catch(y){console.error("Failed to reject user:",y)}finally{o(!1)}}},f=()=>{i(""),x(!1),o(!1),t()};return!c||!s?null:e.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:e.jsxs("div",{className:"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white",children:[e.jsxs("div",{className:"flex items-center justify-between pb-4 border-b border-gray-200",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"User Approval Review"}),e.jsx("button",{onClick:f,className:"text-gray-400 hover:text-gray-600",children:e.jsx(L,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6 mb-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"flex-shrink-0 h-16 w-16",children:e.jsx("div",{className:"h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center",children:e.jsx(oe,{className:"h-8 w-8 text-blue-600"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h4",{className:"text-xl font-semibold text-gray-900",children:s.name}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Pending approval since ",new Date(s.created_at).toLocaleDateString()]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ge,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Email"}),e.jsx("p",{className:"text-sm text-gray-600",children:s.email})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(us,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Role"}),e.jsx("p",{className:"text-sm text-gray-600 capitalize",children:s.role})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(ze,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Created"}),e.jsx("p",{className:"text-sm text-gray-600",children:new Date(s.created_at).toLocaleString()})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(oe,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Created By"}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Manager ID: ",s.created_by]})]})]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Q,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm font-medium text-orange-800",children:"Approval Required"}),e.jsx("p",{className:"text-sm text-orange-700 mt-1",children:"This user was created by a Manager and requires Super Admin approval before gaining system access."})]})]})})}),r&&e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"rejectionReason",className:"block text-sm font-medium text-gray-700 mb-2",children:"Rejection Reason *"}),e.jsx("textarea",{id:"rejectionReason",rows:4,value:m,onChange:y=>i(y.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500",placeholder:"Please provide a reason for rejecting this user...",required:!0}),!m.trim()&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:"Rejection reason is required"})]}),e.jsxs("div",{className:"flex justify-end space-x-3",children:[e.jsx("button",{onClick:f,className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",disabled:n,children:"Cancel"}),r?e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>x(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",disabled:n,children:"Back"}),e.jsxs("button",{onClick:g,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",disabled:n||!m.trim(),children:[n?e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):e.jsx(Z,{className:"h-4 w-4 mr-2"}),"Confirm Rejection"]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>x(!0),className:"inline-flex items-center px-4 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",disabled:n,children:[e.jsx(Z,{className:"h-4 w-4 mr-2"}),"Reject"]}),e.jsxs("button",{onClick:j,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",disabled:n,children:[n?e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):e.jsx(ue,{className:"h-4 w-4 mr-2"}),"Approve User"]})]})]})]})]})})};Ze.propTypes={isOpen:b.bool.isRequired,onClose:b.func.isRequired,user:b.object,onApprove:b.func.isRequired,onReject:b.func.isRequired};const se=({onFileSelect:c,acceptedTypes:t=[".xlsx",".xls",".pdf"],maxSize:s=10*1024*1024,multiple:a=!1,disabled:d=!1,className:n=""})=>{const[o,m]=l.useState(!1),[i,r]=l.useState([]),[x,j]=l.useState([]),g=l.useRef(null),f=u=>{var R;const h=[],v="."+((R=u.name.split(".").pop())==null?void 0:R.toLowerCase());if(t.includes(v)||h.push(`Invalid file type. Accepted types: ${t.join(", ")}`),u.size>s){const E=Math.round(s/1048576);h.push(`File size exceeds ${E}MB limit`)}return u.size===0&&h.push("File is empty"),h},y=u=>{const h=Array.from(u),v=[],R=[];h.forEach(E=>{const X=f(E);X.length===0?v.push(E):R.push(`${E.name}: ${X.join(", ")}`)}),j(R),a?(r(E=>[...E,...v]),c([...i,...v])):(r(v.slice(0,1)),c(v[0]||null))},k=u=>{u.preventDefault(),u.stopPropagation(),u.type==="dragenter"||u.type==="dragover"?m(!0):u.type==="dragleave"&&m(!1)},C=u=>{u.preventDefault(),u.stopPropagation(),m(!1),!d&&u.dataTransfer.files&&u.dataTransfer.files[0]&&y(u.dataTransfer.files)},N=u=>{u.preventDefault(),!d&&u.target.files&&u.target.files[0]&&y(u.target.files)},I=u=>{const h=i.filter((v,R)=>R!==u);r(h),c(a?h:null)},w=()=>{!d&&g.current&&g.current.click()},p=u=>{if(u===0)return"0 Bytes";const h=1024,v=["Bytes","KB","MB","GB"],R=Math.floor(Math.log(u)/Math.log(h));return parseFloat((u/Math.pow(h,R)).toFixed(2))+" "+v[R]};return e.jsxs("div",{className:`w-full ${n}`,children:[e.jsxs("div",{className:`
          relative border-2 border-dashed rounded-lg p-6 text-center transition-colors
          ${o?"border-primary bg-primary bg-opacity-5":"border-gray-300"}
          ${d?"bg-gray-100 cursor-not-allowed":"hover:border-primary hover:bg-primary hover:bg-opacity-5 cursor-pointer"}
        `,onDragEnter:k,onDragLeave:k,onDragOver:k,onDrop:C,onClick:w,children:[e.jsx("input",{ref:g,type:"file",multiple:a,accept:t.join(","),onChange:N,disabled:d,className:"hidden"}),e.jsx(xe,{className:`mx-auto h-12 w-12 ${d?"text-gray-400":"text-gray-500"}`}),e.jsxs("div",{className:"mt-4",children:[e.jsx("p",{className:`text-sm font-medium ${d?"text-gray-400":"text-gray-900"}`,children:o?"Drop files here":"Click to upload or drag and drop"}),e.jsxs("p",{className:`text-xs mt-1 ${d?"text-gray-400":"text-gray-500"}`,children:[t.join(", ")," up to ",Math.round(s/(1024*1024)),"MB"]})]})]}),x.length>0&&e.jsx("div",{className:"mt-3 space-y-1",children:x.map((u,h)=>e.jsxs("div",{className:"flex items-center text-sm text-red-600",children:[e.jsx(Q,{className:"h-4 w-4 mr-2 flex-shrink-0"}),u]},h))}),i.length>0&&e.jsxs("div",{className:"mt-4 space-y-2",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Selected Files:"}),i.map((u,h)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center min-w-0 flex-1",children:[e.jsx(Be,{className:"h-5 w-5 text-gray-400 mr-3 flex-shrink-0"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:u.name}),e.jsx("p",{className:"text-xs text-gray-500",children:p(u.size)})]})]}),e.jsxs("div",{className:"flex items-center ml-4",children:[e.jsx(fe,{className:"h-5 w-5 text-green-500 mr-2"}),e.jsx("button",{type:"button",onClick:v=>{v.stopPropagation(),I(h)},className:"p-1 hover:bg-gray-200 rounded",disabled:d,children:e.jsx(L,{className:"h-4 w-4 text-gray-400"})})]})]},h))]})]})};se.propTypes={onFileSelect:b.func.isRequired,acceptedTypes:b.arrayOf(b.string),maxSize:b.number,multiple:b.bool,disabled:b.bool,className:b.string};const Xe=({isOpen:c,onClose:t,onSubmit:s,userRole:a="superadmin",currentUserId:d=null,targetUserId:n=null})=>{const[o,m]=l.useState({name:"",companyDirector:"",registrationTerritory:"",einNumber:"",marketplace:"",customMarketplace:"",description:"",industry:"",email:"",phone:"",address:"",website:"",managerId:a==="manager"?d:""}),[i,r]=l.useState(!1),[x,j]=l.useState({}),[g,f]=l.useState([]),[y,k]=l.useState([]),[C,N]=l.useState([]),[I,w]=l.useState("basic"),p=["United States","Canada","United Kingdom","Germany","France","Italy","Spain","Netherlands","Australia","Japan","South Korea","Singapore","Hong Kong","India","China","Brazil","Mexico","Argentina","Chile","Colombia","Peru","South Africa","Nigeria","Kenya","Egypt","Morocco","Turkey","Russia","Poland","Czech Republic","Hungary","Romania","Greece","Portugal","Belgium","Switzerland","Austria","Denmark","Sweden","Norway","Finland","Ireland","New Zealand","Thailand","Malaysia","Indonesia","Philippines","Vietnam","Pakistan","Bangladesh","Sri Lanka","Nepal","Afghanistan","Iran","Iraq","Saudi Arabia","UAE","Qatar","Kuwait","Bahrain","Oman","Jordan","Lebanon","Israel"].sort(),u=["Amazon","Walmart","TikTok","Shopify","Others"];l.useEffect(()=>{(async()=>{if(c)try{a==="superadmin"&&f([{id:"2",name:"Jane Manager",email:"<EMAIL>"},{id:"5",name:"Mike Johnson",email:"<EMAIL>"}]),k([{id:"3",name:"Bob Partner",email:"<EMAIL>"},{id:"4",name:"Alice Smith",email:"<EMAIL>"},{id:"6",name:"Sarah Wilson",email:"<EMAIL>"},{id:"7",name:"David Brown",email:"<EMAIL>"}])}catch(M){console.error("Failed to load users data:",M),f([]),k([])}})()},[a,c]);const h=S=>{const{name:M,value:O}=S.target;m(G=>({...G,[M]:O})),x[M]&&j(G=>({...G,[M]:""}))},v=()=>{const S={};return o.name.trim()||(S.name="Company name is required"),o.companyDirector.trim()||(S.companyDirector="Company director is required"),o.registrationTerritory.trim()||(S.registrationTerritory="Registration territory is required"),o.einNumber.trim()||(S.einNumber="EIN number is required"),o.marketplace.trim()||(S.marketplace="Marketplace is required"),o.marketplace==="Others"&&!o.customMarketplace.trim()&&(S.customMarketplace="Please specify the marketplace"),o.email.trim()&&!/\S+@\S+\.\S+/.test(o.email)&&(S.email="Email is invalid"),j(S),Object.keys(S).length===0},R=(S,M)=>{if(S){const O={id:Date.now(),file:S,type:M,name:S.name,size:S.size,status:"pending"};N(G=>[...G,O])}},E=S=>{N(M=>M.filter(O=>O.id!==S))},X=async S=>{if(S.preventDefault(),!!v()){r(!0);try{const M={...o,documents:C};await s(M),ee()}catch(M){console.error("Failed to add company:",M),j({submit:"Failed to add company. Please try again."})}finally{r(!1)}}},ee=()=>{m({name:"",companyDirector:"",registrationTerritory:"",einNumber:"",marketplace:"",customMarketplace:"",description:"",industry:"",email:"",phone:"",address:"",website:"",managerId:a==="manager"?d:""}),j({}),N([]),w("basic"),r(!1),t()};return c?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:ee}),e.jsxs("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full",children:[e.jsxs("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Add New Company"}),e.jsx("button",{onClick:ee,className:"text-gray-400 hover:text-gray-600",children:e.jsx(L,{className:"h-6 w-6"})})]}),e.jsx("div",{className:"mt-4",children:e.jsx("div",{className:"border-b border-gray-200",children:e.jsxs("nav",{className:"-mb-px flex space-x-8",children:[e.jsx("button",{type:"button",onClick:()=>w("basic"),className:`${I==="basic"?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`,children:"Basic Information"}),e.jsxs("button",{type:"button",onClick:()=>w("documents"),className:`${I==="documents"?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`,children:[e.jsx(re,{className:"h-4 w-4 mr-1"}),"Documents",C.length>0&&e.jsx("span",{className:"ml-1 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:C.length})]})]})})})]}),e.jsxs("form",{onSubmit:X,children:[e.jsxs("div",{className:"px-4 pb-4 sm:px-6 sm:pb-6",children:[x.submit&&e.jsx("div",{className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:x.submit}),I==="basic"&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Company Name *"}),e.jsx("input",{type:"text",id:"name",name:"name",value:o.name,onChange:h,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${x.name?"border-red-300":"border-gray-300"}`,placeholder:"Enter company name"}),x.name&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:x.name})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"companyDirector",className:"block text-sm font-medium text-gray-700",children:"Company Director *"}),e.jsxs("select",{id:"companyDirector",name:"companyDirector",value:o.companyDirector,onChange:h,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${x.companyDirector?"border-red-300":"border-gray-300"}`,children:[e.jsx("option",{value:"",children:"Select company director"}),y.map(S=>e.jsxs("option",{value:S.id,children:[S.name," (",S.email,")"]},S.id))]}),x.companyDirector&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:x.companyDirector})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"registrationTerritory",className:"block text-sm font-medium text-gray-700",children:"Company Registration Territory *"}),e.jsxs("select",{id:"registrationTerritory",name:"registrationTerritory",value:o.registrationTerritory,onChange:h,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${x.registrationTerritory?"border-red-300":"border-gray-300"}`,children:[e.jsx("option",{value:"",children:"Select country"}),p.map(S=>e.jsx("option",{value:S,children:S},S))]}),x.registrationTerritory&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:x.registrationTerritory})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"einNumber",className:"block text-sm font-medium text-gray-700",children:"EIN Number *"}),e.jsx("input",{type:"text",id:"einNumber",name:"einNumber",value:o.einNumber,onChange:h,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${x.einNumber?"border-red-300":"border-gray-300"}`,placeholder:"Enter EIN number"}),x.einNumber&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:x.einNumber})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"marketplace",className:"block text-sm font-medium text-gray-700",children:"Marketplace *"}),e.jsxs("select",{id:"marketplace",name:"marketplace",value:o.marketplace,onChange:h,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${x.marketplace?"border-red-300":"border-gray-300"}`,children:[e.jsx("option",{value:"",children:"Select marketplace"}),u.map(S=>e.jsx("option",{value:S,children:S},S))]}),x.marketplace&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:x.marketplace})]}),o.marketplace==="Others"&&e.jsxs("div",{children:[e.jsx("label",{htmlFor:"customMarketplace",className:"block text-sm font-medium text-gray-700",children:"Specify Marketplace *"}),e.jsx("input",{type:"text",id:"customMarketplace",name:"customMarketplace",value:o.customMarketplace,onChange:h,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${x.customMarketplace?"border-red-300":"border-gray-300"}`,placeholder:"Enter marketplace name"}),x.customMarketplace&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:x.customMarketplace})]}),e.jsxs("div",{className:"pt-6 border-t border-gray-200",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-4",children:"Additional Information (Optional)"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Company Email"}),e.jsx("input",{type:"email",id:"email",name:"email",value:o.email,onChange:h,className:`mt-1 block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary ${x.email?"border-red-300":"border-gray-300"}`,placeholder:"<EMAIL>"}),x.email&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:x.email})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"industry",className:"block text-sm font-medium text-gray-700",children:"Industry"}),e.jsxs("select",{id:"industry",name:"industry",value:o.industry,onChange:h,className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",children:[e.jsx("option",{value:"",children:"Select industry"}),e.jsx("option",{value:"Technology",children:"Technology"}),e.jsx("option",{value:"Manufacturing",children:"Manufacturing"}),e.jsx("option",{value:"Retail",children:"Retail"}),e.jsx("option",{value:"Healthcare",children:"Healthcare"}),e.jsx("option",{value:"Finance",children:"Finance"}),e.jsx("option",{value:"Education",children:"Education"}),e.jsx("option",{value:"Other",children:"Other"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone"}),e.jsx("input",{type:"tel",id:"phone",name:"phone",value:o.phone,onChange:h,className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"+****************"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"address",className:"block text-sm font-medium text-gray-700",children:"Address"}),e.jsx("textarea",{id:"address",name:"address",rows:2,value:o.address,onChange:h,className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter company address"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-700",children:"Website"}),e.jsx("input",{type:"url",id:"website",name:"website",value:o.website,onChange:h,className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"https://company.com"})]}),a==="superadmin"&&e.jsxs("div",{children:[e.jsx("label",{htmlFor:"managerId",className:"block text-sm font-medium text-gray-700",children:"Assign Manager (Optional)"}),e.jsxs("select",{id:"managerId",name:"managerId",value:o.managerId,onChange:h,className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",children:[e.jsx("option",{value:"",children:"No manager assigned"}),g.map(S=>e.jsxs("option",{value:S.id,children:[S.name," (",S.email,")"]},S.id))]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700",children:"Description"}),e.jsx("textarea",{id:"description",name:"description",rows:3,value:o.description,onChange:h,className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",placeholder:"Enter company description"})]})]})]})]}),I==="documents"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Company Documents"}),e.jsx("p",{className:"text-sm text-gray-600 mb-6",children:"Upload required documents for company verification. These will be reviewed during the approval process."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-3",children:"Company Registration"}),e.jsx(se,{onFileSelect:S=>R(S,"registration"),acceptedTypes:[".pdf",".jpg",".jpeg",".png"],maxSize:5*1024*1024,multiple:!1,className:"mb-3"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Upload company registration certificate"})]}),e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-3",children:"Tax Certificate"}),e.jsx(se,{onFileSelect:S=>R(S,"tax_certificate"),acceptedTypes:[".pdf",".jpg",".jpeg",".png"],maxSize:5*1024*1024,multiple:!1,className:"mb-3"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Upload tax registration certificate"})]}),e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-3",children:"Business License"}),e.jsx(se,{onFileSelect:S=>R(S,"business_license"),acceptedTypes:[".pdf",".jpg",".jpeg",".png"],maxSize:5*1024*1024,multiple:!1,className:"mb-3"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Upload business license (optional)"})]}),e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-3",children:"Other Documents"}),e.jsx(se,{onFileSelect:S=>R(S,"other"),acceptedTypes:[".pdf",".jpg",".jpeg",".png"],maxSize:5*1024*1024,multiple:!1,className:"mb-3"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Upload any additional documents"})]})]}),C.length>0&&e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-3",children:"Uploaded Documents"}),e.jsx("div",{className:"space-y-2",children:C.map(S=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(re,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:S.name}),e.jsx("p",{className:"text-xs text-gray-500 capitalize",children:S.type.replace("_"," ")})]})]}),e.jsx("button",{type:"button",onClick:()=>E(S.id),className:"text-red-600 hover:text-red-800",children:e.jsx(Z,{className:"h-5 w-5"})})]},S.id))})]})]})]}),e.jsxs("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[e.jsx("button",{type:"submit",disabled:i,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:i?"Adding...":"Add Company"}),e.jsx("button",{type:"button",onClick:ee,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"Cancel"})]})]})]})]})}):null},Ws=({isOpen:c,onClose:t,user:s,onRefresh:a})=>{const{userRole:d}=l.useContext(U),[n,o]=l.useState("profile"),[m,i]=l.useState(!1),[r,x]=l.useState([]),[j,g]=l.useState([]),[f,y]=l.useState(!1);l.useEffect(()=>{c&&s&&k()},[c,s]);const k=async()=>{y(!0);try{s.role==="partner"?x([{id:"1",name:"Acme Corp",status:"active",inventoryItems:15,totalValue:25e3},{id:"2",name:"TechStart Inc",status:"active",inventoryItems:8,totalValue:12e3}]):s.role==="manager"&&x([{id:"3",name:"Global Ventures",status:"active",inventoryItems:25,totalValue:45e3},{id:"4",name:"Innovation Hub",status:"pending",inventoryItems:0,totalValue:0}]),g([{category:"Electronics",items:12,value:15e3},{category:"Clothing",items:8,value:3500},{category:"Books",items:15,value:750}])}catch(w){console.error("Error loading user data:",w)}finally{y(!1)}},C=async w=>{try{console.log("Adding company for user:",s.id,w),i(!1),await k(),a&&a()}catch(p){console.error("Error adding company:",p)}},N=()=>{console.log("Assign existing company to user:",s.id)},I=async w=>{if(window.confirm("Are you sure you want to remove this company assignment?"))try{console.log("Removing company:",w,"from user:",s.id),await k()}catch(p){console.error("Error removing company:",p)}};return!c||!s?null:e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:t}),e.jsxs("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full",children:[e.jsxs("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-12 w-12",children:e.jsx("div",{className:"h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center",children:e.jsx(oe,{className:"h-6 w-6 text-gray-500"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:s.name||`${s.firstName} ${s.lastName}`}),e.jsx("p",{className:"text-sm text-gray-500",children:s.email}),e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${s.role==="superadmin"?"bg-purple-100 text-purple-800":s.role==="manager"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:s.role.charAt(0).toUpperCase()+s.role.slice(1)})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[(d==="superadmin"||d==="manager")&&s.role!=="superadmin"&&e.jsx(e.Fragment,{children:d==="superadmin"?e.jsxs("button",{onClick:()=>i(!0),className:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary-dark",children:[e.jsx(ce,{className:"h-4 w-4 mr-1"}),"Add Company"]}):e.jsxs("button",{onClick:N,className:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[e.jsx(ce,{className:"h-4 w-4 mr-1"}),"Assign Company"]})}),e.jsx("button",{onClick:t,className:"text-gray-400 hover:text-gray-600",children:e.jsx(L,{className:"h-6 w-6"})})]})]}),e.jsx("div",{className:"mt-6",children:e.jsx("div",{className:"border-b border-gray-200",children:e.jsx("nav",{className:"-mb-px flex space-x-8",children:["profile","companies","inventory"].map(w=>e.jsx("button",{onClick:()=>o(w),className:`py-2 px-1 border-b-2 font-medium text-sm ${n===w?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:w.charAt(0).toUpperCase()+w.slice(1)},w))})})})]}),e.jsx("div",{className:"px-4 pb-4 sm:px-6 sm:pb-6",children:f?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}):e.jsxs(e.Fragment,{children:[n==="profile"&&e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-4",children:"Personal Information"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Full Name"}),e.jsx("p",{className:"mt-1 text-sm text-gray-900",children:s.name||`${s.firstName} ${s.lastName}`})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),e.jsx("p",{className:"mt-1 text-sm text-gray-900",children:s.email})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Mobile"}),e.jsx("p",{className:"mt-1 text-sm text-gray-900",children:s.mobile||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Address"}),e.jsx("p",{className:"mt-1 text-sm text-gray-900",children:s.address||"Not provided"})]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-4",children:"Account Information"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Role"}),e.jsx("p",{className:"mt-1 text-sm text-gray-900",children:s.role})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Status"}),e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${s.status==="active"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:s.status})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Business Model"}),e.jsx("p",{className:"mt-1 text-sm text-gray-900",children:s.businessModel||"Not specified"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Created At"}),e.jsx("p",{className:"mt-1 text-sm text-gray-900",children:s.created_at?new Date(s.created_at).toLocaleDateString():"N/A"})]})]})]})]})}),n==="companies"&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900",children:"Associated Companies"}),e.jsxs("span",{className:"text-sm text-gray-500",children:[r.length," companies"]})]}),r.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(J,{className:"mx-auto h-12 w-12 text-gray-400"}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No companies assigned"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"This user is not associated with any companies yet."})]}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:r.map(w=>e.jsx("div",{className:"border border-gray-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900",children:w.name}),e.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:[w.inventoryItems," items • $",w.totalValue.toLocaleString()]}),e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-2 ${w.status==="active"?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:w.status})]}),(d==="superadmin"||d==="manager")&&e.jsx("button",{onClick:()=>I(w.id),className:"text-red-600 hover:text-red-800",children:e.jsx(ps,{className:"h-4 w-4"})})]})},w.id))})]}),n==="inventory"&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900",children:"Inventory Summary"}),e.jsxs("span",{className:"text-sm text-gray-500",children:[j.reduce((w,p)=>w+p.items,0)," total items"]})]}),j.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(W,{className:"mx-auto h-12 w-12 text-gray-400"}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No inventory data"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"This user has no inventory items yet."})]}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:j.map((w,p)=>e.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900",children:w.category}),e.jsx("p",{className:"text-2xl font-bold text-primary mt-2",children:w.items}),e.jsx("p",{className:"text-sm text-gray-500",children:"Items"}),e.jsxs("p",{className:"text-sm font-medium text-gray-900 mt-1",children:["$",w.value.toLocaleString()]})]},p))})]})]})})]})]})}),e.jsx(Xe,{isOpen:m,onClose:()=>i(!1),onSubmit:C,userRole:d,targetUserId:s==null?void 0:s.id})]})},Js=()=>{const{userRole:c,user:t}=l.useContext(U),[s,a]=l.useState([{id:"1",name:"John Admin",email:"<EMAIL>",role:"superadmin",status:"active",approval_status:"approved"},{id:"2",name:"Jane Manager",email:"<EMAIL>",role:"manager",status:"active",approval_status:"approved"},{id:"3",name:"Bob Partner",email:"<EMAIL>",role:"partner",status:"active",approval_status:"approved"},{id:"4",name:"Alice Smith",email:"<EMAIL>",role:"partner",status:"active",approval_status:"pending",created_by:"2",created_at:"2025-07-15T10:30:00Z"},{id:"5",name:"Mike Johnson",email:"<EMAIL>",role:"manager",status:"active",approval_status:"pending",created_by:"2",created_at:"2025-07-15T09:15:00Z"}]),[d,n]=l.useState(!1),[o,m]=l.useState(!1),[i,r]=l.useState(!1),[x,j]=l.useState(!1),[g,f]=l.useState(null),[y,k]=l.useState("all"),[C,N]=l.useState("managers"),[I,w]=l.useState(""),[p,u]=l.useState("all"),[h,v]=l.useState("all"),R=async D=>{try{console.log("Creating user with data:",D);const A=await Ye.createUser(D);console.log("User created successfully:",A),a(P=>[...P,A]),n(!1),m(!1)}catch(A){throw console.error("Failed to add user:",A),A}},E=async(D,A)=>{try{a(P=>P.map(q=>q.id===D?{...q,approval_status:"approved",approved_by:q.id,approved_at:new Date().toISOString()}:q)),r(!1),f(null)}catch(P){throw console.error("Failed to approve user:",P),P}},X=async(D,A)=>{try{a(P=>P.map(q=>q.id===D?{...q,approval_status:"rejected",rejection_reason:A,rejected_at:new Date().toISOString()}:q)),r(!1),f(null)}catch(P){throw console.error("Failed to reject user:",P),P}},ee=D=>{f(D),j(!0)},S=()=>{console.log("Refreshing user data...")},M=s.filter(D=>D.role==="manager"),O=s.filter(D=>D.role==="partner"),G=D=>D.filter(A=>{var Te,Me,Ue,$e;const P=I===""||((Te=A.name)==null?void 0:Te.toLowerCase().includes(I.toLowerCase()))||((Me=A.firstName)==null?void 0:Me.toLowerCase().includes(I.toLowerCase()))||((Ue=A.lastName)==null?void 0:Ue.toLowerCase().includes(I.toLowerCase()))||(($e=A.email)==null?void 0:$e.toLowerCase().includes(I.toLowerCase())),q=p==="all"||A.status===p;return P&&q}),Ee=G(M),Pe=G(O),ts=D=>{f(D),r(!0)};s.filter(D=>y==="all"?!0:y==="pending"?D.approval_status==="pending":y==="approved"?D.approval_status==="approved":y==="rejected"?D.approval_status==="rejected":!0);const ne=s.filter(D=>D.approval_status==="pending").length;return e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"User Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage system users and their permissions"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[ne>0&&e.jsx("div",{className:"bg-orange-100 border border-orange-200 rounded-lg px-3 py-2",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(le,{className:"h-4 w-4 text-orange-600 mr-2"}),e.jsxs("span",{className:"text-sm font-medium text-orange-800",children:[ne," pending approval",ne!==1?"s":""]})]})}),e.jsxs("button",{onClick:()=>m(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 mr-3",children:[e.jsx(ce,{className:"h-4 w-4 mr-2"}),"Add Manager"]}),e.jsxs("button",{onClick:()=>n(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(ce,{className:"h-4 w-4 mr-2"}),"Add Partner"]})]})]})}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"border-b border-gray-200",children:e.jsx("nav",{className:"-mb-px flex space-x-8",children:[{id:"managers",name:"Managers",count:M.length},{id:"partners",name:"Partners",count:O.length},{id:"pending",name:"Pending Approval",count:ne}].map(D=>e.jsxs("button",{onClick:()=>N(D.id),className:`${C===D.id?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`,children:[D.name,e.jsx("span",{className:`ml-2 py-0.5 px-2 rounded-full text-xs ${C===D.id?"bg-primary text-white":"bg-gray-100 text-gray-600"}`,children:D.count})]},D.id))})})}),e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx(pe,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search users...",value:I,onChange:D=>w(D.target.value),className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"})]}),e.jsxs("select",{value:p,onChange:D=>u(D.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"}),e.jsx("option",{value:"suspended",children:"Suspended"})]})]}),e.jsxs("div",{className:"text-sm text-gray-500",children:[C==="managers"&&`${Ee.length} managers`,C==="partners"&&`${Pe.length} partners`,C==="pending"&&`${ne} pending approval`]})]})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Approval"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:(()=>{let D=[];return C==="managers"?D=Ee:C==="partners"?D=Pe:C==="pending"&&(D=s.filter(A=>A.approval_status==="pending")),D.map(A=>e.jsxs("tr",{className:"hover:bg-gray-50 cursor-pointer",onClick:()=>ee(A),children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:e.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center",children:e.jsx(oe,{className:"h-5 w-5 text-gray-500"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:A.name||`${A.firstName} ${A.lastName}`}),e.jsx("div",{className:"text-sm text-gray-500",children:A.email})]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${A.role==="superadmin"?"bg-purple-100 text-purple-800":A.role==="manager"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:A.role})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${A.status==="active"?"bg-green-100 text-green-800":A.status==="inactive"?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:A.status})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${A.approval_status==="approved"?"bg-green-100 text-green-800":A.approval_status==="pending"?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800"}`,children:[A.approval_status==="approved"&&e.jsx(ue,{className:"h-3 w-3 mr-1"}),A.approval_status==="pending"&&e.jsx(le,{className:"h-3 w-3 mr-1"}),A.approval_status==="rejected"&&e.jsx(L,{className:"h-3 w-3 mr-1"}),A.approval_status]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:A.created_at?new Date(A.created_at).toLocaleDateString():"-"}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[A.approval_status==="pending"&&e.jsx("button",{onClick:P=>{P.stopPropagation(),ts(A)},className:"text-primary hover:text-primary-dark mr-3",children:"Review"}),e.jsx("button",{onClick:P=>P.stopPropagation(),className:"text-gray-600 hover:text-gray-900 mr-3",children:"Edit"}),e.jsx("button",{onClick:P=>P.stopPropagation(),className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},A.id))})()})]})})]}),e.jsx(_e,{isOpen:o,onClose:()=>m(!1),onSubmit:R,userRole:c,modalType:"manager"}),e.jsx(_e,{isOpen:d,onClose:()=>n(!1),onSubmit:R,userRole:c,modalType:"partner"}),e.jsx(Ze,{isOpen:i,onClose:()=>r(!1),user:g,onApprove:E,onReject:X}),e.jsx(Ws,{isOpen:x,onClose:()=>j(!1),user:g,onRefresh:S})]})},es=({isOpen:c,onClose:t,company:s,onApprove:a,onReject:d})=>{const[n,o]=l.useState(!1),[m,i]=l.useState(""),[r,x]=l.useState(!1),[j,g]=l.useState("all"),f=async()=>{o(!0);try{await a(s.id,{approved:!0,notes:"Company approved by Super Admin"})}catch(N){console.error("Failed to approve company:",N)}finally{o(!1)}},y=async()=>{if(m.trim()){o(!0);try{await d(s.id,m)}catch(N){console.error("Failed to reject company:",N)}finally{o(!1)}}},k=()=>{i(""),x(!1),o(!1),t()};if(!c||!s)return null;const C=N=>{switch(N){case"verified":return"bg-green-100 text-green-800";case"pending":return"bg-orange-100 text-orange-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return e.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:e.jsxs("div",{className:"relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white",children:[e.jsxs("div",{className:"flex items-center justify-between pb-4 border-b border-gray-200",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Company Approval Review"}),e.jsx("button",{onClick:k,className:"text-gray-400 hover:text-gray-600",children:e.jsx(L,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6 mb-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"flex-shrink-0 h-16 w-16",children:e.jsx("div",{className:"h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center",children:e.jsx(J,{className:"h-8 w-8 text-blue-600"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h4",{className:"text-xl font-semibold text-gray-900",children:s.name}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Pending approval since ",new Date(s.created_at).toLocaleDateString()]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ge,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Email"}),e.jsx("p",{className:"text-sm text-gray-600",children:s.email||"Not provided"})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(hs,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Phone"}),e.jsx("p",{className:"text-sm text-gray-600",children:s.phone||"Not provided"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ze,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Industry"}),e.jsx("p",{className:"text-sm text-gray-600",children:s.industry||"Not specified"})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(J,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Created By"}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Manager ID: ",s.created_by]})]})]})]})]}),s.description&&e.jsxs("div",{className:"mt-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 mb-1",children:"Description"}),e.jsx("p",{className:"text-sm text-gray-600",children:s.description})]})]}),s.documents&&s.documents.length>0&&e.jsxs("div",{className:"mb-6",children:[e.jsx("h5",{className:"text-lg font-medium text-gray-900 mb-4",children:"Document Verification"}),e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsx("div",{className:"flex space-x-4",children:["all","registration","tax_certificate","business_license"].map(N=>e.jsx("button",{onClick:()=>g(N),className:`px-3 py-2 text-sm font-medium rounded-md ${j===N?"bg-primary text-white":"text-gray-500 hover:text-gray-700"}`,children:N==="all"?"All Documents":N.replace("_"," ").replace(/\b\w/g,I=>I.toUpperCase())},N))})}),e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"space-y-3",children:s.documents.filter(N=>j==="all"||N.type===j).map((N,I)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(re,{className:"h-5 w-5 text-gray-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:N.filename}),e.jsx("p",{className:"text-xs text-gray-500 capitalize",children:N.type.replace("_"," ")})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${C(N.status)}`,children:N.status}),e.jsx("button",{className:"text-primary hover:text-primary-dark",children:e.jsx(me,{className:"h-4 w-4"})})]})]},I))})})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Q,{className:"h-5 w-5 text-orange-600 mr-3"}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-sm font-medium text-orange-800",children:"Approval Required"}),e.jsx("p",{className:"text-sm text-orange-700 mt-1",children:"This company was created by a Manager and requires Super Admin approval before becoming active in the system."})]})]})})}),r&&e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"rejectionReason",className:"block text-sm font-medium text-gray-700 mb-2",children:"Rejection Reason *"}),e.jsx("textarea",{id:"rejectionReason",rows:4,value:m,onChange:N=>i(N.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500",placeholder:"Please provide a reason for rejecting this company...",required:!0}),!m.trim()&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:"Rejection reason is required"})]}),e.jsxs("div",{className:"flex justify-end space-x-3",children:[e.jsx("button",{onClick:k,className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",disabled:n,children:"Cancel"}),r?e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>x(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",disabled:n,children:"Back"}),e.jsxs("button",{onClick:y,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",disabled:n||!m.trim(),children:[n?e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):e.jsx(Z,{className:"h-4 w-4 mr-2"}),"Confirm Rejection"]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>x(!0),className:"inline-flex items-center px-4 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",disabled:n,children:[e.jsx(Z,{className:"h-4 w-4 mr-2"}),"Reject"]}),e.jsxs("button",{onClick:f,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",disabled:n,children:[n?e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):e.jsx(ue,{className:"h-4 w-4 mr-2"}),"Approve Company"]})]})]})]})]})})};es.propTypes={isOpen:b.bool.isRequired,onClose:b.func.isRequired,company:b.object,onApprove:b.func.isRequired,onReject:b.func.isRequired};const Hs=()=>{const{user:c,userRole:t}=l.useContext(U),[s,a]=l.useState([]),[d,n]=l.useState(!0),[o,m]=l.useState(!1),[i,r]=l.useState(!1),[x,j]=l.useState(null),[g,f]=l.useState("all");l.useEffect(()=>{c&&(async()=>{try{n(!0);const h=(await ae.getCompanies()).map(v=>({...v,approval_status:v.id<=3?"approved":"pending",created_by:v.id>3?"2":"1",created_at:v.id>3?"2025-07-15T11:00:00Z":"2025-07-10T10:00:00Z",documents:v.id>3?[{type:"registration",filename:"company_registration.pdf",status:"pending"},{type:"tax_certificate",filename:"tax_cert.pdf",status:"pending"}]:[]}));a(h)}catch(u){console.error("Failed to load companies:",u),a([])}finally{n(!1)}})()},[c]);const y=async p=>{try{const h={...await ae.createCompany(p),approval_status:t==="superadmin"?"approved":"pending",created_by:c.id,created_at:new Date().toISOString(),documents:[]};a(v=>[...v,h]),m(!1)}catch(u){throw console.error("Failed to add company:",u),u}},k=async(p,u)=>{try{a(h=>h.map(v=>v.id===p?{...v,approval_status:"approved",approved_by:c.id,approved_at:new Date().toISOString()}:v)),r(!1),j(null)}catch(h){throw console.error("Failed to approve company:",h),h}},C=async(p,u)=>{try{a(h=>h.map(v=>v.id===p?{...v,approval_status:"rejected",rejection_reason:u,rejected_at:new Date().toISOString()}:v)),r(!1),j(null)}catch(h){throw console.error("Failed to reject company:",h),h}},N=p=>{j(p),r(!0)},I=s.filter(p=>g==="all"?!0:g==="pending"?p.approval_status==="pending":g==="approved"?p.approval_status==="approved":g==="rejected"?p.approval_status==="rejected":!0),w=s.filter(p=>p.approval_status==="pending").length;return d?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Company Oversight"}),e.jsx("p",{className:"text-gray-600",children:"Monitor and manage all companies in the system"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})})]}):e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Company Oversight"}),e.jsx("p",{className:"text-gray-600",children:"Monitor and manage all companies in the system"})]}),w>0&&e.jsx("div",{className:"bg-orange-100 border border-orange-200 rounded-lg px-3 py-2",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(le,{className:"h-4 w-4 text-orange-600 mr-2"}),e.jsxs("span",{className:"text-sm font-medium text-orange-800",children:[w," pending approval",w!==1?"s":""]})]})})]})}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"border-b border-gray-200",children:e.jsx("nav",{className:"-mb-px flex space-x-8",children:[{id:"all",name:"All Companies",count:s.length},{id:"pending",name:"Pending Approval",count:w},{id:"approved",name:"Approved",count:s.filter(p=>p.approval_status==="approved").length},{id:"rejected",name:"Rejected",count:s.filter(p=>p.approval_status==="rejected").length}].map(p=>e.jsxs("button",{onClick:()=>f(p.id),className:`${g===p.id?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`,children:[p.name,e.jsx("span",{className:`ml-2 py-0.5 px-2 rounded-full text-xs ${g===p.id?"bg-primary text-white":"bg-gray-100 text-gray-600"}`,children:p.count})]},p.id))})})}),e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(pe,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search companies...",className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary w-64"})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6",children:I.map(p=>e.jsxs("div",{className:`border rounded-lg p-6 hover:shadow-md transition-shadow ${p.approval_status==="pending"?"border-orange-200 bg-orange-50":p.approval_status==="rejected"?"border-red-200 bg-red-50":"border-gray-200 bg-white"}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(J,{className:"h-5 w-5 text-gray-400 mr-2"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:p.name})]}),e.jsxs("span",{className:`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${p.approval_status==="approved"?"bg-green-100 text-green-800":p.approval_status==="pending"?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800"}`,children:[p.approval_status==="approved"&&e.jsx(ue,{className:"h-3 w-3 mr-1"}),p.approval_status==="pending"&&e.jsx(le,{className:"h-3 w-3 mr-1"}),p.approval_status==="rejected"&&e.jsx(L,{className:"h-3 w-3 mr-1"}),p.approval_status]})]}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600 mb-4",children:[e.jsxs("p",{children:["ID: ",p.id]}),e.jsxs("p",{children:["Industry: ",p.industry||"Not specified"]}),e.jsxs("p",{children:["Created: ",new Date(p.created_at).toLocaleDateString()]}),p.documents&&p.documents.length>0&&e.jsxs("div",{className:"flex items-center",children:[e.jsx(re,{className:"h-4 w-4 mr-1"}),e.jsxs("span",{children:[p.documents.length," document",p.documents.length!==1?"s":""]})]})]}),p.approval_status==="pending"&&e.jsxs("div",{className:"border-t border-gray-200 pt-4",children:[e.jsx("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-3 mb-3",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Q,{className:"h-4 w-4 text-orange-600 mr-2"}),e.jsx("span",{className:"text-xs font-medium text-orange-800",children:"Requires approval"})]})}),e.jsx("button",{onClick:()=>N(p),className:"w-full bg-primary text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-primary-dark transition-colors",children:"Review & Approve"})]}),p.approval_status==="rejected"&&p.rejection_reason&&e.jsx("div",{className:"border-t border-gray-200 pt-4",children:e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:[e.jsx("p",{className:"text-xs font-medium text-red-800 mb-1",children:"Rejection Reason:"}),e.jsx("p",{className:"text-xs text-red-700",children:p.rejection_reason})]})}),p.approval_status==="approved"&&e.jsxs("div",{className:"mt-4 flex space-x-2",children:[e.jsx("button",{className:"text-primary hover:text-primary-dark text-sm",children:"View Details"}),e.jsx("button",{className:"text-gray-600 hover:text-gray-900 text-sm",children:"Edit"})]})]},p.id))})]}),e.jsx(Xe,{isOpen:o,onClose:()=>m(!1),onSubmit:y,userRole:t,currentUserId:c==null?void 0:c.id}),e.jsx(es,{isOpen:i,onClose:()=>r(!1),company:x,onApprove:k,onReject:C})]})},je=({selectedCompany:c,onCompanyChange:t,companies:s,className:a="",showAllOption:d=!0,disabled:n=!1})=>{const{user:o,userRole:m}=l.useContext(U),[i,r]=l.useState([]),[x,j]=l.useState([]);l.useEffect(()=>{o&&(async()=>{try{if(s)r(s);else{const y=await ae.getCompanies();j(y);let k=[];switch(m){case"superadmin":case"manager":k=y;break;case"partner":k=(o==null?void 0:o.assigned_companies)||[];break;default:k=[]}r(k),!c&&k.length>0&&!d&&t(k[0].id.toString())}}catch(y){console.error("Failed to load companies:",y),r([])}})()},[o,m,s,c,d,t]);const g=f=>{t(f.target.value)};return e.jsx("div",{className:`relative ${a}`,children:e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:c,onChange:g,disabled:n,className:`
            appearance-none w-full bg-white border border-gray-300 rounded-lg px-4 py-2 pr-10
            text-sm font-medium text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary 
            focus:border-primary disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed
          `,children:[d&&e.jsx("option",{value:"all",children:"All Companies"}),i.map(f=>e.jsx("option",{value:f.id,children:f.name},f.id))]}),e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:e.jsx(Re,{className:"h-4 w-4 text-gray-400"})}),e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(B,{className:"h-4 w-4 text-gray-400"})})]})})};je.propTypes={selectedCompany:b.string.isRequired,onCompanyChange:b.func.isRequired,companies:b.arrayOf(b.shape({id:b.string.isRequired,name:b.string.isRequired})),className:b.string,showAllOption:b.bool,disabled:b.bool};b.arrayOf(b.string).isRequired,b.func.isRequired,b.arrayOf(b.shape({id:b.string.isRequired,name:b.string.isRequired})),b.string,b.string,b.bool,b.bool,b.number;const Fe=({isOpen:c,onClose:t,onUpload:s})=>{var k;const[a,d]=l.useState(null),[n,o]=l.useState("select"),[m,i]=l.useState(0),[r,x]=l.useState(null),j=C=>{d(C),o("select")},g=async()=>{if(a){o("uploading"),i(0);try{const C=setInterval(()=>{i(I=>I>=90?(clearInterval(C),90):I+10)},200),N=await s(a);clearInterval(C),i(100),setTimeout(()=>{x(N),o(N.success?"success":"error")},500)}catch(C){o("error"),x({success:!1,error:C.message||"Upload failed"})}}},f=()=>{d(null),o("select"),i(0),x(null),t()},y=()=>{d(null),o("select"),i(0),x(null)};return c?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:f}),e.jsxs("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[e.jsx("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Upload Inventory File"}),e.jsx("button",{onClick:f,className:"text-gray-400 hover:text-gray-600",children:e.jsx(L,{className:"h-6 w-6"})})]})}),e.jsxs("div",{className:"px-4 pb-4 sm:px-6 sm:pb-6",children:[n==="select"&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Upload Excel (.xlsx, .xls) or PDF files containing inventory data. The system will automatically detect and parse the content."}),e.jsx(se,{onFileSelect:j,acceptedTypes:[".xlsx",".xls",".pdf"],maxSize:10*1024*1024,multiple:!1}),a&&e.jsx("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Be,{className:"h-5 w-5 text-blue-500 mr-2"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-blue-900",children:a.name}),e.jsxs("p",{className:"text-xs text-blue-700",children:[(a.size/1024/1024).toFixed(2)," MB"]})]})]})})]}),n==="uploading"&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(xe,{className:"mx-auto h-12 w-12 text-blue-500 animate-pulse"}),e.jsx("p",{className:"mt-2 text-sm font-medium text-gray-900",children:"Uploading and processing..."}),e.jsx("p",{className:"text-xs text-gray-500",children:"Please wait while we process your file"})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${m}%`}})}),e.jsxs("p",{className:"text-center text-sm text-gray-600",children:[m,"% complete"]})]}),n==="success"&&r&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(fe,{className:"mx-auto h-12 w-12 text-green-500"}),e.jsx("p",{className:"mt-2 text-sm font-medium text-gray-900",children:"Upload Successful!"})]}),e.jsx("div",{className:"bg-green-50 p-4 rounded-lg",children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-900",children:"Processed Items:"}),e.jsx("p",{className:"text-green-700",children:r.processedItems||0})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-900",children:"Created Items:"}),e.jsx("p",{className:"text-green-700",children:r.createdItems||0})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-900",children:"Updated Items:"}),e.jsx("p",{className:"text-green-700",children:r.updatedItems||0})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-green-900",children:"Errors:"}),e.jsx("p",{className:"text-green-700",children:((k=r.errors)==null?void 0:k.length)||0})]})]})}),r.errors&&r.errors.length>0&&e.jsxs("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[e.jsx("p",{className:"font-medium text-yellow-900 mb-2",children:"Warnings:"}),e.jsxs("ul",{className:"text-sm text-yellow-700 space-y-1",children:[r.errors.slice(0,5).map((C,N)=>e.jsxs("li",{children:["• ",C]},N)),r.errors.length>5&&e.jsxs("li",{children:["• ... and ",r.errors.length-5," more"]})]})]})]}),n==="error"&&r&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(Q,{className:"mx-auto h-12 w-12 text-red-500"}),e.jsx("p",{className:"mt-2 text-sm font-medium text-gray-900",children:"Upload Failed"})]}),e.jsx("div",{className:"bg-red-50 p-4 rounded-lg",children:e.jsx("p",{className:"text-sm text-red-700",children:r.error||"An error occurred during upload"})})]})]}),e.jsxs("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[n==="select"&&e.jsxs(e.Fragment,{children:[e.jsx("button",{type:"button",onClick:g,disabled:!a,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm disabled:bg-gray-300 disabled:cursor-not-allowed",children:"Upload File"}),e.jsx("button",{type:"button",onClick:f,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"Cancel"})]}),n==="uploading"&&e.jsx("button",{type:"button",onClick:f,className:"w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:w-auto sm:text-sm",children:"Cancel"}),(n==="success"||n==="error")&&e.jsxs(e.Fragment,{children:[e.jsx("button",{type:"button",onClick:f,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm",children:"Done"}),e.jsx("button",{type:"button",onClick:y,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"Upload Another"})]})]})]})]})}):null};Fe.propTypes={isOpen:b.bool.isRequired,onClose:b.func.isRequired,onUpload:b.func.isRequired};const he=({items:c=[],onItemSelect:t,selectedItems:s=[],showCompanyColumn:a=!0,showActions:d=!0,className:n=""})=>{const[o,m]=l.useState("name"),[i,r]=l.useState("asc"),[x,j]=l.useState(""),[g,f]=l.useState("all"),y=l.useMemo(()=>{let u=c.filter(h=>{var E;const v=h.name.toLowerCase().includes(x.toLowerCase())||((E=h.sku)==null?void 0:E.toLowerCase().includes(x.toLowerCase()))||h.category.toLowerCase().includes(x.toLowerCase()),R=g==="all"||h.status===g;return v&&R});return u.sort((h,v)=>{let R=h[o],E=v[o];return typeof R=="string"&&(R=R.toLowerCase(),E=E.toLowerCase()),i==="asc"?R<E?-1:R>E?1:0:R>E?-1:R<E?1:0}),u},[c,x,g,o,i]),k=u=>{o===u?r(i==="asc"?"desc":"asc"):(m(u),r("asc"))},C=u=>{t(u?y.map(h=>h.id):[])},N=(u,h)=>{t(h?[...s,u]:s.filter(v=>v!==u))},I=({field:u,children:h})=>e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>k(u),children:e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("span",{children:h}),o===u&&(i==="asc"?e.jsx(gs,{className:"h-4 w-4"}):e.jsx(Re,{className:"h-4 w-4"}))]})}),w=u=>{const h={active:"bg-green-100 text-green-800",inactive:"bg-yellow-100 text-yellow-800",discontinued:"bg-red-100 text-red-800"};return e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${h[u]||"bg-gray-100 text-gray-800"}`,children:u})},p=u=>u.currentQuantity<=u.minStockLevel;return e.jsxs("div",{className:`bg-white shadow rounded-lg ${n}`,children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx(pe,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search items...",value:x,onChange:u=>j(u.target.value),className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"})]}),e.jsxs("select",{value:g,onChange:u=>f(u.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary",children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"}),e.jsx("option",{value:"discontinued",children:"Discontinued"})]})]}),e.jsxs("div",{className:"text-sm text-gray-500",children:[y.length," of ",c.length," items"]})]})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[d&&e.jsx("th",{className:"px-6 py-3 text-left",children:e.jsx("input",{type:"checkbox",checked:s.length===y.length&&y.length>0,onChange:u=>C(u.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"})}),e.jsx(I,{field:"name",children:"Name"}),e.jsx(I,{field:"sku",children:"SKU"}),e.jsx(I,{field:"category",children:"Category"}),e.jsx(I,{field:"currentQuantity",children:"Quantity"}),e.jsx(I,{field:"unitPrice",children:"Unit Price"}),e.jsx(I,{field:"totalValue",children:"Total Value"}),a&&e.jsx(I,{field:"companyName",children:"Company"}),e.jsx(I,{field:"status",children:"Status"}),e.jsx(I,{field:"lastUpdated",children:"Last Updated"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:y.map(u=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[d&&e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("input",{type:"checkbox",checked:s.includes(u.id),onChange:h=>N(u.id,h.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:u.name}),u.description&&e.jsx("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:u.description})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:u.sku||"-"}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:u.category}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.jsxs("div",{className:`${p(u)?"text-red-600 font-semibold":""}`,children:[u.currentQuantity,p(u)&&e.jsx("span",{className:"ml-1 text-xs text-red-500",children:"(Low)"})]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Min: ",u.minStockLevel]})]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",u.unitPrice.toFixed(2)]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",u.totalValue.toFixed(2)]}),a&&e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:u.companyName}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:w(u.status)}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(u.lastUpdated).toLocaleDateString()})]},u.id))})]})}),y.length===0&&e.jsx("div",{className:"text-center py-12",children:e.jsx("div",{className:"text-gray-500",children:"No items found"})})]})};he.propTypes={items:b.arrayOf(b.object),onItemSelect:b.func,selectedItems:b.arrayOf(b.string),showCompanyColumn:b.bool,showActions:b.bool,className:b.string};const ss=({stats:c,className:t=""})=>{const s=n=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(n),a=n=>new Intl.NumberFormat("en-US").format(n),d=({title:n,value:o,icon:m,trend:i,trendValue:r,color:x="blue"})=>{const j={blue:"bg-blue-50 text-blue-600",green:"bg-green-50 text-green-600",yellow:"bg-yellow-50 text-yellow-600",red:"bg-red-50 text-red-600",purple:"bg-purple-50 text-purple-600"};return e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:n}),e.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:o}),i&&e.jsxs("div",{className:"flex items-center mt-2",children:[i==="up"?e.jsx(Ce,{className:"h-4 w-4 text-green-500 mr-1"}):e.jsx(fs,{className:"h-4 w-4 text-red-500 mr-1"}),e.jsx("span",{className:`text-sm ${i==="up"?"text-green-600":"text-red-600"}`,children:r})]})]}),e.jsx("div",{className:`p-3 rounded-full ${j[x]}`,children:m})]})})};return e.jsxs("div",{className:`space-y-6 ${t}`,children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.jsx(d,{title:"Total Items",value:a(c.totalItems),icon:e.jsx(W,{className:"h-6 w-6"}),color:"blue"}),e.jsx(d,{title:"Total Value",value:s(c.totalValue),icon:e.jsx(te,{className:"h-6 w-6"}),color:"green"}),e.jsx(d,{title:"Low Stock Items",value:a(c.lowStockItems),icon:e.jsx(Y,{className:"h-6 w-6"}),color:"red"}),e.jsx(d,{title:"Companies",value:a(c.companiesCount),icon:e.jsx(J,{className:"h-6 w-6"}),color:"purple"})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Top Categories"})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:c.topCategories.map((n,o)=>{const m=n.value/c.totalValue*100;return e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:`w-3 h-3 rounded-full ${o===0?"bg-blue-500":o===1?"bg-green-500":o===2?"bg-yellow-500":o===3?"bg-purple-500":"bg-gray-500"}`})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:n.category}),e.jsxs("p",{className:"text-xs text-gray-500",children:[n.count," items"]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:s(n.value)}),e.jsxs("p",{className:"text-xs text-gray-500",children:[m.toFixed(1),"%"]})]})]},n.category)})})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Average Item Value"}),e.jsx("p",{className:"text-xl font-semibold text-gray-900",children:s(c.totalValue/c.totalItems||0)})]}),e.jsx(te,{className:"h-8 w-8 text-gray-400"})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Low Stock Rate"}),e.jsxs("p",{className:"text-xl font-semibold text-gray-900",children:[(c.lowStockItems/c.totalItems*100||0).toFixed(1),"%"]})]}),e.jsx(Y,{className:"h-8 w-8 text-gray-400"})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Recent Transactions"}),e.jsx("p",{className:"text-xl font-semibold text-gray-900",children:a(c.recentTransactions)})]}),e.jsx(Ce,{className:"h-8 w-8 text-gray-400"})]})})]})]})};ss.propTypes={stats:b.shape({totalItems:b.number.isRequired,totalValue:b.number.isRequired,lowStockItems:b.number.isRequired,companiesCount:b.number.isRequired,recentTransactions:b.number.isRequired,topCategories:b.arrayOf(b.shape({category:b.string.isRequired,count:b.number.isRequired,value:b.number.isRequired})).isRequired}).isRequired,className:b.string};const Ks=()=>{const{user:c}=l.useContext(U),[t,s]=l.useState([]),[a,d]=l.useState("all"),[n,o]=l.useState([]),[m,i]=l.useState(!1),[r,x]=l.useState([]),[j,g]=l.useState(!0),[f,y]=l.useState(null);l.useEffect(()=>{c&&(async()=>{try{g(!0);const N=await V.getInventoryItems();s(N);const I={totalItems:N.length,totalValue:N.reduce((w,p)=>w+(p.total_value||0),0),lowStockItems:N.filter(w=>w.current_quantity<=w.min_stock_level).length,categories:[...new Set(N.map(w=>w.category))].length};y(I)}catch(N){console.error("Failed to load inventory data:",N),s([]),y(null)}finally{g(!1)}})()},[c]),l.useEffect(()=>{x(a==="all"?t:t.filter(C=>C.companyId===a))},[t,a]);const k=async C=>new Promise(N=>{setTimeout(()=>{N({success:!0,processedItems:15,createdItems:8,updatedItems:7,errors:['Row 3: Missing SKU for item "Test Item"']})},2e3)});return j?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Inventory Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage inventory across all companies"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})})]}):e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Inventory Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage inventory across all companies"})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsxs("button",{onClick:()=>i(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(xe,{className:"h-4 w-4 mr-2"}),"Upload Inventory"]}),e.jsxs("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[e.jsx(Ve,{className:"h-4 w-4 mr-2"}),"Export"]})]})]})}),f&&e.jsx("div",{className:"mb-8",children:e.jsx(ss,{stats:f})}),e.jsxs("div",{className:"mb-6 flex items-center space-x-4",children:[e.jsx(je,{selectedCompany:a,onCompanyChange:d,className:"w-64"}),e.jsxs("button",{className:"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[e.jsx(ys,{className:"h-4 w-4 mr-2"}),"More Filters"]})]}),e.jsx(he,{items:r,selectedItems:n,onItemSelect:o,showCompanyColumn:a==="all"}),e.jsx(Fe,{isOpen:m,onClose:()=>i(!1),onUpload:k})]})},Qs=()=>{const{userRole:c}=l.useContext(U),[t,s]=l.useState([{id:"1",name:"Bob Partner",email:"<EMAIL>",companies:["Acme Corp","TechStart Inc"],status:"active"},{id:"2",name:"Alice Partner",email:"<EMAIL>",companies:["Global Ventures"],status:"active"}]),[a,d]=l.useState(!1),n=async o=>{try{const m={...o,role:"partner"},i=await Ye.createUser(m);s(r=>[...r,{id:i.id,name:i.name,email:i.email,companies:[],status:i.status||"active"}]),d(!1)}catch(m){throw console.error("Failed to add partner:",m),m}};return e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Partner Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage partners and their company assignments"})]}),e.jsxs("button",{onClick:()=>d(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(ce,{className:"h-4 w-4 mr-2"}),"Add Partner"]})]})}),e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(pe,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search partners...",className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary w-64"})]})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Partner"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Assigned Companies"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map(o=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:o.name}),e.jsx("div",{className:"text-sm text-gray-500",children:o.email})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:o.companies.join(", ")})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:o.status})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[e.jsx("button",{className:"text-primary hover:text-primary-dark mr-3",children:"Edit"}),e.jsx("button",{className:"text-red-600 hover:text-red-900",children:"Remove"})]})]},o.id))})]})})]}),e.jsx(_e,{isOpen:a,onClose:()=>d(!1),onSubmit:n,userRole:c})]})},Ys=()=>{const{user:c,userRole:t}=l.useContext(U),[s,a]=l.useState([]),[d,n]=l.useState(!0);return l.useEffect(()=>{c&&(async()=>{try{n(!0);const m=await ae.getCompanies();a(m)}catch(m){console.error("Failed to load companies:",m),a([])}finally{n(!1)}})()},[c]),d?e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Company Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage companies and their details"})]}),e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})})]}):e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Company Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage companies and their details"})]})})}),e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(pe,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search companies...",className:"pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary w-64"})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6",children:s.map(o=>e.jsxs("div",{className:"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:o.name}),e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:"Active"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsxs("p",{children:["ID: ",o.id]}),e.jsx("p",{children:"Inventory Items: 3"}),e.jsx("p",{children:"Assigned Partners: 1"})]}),e.jsxs("div",{className:"mt-4 flex space-x-2",children:[e.jsx("button",{className:"text-primary hover:text-primary-dark text-sm",children:"View Details"}),e.jsx("button",{className:"text-gray-600 hover:text-gray-900 text-sm",children:"Edit"})]})]},o.id))})]})]})},Zs=()=>{const{user:c}=l.useContext(U),[t,s]=l.useState([]),[a,d]=l.useState("all"),[n,o]=l.useState([]),[m,i]=l.useState(!1),[r,x]=l.useState([]),[j,g]=l.useState(!0);l.useEffect(()=>{c&&(async()=>{try{g(!0);const k=await V.getInventoryItems();s(k)}catch(k){console.error("Failed to load inventory items:",k),s([])}finally{g(!1)}})()},[c]),l.useEffect(()=>{x(a==="all"?t:t.filter(y=>y.companyId===a))},[t,a]);const f=async y=>new Promise(k=>{setTimeout(()=>{k({success:!0,processedItems:10,createdItems:5,updatedItems:5,errors:[]})},2e3)});return e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Inventory Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage inventory for your companies"})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsxs("button",{onClick:()=>i(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(xe,{className:"h-4 w-4 mr-2"}),"Upload Inventory"]}),e.jsxs("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[e.jsx(Ve,{className:"h-4 w-4 mr-2"}),"Export"]})]})]})}),e.jsx("div",{className:"mb-6",children:e.jsx(je,{selectedCompany:a,onCompanyChange:d,className:"w-64"})}),e.jsx(he,{items:r,selectedItems:n,onItemSelect:o,showCompanyColumn:a==="all"}),e.jsx(Fe,{isOpen:m,onClose:()=>i(!1),onUpload:f})]})},Xs=()=>{const c=de(),[t]=l.useState([{id:"1",name:"Acme Corp",inventoryItems:3,totalValue:17899.67,lastUpdate:"2024-01-15"},{id:"2",name:"TechStart Inc",inventoryItems:2,totalValue:2949.83,lastUpdate:"2024-01-13"}]);return e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Your Companies"}),e.jsx("p",{className:"text-gray-600",children:"Companies assigned to your portfolio"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:t.map(s=>e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:s.name}),e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:"Active"})]}),e.jsxs("div",{className:"space-y-3 mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Inventory Items"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(W,{className:"h-4 w-4 text-gray-400 mr-1"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:s.inventoryItems})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Total Value"}),e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["$",s.totalValue.toLocaleString()]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Last Updated"}),e.jsx("span",{className:"text-sm text-gray-500",children:new Date(s.lastUpdate).toLocaleDateString()})]})]}),e.jsxs("button",{onClick:()=>c(`/partner/company/${s.id}`),className:"w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:[e.jsx(me,{className:"h-4 w-4 mr-2"}),"View Details"]})]},s.id))})]})},et=()=>{const{id:c}=ns(),t=de(),{user:s}=l.useContext(U),[a,d]=l.useState(null),[n,o]=l.useState([]),[m,i]=l.useState(!0);if(l.useEffect(()=>{s&&c&&(async()=>{try{i(!0);const g=await ae.getCompany(c);d(g);const f=await V.getInventoryItems({company_id:c});o(f)}catch(g){console.error("Failed to load company data:",g),d(null),o([])}finally{i(!1)}})()},[c,s]),m)return e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})})});if(!a)return e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-gray-500",children:"Company not found"})})});const r=n.reduce((j,g)=>j+g.totalValue,0),x=n.filter(j=>j.currentQuantity<=j.minStockLevel).length;return e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("button",{onClick:()=>t("/partner/companies"),className:"inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mb-4",children:[e.jsx(Oe,{className:"h-4 w-4 mr-1"}),"Back to Companies"]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:a.name}),e.jsx("p",{className:"text-gray-600",children:"Company inventory and details"})]}),e.jsx("span",{className:"inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800",children:"Active"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Items"}),e.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:n.length})]}),e.jsx("div",{className:"p-3 rounded-full bg-blue-50 text-blue-600",children:e.jsx(W,{className:"h-6 w-6"})})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Value"}),e.jsxs("p",{className:"text-2xl font-semibold text-gray-900",children:["$",r.toLocaleString()]})]}),e.jsx("div",{className:"p-3 rounded-full bg-green-50 text-green-600",children:e.jsx(te,{className:"h-6 w-6"})})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Low Stock Items"}),e.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:x})]}),e.jsx("div",{className:"p-3 rounded-full bg-red-50 text-red-600",children:e.jsx(Ce,{className:"h-6 w-6"})})]})})]}),e.jsx(he,{items:n,showCompanyColumn:!1,showActions:!1})]})},st=()=>{const{user:c}=l.useContext(U),[t,s]=l.useState([]),[a,d]=l.useState("all"),[n,o]=l.useState([]),[m,i]=l.useState([]),[r,x]=l.useState(!0);return l.useEffect(()=>{c&&(async()=>{var g;try{x(!0);const f=await V.getInventoryItems();s(f),((g=c==null?void 0:c.assigned_companies)==null?void 0:g.length)>0&&d(c.assigned_companies[0].id.toString())}catch(f){console.error("Failed to load inventory items:",f),s([])}finally{x(!1)}})()},[c]),l.useEffect(()=>{i(a==="all"?t:t.filter(j=>j.companyId===a))},[t,a]),e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Inventory Management"}),e.jsx("p",{className:"text-gray-600",children:"View inventory for your assigned companies"})]}),e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg px-4 py-2",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(De,{className:"h-4 w-4 text-blue-600 mr-2"}),e.jsx("span",{className:"text-sm font-medium text-blue-800",children:"Read-Only Access"})]})})]})}),e.jsx("div",{className:"mb-6",children:e.jsx(je,{selectedCompany:a,onCompanyChange:d,className:"w-64",showAllOption:!0})}),e.jsx(he,{items:m,selectedItems:n,onItemSelect:o,showCompanyColumn:a==="all",showActions:!1})]})};class tt{async getNotifications(t={}){try{const s=await _.getNotifications(t);if(s.success)return s.data||[];throw new Error(s.message||"Failed to fetch notifications")}catch(s){throw console.error("Error fetching notifications:",s),s}}async markAsRead(t){try{const s=await _.markNotificationAsRead(t);if(s.success)return!0;throw new Error(s.message||"Failed to mark notification as read")}catch(s){throw console.error("Error marking notification as read:",s),s}}async markAllAsRead(){try{const t=await _.markAllNotificationsAsRead();if(t.success)return t.data;throw new Error(t.message||"Failed to mark all notifications as read")}catch(t){throw console.error("Error marking all notifications as read:",t),t}}async deleteNotification(t){try{const s=await _.deleteNotification(t);if(s.success)return!0;throw new Error(s.message||"Failed to delete notification")}catch(s){throw console.error("Error deleting notification:",s),s}}async getUnreadCount(){try{return(await this.getNotifications({unread_only:!0})).unread_count||0}catch(t){return console.error("Error fetching unread count:",t),0}}}const rt=new tt,T=({children:c})=>{const[t,s]=l.useState(!1),[a,d]=l.useState(0),{user:n,userRole:o,userEmail:m,logout:i}=l.useContext(U),r=de(),x=is();l.useEffect(()=>{(async()=>{try{if(n){const k=await rt.getNotifications({is_read:!1});d(k.length)}}catch(k){console.error("Failed to load notification count:",k),d(0)}})()},[n]);const j=()=>{i(),r("/login")},f=(()=>{switch(o){case"superadmin":return[{name:"Dashboard",icon:e.jsx(be,{size:20}),path:"/superadmin"},{name:"User Management",icon:e.jsx(Se,{size:20}),path:"/superadmin/users"},{name:"Company Oversight",icon:e.jsx(B,{size:20}),path:"/superadmin/companies"},{name:"Inventory",icon:e.jsx(W,{size:20}),path:"/superadmin/inventory"},{name:"Notifications",icon:e.jsx(K,{size:20}),path:"/superadmin/notifications",badge:a},{name:"Settings",icon:e.jsx(Ne,{size:20}),path:"/superadmin/settings"}];case"manager":return[{name:"Dashboard",icon:e.jsx(be,{size:20}),path:"/manager"},{name:"Partners",icon:e.jsx(Se,{size:20}),path:"/manager/partners"},{name:"Companies",icon:e.jsx(B,{size:20}),path:"/manager/companies"},{name:"Inventory",icon:e.jsx(W,{size:20}),path:"/manager/inventory"},{name:"Notifications",icon:e.jsx(K,{size:20}),path:"/manager/notifications",badge:a},{name:"Settings",icon:e.jsx(Ne,{size:20}),path:"/manager/settings"}];case"partner":return[{name:"Portfolio",icon:e.jsx(be,{size:20}),path:"/partner"},{name:"Companies",icon:e.jsx(B,{size:20}),path:"/partner/companies"},{name:"Inventory",icon:e.jsx(W,{size:20}),path:"/partner/inventory"},{name:"Notifications",icon:e.jsx(K,{size:20}),path:"/partner/notifications",badge:a},{name:"Settings",icon:e.jsx(Ne,{size:20}),path:"/partner/settings"}];default:return[]}})();return e.jsxs("div",{className:"flex h-screen bg-neutral-light overflow-hidden",children:[t&&e.jsx("div",{className:"fixed inset-0 z-20 bg-black bg-opacity-50 transition-opacity md:hidden",onClick:()=>s(!1)}),e.jsx("div",{className:`fixed inset-y-0 left-0 z-30 w-64 transform bg-secondary transition duration-300 ease-in-out md:relative md:translate-x-0 ${t?"translate-x-0":"-translate-x-full"}`,children:e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between px-4 py-5 border-b border-secondary-dark",children:[e.jsx("div",{className:"flex items-center",children:e.jsx("span",{className:"text-xl font-bold text-white",children:"Enterprise Portal"})}),e.jsx("button",{onClick:()=>s(!1),className:"text-white md:hidden",children:e.jsx(L,{size:24})})]}),e.jsx("div",{className:"px-4 py-4 border-b border-secondary-dark",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"h-10 w-10 rounded-full bg-primary flex items-center justify-center text-white font-bold",children:o==null?void 0:o.charAt(0).toUpperCase()}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm font-medium text-white",children:o==="superadmin"?"Super Admin":o==="manager"?"Manager":"Partner"}),e.jsx("p",{className:"text-xs text-white text-opacity-70",children:m||"No email"})]}),e.jsx(Re,{size:16,className:"ml-auto text-white text-opacity-70"})]})}),e.jsx("nav",{className:"flex-1 overflow-y-auto px-2 py-4",children:e.jsx("ul",{className:"space-y-1",children:f.map(y=>e.jsx("li",{children:e.jsxs("a",{href:y.path,onClick:k=>{k.preventDefault(),r(y.path),s(!1)},className:`flex items-center px-4 py-3 text-sm rounded-lg ${x.pathname===y.path?"bg-primary text-white":"text-white text-opacity-80 hover:bg-secondary-dark"}`,children:[e.jsx("span",{className:"mr-3",children:y.icon}),y.name,y.badge&&e.jsx("span",{className:"ml-auto bg-red-500 text-white text-xs font-bold px-2 py-0.5 rounded-full",children:y.badge})]})},y.name))})}),e.jsx("div",{className:"border-t border-secondary-dark p-4",children:e.jsxs("button",{onClick:j,className:"flex w-full items-center px-4 py-3 text-sm text-white text-opacity-80 rounded-lg hover:bg-secondary-dark",children:[e.jsx(js,{size:20,className:"mr-3"}),"Sign Out"]})})]})}),e.jsxs("div",{className:"flex flex-1 flex-col overflow-hidden",children:[e.jsx("header",{className:"bg-white shadow-sm z-10",children:e.jsxs("div",{className:"px-4 py-4 flex items-center justify-between",children:[e.jsx("button",{onClick:()=>s(!0),className:"text-neutral-dark md:hidden",children:e.jsx(bs,{size:24})}),e.jsx("div",{className:"md:hidden font-montserrat font-bold text-lg",children:"Enterprise Portal"}),e.jsx("div",{className:"flex items-center space-x-4",children:e.jsxs("button",{className:"relative p-1 rounded-full text-gray-500 hover:bg-gray-100 hover:text-gray-600",onClick:()=>r(`/${o}/notifications`),children:[e.jsx(K,{size:20}),a>0&&e.jsx("span",{className:"absolute top-0 right-0 block h-4 w-4 rounded-full bg-red-500 text-white text-xs font-bold flex items-center justify-center",children:a})]})})]})}),e.jsx("main",{className:"flex-1 overflow-y-auto bg-neutral-light",children:c})]})]})};T.propTypes={children:b.node.isRequired};const ve=()=>{const[c,t]=l.useState([{id:"1",type:"warning",title:"Low Stock Alert",message:"Conference Tables at TechStart Inc is running low (2 remaining, minimum 1)",timestamp:"2024-01-15T10:30:00Z",read:!1},{id:"2",type:"info",title:"Inventory Upload Completed",message:"Successfully processed 15 items from purchases_january_2024.xlsx",timestamp:"2024-01-15T09:15:00Z",read:!1},{id:"3",type:"success",title:"New Partner Added",message:"Alice Partner has been successfully added to the system",timestamp:"2024-01-14T16:45:00Z",read:!0}]),s=m=>{t(i=>i.map(r=>r.id===m?{...r,read:!0}:r))},a=()=>{t(m=>m.map(i=>({...i,read:!0})))},d=m=>{t(i=>i.filter(r=>r.id!==m))},n=m=>{switch(m){case"warning":return e.jsx(Y,{className:"h-5 w-5 text-yellow-500"});case"info":return e.jsx(De,{className:"h-5 w-5 text-blue-500"});case"success":return e.jsx(ue,{className:"h-5 w-5 text-green-500"});default:return e.jsx(K,{className:"h-5 w-5 text-gray-500"})}},o=c.filter(m=>!m.read).length;return e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"}),e.jsx("p",{className:"text-gray-600",children:o>0?`${o} unread notifications`:"All notifications read"})]}),o>0&&e.jsx("button",{onClick:a,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:"Mark all as read"})]})}),e.jsx("div",{className:"bg-white shadow rounded-lg",children:c.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(K,{className:"mx-auto h-12 w-12 text-gray-400"}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No notifications"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"You're all caught up!"})]}):e.jsx("div",{className:"divide-y divide-gray-200",children:c.map(m=>e.jsx("div",{className:`p-6 hover:bg-gray-50 ${m.read?"":"bg-blue-50"}`,children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:n(m.type)}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("p",{className:`text-sm font-medium ${m.read?"text-gray-700":"text-gray-900"}`,children:m.title}),!m.read&&e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})]}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:m.message}),e.jsx("p",{className:"mt-2 text-xs text-gray-500",children:new Date(m.timestamp).toLocaleString()})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[!m.read&&e.jsx("button",{onClick:()=>s(m.id),className:"text-blue-600 hover:text-blue-800 text-sm",children:"Mark as read"}),e.jsx("button",{onClick:()=>d(m.id),className:"text-gray-400 hover:text-gray-600",children:e.jsx(L,{className:"h-4 w-4"})})]})]})},m.id))})})]})},we=()=>{const{userEmail:c,userRole:t}=l.useContext(U),[s,a]=l.useState({emailNotifications:!0,pushNotifications:!1,lowStockAlerts:!0,weeklyReports:!0,theme:"light",language:"en"}),d=(m,i)=>{a(r=>({...r,[m]:i}))},n=({title:m,icon:i,children:r})=>e.jsxs("div",{className:"bg-white shadow rounded-lg",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-2 bg-gray-100 rounded-lg mr-3",children:i}),e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:m})]})}),e.jsx("div",{className:"p-6",children:r})]}),o=({label:m,description:i,checked:r,onChange:x})=>e.jsxs("div",{className:"flex items-center justify-between py-3",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:m}),i&&e.jsx("p",{className:"text-sm text-gray-500",children:i})]}),e.jsx("button",{onClick:()=>x(!r),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${r?"bg-primary":"bg-gray-200"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${r?"translate-x-6":"translate-x-1"}`})})]});return e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Settings"}),e.jsx("p",{className:"text-gray-600",children:"Manage your account preferences and system settings"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(n,{title:"Profile",icon:e.jsx(oe,{className:"h-5 w-5 text-gray-600"}),children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),e.jsx("input",{type:"email",value:c,disabled:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Role"}),e.jsx("input",{type:"text",value:t,disabled:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm"})]})]})}),e.jsx(n,{title:"Notifications",icon:e.jsx(K,{className:"h-5 w-5 text-gray-600"}),children:e.jsxs("div",{className:"space-y-1",children:[e.jsx(o,{label:"Email Notifications",description:"Receive notifications via email",checked:s.emailNotifications,onChange:m=>d("emailNotifications",m)}),e.jsx(o,{label:"Push Notifications",description:"Receive browser push notifications",checked:s.pushNotifications,onChange:m=>d("pushNotifications",m)}),e.jsx(o,{label:"Low Stock Alerts",description:"Get notified when inventory is running low",checked:s.lowStockAlerts,onChange:m=>d("lowStockAlerts",m)}),e.jsx(o,{label:"Weekly Reports",description:"Receive weekly inventory summary reports",checked:s.weeklyReports,onChange:m=>d("weeklyReports",m)})]})}),e.jsx(n,{title:"Security",icon:e.jsx(Ns,{className:"h-5 w-5 text-gray-600"}),children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:"Change Password"}),e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-600",children:["Last login: ",new Date().toLocaleDateString()," at ",new Date().toLocaleTimeString()]})})]})}),(t==="superadmin"||t==="manager")&&e.jsx(n,{title:"System",icon:e.jsx(vs,{className:"h-5 w-5 text-gray-600"}),children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Default File Upload Size Limit"}),e.jsxs("select",{className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",children:[e.jsx("option",{children:"10 MB"}),e.jsx("option",{children:"25 MB"}),e.jsx("option",{children:"50 MB"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Data Retention Period"}),e.jsxs("select",{className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",children:[e.jsx("option",{children:"1 Year"}),e.jsx("option",{children:"2 Years"}),e.jsx("option",{children:"5 Years"})]})]})]})})]}),e.jsx("div",{className:"mt-8 flex justify-end",children:e.jsx("button",{className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark",children:"Save Changes"})})]})},U=l.createContext({isAuthenticated:!1,user:null,userRole:null,userEmail:"",login:()=>{},logout:()=>{}});function at(){const[c,t]=l.useState(!1),[s,a]=l.useState(null),[d,n]=l.useState(null),[o,m]=l.useState(""),[i,r]=l.useState(!0);l.useEffect(()=>{(async()=>{try{if(H.isAuthenticated()){await H.initializeFromToken();const y=H.getCurrentUser();y&&(t(!0),a(y),n(y.role),m(y.email))}}catch(y){console.error("Auth initialization failed:",y),H.logout()}finally{r(!1)}})()},[]);const x=f=>(t(!0),a(f),n(f.role),m(f.email),!0),j=async()=>{try{await H.logout()}catch(f){console.error("Logout error:",f)}finally{t(!1),a(null),n(null),m("")}};if(i)return e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"}),e.jsx("p",{className:"mt-4 text-gray-600",children:"Loading..."})]})});const g=({children:f,requiredRole:y})=>c?y&&d!==y?d==="superadmin"?e.jsx(z,{to:"/superadmin",replace:!0}):d==="manager"?e.jsx(z,{to:"/manager",replace:!0}):e.jsx(z,{to:"/partner",replace:!0}):f:e.jsx(z,{to:"/login",replace:!0});return g.propTypes={children:b.node.isRequired,requiredRole:b.oneOf(["superadmin","manager","partner"])},e.jsx(U.Provider,{value:{isAuthenticated:c,user:s,userRole:d,userEmail:o,login:x,logout:j},children:e.jsx(ls,{children:e.jsxs(os,{children:[e.jsx(F,{path:"/",element:c?d==="superadmin"?e.jsx(z,{to:"/superadmin",replace:!0}):d==="manager"?e.jsx(z,{to:"/manager",replace:!0}):e.jsx(z,{to:"/partner",replace:!0}):e.jsx(z,{to:"/login",replace:!0})}),e.jsx(F,{path:"/login",element:e.jsx($s,{})}),e.jsx(F,{path:"/reset-password",element:e.jsx(Ls,{})}),e.jsx(F,{path:"/superadmin",element:e.jsx(g,{requiredRole:"superadmin",children:e.jsx(T,{children:e.jsx(Bs,{})})})}),e.jsx(F,{path:"/superadmin/users",element:e.jsx(g,{requiredRole:"superadmin",children:e.jsx(T,{children:e.jsx(Js,{})})})}),e.jsx(F,{path:"/superadmin/companies",element:e.jsx(g,{requiredRole:"superadmin",children:e.jsx(T,{children:e.jsx(Hs,{})})})}),e.jsx(F,{path:"/superadmin/inventory",element:e.jsx(g,{requiredRole:"superadmin",children:e.jsx(T,{children:e.jsx(Ks,{})})})}),e.jsx(F,{path:"/superadmin/notifications",element:e.jsx(g,{requiredRole:"superadmin",children:e.jsx(T,{children:e.jsx(ve,{})})})}),e.jsx(F,{path:"/superadmin/settings",element:e.jsx(g,{requiredRole:"superadmin",children:e.jsx(T,{children:e.jsx(we,{})})})}),e.jsx(F,{path:"/manager",element:e.jsx(g,{requiredRole:"manager",children:e.jsx(T,{children:e.jsx(Vs,{})})})}),e.jsx(F,{path:"/manager/partners",element:e.jsx(g,{requiredRole:"manager",children:e.jsx(T,{children:e.jsx(Qs,{})})})}),e.jsx(F,{path:"/manager/companies",element:e.jsx(g,{requiredRole:"manager",children:e.jsx(T,{children:e.jsx(Ys,{})})})}),e.jsx(F,{path:"/manager/inventory",element:e.jsx(g,{requiredRole:"manager",children:e.jsx(T,{children:e.jsx(Zs,{})})})}),e.jsx(F,{path:"/manager/notifications",element:e.jsx(g,{requiredRole:"manager",children:e.jsx(T,{children:e.jsx(ve,{})})})}),e.jsx(F,{path:"/manager/settings",element:e.jsx(g,{requiredRole:"manager",children:e.jsx(T,{children:e.jsx(we,{})})})}),e.jsx(F,{path:"/partner",element:e.jsx(g,{requiredRole:"partner",children:e.jsx(T,{children:e.jsx(Gs,{})})})}),e.jsx(F,{path:"/partner/companies",element:e.jsx(g,{requiredRole:"partner",children:e.jsx(T,{children:e.jsx(Xs,{})})})}),e.jsx(F,{path:"/partner/company/:id",element:e.jsx(g,{requiredRole:"partner",children:e.jsx(T,{children:e.jsx(et,{})})})}),e.jsx(F,{path:"/partner/inventory",element:e.jsx(g,{requiredRole:"partner",children:e.jsx(T,{children:e.jsx(st,{})})})}),e.jsx(F,{path:"/partner/notifications",element:e.jsx(g,{requiredRole:"partner",children:e.jsx(T,{children:e.jsx(ve,{})})})}),e.jsx(F,{path:"/partner/settings",element:e.jsx(g,{requiredRole:"partner",children:e.jsx(T,{children:e.jsx(we,{})})})}),e.jsx(F,{path:"*",element:e.jsx(z,{to:"/login",replace:!0})})]})})})}const nt=Je(document.getElementById("root"));nt.render(e.jsx(at,{}));
