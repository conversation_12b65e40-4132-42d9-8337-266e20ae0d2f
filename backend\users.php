<?php
// Enable CORS for custom domain
$allowedOrigins = [
    'https://eskillvisor.wallistry.pk',
    'https://wallistry.pk',
    'https://www.wallistry.pk',
    'https://inventory-system-e-skill-visor.vercel.app',
    'http://localhost:5173',
    'http://localhost:3000'
];

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, $allowedOrigins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    header('Access-Control-Allow-Origin: https://eskillvisor.wallistry.pk');
}

header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

try {
    $db = Database::getInstance();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Check if requesting specific user profile
        $userId = $_GET['id'] ?? null;

        if ($userId) {
            // Get specific user profile with detailed information
            $user = $db->fetch("
                SELECT id, email, first_name, last_name, role, status,
                       created_at, updated_at, last_login, profile_image,
                       phone, department
                FROM users
                WHERE id = ?
            ", [$userId]);

            if (!$user) {
                echo json_encode([
                    'success' => false,
                    'message' => 'User not found'
                ]);
                exit;
            }

            // Get role-specific data
            $profileData = [
                'user' => $user,
                'assigned_companies' => [],
                'inventory_summary' => [],
                'activity_log' => [],
                'permissions' => []
            ];

            // Get assigned companies for managers and partners (simplified)
            if ($user['role'] === 'manager' || $user['role'] === 'partner') {
                try {
                    $profileData['assigned_companies'] = $db->fetchAll("
                        SELECT c.id, c.name, c.status, c.created_at
                        FROM companies c
                        LEFT JOIN company_partners cp ON c.id = cp.company_id
                        WHERE cp.user_id = ?
                        ORDER BY c.name
                    ", [$userId]);
                } catch (Exception $e) {
                    $profileData['assigned_companies'] = [];
                }
            }

            // Get inventory summary for partners (simplified)
            if ($user['role'] === 'partner') {
                try {
                    $profileData['inventory_summary'] = $db->fetchAll("
                        SELECT c.name as company_name, c.id as company_id
                        FROM companies c
                        LEFT JOIN company_partners cp ON c.id = cp.company_id
                        WHERE cp.user_id = ?
                        ORDER BY c.name
                    ", [$userId]);
                } catch (Exception $e) {
                    $profileData['inventory_summary'] = [];
                }
            }

            // Get recent activity log (simplified)
            try {
                $profileData['activity_log'] = $db->fetchAll("
                    SELECT action, table_name, record_id, created_at
                    FROM audit_trail
                    WHERE user_id = ?
                    ORDER BY created_at DESC
                    LIMIT 20
                ", [$userId]);
            } catch (Exception $e) {
                $profileData['activity_log'] = [];
            }

            echo json_encode([
                'success' => true,
                'data' => $profileData
            ]);
        } else {
            // Get all users (existing functionality)
            $users = $db->fetchAll("
                SELECT id, email, first_name, last_name, role, status,
                       created_at, updated_at, last_login, phone, department
                FROM users
                ORDER BY first_name, last_name
            ");

            echo json_encode([
                'success' => true,
                'data' => $users
            ]);
        }
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Create new user
        $input = json_decode(file_get_contents('php://input'), true);

        $firstName = $input['first_name'] ?? '';
        $lastName = $input['last_name'] ?? '';
        $email = $input['email'] ?? '';
        $password = $input['password'] ?? '';
        $role = $input['role'] ?? '';
        $phone = $input['phone'] ?? '';
        $department = $input['department'] ?? '';

        if (empty($firstName) || empty($email) || empty($password) || empty($role)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'First name, email, password, and role are required']);
            exit();
        }

        // Check if email already exists
        $existingUser = $db->fetch("SELECT id FROM users WHERE email = ?", [$email]);
        if ($existingUser) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Email already exists']);
            exit();
        }

        // Hash password
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        $result = $db->execute(
            "INSERT INTO users (first_name, last_name, email, password_hash, role, status, phone, department, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, 'active', ?, ?, NOW(), NOW())",
            [$firstName, $lastName, $email, $hashedPassword, $role, $phone, $department]
        );

        if ($result) {
            $userId = $db->lastInsertId();
            $user = $db->fetch("
                SELECT id, first_name, last_name, email, role, status, phone, department, created_at, updated_at
                FROM users WHERE id = ?
            ", [$userId]);

            echo json_encode([
                'success' => true,
                'data' => $user,
                'message' => 'User created successfully'
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to create user']);
        }
    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        // Update user profile
        $userId = $_GET['id'] ?? null;
        if (!$userId) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'User ID is required']);
            exit();
        }

        $input = json_decode(file_get_contents('php://input'), true);

        // Check if user exists
        $existingUser = $db->fetch("SELECT id FROM users WHERE id = ?", [$userId]);
        if (!$existingUser) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'User not found']);
            exit();
        }

        // Build update query dynamically
        $updateFields = [];
        $params = [];

        if (isset($input['first_name'])) {
            $updateFields[] = "first_name = ?";
            $params[] = $input['first_name'];
        }

        if (isset($input['last_name'])) {
            $updateFields[] = "last_name = ?";
            $params[] = $input['last_name'];
        }

        if (isset($input['email'])) {
            // Check if email is already taken by another user
            $emailCheck = $db->fetch("SELECT id FROM users WHERE email = ? AND id != ?", [$input['email'], $userId]);
            if ($emailCheck) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Email already exists']);
                exit();
            }
            $updateFields[] = "email = ?";
            $params[] = $input['email'];
        }

        if (isset($input['phone'])) {
            $updateFields[] = "phone = ?";
            $params[] = $input['phone'];
        }

        if (isset($input['department'])) {
            $updateFields[] = "department = ?";
            $params[] = $input['department'];
        }

        if (isset($input['status'])) {
            $updateFields[] = "status = ?";
            $params[] = $input['status'];
        }

        if (isset($input['password']) && !empty($input['password'])) {
            $updateFields[] = "password_hash = ?";
            $params[] = password_hash($input['password'], PASSWORD_DEFAULT);
        }

        if (empty($updateFields)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No fields to update']);
            exit();
        }

        $updateFields[] = "updated_at = NOW()";
        $params[] = $userId;

        $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $result = $db->execute($sql, $params);

        if ($result) {
            $updatedUser = $db->fetch("
                SELECT id, first_name, last_name, email, role, status, phone, department, created_at, updated_at
                FROM users WHERE id = ?
            ", [$userId]);

            echo json_encode([
                'success' => true,
                'data' => $updatedUser,
                'message' => 'User updated successfully'
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to update user']);
        }

    } elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
        // Delete user
        $userId = $_GET['id'] ?? null;
        if (!$userId) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'User ID is required']);
            exit();
        }

        // Check if user exists
        $existingUser = $db->fetch("SELECT id, role FROM users WHERE id = ?", [$userId]);
        if (!$existingUser) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'User not found']);
            exit();
        }

        // Prevent deletion of the last super admin
        if ($existingUser['role'] === 'super_admin') {
            $adminCount = $db->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'super_admin' AND status = 'active'");
            if ($adminCount['count'] <= 1) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Cannot delete the last super admin']);
                exit();
            }
        }

        // Soft delete by setting status to inactive
        $result = $db->execute("UPDATE users SET status = 'inactive', updated_at = NOW() WHERE id = ?", [$userId]);

        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'User deleted successfully'
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to delete user']);
        }

    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
