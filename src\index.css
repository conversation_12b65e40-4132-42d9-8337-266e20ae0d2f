/* PLEASE NOTE: THESE TAILWIND IMPORTS SHOULD NEVER BE DELETED */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
/* DO NOT DELETE THESE TAILWIND IMPORTS, OTHERWISE THE STYLING WILL NOT RENDER AT ALL */
/* Google Fonts Import */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700&family=Open+Sans:wght@400;600&display=swap');
@layer base {
  html, body, #root {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
  }
  body {
    font-family: 'Open Sans', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #1F2937;
    background-color: #F9FAFB;
  }
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
  }
  h1 { font-size: 36px; }
  h2 { font-size: 30px; }
  h3 { font-size: 24px; }
  h4 { font-size: 20px; }
}
@layer components {
  .glass-card {
    @apply bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg border border-white border-opacity-20 rounded-xl;
  }
  .gradient-bg {
    @apply bg-gradient-to-br from-secondary to-primary;
  }
  .btn-primary {
    @apply bg-gradient-to-r from-primary to-primary-light text-white py-2 px-4 rounded-lg 
           shadow-md hover:shadow-lg hover:scale-[1.03] active:scale-[0.98] transition-all duration-300
           focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50;
  }
  .btn-secondary {
    @apply bg-gradient-to-r from-secondary to-secondary-dark text-white py-2 px-4 rounded-lg 
           shadow-md hover:shadow-lg hover:scale-[1.03] active:scale-[0.98] transition-all duration-300
           focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-opacity-50;
  }
  .data-card {
    @apply bg-white bg-opacity-80 backdrop-filter backdrop-blur-sm 
           border border-primary border-opacity-20 rounded-xl shadow-sm p-4;
  }
  .input-field {
    @apply w-full px-4 py-2 rounded-lg border border-gray-300 focus:border-primary
           focus:ring-2 focus:ring-primary focus:ring-opacity-30 focus:outline-none
           transition-all duration-300;
  }
  .floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
  }
  .floating-particles span {
    position: absolute;
    display: block;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 25s linear infinite;
  }
  @keyframes float {
    0% {
      transform: translateY(0) rotate(0deg);
      opacity: 1;
    }
    100% {
      transform: translateY(-1000px) rotate(720deg);
      opacity: 0;
    }
  }
}