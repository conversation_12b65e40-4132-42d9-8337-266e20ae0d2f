<?php
// Simple database connection test
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

try {
    // Test basic PHP
    echo json_encode([
        'success' => true,
        'message' => 'PHP is working',
        'timestamp' => date('Y-m-d H:i:s'),
        'php_version' => phpversion()
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
