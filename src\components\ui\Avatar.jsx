import React from 'react';
import { UserIcon } from 'lucide-react';

const Avatar = ({ 
  src, 
  alt, 
  firstName, 
  lastName, 
  size = 'md', 
  className = '',
  showFallback = true 
}) => {
  const sizeClasses = {
    xs: 'h-6 w-6 text-xs',
    sm: 'h-8 w-8 text-sm',
    md: 'h-10 w-10 text-base',
    lg: 'h-16 w-16 text-xl',
    xl: 'h-24 w-24 text-2xl',
    '2xl': 'h-32 w-32 text-3xl'
  };

  const iconSizes = {
    xs: 12,
    sm: 16,
    md: 20,
    lg: 24,
    xl: 32,
    '2xl': 40
  };

  const getInitials = () => {
    if (firstName && lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    }
    if (firstName) {
      return firstName.charAt(0).toUpperCase();
    }
    if (alt) {
      return alt.charAt(0).toUpperCase();
    }
    return '';
  };

  if (src) {
    return (
      <img
        src={src}
        alt={alt || `${firstName} ${lastName}`}
        className={`${sizeClasses[size]} rounded-full object-cover ${className}`}
        onError={(e) => {
          if (showFallback) {
            e.target.style.display = 'none';
            e.target.nextSibling.style.display = 'flex';
          }
        }}
      />
    );
  }

  const initials = getInitials();

  return (
    <div className={`${sizeClasses[size]} rounded-full bg-primary flex items-center justify-center text-white font-semibold ${className}`}>
      {initials ? (
        <span>{initials}</span>
      ) : (
        <UserIcon size={iconSizes[size]} />
      )}
    </div>
  );
};

export default Avatar;
