{"project": {"name": "EskillVisor", "version": "1.0.0", "environment": "production"}, "deployment": {"targets": {"frontend": {"type": "static", "source": "dist/", "destination": "/home9/wallistry/eskillvisor.wallistry.pk/", "buildCommand": "npm run build", "excludePatterns": ["*.map", "*.log"], "permissions": {"files": "644", "directories": "755"}}, "backend": {"type": "php", "source": "backend/", "destination": "/home9/wallistry/eskillvisor.wallistry.pk/api/", "excludePatterns": ["config.local.php", "*.log", "uploads/*"], "permissions": {"files": "644", "directories": "755"}}, "database": {"type": "mysql", "host": "localhost", "database": "wallistry_eskillvisor_db", "user": "wallistry_eskill", "migrationsPath": "backend/migrations/", "backupPath": "deployment/backups/"}}, "dependencies": {"database_schema": ["backend_api"], "backend_config": ["frontend_build"], "backend_api": ["frontend_integration"]}, "verification": {"frontend": ["https://eskillvisor.wallistry.pk/", "https://eskillvisor.wallistry.pk/assets/"], "backend": ["https://eskillvisor.wallistry.pk/api/test", "https://eskillvisor.wallistry.pk/api/login.php"], "database": ["SELECT COUNT(*) FROM users", "SELECT COUNT(*) FROM companies"]}}, "git": {"trackingBranch": "main", "deploymentStateFile": "deployment/last-deployment.json", "changelogFile": "deployment/changelog.md"}, "automation": {"modes": ["manual", "semi-automatic", "fully-automatic"], "defaultMode": "semi-automatic", "requireConfirmation": true, "enableRollback": true, "maxRollbackVersions": 5}, "logging": {"level": "info", "file": "deployment/logs/deployment.log", "maxSize": "10MB", "retention": "30 days"}}