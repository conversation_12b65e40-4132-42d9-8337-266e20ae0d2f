const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs-extra');

// Import core modules
const ConfigManager = require('./core/ConfigManager');
const ChangeDetector = require('./core/ChangeDetector');
const DeploymentManager = require('./core/DeploymentManager');
const DocumentationGenerator = require('./generators/DocumentationGenerator');
const HistoryManager = require('./core/HistoryManager');
const ErrorHandler = require('./utils/ErrorHandler');

class UniversalDeploymentApp {
    constructor() {
        this.mainWindow = null;
        this.configManager = new ConfigManager();
        this.changeDetector = new ChangeDetector();
        this.deploymentManager = new DeploymentManager();
        this.docGenerator = new DocumentationGenerator();
        this.historyManager = new HistoryManager();
        this.errorHandler = new ErrorHandler();
    }

    async createWindow() {
        this.mainWindow = new BrowserWindow({
            width: 1200,
            height: 800,
            minWidth: 1000,
            minHeight: 600,
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false,
                enableRemoteModule: true
            },
            icon: path.join(__dirname, '../assets/icon.png'),
            title: 'Universal Deployment System',
            show: false
        });

        await this.mainWindow.loadFile(path.join(__dirname, 'gui/index.html'));
        
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
            if (process.argv.includes('--dev')) {
                this.mainWindow.webContents.openDevTools();
            }
        });

        this.setupIpcHandlers();
    }

    setupIpcHandlers() {
        // Project configuration
        ipcMain.handle('select-project-folder', async () => {
            const result = await dialog.showOpenDialog(this.mainWindow, {
                properties: ['openDirectory'],
                title: 'Select Project Root Folder'
            });
            return result.canceled ? null : result.filePaths[0];
        });

        ipcMain.handle('load-project-config', async (event, projectPath) => {
            return await this.configManager.loadProjectConfig(projectPath);
        });

        ipcMain.handle('save-project-config', async (event, projectPath, config) => {
            return await this.configManager.saveProjectConfig(projectPath, config);
        });

        // Change detection
        ipcMain.handle('detect-changes', async (event, projectPath, config) => {
            return await this.changeDetector.detectChanges(projectPath, config);
        });

        // Deployment
        ipcMain.handle('create-deployment', async (event, projectPath, changes, config) => {
            return await this.deploymentManager.createDeployment(projectPath, changes, config);
        });

        // Documentation
        ipcMain.handle('generate-documentation', async (event, deploymentData) => {
            return await this.docGenerator.generateDeploymentGuide(deploymentData);
        });

        // File operations
        ipcMain.handle('open-file-explorer', async (event, filePath) => {
            const { shell } = require('electron');
            return shell.showItemInFolder(filePath);
        });

        ipcMain.handle('open-external', async (event, url) => {
            const { shell } = require('electron');
            return shell.openExternal(url);
        });
    }
}

// Application lifecycle
const deploymentApp = new UniversalDeploymentApp();

app.whenReady().then(() => {
    deploymentApp.createWindow();
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        deploymentApp.createWindow();
    }
});

// Handle app errors
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
