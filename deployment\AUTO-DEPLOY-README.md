# 🚀 EskillVisor Automated cPanel Deployment System

## 🎯 **Overview**

The EskillVisor Automated Deployment System provides **single-command deployment** with intelligent change detection, automated building, and cPanel integration. Deploy your entire application with just `npm run auto-deploy`.

## ⚡ **Quick Start**

### **1. Setup Credentials (One-time)**
```bash
npm run deploy:setup
```
This will interactively collect your cPanel credentials and save them securely.

### **2. Deploy Automatically**
```bash
npm run auto-deploy
```
This single command handles everything:
- ✅ Detects changes since last deployment
- ✅ Builds frontend if needed
- ✅ Creates deployment package
- ✅ Provides upload instructions
- ✅ Verifies deployment
- ✅ Updates deployment state

## 🔧 **System Features**

### **🔍 Intelligent Change Detection**
- Automatically scans for file changes since last deployment
- Categorizes changes: Frontend, Backend, Database, Config
- Skips deployment if no changes detected
- Tracks file checksums for integrity verification

### **🎯 Smart Deployment Logic**
- **No Changes**: Skips deployment, shows status
- **Frontend Only**: Builds and deploys frontend
- **Backend Only**: Deploys backend API changes
- **Full Deployment**: Handles both frontend and backend
- **Database Changes**: Includes migration handling

### **🔐 Secure Credential Management**
- Encrypted credential storage in `deployment/.env`
- Environment variable support
- Secure file permissions (600)
- No credentials in version control

### **📦 Automated Package Creation**
- Builds frontend automatically if needed
- Creates optimized deployment package
- Excludes development files and logs
- Ready-to-upload cPanel structure

## 📋 **Available Commands**

### **Primary Commands**
```bash
# Complete automated deployment
npm run auto-deploy

# Setup credentials (one-time)
npm run deploy:setup
```

### **Manual Deployment Commands**
```bash
# Detect changes only
npm run deploy:detect

# Analyze and create plan
npm run deploy:analyze

# Execute deployment plan
npm run deploy:execute

# Show deployment status
npm run deploy:status
```

## ⚙️ **Configuration**

### **Deployment Configuration**
Edit `deployment/auto-deploy-config.json`:

```json
{
  "deployment": {
    "auto_deploy_enabled": true,
    "verify_deployment": true,
    "create_backups": true,
    "rollback_on_failure": true
  },
  "targets": {
    "frontend": {
      "enabled": true,
      "local_path": "dist/",
      "remote_path": "/home9/wallistry/eskillvisor.wallistry.pk/",
      "build_command": "npm run build"
    },
    "backend": {
      "enabled": true,
      "local_path": "backend/",
      "remote_path": "/home9/wallistry/eskillvisor.wallistry.pk/api/"
    }
  }
}
```

### **Credentials Configuration**
File: `deployment/.env` (created by setup)

```env
CPANEL_HOST=ftp.wallistry.pk
CPANEL_PORT=21
CPANEL_USERNAME=wallistry
CPANEL_PASSWORD=your_password
CPANEL_PROTOCOL=ftp
```

## 🔄 **Deployment Process**

### **Automated Pipeline Steps**

1. **🔍 Change Detection**
   - Scans all project files
   - Compares with last deployment state
   - Categorizes changes by type

2. **📊 Deployment Planning**
   - Analyzes change impact
   - Determines deployment type
   - Estimates execution time

3. **💾 Backup Creation**
   - Creates local backup
   - Preserves current state
   - Enables rollback capability

4. **🔨 Frontend Building**
   - Runs `npm run build` if needed
   - Optimizes assets for production
   - Validates build success

5. **📦 Package Creation**
   - Creates cPanel-ready structure
   - Copies frontend to root
   - Copies backend to `/api/`

6. **🔍 Deployment Verification**
   - Tests website accessibility
   - Verifies API endpoints
   - Confirms deployment success

7. **💾 State Update**
   - Updates deployment timestamp
   - Saves current file state
   - Resets change counter

## 📁 **Deployment Package Structure**

```
cpanel-deployment/public_html/
├── index.html              # Frontend entry point
├── assets/                 # Frontend assets
│   ├── index-[hash].css   # Styles
│   ├── index-[hash].js    # Main application
│   └── vendor-[hash].js   # Dependencies
└── api/                    # Backend API
    ├── index.php          # API entry point
    ├── controllers/       # API controllers
    ├── models/           # Data models
    ├── config/           # Configuration
    └── migrations/       # Database migrations
```

## 🎯 **Manual Upload Instructions**

After running `npm run auto-deploy`:

### **cPanel File Manager Method**
1. **Login to cPanel** at your hosting provider
2. **Open File Manager**
3. **Navigate to**: `/home9/wallistry/eskillvisor.wallistry.pk/`
4. **Upload Contents**: All files from `cpanel-deployment/public_html/`
5. **Set Permissions**:
   - Files: 644
   - Directories: 755
6. **Test**: Visit `https://eskillvisor.wallistry.pk`

### **FTP Client Method**
1. **Connect via FTP**:
   - Host: `ftp.wallistry.pk`
   - Username: `wallistry`
   - Password: Your cPanel password
2. **Navigate to**: `/home9/wallistry/eskillvisor.wallistry.pk/`
3. **Upload**: Contents of `cpanel-deployment/public_html/`
4. **Set Permissions**: Files (644), Directories (755)

## 🔍 **Verification**

### **Automatic Verification**
The system automatically tests:
- ✅ `https://eskillvisor.wallistry.pk/` (Frontend)
- ✅ `https://eskillvisor.wallistry.pk/api/test` (Backend API)

### **Manual Verification**
1. **Frontend**: Visit website and test login
2. **Backend**: Check API responses
3. **Database**: Verify data connectivity
4. **Dashboards**: Test all user roles

## 🛠️ **Troubleshooting**

### **Common Issues**

**"Credentials not found"**
```bash
npm run deploy:setup
```

**"No changes detected"**
- This is normal if no files changed
- Force deployment by modifying a file

**"Build failed"**
```bash
npm run build
# Fix any build errors, then retry
npm run auto-deploy
```

**"Package creation failed"**
- Check disk space
- Verify file permissions
- Ensure `dist/` directory exists

### **Debug Mode**
Enable verbose logging in `deployment/.env`:
```env
DEBUG_MODE=true
VERBOSE_LOGGING=true
LOG_LEVEL=debug
```

## 📊 **Deployment Status**

### **Check Current Status**
```bash
npm run deploy:status
```

Shows:
- Last deployment timestamp
- Current changes count
- Deployment requirement status
- Recent deployment history

### **Deployment Logs**
- **Main Log**: `deployment/logs/deployment.log`
- **Execution Logs**: `deployment/logs/execution_*.json`
- **State File**: `deployment/last-deployment.json`

## 🔄 **Rollback & Recovery**

### **Automatic Rollback**
- Enabled by default on deployment failure
- Restores from automatic backups
- Preserves previous working state

### **Manual Rollback**
```bash
# Restore from backup
cp -r deployment/backups/latest/* ./
npm run auto-deploy
```

## 🎉 **Success Indicators**

### **Successful Deployment Shows**
```
🎉 DEPLOYMENT SUCCESSFUL!
==================================================
Duration: 45 seconds
Website: https://eskillvisor.wallistry.pk
API: https://eskillvisor.wallistry.pk/api/test
```

### **Verification Passed**
```
✅ https://eskillvisor.wallistry.pk/ - OK (200)
✅ https://eskillvisor.wallistry.pk/api/test - OK (200)
```

## 🚀 **Next Steps**

1. **Run Setup**: `npm run deploy:setup`
2. **Test Deployment**: `npm run auto-deploy`
3. **Upload Package**: Follow manual upload instructions
4. **Verify Success**: Test website and API
5. **Automate**: Use `npm run auto-deploy` for all future deployments

**🎯 Single command deployment is now ready: `npm run auto-deploy`**
