# Vercel Cleanup Guide

## 🎯 **Removing Vercel Deployment**

Since you're moving to a cPanel-only deployment, here's how to properly clean up the Vercel deployment.

## 🔧 **Step-by-Step Cleanup Process**

### **Step 1: Disable Vercel Project**

1. **Login to Vercel Dashboard**
   - Go to https://vercel.com/dashboard
   - Login with your account

2. **Find Your Project**
   - Look for "inventory-system-e-skill-visor" project
   - Click on the project name

3. **Access Project Settings**
   - Click on "Settings" tab
   - Scroll down to "Danger Zone"

4. **Pause Deployments (Recommended)**
   - Click "Pause Deployments"
   - This stops new deployments but keeps the project
   - You can re-enable later if needed

   **OR**

5. **Delete Project (Permanent)**
   - Click "Delete Project"
   - Type the project name to confirm
   - Click "Delete"

### **Step 2: Remove GitHub Integration (Optional)**

If you want to completely remove Vercel integration:

1. **Go to GitHub Repository**
   - Visit your repository: https://github.com/ihsansaif313/Inventory-System-eSkillVisor

2. **Access Repository Settings**
   - Click "Settings" tab
   - Go to "Integrations" in the left sidebar

3. **Remove Vercel Integration**
   - Find "Vercel" in the list
   - Click "Configure"
   - Click "Uninstall" or "Remove"

### **Step 3: Update Repository Settings**

1. **Remove Vercel Configuration Files (Optional)**
   - Delete `vercel.json` file from repository
   - This prevents accidental re-deployment

2. **Update README.md**
   - Remove Vercel deployment URLs
   - Update with new custom domain information

### **Step 4: Clean Up DNS/Domain References**

1. **Update Documentation**
   - Replace Vercel URLs in any documentation
   - Update to use your custom domain

2. **Update External Links**
   - If you've shared the Vercel URL anywhere, update them
   - Use your new custom domain instead

## 📋 **Cleanup Checklist**

- [ ] Vercel project paused or deleted
- [ ] GitHub integration removed (if desired)
- [ ] vercel.json file removed (optional)
- [ ] Documentation updated with new domain
- [ ] External links updated
- [ ] Team members notified of URL change

## 🔄 **Alternative: Keep Vercel as Backup**

Instead of completely removing Vercel, you might want to keep it as a backup:

### **Option 1: Pause Deployments**
- Keeps the project but stops automatic deployments
- Can be re-enabled quickly if needed
- No data loss

### **Option 2: Change Branch**
- Configure Vercel to deploy from a different branch
- Keep main branch for cPanel deployment
- Use a "vercel" branch for Vercel deployment

### **Option 3: Rename Project**
- Rename the Vercel project to indicate it's inactive
- Add "INACTIVE" or "BACKUP" to the project name

## 🚨 **Important Considerations**

### **Before Cleanup:**

1. **Ensure Custom Domain Works**
   - Test your custom domain thoroughly
   - Verify all functionality works
   - Confirm SSL certificate is active

2. **Update Bookmarks**
   - Update any bookmarks you have
   - Inform team members of the change

3. **Check Analytics/Monitoring**
   - If you have analytics tracking the Vercel URL
   - Update tracking codes for the new domain

### **After Cleanup:**

1. **Monitor Traffic**
   - Watch for any 404 errors from old Vercel URLs
   - Set up redirects if necessary

2. **Update SEO**
   - If the site was indexed by search engines
   - Submit new sitemap with custom domain

## 🔧 **Removing vercel.json File**

If you decide to remove the Vercel configuration:

```bash
# Remove vercel.json
git rm vercel.json

# Commit the change
git commit -m "Remove Vercel configuration - migrated to cPanel"

# Push to repository
git push origin main
```

## 📝 **Update README.md**

Update your README.md file to reflect the new deployment:

```markdown
# EskillVisor Investment System

## 🌐 Live Application
- **Production URL**: https://eskillvisor.wallistry.pk
- **API Base URL**: https://eskillvisor.wallistry.pk/api

## 🚀 Deployment
This application is deployed on cPanel hosting with a custom domain.

### Frontend
- Built with React and Vite
- Deployed to cPanel public_html

### Backend
- PHP API with MySQL database
- Deployed to cPanel public_html/api

## 🔧 Development
[Your development instructions here]
```

## 🎯 **Benefits of Cleanup**

1. **Simplified Deployment**
   - Single deployment target (cPanel)
   - No confusion about which URL to use

2. **Cost Savings**
   - No Vercel usage (if you were on a paid plan)
   - Single hosting provider

3. **Easier Management**
   - Everything in one place
   - Simplified DNS management

4. **Better Performance**
   - Frontend and backend on same domain
   - No cross-origin requests

## 📞 **Support**

If you encounter issues during cleanup:

1. **Vercel Support**
   - Check Vercel documentation
   - Contact Vercel support if needed

2. **GitHub Integration**
   - GitHub has documentation on removing integrations
   - Check repository settings carefully

3. **DNS Propagation**
   - Allow time for DNS changes to propagate
   - Use DNS checking tools to verify

## ⚠️ **Rollback Plan**

If you need to rollback to Vercel:

1. **Re-enable Vercel Project**
   - Unpause deployments
   - Or create new project

2. **Restore vercel.json**
   - Add back the configuration file
   - Push to trigger deployment

3. **Update DNS**
   - Point domain back to Vercel
   - Or use Vercel's provided URL

Remember: Keep your cPanel deployment as backup during transition!
