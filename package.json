{"name": "magic-patterns-vite-template", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "npx vite", "build": "npx vite build", "lint": "eslint . --ext .js,.jsx", "preview": "npx vite preview", "vercel-build": "npx vite build", "deploy:detect": "node deployment/deploy.cjs detect", "deploy:analyze": "node deployment/deploy.cjs analyze", "deploy:execute": "node deployment/deploy.cjs deploy", "deploy:status": "node deployment/deploy.cjs status", "auto-deploy": "node deployment/master-auto-deployer.cjs", "deploy:setup": "node deployment/setup-credentials.cjs"}, "dependencies": {"basic-ftp": "^5.0.5", "commander": "^14.0.0", "lucide-react": "^0.441.0", "node-ssh": "^13.2.1", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.1", "autoprefixer": "latest", "eslint": "^8.50.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "postcss": "latest", "tailwindcss": "3.4.17", "vite": "^5.2.0"}}