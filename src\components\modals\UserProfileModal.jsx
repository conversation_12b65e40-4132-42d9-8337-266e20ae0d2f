import React, { useState, useEffect, useContext } from 'react';
import { XIcon, UserIcon, BuildingIcon, PackageIcon, PlusIcon, TrashIcon } from 'lucide-react';
import { AuthContext } from '../../App.jsx';
import AddCompanyModal from './AddCompanyModal.jsx';

const UserProfileModal = ({ isOpen, onClose, user, onRefresh }) => {
  const { userRole } = useContext(AuthContext);
  const [activeTab, setActiveTab] = useState('profile');
  const [showAddCompanyModal, setShowAddCompanyModal] = useState(false);
  const [userCompanies, setUserCompanies] = useState([]);
  const [userInventory, setUserInventory] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen && user) {
      loadUserData();
    }
  }, [isOpen, user]);

  const loadUserData = async () => {
    setLoading(true);
    try {
      // Load user's companies
      if (user.role === 'partner') {
        // Mock data for partner companies
        setUserCompanies([
          { id: '1', name: 'Acme Corp', status: 'active', inventoryItems: 15, totalValue: 25000 },
          { id: '2', name: 'TechStart Inc', status: 'active', inventoryItems: 8, totalValue: 12000 }
        ]);
      } else if (user.role === 'manager') {
        // Mock data for manager companies
        setUserCompanies([
          { id: '3', name: 'Global Ventures', status: 'active', inventoryItems: 25, totalValue: 45000 },
          { id: '4', name: 'Innovation Hub', status: 'pending', inventoryItems: 0, totalValue: 0 }
        ]);
      }

      // Load user's inventory summary
      setUserInventory([
        { category: 'Electronics', items: 12, value: 15000 },
        { category: 'Clothing', items: 8, value: 3500 },
        { category: 'Books', items: 15, value: 750 }
      ]);
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCompany = async (companyData) => {
    try {
      // Handle company creation
      console.log('Adding company for user:', user.id, companyData);
      setShowAddCompanyModal(false);
      await loadUserData(); // Refresh data
      if (onRefresh) onRefresh();
    } catch (error) {
      console.error('Error adding company:', error);
    }
  };

  const handleAssignCompany = () => {
    // For managers - show existing companies to assign
    console.log('Assign existing company to user:', user.id);
  };

  const handleRemoveCompany = async (companyId) => {
    if (window.confirm('Are you sure you want to remove this company assignment?')) {
      try {
        // Handle company removal
        console.log('Removing company:', companyId, 'from user:', user.id);
        await loadUserData(); // Refresh data
      } catch (error) {
        console.error('Error removing company:', error);
      }
    }
  };

  if (!isOpen || !user) return null;

  return (
    <>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          {/* Background overlay */}
          <div 
            className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
            onClick={onClose}
          ></div>

          {/* Modal */}
          <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full">
            {/* Header */}
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-12 w-12">
                    <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                      <UserIcon className="h-6 w-6 text-gray-500" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      {user.name || `${user.firstName} ${user.lastName}`}
                    </h3>
                    <p className="text-sm text-gray-500">{user.email}</p>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.role === 'superadmin' ? 'bg-purple-100 text-purple-800' :
                      user.role === 'manager' ? 'bg-blue-100 text-blue-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                    </span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  {/* Add/Assign Company Button */}
                  {(userRole === 'superadmin' || userRole === 'manager') && user.role !== 'superadmin' && (
                    <>
                      {userRole === 'superadmin' ? (
                        <button
                          onClick={() => setShowAddCompanyModal(true)}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary-dark"
                        >
                          <PlusIcon className="h-4 w-4 mr-1" />
                          Add Company
                        </button>
                      ) : (
                        <button
                          onClick={handleAssignCompany}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                        >
                          <PlusIcon className="h-4 w-4 mr-1" />
                          Assign Company
                        </button>
                      )}
                    </>
                  )}
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XIcon className="h-6 w-6" />
                  </button>
                </div>
              </div>

              {/* Tabs */}
              <div className="mt-6">
                <div className="border-b border-gray-200">
                  <nav className="-mb-px flex space-x-8">
                    {['profile', 'companies', 'inventory'].map((tab) => (
                      <button
                        key={tab}
                        onClick={() => setActiveTab(tab)}
                        className={`py-2 px-1 border-b-2 font-medium text-sm ${
                          activeTab === tab
                            ? 'border-primary text-primary'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        {tab.charAt(0).toUpperCase() + tab.slice(1)}
                      </button>
                    ))}
                  </nav>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="px-4 pb-4 sm:px-6 sm:pb-6">
              {loading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : (
                <>
                  {/* Profile Tab */}
                  {activeTab === 'profile' && (
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="text-lg font-medium text-gray-900 mb-4">Personal Information</h4>
                          <div className="space-y-3">
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Full Name</label>
                              <p className="mt-1 text-sm text-gray-900">{user.name || `${user.firstName} ${user.lastName}`}</p>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Email</label>
                              <p className="mt-1 text-sm text-gray-900">{user.email}</p>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Mobile</label>
                              <p className="mt-1 text-sm text-gray-900">{user.mobile || 'Not provided'}</p>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Address</label>
                              <p className="mt-1 text-sm text-gray-900">{user.address || 'Not provided'}</p>
                            </div>
                          </div>
                        </div>
                        <div>
                          <h4 className="text-lg font-medium text-gray-900 mb-4">Account Information</h4>
                          <div className="space-y-3">
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Role</label>
                              <p className="mt-1 text-sm text-gray-900">{user.role}</p>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Status</label>
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                              }`}>
                                {user.status}
                              </span>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Business Model</label>
                              <p className="mt-1 text-sm text-gray-900">{user.businessModel || 'Not specified'}</p>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Created At</label>
                              <p className="mt-1 text-sm text-gray-900">{user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Companies Tab */}
                  {activeTab === 'companies' && (
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <h4 className="text-lg font-medium text-gray-900">Associated Companies</h4>
                        <span className="text-sm text-gray-500">{userCompanies.length} companies</span>
                      </div>
                      
                      {userCompanies.length === 0 ? (
                        <div className="text-center py-8">
                          <BuildingIcon className="mx-auto h-12 w-12 text-gray-400" />
                          <h3 className="mt-2 text-sm font-medium text-gray-900">No companies assigned</h3>
                          <p className="mt-1 text-sm text-gray-500">This user is not associated with any companies yet.</p>
                        </div>
                      ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {userCompanies.map((company) => (
                            <div key={company.id} className="border border-gray-200 rounded-lg p-4">
                              <div className="flex justify-between items-start">
                                <div>
                                  <h5 className="text-sm font-medium text-gray-900">{company.name}</h5>
                                  <p className="text-xs text-gray-500 mt-1">
                                    {company.inventoryItems} items • ${company.totalValue.toLocaleString()}
                                  </p>
                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-2 ${
                                    company.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                                  }`}>
                                    {company.status}
                                  </span>
                                </div>
                                {(userRole === 'superadmin' || userRole === 'manager') && (
                                  <button
                                    onClick={() => handleRemoveCompany(company.id)}
                                    className="text-red-600 hover:text-red-800"
                                  >
                                    <TrashIcon className="h-4 w-4" />
                                  </button>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}

                  {/* Inventory Tab */}
                  {activeTab === 'inventory' && (
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <h4 className="text-lg font-medium text-gray-900">Inventory Summary</h4>
                        <span className="text-sm text-gray-500">
                          {userInventory.reduce((sum, item) => sum + item.items, 0)} total items
                        </span>
                      </div>
                      
                      {userInventory.length === 0 ? (
                        <div className="text-center py-8">
                          <PackageIcon className="mx-auto h-12 w-12 text-gray-400" />
                          <h3 className="mt-2 text-sm font-medium text-gray-900">No inventory data</h3>
                          <p className="mt-1 text-sm text-gray-500">This user has no inventory items yet.</p>
                        </div>
                      ) : (
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {userInventory.map((category, index) => (
                            <div key={index} className="bg-gray-50 rounded-lg p-4">
                              <h5 className="text-sm font-medium text-gray-900">{category.category}</h5>
                              <p className="text-2xl font-bold text-primary mt-2">{category.items}</p>
                              <p className="text-sm text-gray-500">Items</p>
                              <p className="text-sm font-medium text-gray-900 mt-1">
                                ${category.value.toLocaleString()}
                              </p>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Add Company Modal */}
      <AddCompanyModal
        isOpen={showAddCompanyModal}
        onClose={() => setShowAddCompanyModal(false)}
        onSubmit={handleAddCompany}
        userRole={userRole}
        targetUserId={user?.id}
      />
    </>
  );
};

export default UserProfileModal;
