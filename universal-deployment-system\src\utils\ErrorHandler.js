const fs = require('fs-extra');
const path = require('path');

class ErrorHandler {
    constructor() {
        this.logFile = path.join(__dirname, '../../output/logs/error.log');
        this.setupLogging();
    }

    async setupLogging() {
        try {
            await fs.ensureDir(path.dirname(this.logFile));
        } catch (error) {
            console.error('Failed to setup logging directory:', error);
        }
    }

    /**
     * Handle and categorize errors
     */
    handleError(error, context = {}) {
        const errorInfo = this.categorizeError(error, context);
        this.logError(errorInfo);
        return this.formatErrorForUser(errorInfo);
    }

    /**
     * Categorize error types
     */
    categorizeError(error, context) {
        const errorInfo = {
            timestamp: new Date().toISOString(),
            message: error.message || 'Unknown error',
            stack: error.stack,
            context,
            category: 'unknown',
            severity: 'medium',
            userMessage: 'An unexpected error occurred',
            suggestions: [],
            recoverable: true
        };

        // File system errors
        if (error.code === 'ENOENT') {
            errorInfo.category = 'file_not_found';
            errorInfo.severity = 'high';
            errorInfo.userMessage = 'Required file or directory not found';
            errorInfo.suggestions = [
                'Check if the file path is correct',
                'Ensure the file exists and is accessible',
                'Verify file permissions'
            ];
        } else if (error.code === 'EACCES') {
            errorInfo.category = 'permission_denied';
            errorInfo.severity = 'high';
            errorInfo.userMessage = 'Permission denied accessing file or directory';
            errorInfo.suggestions = [
                'Check file permissions',
                'Run as administrator if necessary',
                'Ensure the file is not locked by another process'
            ];
        } else if (error.code === 'ENOSPC') {
            errorInfo.category = 'disk_space';
            errorInfo.severity = 'critical';
            errorInfo.userMessage = 'Insufficient disk space';
            errorInfo.suggestions = [
                'Free up disk space',
                'Choose a different output location',
                'Clean up temporary files'
            ];
            errorInfo.recoverable = false;
        }

        // Network errors
        else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
            errorInfo.category = 'network';
            errorInfo.severity = 'medium';
            errorInfo.userMessage = 'Network connection failed';
            errorInfo.suggestions = [
                'Check your internet connection',
                'Verify the server address',
                'Check firewall settings'
            ];
        } else if (error.code === 'ETIMEDOUT') {
            errorInfo.category = 'timeout';
            errorInfo.severity = 'medium';
            errorInfo.userMessage = 'Operation timed out';
            errorInfo.suggestions = [
                'Try again with a longer timeout',
                'Check network stability',
                'Reduce the scope of the operation'
            ];
        }

        // Configuration errors
        else if (error.message.includes('config') || error.message.includes('configuration')) {
            errorInfo.category = 'configuration';
            errorInfo.severity = 'medium';
            errorInfo.userMessage = 'Configuration error detected';
            errorInfo.suggestions = [
                'Check your project configuration',
                'Verify all required fields are filled',
                'Reset to default configuration if needed'
            ];
        }

        // Validation errors
        else if (error.message.includes('validation') || error.message.includes('invalid')) {
            errorInfo.category = 'validation';
            errorInfo.severity = 'low';
            errorInfo.userMessage = 'Input validation failed';
            errorInfo.suggestions = [
                'Check your input values',
                'Ensure all required fields are provided',
                'Verify data formats are correct'
            ];
        }

        // Archive/compression errors
        else if (error.message.includes('archive') || error.message.includes('zip')) {
            errorInfo.category = 'compression';
            errorInfo.severity = 'medium';
            errorInfo.userMessage = 'Failed to create deployment package';
            errorInfo.suggestions = [
                'Check available disk space',
                'Verify file permissions',
                'Try reducing compression level'
            ];
        }

        // Analysis errors
        else if (error.message.includes('analysis') || error.message.includes('parse')) {
            errorInfo.category = 'analysis';
            errorInfo.severity = 'low';
            errorInfo.userMessage = 'Code analysis failed';
            errorInfo.suggestions = [
                'Some files may have syntax errors',
                'Analysis will continue with available data',
                'Check file encoding and format'
            ];
            errorInfo.recoverable = true;
        }

        return errorInfo;
    }

    /**
     * Log error to file
     */
    async logError(errorInfo) {
        try {
            const logEntry = {
                timestamp: errorInfo.timestamp,
                category: errorInfo.category,
                severity: errorInfo.severity,
                message: errorInfo.message,
                context: errorInfo.context,
                stack: errorInfo.stack
            };

            const logLine = JSON.stringify(logEntry) + '\n';
            await fs.appendFile(this.logFile, logLine);
        } catch (logError) {
            console.error('Failed to write to error log:', logError);
        }
    }

    /**
     * Format error for user display
     */
    formatErrorForUser(errorInfo) {
        return {
            message: errorInfo.userMessage,
            category: errorInfo.category,
            severity: errorInfo.severity,
            suggestions: errorInfo.suggestions,
            recoverable: errorInfo.recoverable,
            timestamp: errorInfo.timestamp,
            details: errorInfo.message // Technical details for advanced users
        };
    }

    /**
     * Validate project configuration
     */
    validateProjectConfig(config) {
        const errors = [];
        const warnings = [];

        // Required fields
        if (!config.projectName || config.projectName.trim() === '') {
            errors.push({
                field: 'projectName',
                message: 'Project name is required',
                suggestion: 'Enter a descriptive name for your project'
            });
        }

        if (!config.deployment?.domain || config.deployment.domain.trim() === '') {
            errors.push({
                field: 'deployment.domain',
                message: 'Deployment domain is required',
                suggestion: 'Enter your website domain (e.g., example.com)'
            });
        }

        if (!config.deployment?.serverPath || config.deployment.serverPath.trim() === '') {
            warnings.push({
                field: 'deployment.serverPath',
                message: 'Server path not specified, using default',
                suggestion: 'Specify the correct server path for your hosting'
            });
        }

        // Validate domain format
        if (config.deployment?.domain) {
            const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
            if (!domainRegex.test(config.deployment.domain)) {
                warnings.push({
                    field: 'deployment.domain',
                    message: 'Domain format may be invalid',
                    suggestion: 'Use format like "example.com" without http:// or www'
                });
            }
        }

        // Validate server path
        if (config.deployment?.serverPath && !config.deployment.serverPath.startsWith('/')) {
            warnings.push({
                field: 'deployment.serverPath',
                message: 'Server path should start with /',
                suggestion: 'Use absolute paths like /public_html or /www'
            });
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * Validate file operations
     */
    async validateFileOperation(filePath, operation = 'read') {
        try {
            const exists = await fs.pathExists(filePath);
            if (!exists) {
                throw new Error(`File not found: ${filePath}`);
            }

            const stats = await fs.stat(filePath);
            
            if (operation === 'read' || operation === 'write') {
                // Check if we can access the file
                await fs.access(filePath, fs.constants.R_OK);
                
                if (operation === 'write') {
                    await fs.access(filePath, fs.constants.W_OK);
                }
            }

            return {
                valid: true,
                stats,
                path: filePath
            };

        } catch (error) {
            return {
                valid: false,
                error: this.handleError(error, { filePath, operation }),
                path: filePath
            };
        }
    }

    /**
     * Validate disk space
     */
    async validateDiskSpace(requiredSpace, targetPath) {
        try {
            const stats = await fs.stat(targetPath);
            // This is a simplified check - in a real implementation,
            // you'd use a library like 'check-disk-space'
            
            return {
                sufficient: true,
                available: 'Unknown',
                required: requiredSpace
            };
        } catch (error) {
            return {
                sufficient: false,
                error: this.handleError(error, { requiredSpace, targetPath })
            };
        }
    }

    /**
     * Create user-friendly error messages
     */
    createUserMessage(error, context = {}) {
        const baseMessage = error.userMessage || error.message || 'An error occurred';
        
        let contextMessage = '';
        if (context.operation) {
            contextMessage = ` while ${context.operation}`;
        }
        if (context.file) {
            contextMessage += ` (${path.basename(context.file)})`;
        }

        return {
            title: this.getErrorTitle(error.category),
            message: baseMessage + contextMessage,
            suggestions: error.suggestions || [],
            severity: error.severity || 'medium',
            recoverable: error.recoverable !== false
        };
    }

    /**
     * Get error title based on category
     */
    getErrorTitle(category) {
        const titles = {
            file_not_found: 'File Not Found',
            permission_denied: 'Permission Denied',
            disk_space: 'Insufficient Disk Space',
            network: 'Network Error',
            timeout: 'Operation Timeout',
            configuration: 'Configuration Error',
            validation: 'Validation Error',
            compression: 'Package Creation Error',
            analysis: 'Analysis Warning',
            unknown: 'Unexpected Error'
        };
        
        return titles[category] || 'Error';
    }

    /**
     * Get recovery suggestions based on error type
     */
    getRecoverySuggestions(error) {
        const suggestions = [];
        
        switch (error.category) {
            case 'file_not_found':
                suggestions.push('Check if the project folder still exists');
                suggestions.push('Verify the file path is correct');
                suggestions.push('Try selecting the project folder again');
                break;
                
            case 'permission_denied':
                suggestions.push('Run the application as administrator');
                suggestions.push('Check file and folder permissions');
                suggestions.push('Close any programs that might be using the files');
                break;
                
            case 'network':
                suggestions.push('Check your internet connection');
                suggestions.push('Try again in a few moments');
                suggestions.push('Verify firewall settings');
                break;
                
            case 'configuration':
                suggestions.push('Review your project configuration');
                suggestions.push('Check all required fields are filled');
                suggestions.push('Try resetting to default settings');
                break;
                
            default:
                suggestions.push('Try the operation again');
                suggestions.push('Restart the application if the problem persists');
                suggestions.push('Check the error log for more details');
        }
        
        return suggestions;
    }

    /**
     * Clean up error logs
     */
    async cleanupLogs(maxAge = 7 * 24 * 60 * 60 * 1000) { // 7 days
        try {
            const logDir = path.dirname(this.logFile);
            const files = await fs.readdir(logDir);
            
            for (const file of files) {
                const filePath = path.join(logDir, file);
                const stats = await fs.stat(filePath);
                
                if (Date.now() - stats.mtime.getTime() > maxAge) {
                    await fs.remove(filePath);
                    console.log(`Cleaned up old log file: ${file}`);
                }
            }
        } catch (error) {
            console.error('Error cleaning up logs:', error);
        }
    }
}

module.exports = ErrorHandler;
