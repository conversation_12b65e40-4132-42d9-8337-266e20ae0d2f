<?php
/**
 * User Profile Management API
 * Handles detailed user profile operations with role-specific data
 */

// Enable CORS for local development
$allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:3000',
    'http://127.0.0.1:5173',
    'http://127.0.0.1:3000'
];

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, $allowedOrigins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    header('Access-Control-Allow-Origin: http://localhost:5173');
}

header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

try {
    // Get authorization header
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? '';
    
    if (strpos($authHeader, 'Bearer ') !== 0) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Authorization token required']);
        exit();
    }
    
    $db = Database::getInstance();
    $userId = $_GET['id'] ?? null;
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        if (!$userId) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'User ID is required']);
            exit();
        }
        
        // Get comprehensive user profile
        $user = $db->fetch("
            SELECT id, email, first_name, last_name, role, status, 
                   created_at, updated_at, last_login, profile_image, 
                   phone, department 
            FROM users 
            WHERE id = ? AND status != 'inactive'
        ", [$userId]);
        
        if (!$user) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'User not found']);
            exit();
        }
        
        $profileData = [
            'user' => $user,
            'assigned_companies' => [],
            'inventory_data' => [],
            'activity_log' => [],
            'statistics' => []
        ];
        
        // Role-specific data loading
        if ($user['role'] === 'manager' || $user['role'] === 'partner') {
            // Get assigned companies with detailed information
            $profileData['assigned_companies'] = $db->fetchAll("
                SELECT c.id, c.name, c.status, c.created_at, c.address, c.contact_email,
                       COUNT(DISTINCT i.id) as inventory_count,
                       COALESCE(SUM(i.price * i.quantity), 0) as total_value,
                       COUNT(DISTINCT cp2.user_id) as partner_count
                FROM companies c
                LEFT JOIN company_partners cp ON c.id = cp.company_id
                LEFT JOIN inventory_items i ON c.id = i.company_id AND i.status = 'active'
                LEFT JOIN company_partners cp2 ON c.id = cp2.company_id
                WHERE cp.user_id = ?
                GROUP BY c.id, c.name, c.status, c.created_at, c.address, c.contact_email
                ORDER BY c.name
            ", [$userId]);
        }
        
        // Partner-specific inventory data
        if ($user['role'] === 'partner') {
            $profileData['inventory_data'] = $db->fetchAll("
                SELECT c.name as company_name, c.id as company_id,
                       i.id, i.name as item_name, i.category, i.quantity, 
                       i.price, i.status, i.created_at
                FROM companies c
                INNER JOIN company_partners cp ON c.id = cp.company_id
                LEFT JOIN inventory_items i ON c.id = i.company_id AND i.status = 'active'
                WHERE cp.user_id = ?
                ORDER BY c.name, i.name
            ", [$userId]);
        }
        
        // SuperAdmin gets global overview
        if ($user['role'] === 'super_admin') {
            $profileData['statistics'] = [
                'total_users' => $db->fetch("SELECT COUNT(*) as count FROM users WHERE status = 'active'")['count'],
                'total_companies' => $db->fetch("SELECT COUNT(*) as count FROM companies WHERE status = 'active'")['count'],
                'total_inventory_value' => $db->fetch("SELECT COALESCE(SUM(price * quantity), 0) as value FROM inventory_items WHERE status = 'active'")['value'],
                'recent_logins' => $db->fetchAll("SELECT email, last_login FROM users WHERE last_login IS NOT NULL ORDER BY last_login DESC LIMIT 5")
            ];
        }
        
        // Get activity log for all roles
        $profileData['activity_log'] = $db->fetchAll("
            SELECT action, table_name, record_id, old_values, new_values, 
                   created_at, ip_address
            FROM audit_trail 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT 50
        ", [$userId]);
        
        echo json_encode([
            'success' => true,
            'data' => $profileData
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        // Update user profile with role-specific validations
        if (!$userId) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'User ID is required']);
            exit();
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        // Validate user exists
        $existingUser = $db->fetch("SELECT id, role FROM users WHERE id = ? AND status != 'inactive'", [$userId]);
        if (!$existingUser) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'User not found']);
            exit();
        }
        
        // Build update query
        $updateFields = [];
        $params = [];
        
        $allowedFields = ['first_name', 'last_name', 'email', 'phone', 'department'];
        
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                if ($field === 'email') {
                    // Check email uniqueness
                    $emailCheck = $db->fetch("SELECT id FROM users WHERE email = ? AND id != ?", [$input[$field], $userId]);
                    if ($emailCheck) {
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Email already exists']);
                        exit();
                    }
                }
                $updateFields[] = "$field = ?";
                $params[] = $input[$field];
            }
        }
        
        if (empty($updateFields)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'No valid fields to update']);
            exit();
        }
        
        $updateFields[] = "updated_at = NOW()";
        $params[] = $userId;
        
        $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $result = $db->execute($sql, $params);
        
        if ($result) {
            // Log the update
            $db->execute("
                INSERT INTO audit_trail (user_id, action, table_name, record_id, new_values, created_at, ip_address)
                VALUES (?, 'UPDATE', 'users', ?, ?, NOW(), ?)
            ", [$userId, $userId, json_encode($input), $_SERVER['REMOTE_ADDR'] ?? 'unknown']);
            
            echo json_encode([
                'success' => true,
                'message' => 'Profile updated successfully'
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to update profile']);
        }
        
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log("User profile error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}
?>
