import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  ArrowLeftIcon,
  UserIcon,
  BuildingIcon,
  PackageIcon,
  ActivityIcon,
  SettingsIcon,
  EditIcon,
  SaveIcon,
  XIcon,
  PhoneIcon,
  MailIcon,
  CalendarIcon,
  MapPinIcon,
  ShieldIcon,
  TrendingUpIcon,
  PlusIcon
} from 'lucide-react';
import { AuthContext } from '../../App.jsx';
import userService from '../../services/userService.js';
import companyService from '../../services/companyService.js';
import LoadingSpinner from '../../components/ui/LoadingSpinner.jsx';
import ErrorMessage from '../../components/ui/ErrorMessage.jsx';
import Avatar from '../../components/ui/Avatar.jsx';
import AddCompanyModal from '../../components/modals/AddCompanyModal.jsx';

const UserProfile = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user: currentUser } = useContext(AuthContext);
  
  const [profileData, setProfileData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('personal');
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({});
  const [showAddCompanyModal, setShowAddCompanyModal] = useState(false);

  useEffect(() => {
    if (id) {
      loadUserProfile();
    }
  }, [id]);

  const loadUserProfile = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await userService.getUserProfile(id);
      setProfileData(data);
      setEditForm({
        first_name: data.user.first_name || '',
        last_name: data.user.last_name || '',
        email: data.user.email || '',
        phone: data.user.phone || '',
        department: data.user.department || ''
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!editForm.first_name?.trim()) {
      errors.first_name = 'First name is required';
    }

    if (!editForm.email?.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(editForm.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (editForm.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(editForm.phone.replace(/[\s\-\(\)]/g, ''))) {
      errors.phone = 'Please enter a valid phone number';
    }

    return errors;
  };

  const handleSaveProfile = async () => {
    const validationErrors = validateForm();

    if (Object.keys(validationErrors).length > 0) {
      setError(`Validation failed: ${Object.values(validationErrors).join(', ')}`);
      return;
    }

    try {
      setError(null);
      await userService.updateUserProfile(id, editForm);
      setIsEditing(false);
      await loadUserProfile(); // Reload to get updated data
    } catch (err) {
      setError(err.message);
    }
  };

  const handleAddCompany = async (companyData) => {
    try {
      setError(null);

      // Add partner assignment, director assignment, and creator info to company data
      const enhancedCompanyData = {
        ...companyData,
        partner_id: id, // Assign to current partner
        companyDirector: id, // Set the partner as company director
        created_by: currentUser.id // Track who created it
      };

      // Create the company (it will automatically assign the partner)
      const newCompany = await companyService.createCompany(enhancedCompanyData);

      // Reload profile to show the new company
      await loadUserProfile();

      setShowAddCompanyModal(false);

      // Return the created company for document upload
      return newCompany;
    } catch (err) {
      setError(`Failed to add company: ${err.message}`);
      throw err; // Re-throw to let modal handle the error
    }
  };

  const getRoleTabs = (userRole) => {
    const baseTabs = [
      { id: 'personal', label: 'Personal Info', icon: <UserIcon size={16} /> }
    ];

    if (userRole === 'super_admin') {
      return [
        ...baseTabs,
        { id: 'overview', label: 'System Overview', icon: <ActivityIcon size={16} /> },
        { id: 'companies', label: 'All Companies', icon: <BuildingIcon size={16} /> },
        { id: 'inventory', label: 'Global Inventory', icon: <PackageIcon size={16} /> },
        { id: 'activity', label: 'Activity Log', icon: <ActivityIcon size={16} /> },
        { id: 'settings', label: 'Settings', icon: <SettingsIcon size={16} /> }
      ];
    } else if (userRole === 'manager') {
      return [
        ...baseTabs,
        { id: 'companies', label: 'Assigned Companies', icon: <BuildingIcon size={16} /> },
        { id: 'activity', label: 'Activity Log', icon: <ActivityIcon size={16} /> },
        { id: 'settings', label: 'Settings', icon: <SettingsIcon size={16} /> }
      ];
    } else if (userRole === 'partner') {
      return [
        ...baseTabs,
        { id: 'companies', label: 'Assigned Companies', icon: <BuildingIcon size={16} /> },
        { id: 'inventory', label: 'Company Inventories', icon: <PackageIcon size={16} /> },
        { id: 'activity', label: 'Activity Log', icon: <ActivityIcon size={16} /> },
        { id: 'settings', label: 'Settings', icon: <SettingsIcon size={16} /> }
      ];
    }

    return baseTabs;
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getRoleDisplayName = (role) => {
    switch (role) {
      case 'super_admin': return 'Super Administrator';
      case 'manager': return 'Manager';
      case 'partner': return 'Partner';
      default: return role;
    }
  };

  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading user profile..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full">
          <ErrorMessage
            title="Error loading profile"
            message={error}
            onRetry={loadUserProfile}
            className="mb-4"
          />
          <button
            onClick={() => navigate('/superadmin/users')}
            className="w-full bg-primary text-white px-4 py-2 rounded hover:bg-primary-dark"
          >
            Back to Users
          </button>
        </div>
      </div>
    );
  }

  if (!profileData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">User not found</p>
          <button
            onClick={() => navigate('/superadmin/users')}
            className="mt-4 bg-primary text-white px-4 py-2 rounded hover:bg-primary-dark"
          >
            Back to Users
          </button>
        </div>
      </div>
    );
  }

  const { user, assigned_companies, inventory_data, activity_log, statistics } = profileData;
  const tabs = getRoleTabs(user.role);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/superadmin/users')}
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <ArrowLeftIcon size={20} className="mr-2" />
                Back to Users
              </button>
              <div className="h-6 border-l border-gray-300"></div>
              <h1 className="text-2xl font-bold text-gray-900">User Profile</h1>
            </div>
            <div className="flex items-center space-x-3">
              {!isEditing ? (
                <>
                  <button
                    onClick={() => setIsEditing(true)}
                    className="flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark"
                  >
                    <EditIcon size={16} className="mr-2" />
                    Edit Profile
                  </button>

                  {/* Add Company button - only for Partners and only for Manager/SuperAdmin */}
                  {user.role === 'partner' && (currentUser.role === 'manager' || currentUser.role === 'super_admin') && (
                    <button
                      onClick={() => setShowAddCompanyModal(true)}
                      className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                      <PlusIcon size={16} className="mr-2" />
                      Add Company
                    </button>
                  )}
                </>
              ) : (
                <div className="flex space-x-2">
                  <button
                    onClick={handleSaveProfile}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    <SaveIcon size={16} className="mr-2" />
                    Save
                  </button>
                  <button
                    onClick={() => {
                      setIsEditing(false);
                      setEditForm({
                        first_name: user.first_name || '',
                        last_name: user.last_name || '',
                        email: user.email || '',
                        phone: user.phone || '',
                        department: user.department || ''
                      });
                    }}
                    className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                  >
                    <XIcon size={16} className="mr-2" />
                    Cancel
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Profile Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Profile Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6">
              {/* Avatar */}
              <div className="text-center mb-6">
                <Avatar
                  firstName={user.first_name}
                  lastName={user.last_name}
                  size="2xl"
                  className="mx-auto mb-4"
                />
                <h2 className="text-xl font-semibold text-gray-900">
                  {user.first_name} {user.last_name}
                </h2>
                <div className="flex items-center justify-center space-x-2 mt-1">
                  <ShieldIcon size={16} className="text-gray-500" />
                  <p className="text-gray-600">{getRoleDisplayName(user.role)}</p>
                </div>
                <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium mt-3 ${getStatusBadgeColor(user.status)}`}>
                  {user.status}
                </span>
              </div>

              {/* Quick Info */}
              <div className="space-y-3 text-sm">
                <div className="flex items-center text-gray-600">
                  <MailIcon size={16} className="mr-2" />
                  {user.email}
                </div>
                {user.phone && (
                  <div className="flex items-center text-gray-600">
                    <PhoneIcon size={16} className="mr-2" />
                    {user.phone}
                  </div>
                )}
                {user.department && (
                  <div className="flex items-center text-gray-600">
                    <MapPinIcon size={16} className="mr-2" />
                    {user.department}
                  </div>
                )}
                <div className="flex items-center text-gray-600">
                  <CalendarIcon size={16} className="mr-2" />
                  Joined {formatDate(user.created_at)}
                </div>
                {user.last_login && (
                  <div className="flex items-center text-gray-600">
                    <ActivityIcon size={16} className="mr-2" />
                    Last login {formatDate(user.last_login)}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Tabs */}
            <div className="bg-white rounded-lg shadow-sm">
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 px-6">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                        activeTab === tab.id
                          ? 'border-primary text-primary'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      {tab.icon}
                      <span className="ml-2">{tab.label}</span>
                    </button>
                  ))}
                </nav>
              </div>

              {/* Tab Content */}
              <div className="p-6">
                {/* Personal Info Tab */}
                {activeTab === 'personal' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">Personal Information</h3>
                    
                    {isEditing ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            First Name
                          </label>
                          <input
                            type="text"
                            value={editForm.first_name}
                            onChange={(e) => setEditForm({...editForm, first_name: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Last Name
                          </label>
                          <input
                            type="text"
                            value={editForm.last_name}
                            onChange={(e) => setEditForm({...editForm, last_name: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Email
                          </label>
                          <input
                            type="email"
                            value={editForm.email}
                            onChange={(e) => setEditForm({...editForm, email: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Phone
                          </label>
                          <input
                            type="tel"
                            value={editForm.phone}
                            onChange={(e) => setEditForm({...editForm, phone: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                          />
                        </div>
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Department
                          </label>
                          <input
                            type="text"
                            value={editForm.department}
                            onChange={(e) => setEditForm({...editForm, department: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                          />
                        </div>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">First Name</label>
                          <p className="mt-1 text-sm text-gray-900">{user.first_name || 'Not provided'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Last Name</label>
                          <p className="mt-1 text-sm text-gray-900">{user.last_name || 'Not provided'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Email</label>
                          <p className="mt-1 text-sm text-gray-900">{user.email}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Phone</label>
                          <p className="mt-1 text-sm text-gray-900">{user.phone || 'Not provided'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Department</label>
                          <p className="mt-1 text-sm text-gray-900">{user.department || 'Not provided'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Role</label>
                          <p className="mt-1 text-sm text-gray-900">{getRoleDisplayName(user.role)}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Status</label>
                          <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium mt-1 ${getStatusBadgeColor(user.status)}`}>
                            {user.status}
                          </span>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Member Since</label>
                          <p className="mt-1 text-sm text-gray-900">{formatDate(user.created_at)}</p>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Companies Tab */}
                {activeTab === 'companies' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">
                      {user.role === 'super_admin' ? 'All Companies' : 'Assigned Companies'}
                    </h3>
                    
                    {assigned_companies && assigned_companies.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {assigned_companies.map((company) => (
                          <div key={company.id} className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                            <div className="flex items-start justify-between mb-2">
                              <h4 className="font-medium text-gray-900">{company.name}</h4>
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                company.approval_status === 'approved'
                                  ? 'bg-green-100 text-green-800'
                                  : company.approval_status === 'rejected'
                                  ? 'bg-red-100 text-red-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {company.approval_status || 'pending'}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">
                              Industry: {company.industry || 'Not specified'}
                            </p>
                            <p className="text-sm text-gray-600 mb-2">
                              Status: <span className={`font-medium ${company.status === 'active' ? 'text-green-600' : 'text-yellow-600'}`}>
                                {company.status}
                              </span>
                            </p>
                            <div className="mt-3 text-sm text-gray-600">
                              <p>Inventory Items: {company.inventory_count || 0}</p>
                              <p>Total Value: ${(company.total_value || 0).toLocaleString()}</p>
                            </div>
                            {company.created_at && (
                              <p className="text-xs text-gray-500 mt-2">
                                Added: {new Date(company.created_at).toLocaleDateString()}
                              </p>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500">No companies assigned</p>
                    )}
                  </div>
                )}

                {/* Inventory Tab (Partners only) */}
                {activeTab === 'inventory' && user.role === 'partner' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">Company Inventories</h3>
                    
                    {inventory_data && inventory_data.length > 0 ? (
                      <div className="space-y-4">
                        {/* Group by company */}
                        {Object.entries(
                          inventory_data.reduce((acc, item) => {
                            if (!acc[item.company_name]) {
                              acc[item.company_name] = [];
                            }
                            if (item.item_name) { // Only include actual inventory items
                              acc[item.company_name].push(item);
                            }
                            return acc;
                          }, {})
                        ).map(([companyName, items]) => (
                          <div key={companyName} className="bg-gray-50 rounded-lg p-4">
                            <h4 className="font-medium text-gray-900 mb-3">{companyName}</h4>
                            {items.length > 0 ? (
                              <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                  <thead className="bg-gray-50">
                                    <tr>
                                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Item</th>
                                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Category</th>
                                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Quantity</th>
                                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Price</th>
                                    </tr>
                                  </thead>
                                  <tbody className="divide-y divide-gray-200">
                                    {items.map((item) => (
                                      <tr key={item.id}>
                                        <td className="px-3 py-2 text-sm text-gray-900">{item.item_name}</td>
                                        <td className="px-3 py-2 text-sm text-gray-600">{item.category}</td>
                                        <td className="px-3 py-2 text-sm text-gray-600">{item.quantity}</td>
                                        <td className="px-3 py-2 text-sm text-gray-600">${item.price}</td>
                                      </tr>
                                    ))}
                                  </tbody>
                                </table>
                              </div>
                            ) : (
                              <p className="text-gray-500 text-sm">No inventory items</p>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500">No inventory data available</p>
                    )}
                  </div>
                )}

                {/* Global Inventory Tab (SuperAdmin only) */}
                {activeTab === 'inventory' && user.role === 'super_admin' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">Global Inventory Overview</h3>
                    
                    {statistics && (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-blue-50 rounded-lg p-4">
                          <h4 className="font-medium text-blue-900">Total Value</h4>
                          <p className="text-2xl font-bold text-blue-600">
                            ${(statistics.total_inventory_value || 0).toLocaleString()}
                          </p>
                        </div>
                        <div className="bg-green-50 rounded-lg p-4">
                          <h4 className="font-medium text-green-900">Total Companies</h4>
                          <p className="text-2xl font-bold text-green-600">
                            {statistics.total_companies || 0}
                          </p>
                        </div>
                        <div className="bg-purple-50 rounded-lg p-4">
                          <h4 className="font-medium text-purple-900">Total Users</h4>
                          <p className="text-2xl font-bold text-purple-600">
                            {statistics.total_users || 0}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* System Overview Tab (SuperAdmin only) */}
                {activeTab === 'overview' && user.role === 'super_admin' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">System Overview</h3>
                    
                    {statistics && (
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                          <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200">
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="font-medium text-blue-900 mb-1">Total Users</h4>
                                <p className="text-3xl font-bold text-blue-600">{statistics.total_users || 0}</p>
                              </div>
                              <UserIcon className="h-8 w-8 text-blue-500" />
                            </div>
                          </div>
                          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-6 border border-green-200">
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="font-medium text-green-900 mb-1">Total Companies</h4>
                                <p className="text-3xl font-bold text-green-600">{statistics.total_companies || 0}</p>
                              </div>
                              <BuildingIcon className="h-8 w-8 text-green-500" />
                            </div>
                          </div>
                          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-6 border border-purple-200">
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="font-medium text-purple-900 mb-1">Inventory Value</h4>
                                <p className="text-3xl font-bold text-purple-600">
                                  ${(statistics.total_inventory_value || 0).toLocaleString()}
                                </p>
                              </div>
                              <TrendingUpIcon className="h-8 w-8 text-purple-500" />
                            </div>
                          </div>
                        </div>

                        {statistics.recent_logins && statistics.recent_logins.length > 0 && (
                          <div className="bg-gray-50 rounded-lg p-4">
                            <h4 className="font-medium text-gray-900 mb-3">Recent User Activity</h4>
                            <div className="space-y-2">
                              {statistics.recent_logins.map((login, index) => (
                                <div key={index} className="flex justify-between text-sm">
                                  <span className="text-gray-900">{login.email}</span>
                                  <span className="text-gray-600">{formatDate(login.last_login)}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}

                {/* Activity Log Tab */}
                {activeTab === 'activity' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">Activity Log</h3>
                    
                    {activity_log && activity_log.length > 0 ? (
                      <div className="space-y-3">
                        {activity_log.map((activity, index) => (
                          <div key={index} className="bg-gray-50 rounded-lg p-4">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-medium text-gray-900">{activity.action}</p>
                                <p className="text-sm text-gray-600">
                                  Table: {activity.table_name} | Record ID: {activity.record_id}
                                </p>
                                {activity.ip_address && (
                                  <p className="text-xs text-gray-500">IP: {activity.ip_address}</p>
                                )}
                              </div>
                              <span className="text-xs text-gray-500">
                                {formatDate(activity.created_at)}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500">No activity recorded</p>
                    )}
                  </div>
                )}

                {/* Settings Tab */}
                {activeTab === 'settings' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-medium text-gray-900">Account Settings</h3>
                    
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <h4 className="font-medium text-yellow-800">Account Management</h4>
                      <p className="text-sm text-yellow-700 mt-1">
                        Advanced account settings and security options will be available here.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add Company Modal */}
      <AddCompanyModal
        isOpen={showAddCompanyModal}
        onClose={() => setShowAddCompanyModal(false)}
        onSubmit={handleAddCompany}
        userRole={currentUser.role}
        currentUserId={currentUser.id}
        targetUserId={id}
      />
    </div>
  );
};

export default UserProfile;
