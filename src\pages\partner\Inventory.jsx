import React, { useState, useEffect, useContext } from 'react';
import { InfoIcon } from 'lucide-react';
import { InventoryTable, CompanyFilter } from '../../components/inventory/index.js';
import { AuthContext } from '../../App.jsx';
import inventoryService from '../../services/inventoryService.js';

const PartnerInventory = () => {
  const { user } = useContext(AuthContext);
  const [items, setItems] = useState([]);
  const [selectedCompany, setSelectedCompany] = useState('all');
  const [selectedItems, setSelectedItems] = useState([]);
  const [filteredItems, setFilteredItems] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadInventoryItems = async () => {
      try {
        setLoading(true);
        // Partners only see inventory for their assigned companies
        const inventoryItems = await inventoryService.getInventoryItems();
        setItems(inventoryItems);

        // Set default company to first assigned company if available
        if (user?.assigned_companies?.length > 0) {
          setSelectedCompany(user.assigned_companies[0].id.toString());
        }
      } catch (error) {
        console.error('Failed to load inventory items:', error);
        setItems([]);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      loadInventoryItems();
    }
  }, [user]);

  useEffect(() => {
    if (selectedCompany === 'all') {
      setFilteredItems(items);
    } else {
      setFilteredItems(items.filter(item => item.companyId === selectedCompany));
    }
  }, [items, selectedCompany]);



  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Inventory Management</h1>
            <p className="text-gray-600">View inventory for your assigned companies</p>
          </div>
          <div className="bg-blue-50 border border-blue-200 rounded-lg px-4 py-2">
            <div className="flex items-center">
              <InfoIcon className="h-4 w-4 text-blue-600 mr-2" />
              <span className="text-sm font-medium text-blue-800">Read-Only Access</span>
            </div>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <CompanyFilter
          selectedCompany={selectedCompany}
          onCompanyChange={setSelectedCompany}
          className="w-64"
          showAllOption={true}
        />
      </div>

      <InventoryTable
        items={filteredItems}
        selectedItems={selectedItems}
        onItemSelect={setSelectedItems}
        showCompanyColumn={selectedCompany === 'all'}
        showActions={false}
      />
    </div>
  );
};

export default PartnerInventory;
