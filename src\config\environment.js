/**
 * Environment Configuration
 * Handles different settings for local development and production
 */

const environment = {
  // Get environment from Vite environment variables
  ENV: import.meta.env.VITE_APP_ENV || 'local',
  
  // API Configuration
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost/Investment-System-eSkillVisor/backend',
  
  // App Configuration
  APP_BASE_URL: import.meta.env.VITE_APP_BASE_URL || 'http://localhost:5173',
  APP_TITLE: import.meta.env.VITE_APP_TITLE || 'Investment System',
  
  // Debug Settings
  DEBUG_MODE: import.meta.env.VITE_DEBUG_MODE === 'true',
  ENABLE_CONSOLE_LOGS: import.meta.env.VITE_ENABLE_CONSOLE_LOGS === 'true',
  
  // Helper methods
  isProduction: () => environment.ENV === 'production',
  isLocal: () => environment.ENV === 'local',
  
  // API endpoint builder
  getApiUrl: (endpoint) => {
    const baseUrl = environment.API_BASE_URL.endsWith('/') 
      ? environment.API_BASE_URL.slice(0, -1) 
      : environment.API_BASE_URL;
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${baseUrl}/${cleanEndpoint}`;
  },
  
  // Console logging wrapper
  log: (...args) => {
    if (environment.ENABLE_CONSOLE_LOGS) {
      console.log('[Investment System]', ...args);
    }
  },
  
  // Error logging wrapper
  error: (...args) => {
    if (environment.ENABLE_CONSOLE_LOGS) {
      console.error('[Investment System Error]', ...args);
    }
  }
};

// Log current environment on load
if (environment.ENABLE_CONSOLE_LOGS) {
  console.log('🚀 Investment System Environment:', {
    ENV: environment.ENV,
    API_BASE_URL: environment.API_BASE_URL,
    APP_BASE_URL: environment.APP_BASE_URL,
    DEBUG_MODE: environment.DEBUG_MODE
  });
}

export default environment;
