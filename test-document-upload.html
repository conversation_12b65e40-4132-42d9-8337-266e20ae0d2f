<!DOCTYPE html>
<html>
<head>
    <title>Test Document Upload</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Document Upload Test</h1>
        
        <div>
            <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
            <button onclick="testUpload()">Test Upload to Company 24</button>
            <button onclick="testGetDocuments()">Get Documents for Company 24</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost/Investment-System-eSkillVisor/backend';
        
        function addResult(title, status, message, details = null) {
            const resultsDiv = document.getElementById('results');
            const statusClass = status === 'success' ? 'success' : 'error';
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${statusClass}`;
            resultDiv.innerHTML = `
                <h4>${title}</h4>
                <p>${message}</p>
                ${details ? `<pre>${JSON.stringify(details, null, 2)}</pre>` : ''}
            `;
            
            resultsDiv.appendChild(resultDiv);
        }
        
        async function testUpload() {
            const fileInput = document.getElementById('fileInput');
            const files = fileInput.files;
            
            if (files.length === 0) {
                addResult('Upload Test', 'error', 'Please select at least one file');
                return;
            }
            
            const formData = new FormData();
            formData.append('company_id', '24');
            formData.append('uploaded_by', '1'); // Admin user
            
            for (let i = 0; i < files.length; i++) {
                formData.append('documents[]', files[i]);
            }
            
            try {
                const response = await fetch(`${API_BASE}/company-documents.php`, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    addResult('Upload Success', 'success', 
                        `Uploaded ${data.uploaded_files.length} files`, {
                            uploaded_files: data.uploaded_files,
                            errors: data.errors
                        });
                } else {
                    addResult('Upload Failed', 'error', data.message, data);
                }
            } catch (error) {
                addResult('Upload Error', 'error', error.message);
            }
        }
        
        async function testGetDocuments() {
            try {
                const response = await fetch(`${API_BASE}/company-documents.php?company_id=24`);
                const data = await response.json();
                
                if (data.success) {
                    addResult('Get Documents Success', 'success', 
                        `Found ${data.data.length} documents`, {
                            documents: data.data
                        });
                } else {
                    addResult('Get Documents Failed', 'error', data.message);
                }
            } catch (error) {
                addResult('Get Documents Error', 'error', error.message);
            }
        }
    </script>
</body>
</html>
