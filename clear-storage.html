<!DOCTYPE html>
<html>
<head>
    <title>Clear Storage</title>
</head>
<body>
    <h1>Clear Browser Storage</h1>
    <button onclick="clearStorage()">Clear All Storage</button>
    <div id="result"></div>
    
    <script>
        function clearStorage() {
            // Clear localStorage
            localStorage.clear();
            
            // Clear sessionStorage
            sessionStorage.clear();
            
            // Show what was cleared
            document.getElementById('result').innerHTML = `
                <p>✅ localStorage cleared</p>
                <p>✅ sessionStorage cleared</p>
                <p>🔄 Now refresh the Investment System page: <a href="http://localhost:5173" target="_blank">http://localhost:5173</a></p>
            `;
            
            console.log('Storage cleared successfully');
        }
    </script>
</body>
</html>
