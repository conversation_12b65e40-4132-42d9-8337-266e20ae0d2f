const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');
const glob = require('glob');
const axios = require('axios');

class ChangeDetector {
    constructor() {
        this.cache = new Map();
    }

    /**
     * Main method to detect changes between local and remote files
     */
    async detectChanges(projectPath, config) {
        try {
            console.log('Starting change detection for:', projectPath);
            
            const startTime = Date.now();
            const result = {
                timestamp: new Date().toISOString(),
                projectPath,
                totalFiles: 0,
                changedFiles: 0,
                addedFiles: 0,
                modifiedFiles: 0,
                deletedFiles: 0,
                unchangedFiles: 0,
                categories: {},
                changes: [],
                errors: [],
                duration: 0
            };

            // Get all local files
            const localFiles = await this.scanLocalFiles(projectPath, config);
            result.totalFiles = localFiles.length;

            // Get remote files if remote checking is enabled
            let remoteFiles = [];
            if (config.changeDetection.remoteCheck) {
                remoteFiles = await this.scanRemoteFiles(config);
            }

            // Compare files and detect changes
            const changes = await this.compareFiles(localFiles, remoteFiles, config);
            
            // Categorize changes
            const categorizedChanges = this.categorizeChanges(changes, config);
            
            // Update result
            result.changes = changes;
            result.categories = categorizedChanges;
            result.changedFiles = changes.length;
            result.addedFiles = changes.filter(c => c.status === 'added').length;
            result.modifiedFiles = changes.filter(c => c.status === 'modified').length;
            result.deletedFiles = changes.filter(c => c.status === 'deleted').length;
            result.unchangedFiles = result.totalFiles - result.changedFiles;
            result.duration = Date.now() - startTime;

            console.log(`Change detection completed in ${result.duration}ms`);
            console.log(`Found ${result.changedFiles} changes out of ${result.totalFiles} files`);

            return result;

        } catch (error) {
            console.error('Error in change detection:', error);
            throw new Error(`Change detection failed: ${error.message}`);
        }
    }

    /**
     * Scan local files in project directory
     */
    async scanLocalFiles(projectPath, config) {
        const files = [];
        const excludePatterns = config.excludePatterns || [];
        
        try {
            // Create glob patterns for included files
            const includePatterns = ['**/*'];
            
            // Use glob to find all files
            const globOptions = {
                cwd: projectPath,
                ignore: excludePatterns,
                nodir: true,
                dot: false
            };

            const filePaths = await new Promise((resolve, reject) => {
                glob('**/*', globOptions, (err, matches) => {
                    if (err) reject(err);
                    else resolve(matches);
                });
            });

            // Process each file
            for (const filePath of filePaths) {
                const fullPath = path.join(projectPath, filePath);
                
                try {
                    const stats = await fs.stat(fullPath);
                    if (stats.isFile()) {
                        const fileInfo = await this.getFileInfo(fullPath, filePath, config);
                        files.push(fileInfo);
                    }
                } catch (error) {
                    console.warn(`Error processing file ${filePath}:`, error.message);
                }
            }

            return files;

        } catch (error) {
            console.error('Error scanning local files:', error);
            throw error;
        }
    }

    /**
     * Get detailed information about a file
     */
    async getFileInfo(fullPath, relativePath, config) {
        const stats = await fs.stat(fullPath);
        const fileInfo = {
            path: relativePath,
            fullPath: fullPath,
            size: stats.size,
            modified: stats.mtime.toISOString(),
            extension: path.extname(relativePath),
            category: this.categorizeFile(relativePath, config),
            hash: null
        };

        // Calculate content hash if using content-based detection
        if (config.changeDetection.method === 'content-hash') {
            fileInfo.hash = await this.calculateFileHash(fullPath);
        }

        return fileInfo;
    }

    /**
     * Calculate MD5 hash of file content
     */
    async calculateFileHash(filePath) {
        try {
            const content = await fs.readFile(filePath);
            return crypto.createHash('md5').update(content).digest('hex');
        } catch (error) {
            console.warn(`Error calculating hash for ${filePath}:`, error.message);
            return null;
        }
    }

    /**
     * Categorize file based on extension and path
     */
    categorizeFile(filePath, config) {
        const extension = path.extname(filePath).toLowerCase();
        const pathParts = filePath.split(path.sep);
        
        for (const [categoryName, categoryConfig] of Object.entries(config.fileCategories)) {
            // Check extensions
            if (categoryConfig.extensions && categoryConfig.extensions.includes(extension)) {
                return categoryName;
            }
            
            // Check directories
            if (categoryConfig.directories) {
                for (const dir of categoryConfig.directories) {
                    if (pathParts.some(part => part.includes(dir))) {
                        return categoryName;
                    }
                }
            }
        }
        
        return 'other';
    }

    /**
     * Scan remote files (simplified version - would need actual FTP/HTTP implementation)
     */
    async scanRemoteFiles(config) {
        // This is a placeholder - in a real implementation, you would:
        // 1. Connect to FTP server
        // 2. List files in remote directory
        // 3. Get file stats (size, modified date)
        // 4. Optionally download files for content comparison
        
        console.log('Remote file scanning not implemented in this demo');
        return [];
    }

    /**
     * Compare local and remote files to detect changes
     */
    async compareFiles(localFiles, remoteFiles, config) {
        const changes = [];
        const remoteFileMap = new Map();
        
        // Create map of remote files for quick lookup
        remoteFiles.forEach(file => {
            remoteFileMap.set(file.path, file);
        });

        // Check each local file
        for (const localFile of localFiles) {
            const remoteFile = remoteFileMap.get(localFile.path);
            
            if (!remoteFile) {
                // File doesn't exist remotely - it's new
                changes.push({
                    ...localFile,
                    status: 'added',
                    reason: 'File does not exist on remote server'
                });
            } else {
                // File exists remotely - check if it's changed
                const hasChanged = await this.hasFileChanged(localFile, remoteFile, config);
                
                if (hasChanged.changed) {
                    changes.push({
                        ...localFile,
                        status: 'modified',
                        reason: hasChanged.reason,
                        remoteInfo: remoteFile
                    });
                }
            }
        }

        // Check for deleted files (exist remotely but not locally)
        for (const remoteFile of remoteFiles) {
            const localExists = localFiles.some(f => f.path === remoteFile.path);
            if (!localExists) {
                changes.push({
                    ...remoteFile,
                    status: 'deleted',
                    reason: 'File exists on remote but not locally'
                });
            }
        }

        return changes;
    }

    /**
     * Check if a file has changed between local and remote
     */
    async hasFileChanged(localFile, remoteFile, config) {
        const method = config.changeDetection.method;
        
        switch (method) {
            case 'content-hash':
                if (localFile.hash && remoteFile.hash) {
                    return {
                        changed: localFile.hash !== remoteFile.hash,
                        reason: 'Content hash differs'
                    };
                }
                // Fall through to size comparison if hash not available
                
            case 'size':
                if (localFile.size !== remoteFile.size) {
                    return {
                        changed: true,
                        reason: `Size differs (local: ${localFile.size}, remote: ${remoteFile.size})`
                    };
                }
                // Fall through to timestamp if size is same
                
            case 'timestamp':
            default:
                const localTime = new Date(localFile.modified);
                const remoteTime = new Date(remoteFile.modified);
                
                if (localTime > remoteTime) {
                    return {
                        changed: true,
                        reason: `Local file is newer (local: ${localFile.modified}, remote: ${remoteFile.modified})`
                    };
                }
                
                return {
                    changed: false,
                    reason: 'File appears unchanged'
                };
        }
    }

    /**
     * Categorize changes by file type
     */
    categorizeChanges(changes, config) {
        const categories = {};
        
        // Initialize categories
        Object.keys(config.fileCategories).forEach(category => {
            categories[category] = {
                files: [],
                count: 0,
                description: config.fileCategories[category].description || category
            };
        });
        
        categories.other = {
            files: [],
            count: 0,
            description: 'Other files'
        };

        // Categorize each change
        changes.forEach(change => {
            const category = change.category || 'other';
            if (categories[category]) {
                categories[category].files.push(change);
                categories[category].count++;
            }
        });

        return categories;
    }

    /**
     * Get change summary for display
     */
    getChangeSummary(changes) {
        const summary = {
            total: changes.length,
            added: 0,
            modified: 0,
            deleted: 0,
            categories: {}
        };

        changes.forEach(change => {
            switch (change.status) {
                case 'added':
                    summary.added++;
                    break;
                case 'modified':
                    summary.modified++;
                    break;
                case 'deleted':
                    summary.deleted++;
                    break;
            }

            const category = change.category || 'other';
            if (!summary.categories[category]) {
                summary.categories[category] = 0;
            }
            summary.categories[category]++;
        });

        return summary;
    }
}

module.exports = ChangeDetector;
