<?php
/**
 * Check Companies in Database
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:5173');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

try {
    $db = Database::getInstance();
    
    // Get all companies
    $companies = $db->fetchAll("SELECT * FROM companies ORDER BY id DESC LIMIT 10");
    
    // Get company count
    $countResult = $db->fetch("SELECT COUNT(*) as total FROM companies");
    $totalCompanies = $countResult[0]['total'] ?? 0;
    
    echo json_encode([
        'success' => true,
        'total_companies' => $totalCompanies,
        'recent_companies' => $companies,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
