@echo off
echo Creating source-based deployment package...

:: Create source deployment directory
mkdir eskillvisor-source-deployment-2025-07-18 2>nul
mkdir eskillvisor-source-deployment-2025-07-18\api 2>nul
mkdir eskillvisor-source-deployment-2025-07-18\src 2>nul

echo Copying API directory (already updated)...
robocopy "eskillvisor-production-2025-07-18\api" "eskillvisor-source-deployment-2025-07-18\api" /E

echo Copying source files with updates...
robocopy "src" "eskillvisor-source-deployment-2025-07-18\src" /E

echo Copying configuration files...
copy "index.html" "eskillvisor-source-deployment-2025-07-18\"
copy "package.json" "eskillvisor-source-deployment-2025-07-18\"
copy "vite.config.js" "eskillvisor-source-deployment-2025-07-18\"
copy "tailwind.config.js" "eskillvisor-source-deployment-2025-07-18\"
copy "postcss.config.js" "eskillvisor-source-deployment-2025-07-18\"

echo Creating .htaccess for production...
echo RewriteEngine On > "eskillvisor-source-deployment-2025-07-18\.htaccess"
echo. >> "eskillvisor-source-deployment-2025-07-18\.htaccess"
echo # Handle React Router >> "eskillvisor-source-deployment-2025-07-18\.htaccess"
echo RewriteCond %%{REQUEST_FILENAME} !-f >> "eskillvisor-source-deployment-2025-07-18\.htaccess"
echo RewriteCond %%{REQUEST_FILENAME} !-d >> "eskillvisor-source-deployment-2025-07-18\.htaccess"
echo RewriteCond %%{REQUEST_URI} !^/api/ >> "eskillvisor-source-deployment-2025-07-18\.htaccess"
echo RewriteRule . /index.html [L] >> "eskillvisor-source-deployment-2025-07-18\.htaccess"
echo. >> "eskillvisor-source-deployment-2025-07-18\.htaccess"
echo # API Routes >> "eskillvisor-source-deployment-2025-07-18\.htaccess"
echo RewriteCond %%{REQUEST_URI} ^/api/ >> "eskillvisor-source-deployment-2025-07-18\.htaccess"
echo RewriteRule ^api/(.*)$ api/index.php [QSA,L] >> "eskillvisor-source-deployment-2025-07-18\.htaccess"

echo Creating deployment instructions...
echo # Source-Based Deployment Instructions > "eskillvisor-source-deployment-2025-07-18\DEPLOY_INSTRUCTIONS.md"
echo. >> "eskillvisor-source-deployment-2025-07-18\DEPLOY_INSTRUCTIONS.md"
echo 1. Upload this entire folder to cPanel >> "eskillvisor-source-deployment-2025-07-18\DEPLOY_INSTRUCTIONS.md"
echo 2. Extract to /home9/wallistry/eskillvisor.wallistry.pk/ >> "eskillvisor-source-deployment-2025-07-18\DEPLOY_INSTRUCTIONS.md"
echo 3. Run: npm install >> "eskillvisor-source-deployment-2025-07-18\DEPLOY_INSTRUCTIONS.md"
echo 4. Run: npm run build >> "eskillvisor-source-deployment-2025-07-18\DEPLOY_INSTRUCTIONS.md"
echo 5. Execute database migration in phpMyAdmin >> "eskillvisor-source-deployment-2025-07-18\DEPLOY_INSTRUCTIONS.md"

echo Creating ZIP package...
powershell "Compress-Archive -Path 'eskillvisor-source-deployment-2025-07-18\*' -DestinationPath 'eskillvisor-source-deployment-2025-07-18.zip' -Force"

echo.
echo ✅ Source-based deployment package created!
echo 📦 File: eskillvisor-source-deployment-2025-07-18.zip
echo 📁 Contains: Complete source code with all user management updates
echo.
echo This package includes:
echo - ✅ Updated source files (src/ directory)
echo - ✅ Updated API (api/ directory)
echo - ✅ Configuration files
echo - ✅ .htaccess for routing
echo.
echo Deploy this package and run npm run build on the server
echo to get the user management updates working.
echo.
pause
