<?php
// Enable CORS for custom domain
$allowedOrigins = [
    'https://eskillvisor.wallistry.pk',
    'https://wallistry.pk',
    'https://www.wallistry.pk',
    'https://inventory-system-e-skill-visor.vercel.app',
    'http://localhost:5173',
    'http://localhost:3000'
];

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, $allowedOrigins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    header('Access-Control-Allow-Origin: https://eskillvisor.wallistry.pk');
}

header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

try {
    $db = Database::getInstance();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get all companies with approval status
        $sql = "SELECT c.*,
                       u.first_name as created_by_name,
                       u.last_name as created_by_lastname,
                       u.role as created_by_role,
                       COUNT(DISTINCT cp.user_id) as partner_count
                FROM companies c
                LEFT JOIN users u ON c.created_by = u.id
                LEFT JOIN company_partners cp ON c.id = cp.company_id AND cp.status = 'active'
                WHERE c.status = 'active'
                GROUP BY c.id
                ORDER BY c.created_at DESC";

        $companies = $db->fetchAll($sql);

        echo json_encode([
            'success' => true,
            'data' => $companies
        ]);
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Create new company
        $input = json_decode(file_get_contents('php://input'), true);

        $name = $input['name'] ?? '';
        $companyDirector = $input['companyDirector'] ?? '';
        $registrationTerritory = $input['registrationTerritory'] ?? '';
        $einNumber = $input['einNumber'] ?? '';
        $marketplace = $input['marketplace'] ?? '';
        $customMarketplace = $input['customMarketplace'] ?? '';
        $description = $input['description'] ?? '';
        $industry = $input['industry'] ?? '';
        $email = $input['email'] ?? '';
        $phone = $input['phone'] ?? '';
        $website = $input['website'] ?? '';
        $address = $input['address'] ?? '';
        $city = $input['city'] ?? '';
        $state = $input['state'] ?? '';
        $country = $input['country'] ?? '';
        $postal_code = $input['postal_code'] ?? '';
        $managerId = $input['managerId'] ?? null;
        $partnerId = $input['partner_id'] ?? null;
        $createdBy = $input['created_by'] ?? null;

        // Determine approval status based on creator role
        $approvalStatus = 'pending';
        if ($createdBy) {
            $creator = $db->fetchOne("SELECT role FROM users WHERE id = ?", [$createdBy]);
            if ($creator && $creator['role'] === 'super_admin') {
                $approvalStatus = 'approved';
            }
        }

        if (empty($name) || empty($companyDirector) || empty($registrationTerritory) || empty($einNumber) || empty($marketplace)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Name, company director, registration territory, EIN number, and marketplace are required']);
            exit();
        }

        try {
            $db->beginTransaction();

            // Insert company
            $result = $db->execute(
                "INSERT INTO companies (name, company_director, registration_territory, ein_number, marketplace, custom_marketplace, description, industry, email, phone, website, address, city, state, country, postal_code, manager_id, created_by, approval_status, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())",
                [$name, $companyDirector, $registrationTerritory, $einNumber, $marketplace, $customMarketplace, $description, $industry, $email, $phone, $website, $address, $city, $state, $country, $postal_code, $managerId, $createdBy, $approvalStatus]
            );

            if (!$result) {
                throw new Exception('Failed to create company');
            }

            $companyId = $db->getLastInsertId();

            // Assign partner to company if specified
            if ($partnerId) {
                $assignResult = $db->execute(
                    "INSERT INTO company_partners (company_id, user_id, assigned_by, status, assigned_at) VALUES (?, ?, ?, 'active', NOW())",
                    [$companyId, $partnerId, $createdBy]
                );

                if (!$assignResult) {
                    throw new Exception('Failed to assign partner to company');
                }
            }

            $db->commit();

            // Get the created company with additional info
            $company = $db->fetchOne(
                "SELECT c.*, u.first_name as created_by_name, u.last_name as created_by_lastname
                 FROM companies c
                 LEFT JOIN users u ON c.created_by = u.id
                 WHERE c.id = ?",
                [$companyId]
            );

            echo json_encode([
                'success' => true,
                'data' => $company,
                'message' => $approvalStatus === 'approved' ? 'Company created and approved' : 'Company created and pending approval'
            ]);

        } catch (Exception $e) {
            $db->rollback();
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        // Handle approval/rejection
        $pathInfo = $_SERVER['PATH_INFO'] ?? '';
        $pathParts = explode('/', trim($pathInfo, '/'));

        if (count($pathParts) >= 2 && $pathParts[1] === 'approve') {
            // Approve company
            $companyId = $pathParts[0];
            $input = json_decode(file_get_contents('php://input'), true);
            $approvedBy = $input['approved_by'] ?? null;

            $result = $db->execute(
                "UPDATE companies SET approval_status = 'approved', approved_by = ?, approved_at = NOW() WHERE id = ?",
                [$approvedBy, $companyId]
            );

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Company approved successfully']);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to approve company']);
            }
        } elseif (count($pathParts) >= 2 && $pathParts[1] === 'reject') {
            // Reject company
            $companyId = $pathParts[0];
            $input = json_decode(file_get_contents('php://input'), true);
            $rejectedBy = $input['rejected_by'] ?? null;
            $rejectionReason = $input['rejection_reason'] ?? '';

            $result = $db->execute(
                "UPDATE companies SET approval_status = 'rejected', rejected_by = ?, rejected_at = NOW(), rejection_reason = ? WHERE id = ?",
                [$rejectedBy, $rejectionReason, $companyId]
            );

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Company rejected successfully']);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to reject company']);
            }
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Endpoint not found']);
        }
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
