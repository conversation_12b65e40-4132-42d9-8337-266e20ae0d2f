import React, { useState, useEffect, useContext } from 'react';
import {
  UsersIcon,
  Building2Icon,
  PackageIcon,
  TrendingUpIcon,
  DollarSignIcon,
  AlertTriangleIcon,
  BarChart3Icon,
  PieChartIcon,
  ActivityIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EyeIcon,
  UserPlusIcon,
  BuildingIcon
} from 'lucide-react';
import { AuthContext } from '../../App.jsx';
import inventoryService from '../../services/inventoryService.js';
import userService from '../../services/userService.js';
import companyService from '../../services/companyService.js';

const SuperadminDashboard = () => {
  const { user } = useContext(AuthContext);
  const [dashboardData, setDashboardData] = useState({
    users: { total_users: 0, active_users: 0, new_users_today: 0 },
    companies: { total_companies: 0, active_companies: 0, new_companies_today: 0 },
    inventory: { total_items: 0, total_value: 0, low_stock_count: 0 },
    top_categories: [],
    recent_activity: []
  });
  const [loading, setLoading] = useState(true);
  const [trends, setTrends] = useState([]);

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);

        // Get dashboard data from API
        const data = await inventoryService.getDashboardData();

        if (data) {
          setDashboardData({
            users: data.users || { total_users: 0, active_users: 0, new_users_today: 0 },
            companies: data.companies || { total_companies: 0, active_companies: 0, new_companies_today: 0 },
            inventory: data.inventory || { total_items: 0, total_value: 0, low_stock_count: 0 },
            top_categories: data.top_categories || [],
            recent_activity: data.recent_activity || []
          });
        }

        // Get trends data
        const trendsResponse = await inventoryService.getTrends();
        if (trendsResponse) {
          setTrends(trendsResponse.trends || []);
        }
      } catch (error) {
        console.error('Failed to load dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      loadDashboardData();
    }
  }, [user]);

  const StatCard = ({ title, value, icon, color = 'blue', subtitle, trend }) => {
    const colorClasses = {
      blue: 'bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 border-blue-200',
      green: 'bg-gradient-to-br from-green-50 to-green-100 text-green-600 border-green-200',
      purple: 'bg-gradient-to-br from-purple-50 to-purple-100 text-purple-600 border-purple-200',
      orange: 'bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600 border-orange-200',
      red: 'bg-gradient-to-br from-red-50 to-red-100 text-red-600 border-red-200',
      indigo: 'bg-gradient-to-br from-indigo-50 to-indigo-100 text-indigo-600 border-indigo-200'
    };

    return (
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
            <p className="text-3xl font-bold text-gray-900 mb-2">{value}</p>
            {subtitle && (
              <p className="text-sm text-gray-500">{subtitle}</p>
            )}
            {trend && (
              <div className="flex items-center mt-2">
                {trend.direction === 'up' ? (
                  <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4 text-red-500 mr-1" />
                )}
                <span className={`text-sm font-medium ${trend.direction === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                  {trend.value}
                </span>
                <span className="text-sm text-gray-500 ml-1">vs last month</span>
              </div>
            )}
          </div>
          <div className={`p-4 rounded-xl border ${colorClasses[color]}`}>
            {icon}
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Super Admin Dashboard</h1>
          <p className="text-gray-600">System-wide overview and analytics</p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Super Admin Dashboard</h1>
        <p className="text-gray-600">Complete system overview and business intelligence - Enhanced v2.0</p>
      </div>

      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <StatCard
          title="Total Users"
          value={dashboardData.users.total_users}
          subtitle={`${dashboardData.users.active_users} active users`}
          icon={<UsersIcon className="h-8 w-8" />}
          color="blue"
          trend={{ direction: 'up', value: '+12%' }}
        />
        <StatCard
          title="Companies"
          value={dashboardData.companies.total_companies}
          subtitle={`${dashboardData.companies.active_companies} active companies`}
          icon={<Building2Icon className="h-8 w-8" />}
          color="green"
          trend={{ direction: 'up', value: '+8%' }}
        />
        <StatCard
          title="Total Inventory Value"
          value={`$${dashboardData.inventory.total_value.toLocaleString()}`}
          subtitle={`${dashboardData.inventory.total_items} total items`}
          icon={<DollarSignIcon className="h-8 w-8" />}
          color="purple"
          trend={{ direction: 'up', value: '+15%' }}
        />
      </div>

      {/* Secondary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <StatCard
          title="Low Stock Alerts"
          value={dashboardData.inventory.low_stock_count}
          subtitle="Items need restocking"
          icon={<AlertTriangleIcon className="h-6 w-6" />}
          color="red"
        />
        <StatCard
          title="New Users Today"
          value={dashboardData.users.new_users_today}
          subtitle="User registrations"
          icon={<UserPlusIcon className="h-6 w-6" />}
          color="indigo"
        />
        <StatCard
          title="New Companies"
          value={dashboardData.companies.new_companies_today}
          subtitle="Companies added today"
          icon={<BuildingIcon className="h-6 w-6" />}
          color="orange"
        />
      </div>

      {/* Analytics Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Top Categories */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center">
              <PieChartIcon className="h-5 w-5 text-purple-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Top Inventory Categories</h3>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {dashboardData.top_categories.slice(0, 5).map((category, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-3 ${
                      ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500', 'bg-red-500'][index]
                    }`}></div>
                    <span className="font-medium text-gray-900">{category.category || 'Uncategorized'}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-semibold text-gray-900">{category.item_count} items</div>
                    <div className="text-xs text-gray-500">${category.total_value?.toLocaleString()}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center">
              <ActivityIcon className="h-5 w-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Recent System Activity</h3>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {dashboardData.recent_activity.slice(0, 6).map((activity, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    activity.action === 'create' ? 'bg-green-500' :
                    activity.action === 'update' ? 'bg-blue-500' :
                    activity.action === 'delete' ? 'bg-red-500' : 'bg-gray-500'
                  }`}></div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">
                      {activity.action === 'create' && 'Created '}
                      {activity.action === 'update' && 'Updated '}
                      {activity.action === 'delete' && 'Deleted '}
                      {activity.entity_type}
                    </p>
                    <p className="text-xs text-gray-500">
                      {activity.user_name} • {new Date(activity.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Business Intelligence */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Growth Trends */}
        <div className="lg:col-span-2 bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center">
              <BarChart3Icon className="h-5 w-5 text-green-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Growth Trends (Last 30 Days)</h3>
            </div>
          </div>
          <div className="p-6">
            {trends.length > 0 ? (
              <div className="space-y-4">
                {trends.slice(-7).map((trend, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {new Date(trend.date).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-gray-500">
                        {trend.items_added} items added
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-semibold text-green-600">
                        +${trend.value_added?.toLocaleString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <BarChart3Icon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No trend data available</p>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
          </div>
          <div className="p-6">
            <div className="space-y-3">
              <button className="w-full flex items-center justify-between p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                <div className="flex items-center">
                  <UserPlusIcon className="h-5 w-5 text-blue-600 mr-3" />
                  <span className="text-sm font-medium text-blue-900">Create New Partner</span>
                </div>
                <ArrowUpIcon className="h-4 w-4 text-blue-600 transform rotate-45" />
              </button>

              <button className="w-full flex items-center justify-between p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors">
                <div className="flex items-center">
                  <BuildingIcon className="h-5 w-5 text-green-600 mr-3" />
                  <span className="text-sm font-medium text-green-900">Add Company</span>
                </div>
                <ArrowUpIcon className="h-4 w-4 text-green-600 transform rotate-45" />
              </button>

              <button className="w-full flex items-center justify-between p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
                <div className="flex items-center">
                  <BarChart3Icon className="h-5 w-5 text-purple-600 mr-3" />
                  <span className="text-sm font-medium text-purple-900">View Reports</span>
                </div>
                <ArrowUpIcon className="h-4 w-4 text-purple-600 transform rotate-45" />
              </button>

              <button className="w-full flex items-center justify-between p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors">
                <div className="flex items-center">
                  <EyeIcon className="h-5 w-5 text-orange-600 mr-3" />
                  <span className="text-sm font-medium text-orange-900">System Overview</span>
                </div>
                <ArrowUpIcon className="h-4 w-4 text-orange-600 transform rotate-45" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuperadminDashboard;
