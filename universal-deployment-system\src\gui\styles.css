/* Universal Deployment System - Professional Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    height: 100vh;
    overflow: hidden;
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: #f8f9fa;
}

/* Header Styles */
.app-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo i {
    font-size: 2rem;
    color: #3498db;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 350px;
    background: white;
    border-right: 1px solid #e9ecef;
    padding: 1.5rem;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0,0,0,0.05);
}

.sidebar h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sidebar h3 i {
    color: #3498db;
}

.project-section,
.config-section,
.actions-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.actions-section {
    border-bottom: none;
}

/* Project Selector */
.project-selector {
    margin-bottom: 1rem;
}

.project-path {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 0.9rem;
    color: #6c757d;
    word-break: break-all;
    min-height: 2rem;
    display: flex;
    align-items: center;
}

/* Form Styles */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #495057;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-block {
    width: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #545b62;
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: linear-gradient(135deg, #229954, #1e7e34);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.btn-info:hover:not(:disabled) {
    background: linear-gradient(135deg, #138496, #0f6674);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background: linear-gradient(135deg, #e67e22, #d35400);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
}

/* Content Area */
.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Status Bar */
.status-bar {
    background: white;
    padding: 1rem 2rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

#statusText {
    font-weight: 500;
    color: #495057;
}

.progress-bar {
    width: 200px;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2980b9);
    width: 0%;
    transition: width 0.3s ease;
    animation: progress-animation 2s infinite;
}

@keyframes progress-animation {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.last-update {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Display Area */
.display-area {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    background: #f8f9fa;
}

/* Welcome Screen */
.welcome-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
}

.welcome-content {
    max-width: 600px;
}

.welcome-icon {
    font-size: 4rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.welcome-content h2 {
    font-size: 2rem;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.welcome-content p {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

.welcome-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.step {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.step i {
    font-size: 1.5rem;
    color: #3498db;
}

/* Changes Display */
.changes-display {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 16px rgba(0,0,0,0.1);
    overflow: hidden;
}

.changes-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 1.5rem 2rem;
}

.changes-header h2 {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.changes-summary {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.summary-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.summary-value {
    font-size: 1.5rem;
    font-weight: 600;
}

.summary-value.added {
    color: #2ecc71;
}

.summary-value.modified {
    color: #f39c12;
}

/* Tabs */
.changes-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.tab-btn {
    padding: 1rem 2rem;
    border: none;
    background: transparent;
    cursor: pointer;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.15s ease;
}

.tab-btn.active {
    background: white;
    color: #3498db;
    border-bottom: 2px solid #3498db;
}

.tab-btn:hover {
    background: #e9ecef;
}

.tab-content {
    padding: 2rem;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* Categories List */
.category-item {
    margin-bottom: 1.5rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.category-header {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: background 0.15s ease;
}

.category-header:hover {
    background: #e9ecef;
}

.category-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.category-count {
    background: #3498db;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
}

.category-files {
    padding: 1rem 1.5rem;
    background: white;
}

.file-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.file-item:last-child {
    border-bottom: none;
}

.file-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.file-status.added {
    background: #2ecc71;
}

.file-status.modified {
    background: #f39c12;
}

.file-status.deleted {
    background: #e74c3c;
}

.file-path {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.file-size {
    font-size: 0.8rem;
    color: #6c757d;
}

/* Features List */
.feature-item {
    padding: 1rem;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.feature-title {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.feature-description {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Deployment Results */
.deployment-results {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 16px rgba(0,0,0,0.1);
    overflow: hidden;
}

.results-header {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    padding: 1.5rem 2rem;
}

.results-header h2 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.results-content {
    padding: 2rem;
}

.package-info {
    margin-bottom: 2rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 500;
    color: #495057;
}

.info-value {
    color: #6c757d;
    font-family: 'Courier New', monospace;
}

.deployment-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Footer */
.app-footer {
    background: #2c3e50;
    color: white;
    padding: 1rem 2rem;
    text-align: center;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#loadingText {
    color: #495057;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        max-height: 300px;
    }

    .changes-summary {
        gap: 1rem;
    }

    .deployment-actions {
        flex-direction: column;
    }

    .footer-content {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
