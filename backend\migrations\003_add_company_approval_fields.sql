-- Add approval fields to companies table
ALTER TABLE companies 
ADD COLUMN approval_status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending' AFTER status,
ADD COLUMN approved_by INT NULL AFTER approval_status,
ADD COLUMN approved_at TIMESTAMP NULL AFTER approved_by,
ADD COLUMN rejected_by INT NULL AFTER approved_at,
ADD COLUMN rejected_at TIMESTAMP NULL AFTER rejected_by,
ADD COLUMN rejection_reason TEXT NULL AFTER rejected_at;

-- Add foreign key constraints
ALTER TABLE companies 
ADD CONSTRAINT fk_companies_approved_by <PERSON>OR<PERSON>G<PERSON> KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
ADD CONSTRAINT fk_companies_rejected_by FOREIGN KEY (rejected_by) REFERENCES users(id) ON DELETE SET NULL;

-- Add indexes for better performance
ALTER TABLE companies 
ADD INDEX idx_approval_status (approval_status),
ADD INDEX idx_approved_by (approved_by),
ADD INDEX idx_rejected_by (rejected_by);
