# Security configuration for uploads directory
# Prevent direct access to uploaded files and execution of scripts

# Deny access to all files by default
Require all denied

# Allow access only to specific file types
<FilesMatch "\.(jpg|jpeg|png|gif|pdf|xlsx|xls|csv)$">
    Require all granted
</FilesMatch>

# Prevent execution of PHP files
<FilesMatch "\.php$">
    Require all denied
</FilesMatch>

# Prevent execution of other script files
<FilesMatch "\.(pl|py|jsp|asp|sh|cgi)$">
    Require all denied
</FilesMatch>

# Remove PHP handler
RemoveHandler .php .phtml .php3 .php4 .php5 .php6

# Disable script execution
Options -ExecCGI
AddHandler cgi-script .php .pl .py .jsp .asp .sh .cgi

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY

# Prevent hotlinking
RewriteEngine On
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?wallistry\.pk [NC]
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?inventory-system-e-skill-visor\.vercel\.app [NC]
RewriteRule \.(jpg|jpeg|png|gif|pdf|xlsx|xls|csv)$ - [F]

# Limit file size (10MB)
LimitRequestBody 10485760
