#!/usr/bin/env node

/**
 * Production Build Script for Investment System
 * Builds both frontend and backend for cPanel deployment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Starting Investment System Production Build...\n');

// Configuration
const DIST_DIR = './dist';
const FRONTEND_DIST = './dist/frontend';
const BACKEND_DIST = './dist/backend';

// Helper functions
function createDirectory(dir) {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`✅ Created directory: ${dir}`);
    }
}

function copyFile(src, dest) {
    const destDir = path.dirname(dest);
    createDirectory(destDir);
    fs.copyFileSync(src, dest);
}

function copyDirectory(src, dest, exclude = []) {
    createDirectory(dest);
    
    const items = fs.readdirSync(src);
    
    for (const item of items) {
        if (exclude.includes(item)) continue;
        
        const srcPath = path.join(src, item);
        const destPath = path.join(dest, item);
        
        if (fs.statSync(srcPath).isDirectory()) {
            copyDirectory(srcPath, destPath, exclude);
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
    }
}

function replaceInFile(filePath, replacements) {
    let content = fs.readFileSync(filePath, 'utf8');
    
    for (const [search, replace] of Object.entries(replacements)) {
        content = content.replace(new RegExp(search, 'g'), replace);
    }
    
    fs.writeFileSync(filePath, content);
}

// Step 1: Clean previous build
console.log('🧹 Cleaning previous build...');
if (fs.existsSync(DIST_DIR)) {
    fs.rmSync(DIST_DIR, { recursive: true, force: true });
}
createDirectory(DIST_DIR);

// Step 2: Build Frontend
console.log('🔨 Building frontend for production...');
try {
    // Copy production environment file
    fs.copyFileSync('.env.production', '.env');
    
    // Build with Vite
    execSync('npm run build', { stdio: 'inherit' });
    
    console.log('✅ Frontend build completed');
} catch (error) {
    console.error('❌ Frontend build failed:', error.message);
    process.exit(1);
}

// Step 3: Prepare Backend
console.log('🔧 Preparing backend for production...');

// Copy backend files
const backendExcludes = [
    'node_modules',
    '.git',
    '.env',
    'logs',
    'uploads',
    'temp'
];

copyDirectory('./backend', BACKEND_DIST, backendExcludes);

// Create production directories
createDirectory(path.join(BACKEND_DIST, 'logs'));
createDirectory(path.join(BACKEND_DIST, 'uploads'));
createDirectory(path.join(BACKEND_DIST, 'uploads/company-documents'));

// Copy production config files
copyFile('./backend/config/config.production.php', path.join(BACKEND_DIST, 'config/config.php'));
copyFile('./backend/config/database.production.php', path.join(BACKEND_DIST, 'config/database.php'));

// Step 4: Create .htaccess files
console.log('📝 Creating .htaccess files...');

// Frontend .htaccess for SPA routing
const frontendHtaccess = `# Investment System Frontend .htaccess
RewriteEngine On

# Handle Angular and React Router
RewriteBase /

# Handle client-side routing
RewriteRule ^index\\.html$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=********; includeSubDomains"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>
`;

// Backend .htaccess for API routing
const backendHtaccess = `# Investment System Backend .htaccess
RewriteEngine On

# Security - Hide sensitive files
<Files "*.php">
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
</Files>

# Deny access to config and sensitive directories
<FilesMatch "\\.(env|log|ini)$">
    Order allow,deny
    Deny from all
</FilesMatch>

<DirectoryMatch "^.*/\\.(git|svn|logs)/">
    Order allow,deny
    Deny from all
</DirectoryMatch>

# PHP settings
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value memory_limit 256M

# Error handling
php_flag display_errors Off
php_flag log_errors On

# CORS headers (handled in PHP files, but backup)
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
</IfModule>
`;

fs.writeFileSync(path.join(FRONTEND_DIST, '.htaccess'), frontendHtaccess);
fs.writeFileSync(path.join(BACKEND_DIST, '.htaccess'), backendHtaccess);

// Step 5: Create deployment structure
console.log('📦 Creating deployment structure...');

// Create deployment instructions
const deploymentInstructions = `# Investment System - cPanel Deployment Instructions

## 📁 Deployment Structure

### Frontend Files (Upload to public_html/)
- Upload all contents of \`dist/frontend/\` to your cPanel public_html directory
- The structure should be:
  \`\`\`
  public_html/
  ├── index.html
  ├── .htaccess
  ├── assets/
  │   ├── css/
  │   ├── js/
  │   └── images/
  └── backend/ (see below)
  \`\`\`

### Backend Files (Upload to public_html/backend/)
- Create a \`backend\` folder in public_html
- Upload all contents of \`dist/backend/\` to public_html/backend/
- The structure should be:
  \`\`\`
  public_html/backend/
  ├── .htaccess
  ├── config/
  ├── *.php (all API files)
  ├── uploads/ (create with 755 permissions)
  └── logs/ (create with 755 permissions)
  \`\`\`

## 🗄️ Database Setup

1. Create a new MySQL database in cPanel
2. Create a database user with full privileges
3. Import the database schema from \`database/investment_system.sql\`
4. Update database credentials in \`backend/config/config.php\`

## 🔧 Configuration

1. Update \`backend/config/config.php\` with your actual:
   - Database credentials
   - JWT secret key
   - Encryption key
   - SMTP settings (if using email)

2. Set proper file permissions:
   - \`backend/uploads/\` → 755
   - \`backend/logs/\` → 755
   - \`backend/config/\` → 644

## 🌐 Domain Configuration

The system is configured for: https://eskillvisor.wallistry.pk

If using a different domain, update:
1. \`.env.production\` file
2. Rebuild with \`npm run build:production\`

## ✅ Testing

After deployment, test:
1. Frontend: https://eskillvisor.wallistry.pk
2. Backend API: https://eskillvisor.wallistry.pk/backend/companies.php
3. Login functionality
4. File upload functionality

## 🔄 Updates

To update the system:
1. Make changes locally
2. Run \`npm run build:production\`
3. Upload only changed files to cPanel
`;

fs.writeFileSync(path.join(DIST_DIR, 'DEPLOYMENT_INSTRUCTIONS.md'), deploymentInstructions);

// Step 6: Create database export
console.log('💾 Creating database export...');
createDirectory(path.join(DIST_DIR, 'database'));

// Copy database schema if it exists
if (fs.existsSync('./database')) {
    copyDirectory('./database', path.join(DIST_DIR, 'database'));
}

// Step 7: Create update script
const updateScript = `#!/usr/bin/env node

/**
 * Quick Update Script
 * Use this to rebuild and update production files
 */

const { execSync } = require('child_process');

console.log('🔄 Updating Investment System...');

try {
    // Copy production environment
    require('fs').copyFileSync('.env.production', '.env');
    
    // Rebuild
    execSync('npm run build', { stdio: 'inherit' });
    
    console.log('✅ Update completed!');
    console.log('📁 Upload dist/frontend/ to public_html/');
    console.log('📁 Upload dist/backend/ to public_html/backend/');
    
} catch (error) {
    console.error('❌ Update failed:', error.message);
}
`;

fs.writeFileSync(path.join(DIST_DIR, 'update.js'), updateScript);

// Restore local environment
fs.copyFileSync('.env.local', '.env');

console.log('\n🎉 Production build completed successfully!');
console.log('\n📁 Deployment files created in ./dist/');
console.log('📖 See DEPLOYMENT_INSTRUCTIONS.md for upload instructions');
console.log('\n🌐 Configured for: https://eskillvisor.wallistry.pk');
