# Testing & Verification Guide for cPanel Deployment

## 🧪 **Complete Testing Checklist**

This guide provides a comprehensive testing checklist to ensure your EskillVisor application works correctly on the custom domain.

## 🌐 **Pre-Testing Setup**

### **1. Domain Accessibility**
- [ ] Custom domain loads: `https://eskillvisor.wallistry.pk`
- [ ] SSL certificate is active (green lock icon)
- [ ] No mixed content warnings
- [ ] Page loads without errors

### **2. DNS Verification**
```bash
# Check DNS resolution
nslookup eskillvisor.wallistry.pk

# Check HTTP response
curl -I https://eskillvisor.wallistry.pk

# Expected: 200 OK response
```

## 🔧 **Backend API Testing**

### **1. API Endpoint Accessibility**

Test each API endpoint individually:

```bash
# Test main API
curl https://eskillvisor.wallistry.pk/api/test

# Expected response:
{
  "success": true,
  "message": "API endpoint is working!",
  "timestamp": "2024-XX-XXTXX:XX:XX+00:00"
}
```

```bash
# Test login endpoint (should return method not allowed for GET)
curl https://eskillvisor.wallistry.pk/api/login.php

# Expected response:
{
  "success": false,
  "message": "Method not allowed"
}
```

```bash
# Test companies endpoint
curl https://eskillvisor.wallistry.pk/api/companies.php

# Expected: JSON response with companies data
```

```bash
# Test users endpoint
curl https://eskillvisor.wallistry.pk/api/users.php

# Expected: JSON response with users data
```

### **2. CORS Testing**

Open browser developer tools and check:
- [ ] No CORS errors in console
- [ ] API requests succeed from frontend
- [ ] Proper CORS headers in response

### **3. Database Connection**

Verify database connectivity:
- [ ] API endpoints return data (not connection errors)
- [ ] Login attempts reach database
- [ ] No database connection errors in logs

## 🎨 **Frontend Testing**

### **1. Page Loading**
- [ ] Homepage loads completely
- [ ] All CSS styles applied correctly
- [ ] All JavaScript files load without errors
- [ ] No 404 errors for assets

### **2. Navigation**
- [ ] All navigation links work
- [ ] Page routing functions correctly
- [ ] Browser back/forward buttons work
- [ ] Direct URL access works for all routes

### **3. Responsive Design**
- [ ] Desktop view (1920x1080)
- [ ] Tablet view (768x1024)
- [ ] Mobile view (375x667)
- [ ] All elements properly responsive

## 🔐 **Authentication Testing**

### **1. Login Functionality**

**Test with default accounts:**

```
Super Admin:
- Email: <EMAIL>
- Password: password

Manager:
- Email: <EMAIL>
- Password: password

Partner:
- Email: <EMAIL>
- Password: password
```

**Test Cases:**
- [ ] Valid login redirects to correct dashboard
- [ ] Invalid credentials show error message
- [ ] Empty fields show validation errors
- [ ] Login state persists on page refresh
- [ ] Logout functionality works

### **2. Role-Based Access**

**Super Admin Tests:**
- [ ] Can access all sections
- [ ] Can create users with any role
- [ ] Can create companies with manager assignment
- [ ] Can view all data

**Manager Tests:**
- [ ] Can access manager sections
- [ ] Can only create partner accounts
- [ ] Can create companies (auto-assigned as manager)
- [ ] Cannot access super admin features

**Partner Tests:**
- [ ] Can access partner sections
- [ ] Limited to assigned companies
- [ ] Cannot create users or companies
- [ ] Proper access restrictions

## 👥 **User Management Testing**

### **1. Add User Functionality**

**Super Admin:**
- [ ] Add User button opens modal
- [ ] All role options available (superadmin, manager, partner)
- [ ] Form validation works
- [ ] User creation succeeds
- [ ] New user appears in list

**Manager:**
- [ ] Add Partner button opens modal
- [ ] Only partner role available
- [ ] Form validation works
- [ ] Partner creation succeeds
- [ ] New partner appears in list

### **2. User List Display**
- [ ] All users display correctly
- [ ] User information accurate
- [ ] Status indicators work
- [ ] Search/filter functionality (if implemented)

## 🏢 **Company Management Testing**

### **1. Add Company Functionality**

**Super Admin:**
- [ ] Add Company button opens modal
- [ ] Manager assignment field visible
- [ ] Can assign or leave unassigned
- [ ] Form validation works
- [ ] Company creation succeeds

**Manager:**
- [ ] Add Company button opens modal
- [ ] No manager assignment field
- [ ] Auto-assigned as manager
- [ ] Form validation works
- [ ] Company creation succeeds

### **2. Company List Display**
- [ ] All companies display correctly
- [ ] Company information accurate
- [ ] Manager assignments shown
- [ ] Status indicators work

## 📊 **Dashboard Testing**

### **1. Dashboard Loading**
- [ ] Dashboard loads for all user roles
- [ ] Appropriate data displayed for each role
- [ ] Charts/graphs render correctly (if any)
- [ ] Statistics are accurate

### **2. Navigation Menu**
- [ ] Sidebar navigation works
- [ ] Menu items appropriate for user role
- [ ] Active page highlighted
- [ ] Mobile menu functionality

## 🔍 **Error Handling Testing**

### **1. Network Errors**
- [ ] Graceful handling of API failures
- [ ] User-friendly error messages
- [ ] Retry mechanisms work
- [ ] No application crashes

### **2. Validation Errors**
- [ ] Form validation messages clear
- [ ] Required field indicators
- [ ] Format validation (email, etc.)
- [ ] Error state clearing

## 🚀 **Performance Testing**

### **1. Page Load Speed**
- [ ] Initial page load < 3 seconds
- [ ] Subsequent navigation < 1 second
- [ ] API responses < 2 seconds
- [ ] No unnecessary requests

### **2. Resource Loading**
- [ ] All assets load efficiently
- [ ] No broken images or resources
- [ ] Proper caching headers
- [ ] Optimized file sizes

## 🔒 **Security Testing**

### **1. HTTPS Enforcement**
- [ ] HTTP redirects to HTTPS
- [ ] SSL certificate valid
- [ ] No mixed content warnings
- [ ] Secure headers present

### **2. Authentication Security**
- [ ] Sessions expire appropriately
- [ ] Unauthorized access blocked
- [ ] Password requirements enforced
- [ ] No sensitive data in URLs

## 📱 **Cross-Browser Testing**

Test in multiple browsers:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile browsers

## 🐛 **Common Issues & Solutions**

### **1. API Not Responding**
```bash
# Check if API is accessible
curl https://eskillvisor.wallistry.pk/api/test

# Check PHP error logs in cPanel
# File Manager → Error Logs
```

### **2. CORS Errors**
- Verify CORS settings in backend files
- Check allowed origins include your domain
- Clear browser cache

### **3. 404 Errors**
- Verify file upload to correct directory
- Check file permissions (644/755)
- Confirm domain document root

### **4. Database Connection Issues**
- Check database credentials in config.php
- Verify database server status
- Check database user permissions

## 📋 **Final Verification Checklist**

- [ ] All API endpoints respond correctly
- [ ] Frontend loads and functions properly
- [ ] Authentication works for all user types
- [ ] Role-based permissions enforced
- [ ] User management fully functional
- [ ] Company management fully functional
- [ ] No console errors
- [ ] No network errors
- [ ] SSL certificate active
- [ ] Performance acceptable
- [ ] Cross-browser compatibility confirmed

## 📞 **Support & Debugging**

### **Browser Developer Tools:**
1. **Console Tab**: Check for JavaScript errors
2. **Network Tab**: Monitor API requests/responses
3. **Application Tab**: Check local storage/cookies

### **cPanel Tools:**
1. **Error Logs**: Check PHP errors
2. **File Manager**: Verify file permissions
3. **Database**: Check database connectivity

### **External Tools:**
- **SSL Test**: https://www.ssllabs.com/ssltest/
- **DNS Check**: https://dnschecker.org/
- **Performance**: https://pagespeed.web.dev/
