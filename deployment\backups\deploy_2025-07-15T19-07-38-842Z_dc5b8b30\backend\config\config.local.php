<?php
/**
 * Local Development Configuration
 * This file contains settings for local development environment
 */

// Application settings
define('APP_NAME', 'EskillVisor Investment System');
define('APP_VERSION', '1.0.0');
define('APP_ENV', 'development');
define('APP_DEBUG', true);

// Database configuration for local development
define('DB_HOST', 'localhost');
define('DB_NAME', 'eskillvisor_local');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// JWT Configuration - Development keys
define('JWT_SECRET', 'EskillVisor_Local_JWT_Secret_2024_Development_Key');
define('JWT_ALGORITHM', 'HS256');
define('JWT_EXPIRATION', 3600 * 24); // 24 hours
define('JWT_REFRESH_EXPIRATION', 3600 * 24 * 7); // 7 days

// File upload settings
define('UPLOAD_MAX_SIZE', 10 * 1024 * 1024); // 10MB
define('UPLOAD_ALLOWED_TYPES', ['xlsx', 'xls', 'pdf']);
define('UPLOAD_PATH', __DIR__ . '/../uploads/');

// Email configuration (for password reset)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'EskillVisor System');

// API Configuration
define('API_RATE_LIMIT', 1000); // requests per minute (higher for development)
define('API_TIMEOUT', 30); // seconds

// Security settings
define('BCRYPT_COST', 10); // Lower for development
define('SESSION_LIFETIME', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 10); // Higher for development
define('LOGIN_LOCKOUT_TIME', 300); // 5 minutes

// Logging
define('LOG_LEVEL', 'DEBUG'); // Full logging for development
define('LOG_MAX_FILES', 7); // Keep logs for 7 days

// Pagination
define('DEFAULT_PAGE_SIZE', 20);
define('MAX_PAGE_SIZE', 100);

// Notification settings
define('LOW_STOCK_THRESHOLD', 10);
define('NOTIFICATION_RETENTION_DAYS', 30);

// File processing
define('EXCEL_MAX_ROWS', 10000);
define('PDF_MAX_SIZE', 5 * 1024 * 1024); // 5MB

// Cache settings
define('CACHE_ENABLED', false); // Disabled for development
define('CACHE_TTL', 300); // 5 minutes

// CORS settings for development
define('CORS_ALLOWED_ORIGINS', [
    'http://localhost:3000',
    'http://localhost:5173',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:5173',
    'https://eskillvisor.wallistry.pk'
]);

// Error reporting - enabled for development
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/php_errors.log');

// Timezone
date_default_timezone_set('UTC');

// Include helper functions if file exists
if (file_exists(__DIR__ . '/../core/helpers.php')) {
    require_once __DIR__ . '/../core/helpers.php';
}
?>
