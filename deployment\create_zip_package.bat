@echo off
echo Creating deployment ZIP package...

:: Create temporary directory structure
mkdir temp_package\src\components\modals 2>nul
mkdir temp_package\src\pages\superadmin 2>nul
mkdir temp_package\src\pages\manager 2>nul

:: Copy files to temporary structure
copy "files\src\components\modals\UserProfileModal.jsx" "temp_package\src\components\modals\"
copy "files\src\pages\superadmin\UserManagement.jsx" "temp_package\src\pages\superadmin\"
copy "files\src\components\modals\AddCompanyModal.jsx" "temp_package\src\components\modals\"
copy "files\src\pages\superadmin\CompanyOversight.jsx" "temp_package\src\pages\superadmin\"
copy "files\src\pages\manager\CompanyManagement.jsx" "temp_package\src\pages\manager\"

:: Create ZIP file using PowerShell
powershell "Compress-Archive -Path 'temp_package\*' -DestinationPath 'user-management-updates-2025-07-18.zip' -Force"

:: Clean up temporary directory
rmdir /s /q temp_package

echo ZIP package created: user-management-updates-2025-07-18.zip
pause
