import React, { useState } from 'react';
import { XIcon, CheckIcon, XCircleIcon, BuildingIcon, MailIcon, PhoneIcon, CalendarIcon, AlertCircleIcon, FileTextIcon, EyeIcon } from 'lucide-react';
import PropTypes from 'prop-types';

const CompanyApprovalModal = ({ isOpen, onClose, company, onApprove, onReject }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectForm, setShowRejectForm] = useState(false);
  const [activeDocumentTab, setActiveDocumentTab] = useState('all');

  const handleApprove = async () => {
    setIsSubmitting(true);
    try {
      await onApprove(company.id, {
        approved: true,
        notes: 'Company approved by Super Admin'
      });
    } catch (error) {
      console.error('Failed to approve company:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReject = async () => {
    if (!rejectionReason.trim()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onReject(company.id, rejectionReason);
    } catch (error) {
      console.error('Failed to reject company:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setRejectionReason('');
    setShowRejectForm(false);
    setIsSubmitting(false);
    onClose();
  };

  if (!isOpen || !company) return null;

  const getDocumentStatusColor = (status) => {
    switch (status) {
      case 'verified': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-orange-100 text-orange-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between pb-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Company Approval Review</h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="mt-6">
          {/* Company Information Card */}
          <div className="bg-gray-50 rounded-lg p-6 mb-6">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0 h-16 w-16">
                <div className="h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center">
                  <BuildingIcon className="h-8 w-8 text-blue-600" />
                </div>
              </div>
              <div className="ml-4">
                <h4 className="text-xl font-semibold text-gray-900">{company.name}</h4>
                <p className="text-sm text-gray-600">Pending approval since {new Date(company.created_at).toLocaleDateString()}</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center">
                  <MailIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Email</p>
                    <p className="text-sm text-gray-600">{company.email || 'Not provided'}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <PhoneIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Phone</p>
                    <p className="text-sm text-gray-600">{company.phone || 'Not provided'}</p>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex items-center">
                  <CalendarIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Industry</p>
                    <p className="text-sm text-gray-600">{company.industry || 'Not specified'}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <BuildingIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Created By</p>
                    <p className="text-sm text-gray-600">Manager ID: {company.created_by}</p>
                  </div>
                </div>
              </div>
            </div>

            {company.description && (
              <div className="mt-4">
                <p className="text-sm font-medium text-gray-900 mb-1">Description</p>
                <p className="text-sm text-gray-600">{company.description}</p>
              </div>
            )}
          </div>

          {/* Document Verification Section */}
          {company.documents && company.documents.length > 0 && (
            <div className="mb-6">
              <h5 className="text-lg font-medium text-gray-900 mb-4">Document Verification</h5>
              <div className="bg-white border border-gray-200 rounded-lg">
                <div className="p-4 border-b border-gray-200">
                  <div className="flex space-x-4">
                    {['all', 'registration', 'tax_certificate', 'business_license'].map((tab) => (
                      <button
                        key={tab}
                        onClick={() => setActiveDocumentTab(tab)}
                        className={`px-3 py-2 text-sm font-medium rounded-md ${
                          activeDocumentTab === tab
                            ? 'bg-primary text-white'
                            : 'text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        {tab === 'all' ? 'All Documents' : tab.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </button>
                    ))}
                  </div>
                </div>
                <div className="p-4">
                  <div className="space-y-3">
                    {company.documents
                      .filter(doc => activeDocumentTab === 'all' || doc.type === activeDocumentTab)
                      .map((document, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center">
                            <FileTextIcon className="h-5 w-5 text-gray-400 mr-3" />
                            <div>
                              <p className="text-sm font-medium text-gray-900">{document.filename}</p>
                              <p className="text-xs text-gray-500 capitalize">{document.type.replace('_', ' ')}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getDocumentStatusColor(document.status)}`}>
                              {document.status}
                            </span>
                            <button className="text-primary hover:text-primary-dark">
                              <EyeIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Approval Status */}
          <div className="mb-6">
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircleIcon className="h-5 w-5 text-orange-600 mr-3" />
                <div>
                  <h5 className="text-sm font-medium text-orange-800">Approval Required</h5>
                  <p className="text-sm text-orange-700 mt-1">
                    This company was created by a Manager and requires Super Admin approval before becoming active in the system.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Rejection Form */}
          {showRejectForm && (
            <div className="mb-6">
              <label htmlFor="rejectionReason" className="block text-sm font-medium text-gray-700 mb-2">
                Rejection Reason *
              </label>
              <textarea
                id="rejectionReason"
                rows={4}
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500"
                placeholder="Please provide a reason for rejecting this company..."
                required
              />
              {!rejectionReason.trim() && (
                <p className="mt-1 text-sm text-red-600">Rejection reason is required</p>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              disabled={isSubmitting}
            >
              Cancel
            </button>

            {!showRejectForm ? (
              <>
                <button
                  onClick={() => setShowRejectForm(true)}
                  className="inline-flex items-center px-4 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  disabled={isSubmitting}
                >
                  <XCircleIcon className="h-4 w-4 mr-2" />
                  Reject
                </button>
                <button
                  onClick={handleApprove}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <CheckIcon className="h-4 w-4 mr-2" />
                  )}
                  Approve Company
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => setShowRejectForm(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  disabled={isSubmitting}
                >
                  Back
                </button>
                <button
                  onClick={handleReject}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  disabled={isSubmitting || !rejectionReason.trim()}
                >
                  {isSubmitting ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <XCircleIcon className="h-4 w-4 mr-2" />
                  )}
                  Confirm Rejection
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

CompanyApprovalModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  company: PropTypes.object,
  onApprove: PropTypes.func.isRequired,
  onReject: PropTypes.func.isRequired
};

export default CompanyApprovalModal;
