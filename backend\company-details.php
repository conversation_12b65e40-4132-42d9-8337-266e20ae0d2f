<?php
/**
 * Company Details API - Get complete company information with documents and manager details
 */

// Enable CORS for local development
$allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:3000',
    'http://127.0.0.1:5173',
    'http://127.0.0.1:3000'
];

$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (in_array($origin, $allowedOrigins)) {
    header('Access-Control-Allow-Origin: ' . $origin);
} else {
    header('Access-Control-Allow-Origin: http://localhost:5173');
}

header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

try {
    $db = Database::getInstance();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $companyId = $_GET['id'] ?? null;
        
        if (!$companyId) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Company ID is required']);
            exit();
        }
        
        // Get company details with manager and creator information
        $company = $db->fetchAll(
            "SELECT c.*,
                    m.first_name as manager_first_name,
                    m.last_name as manager_last_name,
                    m.email as manager_email,
                    CONCAT(COALESCE(m.first_name, ''), ' ', COALESCE(m.last_name, '')) as manager_name,
                    creator.first_name as created_by_name,
                    creator.last_name as created_by_lastname,
                    creator.email as created_by_email,
                    approver.first_name as approved_by_name,
                    approver.last_name as approved_by_lastname,
                    rejector.first_name as rejected_by_name,
                    rejector.last_name as rejected_by_lastname
             FROM companies c
             LEFT JOIN users m ON c.manager_id = m.id
             LEFT JOIN users creator ON c.created_by = creator.id
             LEFT JOIN users approver ON c.approved_by = approver.id
             LEFT JOIN users rejector ON c.rejected_by = rejector.id
             WHERE c.id = ?",
            [$companyId]
        );
        
        if (!$company || empty($company) || !isset($company[0])) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Company not found']);
            exit();
        }

        $company = $company[0];
        
        // Get company documents
        $documents = $db->fetchAll(
            "SELECT cd.*, 
                    u.first_name as uploaded_by_name,
                    u.last_name as uploaded_by_lastname
             FROM company_documents cd
             LEFT JOIN users u ON cd.uploaded_by = u.id
             WHERE cd.company_id = ? AND cd.status = 'active'
             ORDER BY cd.uploaded_at DESC",
            [$companyId]
        );
        
        // Get assigned partners
        $partners = $db->fetchAll(
            "SELECT u.id, u.first_name, u.last_name, u.email,
                    cp.assigned_at, cp.status as assignment_status,
                    assigner.first_name as assigned_by_name,
                    assigner.last_name as assigned_by_lastname
             FROM company_partners cp
             JOIN users u ON cp.user_id = u.id
             LEFT JOIN users assigner ON cp.assigned_by = assigner.id
             WHERE cp.company_id = ? AND cp.status = 'active'
             ORDER BY cp.assigned_at DESC",
            [$companyId]
        );
        
        // Get approval history (if we have an audit trail)
        $approvalHistory = [];
        try {
            $approvalHistory = $db->fetchAll(
                "SELECT action, performed_at, 
                        u.first_name, u.last_name, u.email,
                        details
                 FROM audit_trail at
                 LEFT JOIN users u ON at.performed_by = u.id
                 WHERE at.entity_type = 'company' AND at.entity_id = ?
                 ORDER BY at.performed_at DESC",
                [$companyId]
            );
        } catch (Exception $e) {
            // Audit trail table might not exist or have different structure
            $approvalHistory = [];
        }
        
        echo json_encode([
            'success' => true,
            'data' => [
                'company' => $company,
                'documents' => $documents,
                'partners' => $partners,
                'approval_history' => $approvalHistory
            ]
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log("Company Details API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
