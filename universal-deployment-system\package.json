{"name": "universal-deployment-system", "version": "1.0.0", "description": "Professional deployment automation system for web projects", "main": "src/main.js", "scripts": {"start": "electron src/main.js", "dev": "electron src/main.js --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "test": "node src/utils/SystemValidator.js", "validate": "node -e \"const SystemValidator = require('./src/utils/SystemValidator'); new SystemValidator().validateSystem();\"", "install-system": "node install.js", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "postinstall": "node install.js"}, "keywords": ["deployment", "automation", "cpanel", "web-development", "file-sync"], "author": "Universal Deployment System", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"archiver": "^6.0.1", "axios": "^1.6.0", "cheerio": "^1.0.0-rc.12", "crypto": "^1.0.1", "fs-extra": "^11.1.1", "glob": "^10.3.10", "mime-types": "^2.1.35", "node-fetch": "^3.3.2", "path": "^0.12.7"}, "build": {"appId": "com.universaldeployment.app", "productName": "Universal Deployment System", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}